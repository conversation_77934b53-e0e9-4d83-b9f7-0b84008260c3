#!/bin/bash

echo "=== 文件URL问题调试脚本 ==="

# 1. 检查nginx配置
echo "1. 检查nginx配置..."
echo "当前nginx配置文件:"
cat /etc/nginx/conf.d/xuegong.conf | grep -A 10 -B 2 "location /uploads"

# 2. 检查nginx进程和端口
echo ""
echo "2. 检查nginx进程和端口..."
ps aux | grep nginx
netstat -tlnp | grep :8085

# 3. 检查后端服务
echo ""
echo "3. 检查后端服务..."
ps aux | grep "node server.js"
netstat -tlnp | grep :3002

# 4. 测试文件访问
echo ""
echo "4. 测试文件访问..."
echo "测试前端8085端口的uploads访问:"
curl -I http://localhost:8085/uploads/ 2>/dev/null | head -3

echo "测试后端3002端口的uploads访问:"
curl -I http://localhost:3002/uploads/ 2>/dev/null | head -3

# 5. 检查数据库中的URL
echo ""
echo "5. 检查数据库中的URL..."
mysql -u root -p12345678 xuegong << 'SQL'
SELECT 'teachers表中的证书URL示例:' as info;
SELECT id, name, title_certificate, degree_certificate FROM teachers 
WHERE (title_certificate IS NOT NULL AND title_certificate != '') 
   OR (degree_certificate IS NOT NULL AND degree_certificate != '') 
LIMIT 3;

SELECT 'teacher_materials表中的文件URL示例:' as info;
SELECT id, material_name, file_url FROM teacher_materials 
WHERE file_url IS NOT NULL AND file_url != '' 
LIMIT 3;

SELECT 'teacher_achievements表中的文件URL示例:' as info;
SELECT id, achievement_name, file_url FROM teacher_achievements 
WHERE file_url IS NOT NULL AND file_url != '' 
LIMIT 3;
SQL

# 6. 检查uploads目录
echo ""
echo "6. 检查uploads目录..."
echo "后端uploads目录结构:"
ls -la /www/wwwroot/xuegong-system/backend/uploads/ 2>/dev/null || echo "uploads目录不存在"

echo "teachers子目录:"
ls -la /www/wwwroot/xuegong-system/backend/uploads/teachers/ 2>/dev/null | head -5

echo "teacher-materials子目录:"
ls -la /www/wwwroot/xuegong-system/backend/uploads/teacher-materials/ 2>/dev/null | head -5

# 7. 检查前端构建文件
echo ""
echo "7. 检查前端构建文件..."
echo "前端dist目录:"
ls -la /www/wwwroot/xuegong-system/dist/ | head -5

echo "前端assets目录中的JS文件（检查是否有新的时间戳）:"
ls -la /www/wwwroot/xuegong-system/dist/assets/*.js | head -3

# 8. 实时测试API
echo ""
echo "8. 实时测试API..."
echo "测试教师列表API:"
curl -s "http://localhost:3002/api/teachers" -H "Authorization: Bearer test" | head -200

echo ""
echo "测试健康检查:"
curl -s "http://localhost:3002/health"

# 9. 检查浏览器可能访问的URL
echo ""
echo "9. 模拟浏览器请求..."
echo "模拟访问一个uploads文件:"
TEST_FILE=$(ls /www/wwwroot/xuegong-system/backend/uploads/teachers/ 2>/dev/null | head -1)
if [ ! -z "$TEST_FILE" ]; then
    echo "测试文件: $TEST_FILE"
    echo "通过8085端口访问:"
    curl -I "http://localhost:8085/uploads/teachers/$TEST_FILE" 2>/dev/null | head -3
    echo "通过3002端口访问:"
    curl -I "http://localhost:3002/uploads/teachers/$TEST_FILE" 2>/dev/null | head -3
else
    echo "没有找到测试文件"
fi

# 10. 检查环境变量
echo ""
echo "10. 检查环境变量..."
echo "PM2进程的环境变量:"
pm2 show xuegong-backend | grep -A 20 "env:"

echo ""
echo "=== 调试完成 ==="
echo "请检查以上输出，找出问题所在。"
