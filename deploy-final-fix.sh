#!/bin/bash

echo "=== 部署最终修复版前端代码 ==="

# 设置变量
LOCAL_DIST_DIR="./dist"
SERVER_IP="***********"
SERVER_USER="root"
SERVER_DIST_DIR="/www/wwwroot/xuegong-system/dist"
SERVER_PROJECT_DIR="/www/wwwroot/xuegong-system"

# 1. 检查本地构建文件
echo "1. 检查本地构建文件..."
if [ ! -d "$LOCAL_DIST_DIR" ]; then
    echo "❌ 本地dist目录不存在，请先运行 npm run build"
    exit 1
fi

echo "✅ 本地构建文件存在"
ls -la "$LOCAL_DIST_DIR"

# 2. 打包构建文件
echo "2. 打包构建文件..."
tar -czf dist-final-fix.tar.gz -C dist .
echo "✅ 构建文件已打包为 dist-final-fix.tar.gz"

# 3. 上传到服务器
echo "3. 上传构建文件到服务器..."
scp dist-final-fix.tar.gz "$SERVER_USER@$SERVER_IP:$SERVER_PROJECT_DIR/"
scp fix-file-url-issue.sh "$SERVER_USER@$SERVER_IP:$SERVER_PROJECT_DIR/"

if [ $? -eq 0 ]; then
    echo "✅ 文件上传成功"
else
    echo "❌ 文件上传失败"
    exit 1
fi

# 4. 在服务器上部署
echo "4. 在服务器上部署..."
ssh "$SERVER_USER@$SERVER_IP" << 'EOF'
cd /www/wwwroot/xuegong-system

# 备份现有前端文件
if [ -d "dist" ]; then
    mv dist dist.backup.$(date +%Y%m%d_%H%M%S)
    echo "✅ 已备份现有前端文件"
fi

# 解压新的前端文件
mkdir -p dist
tar -xzf dist-final-fix.tar.gz -C dist
echo "✅ 新前端文件已部署"

# 设置权限
chown -R www-data:www-data dist
chmod -R 755 dist
echo "✅ 文件权限已设置"

# 执行修复脚本
chmod +x fix-file-url-issue.sh
echo "开始执行修复脚本..."
./fix-file-url-issue.sh

echo "✅ 部署和修复完成"
EOF

# 5. 清理本地文件
echo "5. 清理本地文件..."
rm -f dist-final-fix.tar.gz

# 6. 测试部署结果
echo "6. 测试部署结果..."
echo "等待服务启动..."
sleep 10

echo "测试前端访问:"
curl -s -I http://***********:8085/ | head -1

echo "测试API访问:"
curl -s http://***********:8085/api/health | head -1

echo "测试uploads代理:"
curl -s -I http://***********:8085/uploads/ | head -1

echo ""
echo "=== 部署完成 ==="
echo "前端地址: http://***********:8085"
echo "登录凭据: admin / 123456"
echo ""
echo "现在教师证书文件应该能够正常访问了！"
echo "如果还有问题，请检查浏览器控制台的网络请求。"
