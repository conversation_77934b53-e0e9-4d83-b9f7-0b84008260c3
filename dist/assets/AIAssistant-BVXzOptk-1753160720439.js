import{j as e}from"./index-DXaqwR6F-1753160720439.js";import{a0 as s,r as t,E as a,R as l,F as r,a as i,b4 as n,bm as c,A as x,ae as d,I as m,f as o,g as h}from"./antd-lXsGnH6e-1753160720439.js";import"./vendor-D2RBMdQ0-1753160720439.js";const{TextArea:u}=m,{Title:y,Text:j}=a,p=()=>{const{message:a}=s.useApp(),[m,p]=t.useState([]),[g,b]=t.useState(""),[f,N]=t.useState(!1),v=t.useRef(null);t.useEffect(()=>{var e;null==(e=v.current)||e.scrollIntoView({behavior:"smooth"})},[m]);const w=async()=>{if(!g.trim()||f)return;const e={id:Date.now().toString(),type:"user",content:g.trim(),timestamp:new Date};p(s=>[...s,e]),b(""),N(!0);try{setTimeout(()=>{const s={id:(Date.now()+1).toString(),type:"ai",content:`我收到了您的问题："${e.content}"。\n\n基于当前的学生工作数据，我为您提供以下分析：\n\n📊 数据概览：系统包含学生基本信息、学业表现、心理健康等多维度数据\n🔍 分析建议：建议从学生画像、风险评估、趋势预测等角度深入分析  \n📈 后续行动：可生成详细分析报表，为决策提供数据支持\n\n还有其他需要了解的吗？`,timestamp:new Date};p(e=>[...e,s]),N(!1)},1500)}catch(s){console.error("发送消息失败:",s),a.error("发送消息失败，请重试"),N(!1)}};return e.jsx("div",{className:"min-h-screen bg-gray-50 p-6",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsxs(y,{level:2,className:"mb-2 text-gray-800",children:[e.jsx(l,{className:"mr-3 text-blue-500"}),"小工 AI助手"]}),e.jsx(j,{type:"secondary",className:"text-base",children:"智能学工数据分析与决策支持系统"})]}),e.jsx(r,{className:"shadow-xl border-0 rounded-2xl overflow-hidden",style:{height:"600px"},styles:{body:{padding:0,height:"100%"}},children:e.jsxs("div",{className:"h-full flex flex-col",children:[e.jsxs("div",{className:"bg-white px-6 py-4 border-b border-gray-100 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center",children:e.jsx(l,{className:"text-white"})}),e.jsxs("div",{children:[e.jsx(j,{strong:!0,className:"text-gray-900",children:"小工助手"}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-2"}),e.jsx(j,{type:"secondary",className:"text-xs",children:"在线"})]})]})]}),e.jsx(i,{type:"text",size:"small",icon:e.jsx(n,{}),className:"text-gray-400 hover:text-gray-600",onClick:()=>p([]),children:"清空"})]}),e.jsxs("div",{className:"flex-1 overflow-y-auto p-6 bg-gray-50/50",children:[0===m.length?e.jsxs("div",{className:"text-center py-20",children:[e.jsx("div",{className:"w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(c,{className:"text-2xl text-white"})}),e.jsx(y,{level:4,className:"text-gray-600 mb-2",children:"开始对话"}),e.jsx(j,{type:"secondary",children:"请输入您的问题，我来为您解答"})]}):e.jsxs(e.Fragment,{children:[m.map(s=>e.jsx("div",{className:`flex ${"user"===s.type?"justify-end":"justify-start"} mb-4`,children:e.jsxs("div",{className:`flex ${"user"===s.type?"flex-row-reverse":"flex-row"} max-w-[70%] items-start space-x-3`,children:[e.jsx(x,{size:36,icon:"user"===s.type?e.jsx(h,{}):e.jsx(l,{}),style:{backgroundColor:"user"===s.type?"#1890ff":"#52c41a",flexShrink:0}}),e.jsxs("div",{className:"px-4 py-3 rounded-2xl "+("user"===s.type?"bg-blue-500 text-white":"bg-gray-100 text-gray-900"),style:{borderRadius:"user"===s.type?"18px 18px 4px 18px":"18px 18px 18px 4px"},children:[e.jsx("div",{className:"whitespace-pre-wrap text-sm leading-relaxed",children:s.content}),e.jsx("div",{className:"text-xs mt-2 opacity-70 "+("user"===s.type?"text-blue-100":"text-gray-500"),children:s.timestamp.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"})})]})]})},s.id)),f&&e.jsx("div",{className:"flex justify-start mb-4",children:e.jsxs("div",{className:"flex items-start space-x-3 max-w-[70%]",children:[e.jsx(x,{size:36,icon:e.jsx(l,{}),style:{backgroundColor:"#52c41a"}}),e.jsx("div",{className:"bg-gray-100 px-4 py-3 rounded-2xl rounded-tl-sm",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{className:"flex space-x-1",children:[e.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),e.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),e.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),e.jsx(j,{type:"secondary",className:"text-sm",children:"正在输入..."})]})})]})})]}),e.jsx("div",{ref:v})]}),0===m.length&&e.jsxs("div",{className:"px-6 py-4 bg-white border-t border-gray-100",children:[e.jsx(j,{type:"secondary",className:"text-sm mb-3 block",children:"💡 快捷操作"}),e.jsx(d,{wrap:!0,children:[{text:"分析学生画像",icon:"👥"},{text:"预测学业表现",icon:"📊"},{text:"风险识别",icon:"⚠️"},{text:"生成报表",icon:"📄"},{text:"决策建议",icon:"💡"}].map((s,t)=>e.jsxs(i,{size:"small",type:"default",className:"rounded-full",onClick:()=>b(s.text),children:[s.icon," ",s.text]},t))})]}),e.jsxs("div",{className:"p-6 bg-white border-t border-gray-100",children:[e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(u,{value:g,onChange:e=>b(e.target.value),placeholder:"请输入您的问题...",autoSize:{minRows:1,maxRows:4},className:"flex-1",onPressEnter:e=>{e.shiftKey||f||(e.preventDefault(),w())},disabled:f}),e.jsx(i,{type:"primary",icon:e.jsx(o,{}),onClick:w,loading:f,disabled:!g.trim(),className:"h-auto",children:"发送"})]}),e.jsx("div",{className:"text-center mt-3",children:e.jsx(j,{type:"secondary",className:"text-xs",children:"🤖 AI助手 · 回答仅供参考，请谨慎核实重要信息"})})]})]})})]})})};export{p as default};
