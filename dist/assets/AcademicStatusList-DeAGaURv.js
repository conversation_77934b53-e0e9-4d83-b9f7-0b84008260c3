import{j as e,a as t,w as s,b as a,d as r,u as n}from"./index-DP6eZxW9.js";import{r as l,at as d,s as i,M as c,F as o,E as u,ae as x,a as h,au as m,_ as g,av as j,aw as p,L as y,ax as S,O as w,ay as f,N as I,ag as v,V as k,G as b,H as N,J as C,af as _,aa as O,I as A,as as R,W as q,Q as J,T as $,aB as E}from"./antd-DYv0PFJq.js";import"./vendor-D2RBMdQ0.js";const{Step:D}=j,{Title:L,Text:P}=u,G=[{key:"index",label:"序号",required:!0},{key:"studentId",label:"学号",required:!0},{key:"name",label:"姓名",required:!0},{key:"gender",label:"性别",required:!0},{key:"ethnicity",label:"民族",required:!1},{key:"birthplace",label:"籍贯",required:!1},{key:"college",label:"学院",required:!0},{key:"level",label:"层次",required:!0},{key:"grade",label:"年级",required:!0},{key:"major",label:"专业",required:!0},{key:"class",label:"班级",required:!0},{key:"failedCourses",label:"挂科课程名称",required:!1},{key:"courseSemester",label:"开课学年学期",required:!1},{key:"retakeRegistered",label:"是否报名重修",required:!1},{key:"expectedGraduation",label:"预计可毕业时间",required:!1},{key:"tuitionOwed",label:"是否欠缴学费",required:!1},{key:"owedAmount",label:"欠缴金额",required:!1},{key:"counselorContact",label:"辅导员/联系电话",required:!1},{key:"studentPhone",label:"学生联系电话",required:!0},{key:"otherSituation",label:"其他情况",required:!1}],T=({visible:n,onCancel:u,onImportSuccess:k,onExportData:b=[]})=>{const[N,C]=l.useState(0),[_,O]=l.useState(!1),[A,R]=l.useState([]),[q,J]=l.useState([]),[$,E]=l.useState(0),[T,z]=l.useState(!1);d.useEffect(()=>{n||(C(0),R([]),J([]),E(0),z(!1))},[n]),d.useEffect(()=>{T&&A.length>0&&(i.success(`成功导入 ${A.length} 条学业情况信息`),k(A),u(),z(!1))},[T,A,k,u]);const B=e=>{const t=[];e.forEach((e,s)=>{const a=[];G.forEach(t=>{t.required&&!e[t.key]&&a.push(`${t.label}不能为空`)}),e.studentId&&!/^\d{10}$/.test(e.studentId)&&a.push("学号格式不正确（应为10位数字）"),e.studentPhone&&!/^1[3-9]\d{9}$/.test(e.studentPhone)&&a.push("学生联系电话格式不正确"),e.owedAmount&&""!==e.owedAmount&&isNaN(Number(e.owedAmount))&&a.push("欠缴金额必须为数字"),a.length>0&&t.push({row:s+1,originalRow:e.originalIndex,studentId:e.studentId,name:e.name,errors:a})}),J(t)};return e.jsx(c,{title:"学生学业情况导入导出",open:n,onCancel:()=>{C(0),R([]),J([]),E(0),u()},width:800,footer:null,children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs(o,{children:[e.jsx(L,{level:4,children:"选择操作"}),e.jsxs(x,{size:"large",children:[e.jsx(h,{type:"primary",icon:e.jsx(m,{}),onClick:()=>{const e=[["学生学业情况登记表"],G.map(e=>e.label)],a=t.aoa_to_sheet(e),r=t.book_new();t.book_append_sheet(r,a,"学业情况");const n=G.map(()=>({wch:15}));a["!cols"]=n,a["!merges"]=[{s:{r:0,c:0},e:{r:0,c:G.length-1}}],s(r,"学生学业情况导入模板.xlsx"),i.success("模板下载成功")},children:"下载导入模板"}),e.jsx(h,{icon:e.jsx(g,{}),onClick:()=>{if(0===b.length)return void i.warning("没有可导出的数据");const e=[...[["学生学业情况登记表"],G.map(e=>e.label)],...b.map((e,t)=>G.map(s=>"index"===s.key?t+1:e[s.key]||""))],a=t.aoa_to_sheet(e),r=t.book_new();t.book_append_sheet(r,a,"学业情况");const n=G.map(()=>({wch:15}));a["!cols"]=n,a["!merges"]=[{s:{r:0,c:0},e:{r:0,c:G.length-1}}],s(r,`学生学业情况导出_${(new Date).toISOString().split("T")[0]}.xlsx`),i.success("数据导出成功")},disabled:0===b.length,children:"导出学业数据"})]})]}),e.jsxs(o,{children:[e.jsx(L,{level:4,children:"导入学业情况信息"}),e.jsxs(j,{current:N,className:"mb-6",children:[e.jsx(D,{title:"上传文件",icon:e.jsx(p,{})}),e.jsx(D,{title:"数据解析"}),e.jsx(D,{title:"数据验证"}),e.jsx(D,{title:"导入完成",icon:e.jsx(y,{})})]}),0===N&&e.jsxs("div",{className:"text-center py-8",children:[e.jsx(S,{accept:".xlsx,.xls",beforeUpload:e=>{O(!0),C(1);const s=new FileReader;return s.onload=e=>{var s;try{const r=new Uint8Array(null==(s=e.target)?void 0:s.result),n=a(r,{type:"array"}),l=n.SheetNames[0],d=n.Sheets[l],i=t.sheet_to_json(d,{header:1}),c=i.slice(2).map((e,t)=>{const s={originalIndex:t+3};return G.forEach((t,a)=>{s[t.key]=e[a]||""}),s}).filter(e=>e.studentId&&e.name);R(c),B(c),O(!1),C(2)}catch(r){i.error("文件解析失败，请检查文件格式"),O(!1),C(0)}},s.readAsArrayBuffer(e),!1},showUploadList:!1,children:e.jsx(h,{type:"primary",icon:e.jsx(p,{}),size:"large",loading:_,children:"选择Excel文件"})}),e.jsx("div",{className:"mt-4 text-gray-500",children:e.jsx(P,{children:"支持 .xlsx 和 .xls 格式，请使用标准模板"})})]}),2===N&&e.jsxs("div",{className:"space-y-4",children:[e.jsx(w,{message:`解析完成，共 ${A.length} 条数据`,description:q.length>0?`发现 ${q.length} 条数据存在问题，请检查后重新上传`:"所有数据验证通过，可以开始导入",type:q.length>0?"warning":"success",showIcon:!0}),q.length>0&&e.jsxs("div",{children:[e.jsx(L,{level:5,children:"数据错误详情："}),e.jsx(f,{dataSource:q,rowKey:"row",size:"small",pagination:{pageSize:5},columns:[{title:"行号",dataIndex:"originalRow",width:80},{title:"学号",dataIndex:"studentId",width:120},{title:"姓名",dataIndex:"name",width:100},{title:"错误信息",dataIndex:"errors",render:t=>e.jsx("div",{children:t.map((t,s)=>e.jsxs("div",{className:"text-red-500",children:[e.jsx(I,{className:"mr-1"}),t]},s))})}]})]}),e.jsx("div",{className:"text-center",children:e.jsxs(x,{children:[e.jsx(h,{onClick:()=>C(0),children:"重新上传"}),e.jsx(h,{type:"primary",onClick:async()=>{if(q.length>0)i.error("请先修复数据错误");else{C(3),E(0);try{const e=await r.importBatch(A),t=setInterval(()=>{E(s=>s>=100?(clearInterval(t),z(!0),e.data.successCount>0&&i.success(`成功导入 ${e.data.successCount} 条学业情况记录`),e.data.errorCount>0&&(i.warning(`${e.data.errorCount} 条记录导入失败`),console.log("导入错误:",e.data.errors)),k&&k(A),100):s+10)},200)}catch(e){console.error("导入学业情况失败:",e),i.error("导入失败，请检查数据格式或网络连接"),C(1)}}},disabled:q.length>0,children:"确认导入"})]})})]}),3===N&&e.jsxs("div",{className:"text-center py-8",children:[e.jsx(v,{type:"circle",percent:$,status:100===$?"success":"active"}),e.jsx("div",{className:"mt-4",children:e.jsx(P,{children:"正在导入数据，请稍候..."})})]})]})]})})},{Option:z}=R,{Search:B}=A,K=()=>{const[t,s]=c.useModal(),[a,d]=l.useState([]),[u,m]=l.useState(!1),[g,j]=l.useState(""),[p,S]=l.useState(""),[w,v]=l.useState(""),[A,D]=l.useState(!1),[L,P]=l.useState([]),[G,K]=l.useState([]),U=n();l.useEffect(()=>{(async()=>{var e;m(!0);try{const t=await r.getList(),s=((null==(e=t.data)?void 0:e.data)||[]).map(e=>({id:e.id,studentId:e.student_id,name:e.student_name||"",gender:e.student_gender||"",ethnicity:e.student_ethnicity||"",birthplace:e.student_birthplace||"",college:e.student_college||"",level:e.student_level||"",grade:e.student_grade||"",major:e.student_major||"",class:e.student_class||"",failedCourses:e.failed_courses||"",courseSemester:e.course_semester||"",retakeRegistered:e.retake_registered||"",expectedGraduation:e.expected_graduation||"",tuitionOwed:e.tuition_owed||"",owedAmount:e.owed_amount||"",counselorContact:e.counselor_contact||"",studentPhone:e.student_contact||"",otherSituation:e.other_situation||"",status:F(e),createdAt:e.created_at?new Date(e.created_at).toISOString().split("T")[0]:""})),a=JSON.parse(localStorage.getItem("academicRecordsList")||"[]"),n=[{id:"1",studentId:"2021001001",name:"张三",gender:"男",ethnicity:"汉族",birthplace:"北京市",college:"计算机学院",level:"本科",grade:"2021",major:"计算机科学与技术",class:"计科2101",failedCourses:"高等数学",courseSemester:"2021-2022-1",retakeRegistered:"是",expectedGraduation:"2025年6月",tuitionOwed:"否",owedAmount:"",counselorContact:"李老师/13700137001",studentPhone:"13800138001",otherSituation:"",status:"warning",createdAt:"2024-01-15"},{id:"2",studentId:"2021001002",name:"李四",gender:"女",ethnicity:"汉族",birthplace:"上海市",college:"计算机学院",level:"本科",grade:"2021",major:"软件工程",class:"软工2101",failedCourses:"",courseSemester:"",retakeRegistered:"否",expectedGraduation:"2025年6月",tuitionOwed:"是",owedAmount:"5000",counselorContact:"王老师/13700137002",studentPhone:"13800138002",otherSituation:"家庭经济困难",status:"critical",createdAt:"2024-01-10"},{id:"3",studentId:"2021001003",name:"王五",gender:"男",ethnicity:"汉族",birthplace:"广州市",college:"计算机学院",level:"本科",grade:"2021",major:"数据科学与大数据技术",class:"数据2101",failedCourses:"",courseSemester:"",retakeRegistered:"否",expectedGraduation:"2025年6月",tuitionOwed:"否",owedAmount:"",counselorContact:"赵老师/13700137003",studentPhone:"13800138003",otherSituation:"",status:"normal",createdAt:"2024-01-08"}],l=JSON.parse(localStorage.getItem("deletedAcademicIds")||"[]"),i=[...s];a.forEach(e=>{const t=s.some(t=>t.studentId===e.studentId),a=l.includes(e.id);t||a||i.push(e)}),n.forEach(e=>{const t=s.some(t=>t.studentId===e.studentId),r=a.some(t=>t.studentId===e.studentId),n=l.includes(e.id);t||r||n||i.push(e)}),d(i),m(!1)}catch(t){console.error("获取学业情况数据失败:",t),i.error("获取学业情况数据失败，显示本地数据");const e=JSON.parse(localStorage.getItem("deletedAcademicIds")||"[]"),s=JSON.parse(localStorage.getItem("academicRecordsList")||"[]"),a=[...s];mockData.forEach(t=>{const r=s.some(e=>e.studentId===t.studentId),n=e.includes(t.id);r||n||a.push(t)}),d(a),m(!1)}})()},[]);const F=e=>"是"===e.tuitionOwed||e.failedCourses&&"否"===e.retakeRegistered?"critical":e.failedCourses||e.otherSituation?"warning":"normal",M=a.filter(e=>{const t=!g||e.name.toLowerCase().includes(g.toLowerCase())||e.studentId.toLowerCase().includes(g.toLowerCase()),s=!p||e.grade===p,a=!w||e.status===w;return t&&s&&a}),Q=[{title:"学生信息",key:"student",render:(t,s)=>e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:e.jsx(h,{type:"link",onClick:()=>U(`/students/${s.studentId}`),className:"p-0 h-auto",children:s.name})}),e.jsxs("div",{className:"text-gray-500 text-xs",children:[s.studentId," · ",s.class]})]})},{title:"学业状况",key:"academic",render:(t,s)=>e.jsxs("div",{className:"text-sm",children:[s.failedCourses?e.jsxs("div",{className:"text-red-500",children:[e.jsx(O,{className:"mr-1"}),"挂科：",s.failedCourses]}):e.jsxs("div",{className:"text-green-500",children:[e.jsx(y,{className:"mr-1"}),"无挂科记录"]}),"是"===s.retakeRegistered&&e.jsx("div",{className:"text-blue-500 text-xs mt-1",children:"已报名重修"})]})},{title:"学费状况",key:"tuition",render:(t,s)=>e.jsx("div",{className:"text-sm",children:"是"===s.tuitionOwed?e.jsxs("div",{children:[e.jsx(J,{color:"red",children:"欠费"}),s.owedAmount&&e.jsxs("div",{className:"text-xs mt-1",children:["金额：¥",s.owedAmount]})]}):e.jsx(J,{color:"green",children:"已缴费"})})},{title:"预计毕业时间",dataIndex:"expectedGraduation",key:"expectedGraduation"},{title:"联系方式",dataIndex:"studentPhone",key:"studentPhone"},{title:"状态",dataIndex:"status",key:"status",render:t=>{const s={normal:{color:"green",text:"正常",icon:e.jsx(y,{})},warning:{color:"orange",text:"关注",icon:e.jsx(I,{})},critical:{color:"red",text:"重点关注",icon:e.jsx(O,{})}}[t];return e.jsx(J,{color:s.color,icon:s.icon,children:s.text})}},{title:"操作",key:"action",render:(s,r)=>e.jsxs(x,{children:[e.jsx($,{title:"查看详情",children:e.jsx(h,{type:"text",icon:e.jsx(_,{}),onClick:()=>U(`/students/${r.studentId}`)})}),e.jsx($,{title:"编辑",children:e.jsx(h,{type:"text",icon:e.jsx(E,{}),onClick:()=>(e=>{i.info(`编辑学业记录: ${e.name}`)})(r)})}),e.jsx($,{title:"删除",children:e.jsx(h,{type:"text",danger:!0,icon:e.jsx(q,{}),onClick:()=>(e=>{t.confirm({title:"确认删除",content:`确定要删除 ${e.name} 的学业记录吗？`,onOk:()=>{const t=a.filter(t=>t.id!==e.id);d(t),localStorage.setItem("academicRecordsList",JSON.stringify(t));const s=JSON.parse(localStorage.getItem("deletedAcademicIds")||"[]");s.includes(e.id)||(s.push(e.id),localStorage.setItem("deletedAcademicIds",JSON.stringify(s)));const r=JSON.parse(localStorage.getItem("academicRecordsData")||"{}");delete r[e.studentId],localStorage.setItem("academicRecordsData",JSON.stringify(r)),i.success("删除成功")}})})(r)})})]})}],H={selectedRowKeys:L,onChange:(e,t)=>{P(e),K(t)},onSelectAll:(e,t,s)=>{if(e){const e=M.map(e=>e.id);P(e),K(M)}else P([]),K([])},getCheckboxProps:e=>({name:e.name})},V=a.length,W=a.filter(e=>"normal"===e.status).length,X=a.filter(e=>"warning"===e.status).length,Y=a.filter(e=>"critical"===e.status).length;return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"学业情况管理"}),e.jsx("p",{className:"text-gray-600",children:"管理学生学业状况、挂科情况、学费缴纳等信息"})]}),e.jsxs(x,{children:[e.jsx(h,{type:"primary",icon:e.jsx(k,{}),onClick:()=>{D(!0)},size:"large",children:"导入导出"}),!1]})]}),e.jsxs(b,{gutter:16,children:[e.jsx(N,{span:6,children:e.jsx(o,{children:e.jsx(C,{title:"总记录数",value:V,prefix:e.jsx(_,{})})})}),e.jsx(N,{span:6,children:e.jsx(o,{children:e.jsx(C,{title:"正常状态",value:W,valueStyle:{color:"#3f8600"},prefix:e.jsx(y,{})})})}),e.jsx(N,{span:6,children:e.jsx(o,{children:e.jsx(C,{title:"需要关注",value:X,valueStyle:{color:"#faad14"},prefix:e.jsx(I,{})})})}),e.jsx(N,{span:6,children:e.jsx(o,{children:e.jsx(C,{title:"重点关注",value:Y,valueStyle:{color:"#ff4d4f"},prefix:e.jsx(O,{})})})})]}),e.jsxs(o,{children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-4",children:[e.jsx(B,{placeholder:"搜索学号或姓名",value:g,onChange:e=>j(e.target.value),style:{width:300}}),e.jsxs(R,{placeholder:"选择年级",value:p,onChange:S,allowClear:!0,style:{width:120},children:[e.jsx(z,{value:"2021",children:"2021级"}),e.jsx(z,{value:"2022",children:"2022级"}),e.jsx(z,{value:"2023",children:"2023级"}),e.jsx(z,{value:"2024",children:"2024级"})]}),e.jsxs(R,{placeholder:"状态",value:w,onChange:v,allowClear:!0,style:{width:120},children:[e.jsx(z,{value:"normal",children:"正常"}),e.jsx(z,{value:"warning",children:"关注"}),e.jsx(z,{value:"critical",children:"重点关注"})]})]}),L.length>0&&e.jsxs("div",{className:"flex items-center justify-between mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-blue-600 font-medium",children:["已选择 ",L.length," 条记录"]}),e.jsx(h,{type:"link",onClick:()=>{P([]),K([])},className:"text-blue-600 p-0",children:"清空选择"})]}),e.jsx(x,{children:e.jsx(h,{danger:!0,icon:e.jsx(q,{}),onClick:()=>{0!==L.length?t.confirm({title:"确认批量删除",content:`确定要删除选中的 ${L.length} 条学业记录吗？此操作不可撤销。`,icon:e.jsx(I,{}),okText:"确认删除",okType:"danger",cancelText:"取消",onOk:()=>{const e=a.filter(e=>L.includes(e.id)),t=a.filter(e=>!L.includes(e.id));d(t),localStorage.setItem("academicRecordsList",JSON.stringify(t));const s=JSON.parse(localStorage.getItem("deletedAcademicIds")||"[]");L.forEach(e=>{s.includes(e)||s.push(e)}),localStorage.setItem("deletedAcademicIds",JSON.stringify(s));const r=JSON.parse(localStorage.getItem("academicRecordsData")||"{}");e.forEach(e=>{delete r[e.studentId]}),localStorage.setItem("academicRecordsData",JSON.stringify(r)),P([]),K([]),i.success(`成功删除 ${L.length} 条记录`)}}):i.warning("请先选择要删除的记录")},children:"批量删除"})})]}),e.jsx(f,{columns:Q,dataSource:M,rowKey:"id",loading:u,rowSelection:H,pagination:{total:M.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 条记录`},scroll:{x:1200}})]}),e.jsx(T,{visible:A,onCancel:()=>D(!1),onImportSuccess:e=>{const t=e.map((e,t)=>({id:`imported_${Date.now()}_${t}`,studentId:e.studentId||"",name:e.name||"",gender:e.gender||"",ethnicity:e.ethnicity||"",birthplace:e.birthplace||"",college:e.college||"",level:e.level||"",grade:e.grade||"",major:e.major||"",class:e.class||"",failedCourses:e.failedCourses||"",courseSemester:e.courseSemester||"",retakeRegistered:e.retakeRegistered||"",expectedGraduation:e.expectedGraduation||"",tuitionOwed:e.tuitionOwed||"",owedAmount:e.owedAmount||"",counselorContact:e.counselorContact||"",studentPhone:e.studentPhone||"",otherSituation:e.otherSituation||"",status:F(e),createdAt:(new Date).toISOString().split("T")[0]})),s=[...t,...a];d(s),localStorage.setItem("academicRecordsList",JSON.stringify(s));const r=JSON.parse(localStorage.getItem("academicRecordsData")||"{}");t.forEach((t,s)=>{r[t.studentId]=e[s]}),localStorage.setItem("academicRecordsData",JSON.stringify(r)),i.success(`成功导入 ${t.length} 条学业情况信息`),D(!1),window.location.reload()},onExportData:a}),s]})};export{K as default};
