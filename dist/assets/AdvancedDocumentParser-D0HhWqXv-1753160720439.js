var e=Object.defineProperty;import{b as n,c as t,g as i}from"./vendor-D2RBMdQ0-1753160720439.js";import{r}from"./jszip.min-DISjQb3B-1753160720439.js";function a(e,n){for(var t=0;t<n.length;t++){const i=n[t];if("string"!=typeof i&&!Array.isArray(i))for(const n in i)if("default"!==n&&!(n in e)){const t=Object.getOwnPropertyDescriptor(i,n);t&&Object.defineProperty(e,n,t.get?t:{enumerable:!0,get:()=>i[n]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var o={},c="1.13.7",s="object"==typeof self&&self.self===self&&self||"object"==typeof global&&global.global===global&&global||Function("return this")()||{},d=Array.prototype,u=Object.prototype,l="undefined"!=typeof Symbol?Symbol.prototype:null,h=d.push,p=d.slice,f=u.toString,g=u.hasOwnProperty,b="undefined"!=typeof ArrayBuffer,m="undefined"!=typeof DataView,y=Array.isArray,x=Object.keys,D=Object.create,U=b&&ArrayBuffer.isView,v=isNaN,T=isFinite,_=!{toString:null}.propertyIsEnumerable("toString"),w=["valueOf","isPrototypeOf","toString","propertyIsEnumerable","hasOwnProperty","toLocaleString"],F=Math.pow(2,53)-1;function E(e,n){return n=null==n?e.length-1:+n,function(){for(var t=Math.max(arguments.length-n,0),i=Array(t),r=0;r<t;r++)i[r]=arguments[r+n];switch(n){case 0:return e.call(this,i);case 1:return e.call(this,arguments[0],i);case 2:return e.call(this,arguments[0],arguments[1],i)}var a=Array(n+1);for(r=0;r<n;r++)a[r]=arguments[r];return a[n]=i,e.apply(this,a)}}function W(e){var n=typeof e;return"function"===n||"object"===n&&!!e}function C(e){return null===e}function A(e){return void 0===e}function S(e){return!0===e||!1===e||"[object Boolean]"===f.call(e)}function N(e){return!(!e||1!==e.nodeType)}function B(e){var n="[object "+e+"]";return function(e){return f.call(e)===n}}const k=B("String"),O=B("Number"),I=B("Date"),R=B("RegExp"),j=B("Error"),P=B("Symbol"),L=B("ArrayBuffer");var q=B("Function"),M=s.document&&s.document.childNodes;"function"!=typeof/./&&"object"!=typeof Int8Array&&"function"!=typeof M&&(q=function(e){return"function"==typeof e||!1});const V=q,H=B("Object");var z=m&&(!/\[native code\]/.test(String(DataView))||H(new DataView(new ArrayBuffer(8)))),G="undefined"!=typeof Map&&H(new Map),X=B("DataView");const $=z?function(e){return null!=e&&V(e.getInt8)&&L(e.buffer)}:X,K=y||B("Array");function Q(e,n){return null!=e&&g.call(e,n)}var Y=B("Arguments");!function(){Y(arguments)||(Y=function(e){return Q(e,"callee")})}();const Z=Y;function J(e){return!P(e)&&T(e)&&!isNaN(parseFloat(e))}function ee(e){return O(e)&&v(e)}function ne(e){return function(){return e}}function te(e){return function(n){var t=e(n);return"number"==typeof t&&t>=0&&t<=F}}function ie(e){return function(n){return null==n?void 0:n[e]}}const re=ie("byteLength"),ae=te(re);var oe=/\[object ((I|Ui)nt(8|16|32)|Float(32|64)|Uint8Clamped|Big(I|Ui)nt64)Array\]/;const ce=b?function(e){return U?U(e)&&!$(e):ae(e)&&oe.test(f.call(e))}:ne(!1),se=ie("length");function de(e,n){n=function(e){for(var n={},t=e.length,i=0;i<t;++i)n[e[i]]=!0;return{contains:function(e){return!0===n[e]},push:function(t){return n[t]=!0,e.push(t)}}}(n);var t=w.length,i=e.constructor,r=V(i)&&i.prototype||u,a="constructor";for(Q(e,a)&&!n.contains(a)&&n.push(a);t--;)(a=w[t])in e&&e[a]!==r[a]&&!n.contains(a)&&n.push(a)}function ue(e){if(!W(e))return[];if(x)return x(e);var n=[];for(var t in e)Q(e,t)&&n.push(t);return _&&de(e,n),n}function le(e){if(null==e)return!0;var n=se(e);return"number"==typeof n&&(K(e)||k(e)||Z(e))?0===n:0===se(ue(e))}function he(e,n){var t=ue(n),i=t.length;if(null==e)return!i;for(var r=Object(e),a=0;a<i;a++){var o=t[a];if(n[o]!==r[o]||!(o in r))return!1}return!0}function pe(e){return e instanceof pe?e:this instanceof pe?void(this._wrapped=e):new pe(e)}function fe(e){return new Uint8Array(e.buffer||e,e.byteOffset||0,re(e))}pe.VERSION=c,pe.prototype.value=function(){return this._wrapped},pe.prototype.valueOf=pe.prototype.toJSON=pe.prototype.value,pe.prototype.toString=function(){return String(this._wrapped)};var ge="[object DataView]";function be(e,n,t,i){if(e===n)return 0!==e||1/e==1/n;if(null==e||null==n)return!1;if(e!=e)return n!=n;var r=typeof e;return("function"===r||"object"===r||"object"==typeof n)&&me(e,n,t,i)}function me(e,n,t,i){e instanceof pe&&(e=e._wrapped),n instanceof pe&&(n=n._wrapped);var r=f.call(e);if(r!==f.call(n))return!1;if(z&&"[object Object]"==r&&$(e)){if(!$(n))return!1;r=ge}switch(r){case"[object RegExp]":case"[object String]":return""+e==""+n;case"[object Number]":return+e!=+e?+n!=+n:0===+e?1/+e==1/n:+e===+n;case"[object Date]":case"[object Boolean]":return+e===+n;case"[object Symbol]":return l.valueOf.call(e)===l.valueOf.call(n);case"[object ArrayBuffer]":case ge:return me(fe(e),fe(n),t,i)}var a="[object Array]"===r;if(!a&&ce(e)){if(re(e)!==re(n))return!1;if(e.buffer===n.buffer&&e.byteOffset===n.byteOffset)return!0;a=!0}if(!a){if("object"!=typeof e||"object"!=typeof n)return!1;var o=e.constructor,c=n.constructor;if(o!==c&&!(V(o)&&o instanceof o&&V(c)&&c instanceof c)&&"constructor"in e&&"constructor"in n)return!1}i=i||[];for(var s=(t=t||[]).length;s--;)if(t[s]===e)return i[s]===n;if(t.push(e),i.push(n),a){if((s=e.length)!==n.length)return!1;for(;s--;)if(!be(e[s],n[s],t,i))return!1}else{var d,u=ue(e);if(s=u.length,ue(n).length!==s)return!1;for(;s--;)if(!Q(n,d=u[s])||!be(e[d],n[d],t,i))return!1}return t.pop(),i.pop(),!0}function ye(e,n){return be(e,n)}function xe(e){if(!W(e))return[];var n=[];for(var t in e)n.push(t);return _&&de(e,n),n}function De(e){var n=se(e);return function(t){if(null==t)return!1;var i=xe(t);if(se(i))return!1;for(var r=0;r<n;r++)if(!V(t[e[r]]))return!1;return e!==we||!V(t[Ue])}}var Ue="forEach",ve=["clear","delete"],Te=["get","has","set"],_e=ve.concat(Ue,Te),we=ve.concat(Te),Fe=["add"].concat(ve,Ue,"has");const Ee=G?De(_e):B("Map"),We=G?De(we):B("WeakMap"),Ce=G?De(Fe):B("Set"),Ae=B("WeakSet");function Se(e){for(var n=ue(e),t=n.length,i=Array(t),r=0;r<t;r++)i[r]=e[n[r]];return i}function Ne(e){for(var n=ue(e),t=n.length,i=Array(t),r=0;r<t;r++)i[r]=[n[r],e[n[r]]];return i}function Be(e){for(var n={},t=ue(e),i=0,r=t.length;i<r;i++)n[e[t[i]]]=t[i];return n}function ke(e){var n=[];for(var t in e)V(e[t])&&n.push(t);return n.sort()}function Oe(e,n){return function(t){var i=arguments.length;if(n&&(t=Object(t)),i<2||null==t)return t;for(var r=1;r<i;r++)for(var a=arguments[r],o=e(a),c=o.length,s=0;s<c;s++){var d=o[s];n&&void 0!==t[d]||(t[d]=a[d])}return t}}const Ie=Oe(xe),Re=Oe(ue),je=Oe(xe,!0);function Pe(e){if(!W(e))return{};if(D)return D(e);var n=function(){};n.prototype=e;var t=new n;return n.prototype=null,t}function Le(e,n){var t=Pe(e);return n&&Re(t,n),t}function qe(e){return W(e)?K(e)?e.slice():Ie({},e):e}function Me(e,n){return n(e),e}function Ve(e){return K(e)?e:[e]}function He(e){return pe.toPath(e)}function ze(e,n){for(var t=n.length,i=0;i<t;i++){if(null==e)return;e=e[n[i]]}return t?e:void 0}function Ge(e,n,t){var i=ze(e,He(n));return A(i)?t:i}function Xe(e,n){for(var t=(n=He(n)).length,i=0;i<t;i++){var r=n[i];if(!Q(e,r))return!1;e=e[r]}return!!t}function $e(e){return e}function Ke(e){return e=Re({},e),function(n){return he(n,e)}}function Qe(e){return e=He(e),function(n){return ze(n,e)}}function Ye(e,n,t){if(void 0===n)return e;switch(null==t?3:t){case 1:return function(t){return e.call(n,t)};case 3:return function(t,i,r){return e.call(n,t,i,r)};case 4:return function(t,i,r,a){return e.call(n,t,i,r,a)}}return function(){return e.apply(n,arguments)}}function Ze(e,n,t){return null==e?$e:V(e)?Ye(e,n,t):W(e)&&!K(e)?Ke(e):Qe(e)}function Je(e,n){return Ze(e,n,1/0)}function en(e,n,t){return pe.iteratee!==Je?pe.iteratee(e,n):Ze(e,n,t)}function nn(e,n,t){n=en(n,t);for(var i=ue(e),r=i.length,a={},o=0;o<r;o++){var c=i[o];a[c]=n(e[c],c,e)}return a}function tn(){}function rn(e){return null==e?tn:function(n){return Ge(e,n)}}function an(e,n,t){var i=Array(Math.max(0,e));n=Ye(n,t,1);for(var r=0;r<e;r++)i[r]=n(r);return i}function on(e,n){return null==n&&(n=e,e=0),e+Math.floor(Math.random()*(n-e+1))}pe.toPath=Ve,pe.iteratee=Je;const cn=Date.now||function(){return(new Date).getTime()};function sn(e){var n=function(n){return e[n]},t="(?:"+ue(e).join("|")+")",i=RegExp(t),r=RegExp(t,"g");return function(e){return e=null==e?"":""+e,i.test(e)?e.replace(r,n):e}}const dn={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"},un=sn(dn),ln=sn(Be(dn)),hn=pe.templateSettings={evaluate:/<%([\s\S]+?)%>/g,interpolate:/<%=([\s\S]+?)%>/g,escape:/<%-([\s\S]+?)%>/g};var pn=/(.)^/,fn={"'":"'","\\":"\\","\r":"r","\n":"n","\u2028":"u2028","\u2029":"u2029"},gn=/\\|'|\r|\n|\u2028|\u2029/g;function bn(e){return"\\"+fn[e]}var mn=/^\s*(\w|\$)+\s*$/;function yn(e,n,t){!n&&t&&(n=t),n=je({},n,pe.templateSettings);var i=RegExp([(n.escape||pn).source,(n.interpolate||pn).source,(n.evaluate||pn).source].join("|")+"|$","g"),r=0,a="__p+='";e.replace(i,function(n,t,i,o,c){return a+=e.slice(r,c).replace(gn,bn),r=c+n.length,t?a+="'+\n((__t=("+t+"))==null?'':_.escape(__t))+\n'":i?a+="'+\n((__t=("+i+"))==null?'':__t)+\n'":o&&(a+="';\n"+o+"\n__p+='"),n}),a+="';\n";var o,c=n.variable;if(c){if(!mn.test(c))throw new Error("variable is not a bare identifier: "+c)}else a="with(obj||{}){\n"+a+"}\n",c="obj";a="var __t,__p='',__j=Array.prototype.join,print=function(){__p+=__j.call(arguments,'');};\n"+a+"return __p;\n";try{o=new Function(c,"_",a)}catch(d){throw d.source=a,d}var s=function(e){return o.call(this,e,pe)};return s.source="function("+c+"){\n"+a+"}",s}function xn(e,n,t){var i=(n=He(n)).length;if(!i)return V(t)?t.call(e):t;for(var r=0;r<i;r++){var a=null==e?void 0:e[n[r]];void 0===a&&(a=t,r=i),e=V(a)?a.call(e):a}return e}var Dn=0;function Un(e){var n=++Dn+"";return e?e+n:n}function vn(e){var n=pe(e);return n._chain=!0,n}function Tn(e,n,t,i,r){if(!(i instanceof n))return e.apply(t,r);var a=Pe(e.prototype),o=e.apply(a,r);return W(o)?o:a}var _n=E(function(e,n){var t=_n.placeholder,i=function(){for(var r=0,a=n.length,o=Array(a),c=0;c<a;c++)o[c]=n[c]===t?arguments[r++]:n[c];for(;r<arguments.length;)o.push(arguments[r++]);return Tn(e,i,this,this,o)};return i});_n.placeholder=pe;const wn=E(function(e,n,t){if(!V(e))throw new TypeError("Bind must be called on a function");var i=E(function(r){return Tn(e,i,n,this,t.concat(r))});return i}),Fn=te(se);function En(e,n,t,i){if(i=i||[],n||0===n){if(n<=0)return i.concat(e)}else n=1/0;for(var r=i.length,a=0,o=se(e);a<o;a++){var c=e[a];if(Fn(c)&&(K(c)||Z(c)))if(n>1)En(c,n-1,t,i),r=i.length;else for(var s=0,d=c.length;s<d;)i[r++]=c[s++];else t||(i[r++]=c)}return i}const Wn=E(function(e,n){var t=(n=En(n,!1,!1)).length;if(t<1)throw new Error("bindAll must be passed function names");for(;t--;){var i=n[t];e[i]=wn(e[i],e)}return e});function Cn(e,n){var t=function(i){var r=t.cache,a=""+(n?n.apply(this,arguments):i);return Q(r,a)||(r[a]=e.apply(this,arguments)),r[a]};return t.cache={},t}const An=E(function(e,n,t){return setTimeout(function(){return e.apply(null,t)},n)}),Sn=_n(An,pe,1);function Nn(e,n,t){var i,r,a,o,c=0;t||(t={});var s=function(){c=!1===t.leading?0:cn(),i=null,o=e.apply(r,a),i||(r=a=null)},d=function(){var d=cn();c||!1!==t.leading||(c=d);var u=n-(d-c);return r=this,a=arguments,u<=0||u>n?(i&&(clearTimeout(i),i=null),c=d,o=e.apply(r,a),i||(r=a=null)):i||!1===t.trailing||(i=setTimeout(s,u)),o};return d.cancel=function(){clearTimeout(i),c=0,i=r=a=null},d}function Bn(e,n,t){var i,r,a,o,c,s=function(){var d=cn()-r;n>d?i=setTimeout(s,n-d):(i=null,t||(o=e.apply(c,a)),i||(a=c=null))},d=E(function(d){return c=this,a=d,r=cn(),i||(i=setTimeout(s,n),t&&(o=e.apply(c,a))),o});return d.cancel=function(){clearTimeout(i),i=a=c=null},d}function kn(e,n){return _n(n,e)}function On(e){return function(){return!e.apply(this,arguments)}}function In(){var e=arguments,n=e.length-1;return function(){for(var t=n,i=e[n].apply(this,arguments);t--;)i=e[t].call(this,i);return i}}function Rn(e,n){return function(){if(--e<1)return n.apply(this,arguments)}}function jn(e,n){var t;return function(){return--e>0&&(t=n.apply(this,arguments)),e<=1&&(n=null),t}}const Pn=_n(jn,2);function Ln(e,n,t){n=en(n,t);for(var i,r=ue(e),a=0,o=r.length;a<o;a++)if(n(e[i=r[a]],i,e))return i}function qn(e){return function(n,t,i){t=en(t,i);for(var r=se(n),a=e>0?0:r-1;a>=0&&a<r;a+=e)if(t(n[a],a,n))return a;return-1}}const Mn=qn(1),Vn=qn(-1);function Hn(e,n,t,i){for(var r=(t=en(t,i,1))(n),a=0,o=se(e);a<o;){var c=Math.floor((a+o)/2);t(e[c])<r?a=c+1:o=c}return a}function zn(e,n,t){return function(i,r,a){var o=0,c=se(i);if("number"==typeof a)e>0?o=a>=0?a:Math.max(a+c,o):c=a>=0?Math.min(a+1,c):a+c+1;else if(t&&a&&c)return i[a=t(i,r)]===r?a:-1;if(r!=r)return(a=n(p.call(i,o,c),ee))>=0?a+o:-1;for(a=e>0?o:c-1;a>=0&&a<c;a+=e)if(i[a]===r)return a;return-1}}const Gn=zn(1,Mn,Hn),Xn=zn(-1,Vn);function $n(e,n,t){var i=(Fn(e)?Mn:Ln)(e,n,t);if(void 0!==i&&-1!==i)return e[i]}function Kn(e,n){return $n(e,Ke(n))}function Qn(e,n,t){var i,r;if(n=Ye(n,t),Fn(e))for(i=0,r=e.length;i<r;i++)n(e[i],i,e);else{var a=ue(e);for(i=0,r=a.length;i<r;i++)n(e[a[i]],a[i],e)}return e}function Yn(e,n,t){n=en(n,t);for(var i=!Fn(e)&&ue(e),r=(i||e).length,a=Array(r),o=0;o<r;o++){var c=i?i[o]:o;a[o]=n(e[c],c,e)}return a}function Zn(e){return function(n,t,i,r){var a=arguments.length>=3;return function(n,t,i,r){var a=!Fn(n)&&ue(n),o=(a||n).length,c=e>0?0:o-1;for(r||(i=n[a?a[c]:c],c+=e);c>=0&&c<o;c+=e){var s=a?a[c]:c;i=t(i,n[s],s,n)}return i}(n,Ye(t,r,4),i,a)}}const Jn=Zn(1),et=Zn(-1);function nt(e,n,t){var i=[];return n=en(n,t),Qn(e,function(e,t,r){n(e,t,r)&&i.push(e)}),i}function tt(e,n,t){return nt(e,On(en(n)),t)}function it(e,n,t){n=en(n,t);for(var i=!Fn(e)&&ue(e),r=(i||e).length,a=0;a<r;a++){var o=i?i[a]:a;if(!n(e[o],o,e))return!1}return!0}function rt(e,n,t){n=en(n,t);for(var i=!Fn(e)&&ue(e),r=(i||e).length,a=0;a<r;a++){var o=i?i[a]:a;if(n(e[o],o,e))return!0}return!1}function at(e,n,t,i){return Fn(e)||(e=Se(e)),("number"!=typeof t||i)&&(t=0),Gn(e,n,t)>=0}const ot=E(function(e,n,t){var i,r;return V(n)?r=n:(n=He(n),i=n.slice(0,-1),n=n[n.length-1]),Yn(e,function(e){var a=r;if(!a){if(i&&i.length&&(e=ze(e,i)),null==e)return;a=e[n]}return null==a?a:a.apply(e,t)})});function ct(e,n){return Yn(e,Qe(n))}function st(e,n){return nt(e,Ke(n))}function dt(e,n,t){var i,r,a=-1/0,o=-1/0;if(null==n||"number"==typeof n&&"object"!=typeof e[0]&&null!=e)for(var c=0,s=(e=Fn(e)?e:Se(e)).length;c<s;c++)null!=(i=e[c])&&i>a&&(a=i);else n=en(n,t),Qn(e,function(e,t,i){((r=n(e,t,i))>o||r===-1/0&&a===-1/0)&&(a=e,o=r)});return a}function ut(e,n,t){var i,r,a=1/0,o=1/0;if(null==n||"number"==typeof n&&"object"!=typeof e[0]&&null!=e)for(var c=0,s=(e=Fn(e)?e:Se(e)).length;c<s;c++)null!=(i=e[c])&&i<a&&(a=i);else n=en(n,t),Qn(e,function(e,t,i){((r=n(e,t,i))<o||r===1/0&&a===1/0)&&(a=e,o=r)});return a}var lt=/[^\ud800-\udfff]|[\ud800-\udbff][\udc00-\udfff]|[\ud800-\udfff]/g;function ht(e){return e?K(e)?p.call(e):k(e)?e.match(lt):Fn(e)?Yn(e,$e):Se(e):[]}function pt(e,n,t){if(null==n||t)return Fn(e)||(e=Se(e)),e[on(e.length-1)];var i=ht(e),r=se(i);n=Math.max(Math.min(n,r),0);for(var a=r-1,o=0;o<n;o++){var c=on(o,a),s=i[o];i[o]=i[c],i[c]=s}return i.slice(0,n)}function ft(e){return pt(e,1/0)}function gt(e,n,t){var i=0;return n=en(n,t),ct(Yn(e,function(e,t,r){return{value:e,index:i++,criteria:n(e,t,r)}}).sort(function(e,n){var t=e.criteria,i=n.criteria;if(t!==i){if(t>i||void 0===t)return 1;if(t<i||void 0===i)return-1}return e.index-n.index}),"value")}function bt(e,n){return function(t,i,r){var a=n?[[],[]]:{};return i=en(i,r),Qn(t,function(n,r){var o=i(n,r,t);e(a,n,o)}),a}}const mt=bt(function(e,n,t){Q(e,t)?e[t].push(n):e[t]=[n]}),yt=bt(function(e,n,t){e[t]=n}),xt=bt(function(e,n,t){Q(e,t)?e[t]++:e[t]=1}),Dt=bt(function(e,n,t){e[t?0:1].push(n)},!0);function Ut(e){return null==e?0:Fn(e)?e.length:ue(e).length}function vt(e,n,t){return n in t}const Tt=E(function(e,n){var t={},i=n[0];if(null==e)return t;V(i)?(n.length>1&&(i=Ye(i,n[1])),n=xe(e)):(i=vt,n=En(n,!1,!1),e=Object(e));for(var r=0,a=n.length;r<a;r++){var o=n[r],c=e[o];i(c,o,e)&&(t[o]=c)}return t}),_t=E(function(e,n){var t,i=n[0];return V(i)?(i=On(i),n.length>1&&(t=n[1])):(n=Yn(En(n,!1,!1),String),i=function(e,t){return!at(n,t)}),Tt(e,i,t)});function wt(e,n,t){return p.call(e,0,Math.max(0,e.length-(null==n||t?1:n)))}function Ft(e,n,t){return null==e||e.length<1?null==n||t?void 0:[]:null==n||t?e[0]:wt(e,e.length-n)}function Et(e,n,t){return p.call(e,null==n||t?1:n)}function Wt(e,n,t){return null==e||e.length<1?null==n||t?void 0:[]:null==n||t?e[e.length-1]:Et(e,Math.max(0,e.length-n))}function Ct(e){return nt(e,Boolean)}function At(e,n){return En(e,n,!1)}const St=E(function(e,n){return n=En(n,!0,!0),nt(e,function(e){return!at(n,e)})}),Nt=E(function(e,n){return St(e,n)});function Bt(e,n,t,i){S(n)||(i=t,t=n,n=!1),null!=t&&(t=en(t,i));for(var r=[],a=[],o=0,c=se(e);o<c;o++){var s=e[o],d=t?t(s,o,e):s;n&&!t?(o&&a===d||r.push(s),a=d):t?at(a,d)||(a.push(d),r.push(s)):at(r,s)||r.push(s)}return r}const kt=E(function(e){return Bt(En(e,!0,!0))});function Ot(e){for(var n=[],t=arguments.length,i=0,r=se(e);i<r;i++){var a=e[i];if(!at(n,a)){var o;for(o=1;o<t&&at(arguments[o],a);o++);o===t&&n.push(a)}}return n}function It(e){for(var n=e&&dt(e,se).length||0,t=Array(n),i=0;i<n;i++)t[i]=ct(e,i);return t}const Rt=E(It);function jt(e,n){for(var t={},i=0,r=se(e);i<r;i++)n?t[e[i]]=n[i]:t[e[i][0]]=e[i][1];return t}function Pt(e,n,t){null==n&&(n=e||0,e=0),t||(t=n<e?-1:1);for(var i=Math.max(Math.ceil((n-e)/t),0),r=Array(i),a=0;a<i;a++,e+=t)r[a]=e;return r}function Lt(e,n){if(null==n||n<1)return[];for(var t=[],i=0,r=e.length;i<r;)t.push(p.call(e,i,i+=n));return t}function qt(e,n){return e._chain?pe(n).chain():n}function Mt(e){return Qn(ke(e),function(n){var t=pe[n]=e[n];pe.prototype[n]=function(){var e=[this._wrapped];return h.apply(e,arguments),qt(this,t.apply(pe,e))}}),pe}Qn(["pop","push","reverse","shift","sort","splice","unshift"],function(e){var n=d[e];pe.prototype[e]=function(){var t=this._wrapped;return null!=t&&(n.apply(t,arguments),"shift"!==e&&"splice"!==e||0!==t.length||delete t[0]),qt(this,t)}}),Qn(["concat","join","slice"],function(e){var n=d[e];pe.prototype[e]=function(){var e=this._wrapped;return null!=e&&(e=n.apply(e,arguments)),qt(this,e)}});var Vt=Mt(Object.freeze(Object.defineProperty({__proto__:null,VERSION:c,after:Rn,all:it,allKeys:xe,any:rt,assign:Re,before:jn,bind:wn,bindAll:Wn,chain:vn,chunk:Lt,clone:qe,collect:Yn,compact:Ct,compose:In,constant:ne,contains:at,countBy:xt,create:Le,debounce:Bn,default:pe,defaults:je,defer:Sn,delay:An,detect:$n,difference:St,drop:Et,each:Qn,escape:un,every:it,extend:Ie,extendOwn:Re,filter:nt,find:$n,findIndex:Mn,findKey:Ln,findLastIndex:Vn,findWhere:Kn,first:Ft,flatten:At,foldl:Jn,foldr:et,forEach:Qn,functions:ke,get:Ge,groupBy:mt,has:Xe,head:Ft,identity:$e,include:at,includes:at,indexBy:yt,indexOf:Gn,initial:wt,inject:Jn,intersection:Ot,invert:Be,invoke:ot,isArguments:Z,isArray:K,isArrayBuffer:L,isBoolean:S,isDataView:$,isDate:I,isElement:N,isEmpty:le,isEqual:ye,isError:j,isFinite:J,isFunction:V,isMap:Ee,isMatch:he,isNaN:ee,isNull:C,isNumber:O,isObject:W,isRegExp:R,isSet:Ce,isString:k,isSymbol:P,isTypedArray:ce,isUndefined:A,isWeakMap:We,isWeakSet:Ae,iteratee:Je,keys:ue,last:Wt,lastIndexOf:Xn,map:Yn,mapObject:nn,matcher:Ke,matches:Ke,max:dt,memoize:Cn,methods:ke,min:ut,mixin:Mt,negate:On,noop:tn,now:cn,object:jt,omit:_t,once:Pn,pairs:Ne,partial:_n,partition:Dt,pick:Tt,pluck:ct,property:Qe,propertyOf:rn,random:on,range:Pt,reduce:Jn,reduceRight:et,reject:tt,rest:Et,restArguments:E,result:xn,sample:pt,select:nt,shuffle:ft,size:Ut,some:rt,sortBy:gt,sortedIndex:Hn,tail:Et,take:Ft,tap:Me,template:yn,templateSettings:hn,throttle:Nn,times:an,toArray:ht,toPath:Ve,transpose:It,unescape:ln,union:kt,uniq:Bt,unique:Bt,uniqueId:Un,unzip:It,values:Se,where:st,without:Nt,wrap:kn,zip:Rt},Symbol.toStringTag,{value:"Module"})));Vt._=Vt;const Ht=n(Object.freeze(Object.defineProperty({__proto__:null,VERSION:c,after:Rn,all:it,allKeys:xe,any:rt,assign:Re,before:jn,bind:wn,bindAll:Wn,chain:vn,chunk:Lt,clone:qe,collect:Yn,compact:Ct,compose:In,constant:ne,contains:at,countBy:xt,create:Le,debounce:Bn,default:Vt,defaults:je,defer:Sn,delay:An,detect:$n,difference:St,drop:Et,each:Qn,escape:un,every:it,extend:Ie,extendOwn:Re,filter:nt,find:$n,findIndex:Mn,findKey:Ln,findLastIndex:Vn,findWhere:Kn,first:Ft,flatten:At,foldl:Jn,foldr:et,forEach:Qn,functions:ke,get:Ge,groupBy:mt,has:Xe,head:Ft,identity:$e,include:at,includes:at,indexBy:yt,indexOf:Gn,initial:wt,inject:Jn,intersection:Ot,invert:Be,invoke:ot,isArguments:Z,isArray:K,isArrayBuffer:L,isBoolean:S,isDataView:$,isDate:I,isElement:N,isEmpty:le,isEqual:ye,isError:j,isFinite:J,isFunction:V,isMap:Ee,isMatch:he,isNaN:ee,isNull:C,isNumber:O,isObject:W,isRegExp:R,isSet:Ce,isString:k,isSymbol:P,isTypedArray:ce,isUndefined:A,isWeakMap:We,isWeakSet:Ae,iteratee:Je,keys:ue,last:Wt,lastIndexOf:Xn,map:Yn,mapObject:nn,matcher:Ke,matches:Ke,max:dt,memoize:Cn,methods:ke,min:ut,mixin:Mt,negate:On,noop:tn,now:cn,object:jt,omit:_t,once:Pn,pairs:Ne,partial:_n,partition:Dt,pick:Tt,pluck:ct,property:Qe,propertyOf:rn,random:on,range:Pt,reduce:Jn,reduceRight:et,reject:tt,rest:Et,restArguments:E,result:xn,sample:pt,select:nt,shuffle:ft,size:Ut,some:rt,sortBy:gt,sortedIndex:Hn,tail:Et,take:Ft,tap:Me,template:yn,templateSettings:hn,throttle:Nn,times:an,toArray:ht,toPath:Ve,transpose:It,unescape:ln,union:kt,uniq:Bt,unique:Bt,uniqueId:Un,unzip:It,values:Se,where:st,without:Nt,wrap:kn,zip:Rt},Symbol.toStringTag,{value:"Module"})));var zt,Gt,Xt,$t={},Kt={},Qt={exports:{}},Yt={exports:{}};function Zt(){if(zt)return Yt.exports;zt=1;var e=function(){return void 0===this}();if(e)Yt.exports={freeze:Object.freeze,defineProperty:Object.defineProperty,getDescriptor:Object.getOwnPropertyDescriptor,keys:Object.keys,names:Object.getOwnPropertyNames,getPrototypeOf:Object.getPrototypeOf,isArray:Array.isArray,isES5:e,propertyIsWritable:function(e,n){var t=Object.getOwnPropertyDescriptor(e,n);return!(t&&!t.writable&&!t.set)}};else{var n={}.hasOwnProperty,t={}.toString,i={}.constructor.prototype,r=function(e){var t=[];for(var i in e)n.call(e,i)&&t.push(i);return t};Yt.exports={isArray:function(e){try{return"[object Array]"===t.call(e)}catch(n){return!1}},keys:r,names:r,defineProperty:function(e,n,t){return e[n]=t.value,e},getDescriptor:function(e,n){return{value:e[n]}},freeze:function(e){return e},getPrototypeOf:function(e){try{return Object(e).constructor.prototype}catch(n){return i}},isES5:e,propertyIsWritable:function(){return!0}}}return Yt.exports}function Jt(){if(Xt)return Gt;Xt=1;var e,n={},i=Zt(),r="undefined"==typeof navigator,a={e:{}},o="undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==t?t:void 0!==Gt?Gt:null;function c(){try{var n=e;return e=null,n.apply(this,arguments)}catch(t){return a.e=t,a}}function s(e){return null==e||!0===e||!1===e||"string"==typeof e||"number"==typeof e}function d(e,n,t){if(s(e))return e;var r={value:t,configurable:!0,enumerable:!1,writable:!0};return i.defineProperty(e,n,r),e}var u=function(){var e=[Array.prototype,Object.prototype,Function.prototype],n=function(n){for(var t=0;t<e.length;++t)if(e[t]===n)return!0;return!1};if(i.isES5){var t=Object.getOwnPropertyNames;return function(e){for(var r=[],a=Object.create(null);null!=e&&!n(e);){var o;try{o=t(e)}catch(u){return r}for(var c=0;c<o.length;++c){var s=o[c];if(!a[s]){a[s]=!0;var d=Object.getOwnPropertyDescriptor(e,s);null!=d&&null==d.get&&null==d.set&&r.push(s)}}e=i.getPrototypeOf(e)}return r}}var r={}.hasOwnProperty;return function(t){if(n(t))return[];var i=[];e:for(var a in t)if(r.call(t,a))i.push(a);else{for(var o=0;o<e.length;++o)if(r.call(e[o],a))continue e;i.push(a)}return i}}(),l=/this\s*\.\s*\S+\s*=/;var h=/^[a-z$_][a-z$_0-9]*$/i;function p(e){try{return e+""}catch(n){return"[no string representation]"}}function f(e){return null!==e&&"object"==typeof e&&"string"==typeof e.message&&"string"==typeof e.name}function g(e){return f(e)&&i.propertyIsWritable(e,"stack")}var b="stack"in new Error?function(e){return g(e)?e:new Error(p(e))}:function(e){if(g(e))return e;try{throw new Error(p(e))}catch(n){return n}};function m(e){return{}.toString.call(e)}var y=function(e){return i.isArray(e)?e:null};if("undefined"!=typeof Symbol&&Symbol.iterator){var x="function"==typeof Array.from?function(e){return Array.from(e)}:function(e){for(var n,t=[],i=e[Symbol.iterator]();!(n=i.next()).done;)t.push(n.value);return t};y=function(e){return i.isArray(e)?e:null!=e&&"function"==typeof e[Symbol.iterator]?x(e):null}}var D="undefined"!=typeof process&&"[object process]"===m(process).toLowerCase(),U="undefined"!=typeof process&&!0;var v,T={isClass:function(e){try{if("function"==typeof e){var n=i.names(e.prototype),t=i.isES5&&n.length>1,r=n.length>0&&!(1===n.length&&"constructor"===n[0]),a=l.test(e+"")&&i.names(e).length>0;if(t||r||a)return!0}return!1}catch(o){return!1}},isIdentifier:function(e){return h.test(e)},inheritedDataKeys:u,getDataPropertyOrDefault:function(e,n,t){if(!i.isES5)return{}.hasOwnProperty.call(e,n)?e[n]:void 0;var r=Object.getOwnPropertyDescriptor(e,n);return null!=r?null==r.get&&null==r.set?r.value:t:void 0},thrower:function(e){throw e},isArray:i.isArray,asArray:y,notEnumerableProp:d,isPrimitive:s,isObject:function(e){return"function"==typeof e||"object"==typeof e&&null!==e},isError:f,canEvaluate:r,errorObj:a,tryCatch:function(n){return e=n,c},inherits:function(e,n){var t={}.hasOwnProperty;function i(){for(var i in this.constructor=e,this.constructor$=n,n.prototype)t.call(n.prototype,i)&&"$"!==i.charAt(i.length-1)&&(this[i+"$"]=n.prototype[i])}return i.prototype=n.prototype,e.prototype=new i,e.prototype},withAppended:function(e,n){var t,i=e.length,r=new Array(i+1);for(t=0;t<i;++t)r[t]=e[t];return r[t]=n,r},maybeWrapAsError:function(e){return s(e)?new Error(p(e)):e},toFastProperties:function(e){return e},filledRange:function(e,n,t){for(var i=new Array(e),r=0;r<e;++r)i[r]=n+r+t;return i},toString:p,canAttachTrace:g,ensureErrorObject:b,originatesFromRejection:function(e){return null!=e&&(e instanceof Error.__BluebirdErrorTypes__.OperationalError||!0===e.isOperational)},markAsOriginatingFromRejection:function(e){try{d(e,"isOperational",!0)}catch(n){}},classString:m,copyDescriptors:function(e,n,t){for(var r=i.names(e),a=0;a<r.length;++a){var o=r[a];if(t(o))try{i.defineProperty(n,o,i.getDescriptor(e,o))}catch(c){}}},hasDevTools:"undefined"!=typeof chrome&&chrome&&"function"==typeof chrome.loadTimes,isNode:D,hasEnvVariables:U,env:function(e){return U?n[e]:void 0},global:o,getNativePromise:function(){if("function"==typeof Promise)try{var e=new Promise(function(){});if("[object Promise]"==={}.toString.call(e))return Promise}catch(n){}},domainBind:function(e,n){return e.bind(n)}};T.isRecentNode=T.isNode&&(0===(v=process.versions.node.split(".").map(Number))[0]&&v[1]>10||v[0]>0),T.isNode&&T.toFastProperties(process);try{throw new Error}catch(_){T.lastLineError=_}return Gt=T}var ei,ni,ti,ii,ri,ai,oi,ci,si,di,ui,li,hi,pi,fi,gi,bi,mi,yi,xi,Di,Ui,vi,Ti,_i,wi,Fi,Ei,Wi,Ci,Ai,Si,Ni,Bi,ki,Oi,Ii,Ri,ji,Pi,Li,qi,Mi,Vi,Hi,zi,Gi,Xi,$i,Ki,Qi,Yi,Zi,Ji,er,nr,tr,ir,rr,ar,or,cr,sr,dr,ur,lr={exports:{}};function hr(){if(ni)return ei;ni=1;var e,n=Jt(),i=n.getNativePromise();if(n.isNode&&"undefined"==typeof MutationObserver){var r=t.setImmediate,a=process.nextTick;e=n.isRecentNode?function(e){r.call(t,e)}:function(e){a.call(process,e)}}else if("function"==typeof i&&"function"==typeof i.resolve){var o=i.resolve();e=function(e){o.then(e)}}else e="undefined"==typeof MutationObserver||"undefined"!=typeof window&&window.navigator&&(window.navigator.standalone||window.cordova)?"undefined"!=typeof setImmediate?function(e){setImmediate(e)}:"undefined"!=typeof setTimeout?function(e){setTimeout(e,0)}:function(){throw new Error("No async scheduler available\n\n    See http://goo.gl/MqrFmX\n")}:function(){var e=document.createElement("div"),n={attributes:!0},t=!1,i=document.createElement("div");new MutationObserver(function(){e.classList.toggle("foo"),t=!1}).observe(i,n);return function(r){var a=new MutationObserver(function(){a.disconnect(),r()});a.observe(e,n),t||(t=!0,i.classList.toggle("foo"))}}();return ei=e}function pr(){if(ri)return lr.exports;var e;ri=1;try{throw new Error}catch(s){e=s}var n=hr(),t=function(){if(ii)return ti;function e(e){this._capacity=e,this._length=0,this._front=0}return ii=1,e.prototype._willBeOverCapacity=function(e){return this._capacity<e},e.prototype._pushOne=function(e){var n=this.length();this._checkCapacity(n+1),this[this._front+n&this._capacity-1]=e,this._length=n+1},e.prototype.push=function(e,n,t){var i=this.length()+3;if(this._willBeOverCapacity(i))return this._pushOne(e),this._pushOne(n),void this._pushOne(t);var r=this._front+i-3;this._checkCapacity(i);var a=this._capacity-1;this[r+0&a]=e,this[r+1&a]=n,this[r+2&a]=t,this._length=i},e.prototype.shift=function(){var e=this._front,n=this[e];return this[e]=void 0,this._front=e+1&this._capacity-1,this._length--,n},e.prototype.length=function(){return this._length},e.prototype._checkCapacity=function(e){this._capacity<e&&this._resizeTo(this._capacity<<1)},e.prototype._resizeTo=function(e){var n=this._capacity;this._capacity=e,function(e,n,t,i,r){for(var a=0;a<r;++a)t[a+i]=e[a+n],e[a+n]=void 0}(this,0,this,n,this._front+this._length&n-1)},ti=e}(),i=Jt();function r(){this._customScheduler=!1,this._isTickUsed=!1,this._lateQueue=new t(16),this._normalQueue=new t(16),this._haveDrainedQueues=!1,this._trampolineEnabled=!0;var e=this;this.drainQueues=function(){e._drainQueues()},this._schedule=n}function a(e,n,t){this._lateQueue.push(e,n,t),this._queueTick()}function o(e,n,t){this._normalQueue.push(e,n,t),this._queueTick()}function c(e){this._normalQueue._pushOne(e),this._queueTick()}return r.prototype.setScheduler=function(e){var n=this._schedule;return this._schedule=e,this._customScheduler=!0,n},r.prototype.hasCustomScheduler=function(){return this._customScheduler},r.prototype.enableTrampoline=function(){this._trampolineEnabled=!0},r.prototype.disableTrampolineIfNecessary=function(){i.hasDevTools&&(this._trampolineEnabled=!1)},r.prototype.haveItemsQueued=function(){return this._isTickUsed||this._haveDrainedQueues},r.prototype.fatalError=function(e,n){n?(process.stderr.write("Fatal "+(e instanceof Error?e.stack:e)+"\n"),process.exit(2)):this.throwLater(e)},r.prototype.throwLater=function(e,n){if(1===arguments.length&&(n=e,e=function(){throw n}),"undefined"!=typeof setTimeout)setTimeout(function(){e(n)},0);else try{this._schedule(function(){e(n)})}catch(s){throw new Error("No async scheduler available\n\n    See http://goo.gl/MqrFmX\n")}},i.hasDevTools?(r.prototype.invokeLater=function(e,n,t){this._trampolineEnabled?a.call(this,e,n,t):this._schedule(function(){setTimeout(function(){e.call(n,t)},100)})},r.prototype.invoke=function(e,n,t){this._trampolineEnabled?o.call(this,e,n,t):this._schedule(function(){e.call(n,t)})},r.prototype.settlePromises=function(e){this._trampolineEnabled?c.call(this,e):this._schedule(function(){e._settlePromises()})}):(r.prototype.invokeLater=a,r.prototype.invoke=o,r.prototype.settlePromises=c),r.prototype._drainQueue=function(e){for(;e.length()>0;){var n=e.shift();if("function"==typeof n){var t=e.shift(),i=e.shift();n.call(t,i)}else n._settlePromises()}},r.prototype._drainQueues=function(){this._drainQueue(this._normalQueue),this._reset(),this._haveDrainedQueues=!0,this._drainQueue(this._lateQueue)},r.prototype._queueTick=function(){this._isTickUsed||(this._isTickUsed=!0,this._schedule(this.drainQueues))},r.prototype._reset=function(){this._isTickUsed=!1},lr.exports=r,lr.exports.firstLineError=e,lr.exports}function fr(){if(oi)return ai;oi=1;var e,n,t=Zt(),i=t.freeze,r=Jt(),a=r.inherits,o=r.notEnumerableProp;function c(e,n){function t(i){if(!(this instanceof t))return new t(i);o(this,"message","string"==typeof i?i:n),o(this,"name",e),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):Error.call(this)}return a(t,Error),t}var s=c("Warning","warning"),d=c("CancellationError","cancellation error"),u=c("TimeoutError","timeout error"),l=c("AggregateError","aggregate error");try{e=TypeError,n=RangeError}catch(m){e=c("TypeError","type error"),n=c("RangeError","range error")}for(var h="join pop push shift unshift slice filter forEach some every map indexOf lastIndexOf reduce reduceRight sort reverse".split(" "),p=0;p<h.length;++p)"function"==typeof Array.prototype[h[p]]&&(l.prototype[h[p]]=Array.prototype[h[p]]);t.defineProperty(l.prototype,"length",{value:0,configurable:!1,writable:!0,enumerable:!0}),l.prototype.isOperational=!0;var f=0;function g(e){if(!(this instanceof g))return new g(e);o(this,"name","OperationalError"),o(this,"message",e),this.cause=e,this.isOperational=!0,e instanceof Error?(o(this,"message",e.message),o(this,"stack",e.stack)):Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}l.prototype.toString=function(){var e=Array(4*f+1).join(" "),n="\n"+e+"AggregateError of:\n";f++,e=Array(4*f+1).join(" ");for(var t=0;t<this.length;++t){for(var i=this[t]===this?"[Circular AggregateError]":this[t]+"",r=i.split("\n"),a=0;a<r.length;++a)r[a]=e+r[a];n+=(i=r.join("\n"))+"\n"}return f--,n},a(g,Error);var b=Error.__BluebirdErrorTypes__;return b||(b=i({CancellationError:d,TimeoutError:u,OperationalError:g,RejectionError:g,AggregateError:l}),t.defineProperty(Error,"__BluebirdErrorTypes__",{value:b,writable:!1,enumerable:!1,configurable:!1})),ai={Error:Error,TypeError:e,RangeError:n,CancellationError:b.CancellationError,OperationalError:b.OperationalError,TimeoutError:b.TimeoutError,AggregateError:b.AggregateError,Warning:s}}function gr(){return si?ci:(si=1,ci=function(e,n){var t=Jt(),i=t.errorObj,r=t.isObject;var a={}.hasOwnProperty;return function(o,c){if(r(o)){if(o instanceof e)return o;var s=function(e){try{return function(e){return e.then}(e)}catch(n){return i.e=n,i}}(o);if(s===i){c&&c._pushContext();var d=e.reject(s.e);return c&&c._popContext(),d}if("function"==typeof s){if(function(e){try{return a.call(e,"_promise0")}catch(n){return!1}}(o)){d=new e(n);return o._then(d._fulfill,d._reject,void 0,d,null),d}return function(r,a,o){var c=new e(n),s=c;o&&o._pushContext();c._captureStackTrace(),o&&o._popContext();var d=!0,u=t.tryCatch(a).call(r,l,h);d=!1,c&&u===i&&(c._rejectCallback(u.e,!0,!0),c=null);function l(e){c&&(c._resolveCallback(e),c=null)}function h(e){c&&(c._rejectCallback(e,d,!0),c=null)}return s}(o,s,c)}}return o}})}function br(){return fi||(fi=1,pi=function(e,n){var t,i,r,a=e._getDomain,o=e._async,c=fr().Warning,s=Jt(),d=s.canAttachTrace,u=/[\\\/]bluebird[\\\/]js[\\\/](release|debug|instrumented)/,l=/\((?:timers\.js):\d+:\d+\)/,h=/[\/<\(](.+?):(\d+):(\d+)\)?\s*$/,p=null,f=null,g=!1,b=!(0==s.env("BLUEBIRD_DEBUG")||!s.env("BLUEBIRD_DEBUG")&&"development"!==s.env("NODE_ENV")),m=!(0==s.env("BLUEBIRD_WARNINGS")||!b&&!s.env("BLUEBIRD_WARNINGS")),y=!(0==s.env("BLUEBIRD_LONG_STACK_TRACES")||!b&&!s.env("BLUEBIRD_LONG_STACK_TRACES")),x=0!=s.env("BLUEBIRD_W_FORGOTTEN_RETURN")&&(m||!!s.env("BLUEBIRD_W_FORGOTTEN_RETURN"));e.prototype.suppressUnhandledRejections=function(){var e=this._target();e._bitField=-1048577&e._bitField|524288},e.prototype._ensurePossibleRejectionHandled=function(){524288&this._bitField||(this._setRejectionIsUnhandled(),o.invokeLater(this._notifyUnhandledRejection,this,void 0))},e.prototype._notifyUnhandledRejectionIsHandled=function(){q("rejectionHandled",t,void 0,this)},e.prototype._setReturnedNonUndefined=function(){this._bitField=268435456|this._bitField},e.prototype._returnedNonUndefined=function(){return!!(268435456&this._bitField)},e.prototype._notifyUnhandledRejection=function(){if(this._isRejectionUnhandled()){var e=this._settledValue();this._setUnhandledRejectionIsNotified(),q("unhandledRejection",i,e,this)}},e.prototype._setUnhandledRejectionIsNotified=function(){this._bitField=262144|this._bitField},e.prototype._unsetUnhandledRejectionIsNotified=function(){this._bitField=-262145&this._bitField},e.prototype._isUnhandledRejectionNotified=function(){return(262144&this._bitField)>0},e.prototype._setRejectionIsUnhandled=function(){this._bitField=1048576|this._bitField},e.prototype._unsetRejectionIsUnhandled=function(){this._bitField=-1048577&this._bitField,this._isUnhandledRejectionNotified()&&(this._unsetUnhandledRejectionIsNotified(),this._notifyUnhandledRejectionIsHandled())},e.prototype._isRejectionUnhandled=function(){return(1048576&this._bitField)>0},e.prototype._warn=function(e,n,t){return R(e,n,t||this)},e.onPossiblyUnhandledRejection=function(e){var n=a();i="function"==typeof e?null===n?e:s.domainBind(n,e):void 0},e.onUnhandledRejectionHandled=function(e){var n=a();t="function"==typeof e?null===n?e:s.domainBind(n,e):void 0};var D=function(){};e.longStackTraces=function(){if(o.haveItemsQueued()&&!K.longStackTraces)throw new Error("cannot enable long stack traces after promises have been created\n\n    See http://goo.gl/MqrFmX\n");if(!K.longStackTraces&&V()){var t=e.prototype._captureStackTrace,i=e.prototype._attachExtraTrace;K.longStackTraces=!0,D=function(){if(o.haveItemsQueued()&&!K.longStackTraces)throw new Error("cannot enable long stack traces after promises have been created\n\n    See http://goo.gl/MqrFmX\n");e.prototype._captureStackTrace=t,e.prototype._attachExtraTrace=i,n.deactivateLongStackTraces(),o.enableTrampoline(),K.longStackTraces=!1},e.prototype._captureStackTrace=O,e.prototype._attachExtraTrace=I,n.activateLongStackTraces(),o.disableTrampolineIfNecessary()}},e.hasLongStackTraces=function(){return K.longStackTraces&&V()};var U=function(){try{if("function"==typeof CustomEvent){var e=new CustomEvent("CustomEvent");return s.global.dispatchEvent(e),function(e,n){var t=new CustomEvent(e.toLowerCase(),{detail:n,cancelable:!0});return!s.global.dispatchEvent(t)}}if("function"==typeof Event){e=new Event("CustomEvent");return s.global.dispatchEvent(e),function(e,n){var t=new Event(e.toLowerCase(),{cancelable:!0});return t.detail=n,!s.global.dispatchEvent(t)}}return(e=document.createEvent("CustomEvent")).initCustomEvent("testingtheevent",!1,!0,{}),s.global.dispatchEvent(e),function(e,n){var t=document.createEvent("CustomEvent");return t.initCustomEvent(e.toLowerCase(),!1,!0,n),!s.global.dispatchEvent(t)}}catch(n){}return function(){return!1}}(),v=s.isNode?function(){return process.emit.apply(process,arguments)}:s.global?function(e){var n="on"+e.toLowerCase(),t=s.global[n];return!!t&&(t.apply(s.global,[].slice.call(arguments,1)),!0)}:function(){return!1};function T(e,n){return{promise:n}}var _={promiseCreated:T,promiseFulfilled:T,promiseRejected:T,promiseResolved:T,promiseCancelled:T,promiseChained:function(e,n,t){return{promise:n,child:t}},warning:function(e,n){return{warning:n}},unhandledRejection:function(e,n,t){return{reason:n,promise:t}},rejectionHandled:T},w=function(e){var n=!1;try{n=v.apply(null,arguments)}catch(i){o.throwLater(i),n=!0}var t=!1;try{t=U(e,_[e].apply(null,arguments))}catch(i){o.throwLater(i),t=!0}return t||n};function F(){return!1}function E(e,n,t){var i=this;try{e(n,t,function(e){if("function"!=typeof e)throw new TypeError("onCancel must be a function, got: "+s.toString(e));i._attachCancellationCallback(e)})}catch(r){return r}}function W(e){if(!this._isCancellable())return this;var n=this._onCancel();void 0!==n?s.isArray(n)?n.push(e):this._setOnCancel([n,e]):this._setOnCancel(e)}function C(){return this._onCancelField}function A(e){this._onCancelField=e}function S(){this._cancellationParent=void 0,this._onCancelField=void 0}function N(e,n){if(1&n){this._cancellationParent=e;var t=e._branchesRemainingToCancel;void 0===t&&(t=0),e._branchesRemainingToCancel=t+1}2&n&&e._isBound()&&this._setBoundTo(e._boundTo)}e.config=function(n){if("longStackTraces"in(n=Object(n))&&(n.longStackTraces?e.longStackTraces():!n.longStackTraces&&e.hasLongStackTraces()&&D()),"warnings"in n){var t=n.warnings;K.warnings=!!t,x=K.warnings,s.isObject(t)&&"wForgottenReturn"in t&&(x=!!t.wForgottenReturn)}if("cancellation"in n&&n.cancellation&&!K.cancellation){if(o.haveItemsQueued())throw new Error("cannot enable cancellation after promises are in use");e.prototype._clearCancellationData=S,e.prototype._propagateFrom=N,e.prototype._onCancel=C,e.prototype._setOnCancel=A,e.prototype._attachCancellationCallback=W,e.prototype._execute=E,B=N,K.cancellation=!0}return"monitoring"in n&&(n.monitoring&&!K.monitoring?(K.monitoring=!0,e.prototype._fireEvent=w):!n.monitoring&&K.monitoring&&(K.monitoring=!1,e.prototype._fireEvent=F)),e},e.prototype._fireEvent=F,e.prototype._execute=function(e,n,t){try{e(n,t)}catch(i){return i}},e.prototype._onCancel=function(){},e.prototype._setOnCancel=function(e){},e.prototype._attachCancellationCallback=function(e){},e.prototype._captureStackTrace=function(){},e.prototype._attachExtraTrace=function(){},e.prototype._clearCancellationData=function(){},e.prototype._propagateFrom=function(e,n){};var B=function(e,n){2&n&&e._isBound()&&this._setBoundTo(e._boundTo)};function k(){var n=this._boundTo;return void 0!==n&&n instanceof e?n.isFulfilled()?n.value():void 0:n}function O(){this._trace=new X(this._peekContext())}function I(e,n){if(d(e)){var t=this._trace;if(void 0!==t&&n&&(t=t._parent),void 0!==t)t.attachExtraTrace(e);else if(!e.__stackCleaned__){var i=P(e);s.notEnumerableProp(e,"stack",i.message+"\n"+i.stack.join("\n")),s.notEnumerableProp(e,"__stackCleaned__",!0)}}}function R(n,t,i){if(K.warnings){var r,a=new c(n);if(t)i._attachExtraTrace(a);else if(K.longStackTraces&&(r=e._peekContext()))r.attachExtraTrace(a);else{var o=P(a);a.stack=o.message+"\n"+o.stack.join("\n")}w("warning",a)||L(a,"",!0)}}function j(e){for(var n=[],t=0;t<e.length;++t){var i=e[t],r="    (No stack trace)"===i||p.test(i),a=r&&H(i);r&&!a&&(g&&" "!==i.charAt(0)&&(i="    "+i),n.push(i))}return n}function P(e){var n=e.stack,t=e.toString();return n="string"==typeof n&&n.length>0?function(e){for(var n=e.stack.replace(/\s+$/g,"").split("\n"),t=0;t<n.length;++t){var i=n[t];if("    (No stack trace)"===i||p.test(i))break}return t>0&&"SyntaxError"!=e.name&&(n=n.slice(t)),n}(e):["    (No stack trace)"],{message:t,stack:"SyntaxError"==e.name?n:j(n)}}function L(e,n,t){if("undefined"!=typeof console){var i;if(s.isObject(e)){var a=e.stack;i=n+f(a,e)}else i=n+String(e);"function"==typeof r?r(i,t):"function"!=typeof console.log&&"object"!=typeof console.log||console.log(i)}}function q(e,n,t,i){var r=!1;try{"function"==typeof n&&(r=!0,"rejectionHandled"===e?n(i):n(t,i))}catch(a){o.throwLater(a)}"unhandledRejection"===e?w(e,t,i)||r||L(t,"Unhandled rejection "):w(e,i)}function M(e){var n;if("function"==typeof e)n="[function "+(e.name||"anonymous")+"]";else{n=e&&"function"==typeof e.toString?e.toString():s.toString(e);if(/\[object [a-zA-Z0-9$_]+\]/.test(n))try{n=JSON.stringify(e)}catch(t){}0===n.length&&(n="(empty array)")}return"(<"+function(e){var n=41;if(e.length<n)return e;return e.substr(0,n-3)+"..."}(n)+">, no stack trace)"}function V(){return"function"==typeof $}var H=function(){return!1},z=/[\/<\(]([^:\/]+):(\d+):(?:\d+)\)?\s*$/;function G(e){var n=e.match(z);if(n)return{fileName:n[1],line:parseInt(n[2],10)}}function X(e){this._parent=e,this._promisesCreated=0;var n=this._length=1+(void 0===e?0:e._length);$(this,X),n>32&&this.uncycle()}s.inherits(X,Error),n.CapturedTrace=X,X.prototype.uncycle=function(){var e=this._length;if(!(e<2)){for(var n=[],t={},i=0,r=this;void 0!==r;++i)n.push(r),r=r._parent;for(i=(e=this._length=i)-1;i>=0;--i){var a=n[i].stack;void 0===t[a]&&(t[a]=i)}for(i=0;i<e;++i){var o=t[n[i].stack];if(void 0!==o&&o!==i){o>0&&(n[o-1]._parent=void 0,n[o-1]._length=1),n[i]._parent=void 0,n[i]._length=1;var c=i>0?n[i-1]:this;o<e-1?(c._parent=n[o+1],c._parent.uncycle(),c._length=c._parent._length+1):(c._parent=void 0,c._length=1);for(var s=c._length+1,d=i-2;d>=0;--d)n[d]._length=s,s++;return}}}},X.prototype.attachExtraTrace=function(e){if(!e.__stackCleaned__){this.uncycle();for(var n=P(e),t=n.message,i=[n.stack],r=this;void 0!==r;)i.push(j(r.stack.split("\n"))),r=r._parent;!function(e){for(var n=e[0],t=1;t<e.length;++t){for(var i=e[t],r=n.length-1,a=n[r],o=-1,c=i.length-1;c>=0;--c)if(i[c]===a){o=c;break}for(c=o;c>=0;--c){var s=i[c];if(n[r]!==s)break;n.pop(),r--}n=i}}(i),function(e){for(var n=0;n<e.length;++n)(0===e[n].length||n+1<e.length&&e[n][0]===e[n+1][0])&&(e.splice(n,1),n--)}(i),s.notEnumerableProp(e,"stack",function(e,n){for(var t=0;t<n.length-1;++t)n[t].push("From previous event:"),n[t]=n[t].join("\n");return t<n.length&&(n[t]=n[t].join("\n")),e+"\n"+n.join("\n")}(t,i)),s.notEnumerableProp(e,"__stackCleaned__",!0)}};var $=function(){var e=/^\s*at\s*/,n=function(e,n){return"string"==typeof e?e:void 0!==n.name&&void 0!==n.message?n.toString():M(n)};if("number"==typeof Error.stackTraceLimit&&"function"==typeof Error.captureStackTrace){Error.stackTraceLimit+=6,p=e,f=n;var t=Error.captureStackTrace;return H=function(e){return u.test(e)},function(e,n){Error.stackTraceLimit+=6,t(e,n),Error.stackTraceLimit-=6}}var i,r=new Error;if("string"==typeof r.stack&&r.stack.split("\n")[0].indexOf("stackDetection@")>=0)return p=/@/,f=n,g=!0,function(e){e.stack=(new Error).stack};try{throw new Error}catch(a){i="stack"in a}return!("stack"in r)&&i&&"number"==typeof Error.stackTraceLimit?(p=e,f=n,function(e){Error.stackTraceLimit+=6;try{throw new Error}catch(a){e.stack=a.stack}Error.stackTraceLimit-=6}):(f=function(e,n){return"string"==typeof e?e:"object"!=typeof n&&"function"!=typeof n||void 0===n.name||void 0===n.message?M(n):n.toString()},null)}();"undefined"!=typeof console&&void 0!==console.warn&&(r=function(e){console.warn(e)},s.isNode&&process.stderr.isTTY?r=function(e,n){var t=n?"[33m":"[31m";console.warn(t+e+"[0m\n")}:s.isNode||"string"!=typeof(new Error).stack||(r=function(e,n){console.warn("%c"+e,n?"color: darkorange":"color: red")}));var K={warnings:m,longStackTraces:!1,cancellation:!1,monitoring:!1};return y&&e.longStackTraces(),{longStackTraces:function(){return K.longStackTraces},warnings:function(){return K.warnings},cancellation:function(){return K.cancellation},monitoring:function(){return K.monitoring},propagateFromFunction:function(){return B},boundValueFunction:function(){return k},checkForgottenReturns:function(e,n,t,i,r){if(void 0===e&&null!==n&&x){if(void 0!==r&&r._returnedNonUndefined())return;if(!(65535&i._bitField))return;t&&(t+=" ");var a="",o="";if(n._trace){for(var c=n._trace.stack.split("\n"),s=j(c),d=s.length-1;d>=0;--d){var u=s[d];if(!l.test(u)){var p=u.match(h);p&&(a="at "+p[1]+":"+p[2]+":"+p[3]+" ");break}}if(s.length>0){var f=s[0];for(d=0;d<c.length;++d)if(c[d]===f){d>0&&(o="\n"+c[d-1]);break}}}var g="a promise was created in a "+t+"handler "+a+"but was not returned from it, see http://goo.gl/rRqMUw"+o;i._warn(g,!0,n)}},setBounds:function(e,n){if(V()){for(var t,i,r=e.stack.split("\n"),a=n.stack.split("\n"),o=-1,c=-1,s=0;s<r.length;++s){if(d=G(r[s])){t=d.fileName,o=d.line;break}}for(s=0;s<a.length;++s){var d;if(d=G(a[s])){i=d.fileName,c=d.line;break}}o<0||c<0||!t||!i||t!==i||o>=c||(H=function(e){if(u.test(e))return!0;var n=G(e);return!!(n&&n.fileName===t&&o<=n.line&&n.line<=c)})}},warn:R,deprecated:function(e,n){var t=e+" is deprecated and will be removed in a future version.";return n&&(t+=" Use "+n+" instead."),R(t)},CapturedTrace:X,fireDomEvent:U,fireGlobalEvent:v}}),pi}function mr(){if(Di)return xi;Di=1;var e=Jt(),n=e.maybeWrapAsError,t=fr().OperationalError,i=Zt();var r=/^(?:name|message|stack|cause)$/;function a(n){var a;if(function(e){return e instanceof Error&&i.getPrototypeOf(e)===Error.prototype}(n)){(a=new t(n)).name=n.name,a.message=n.message,a.stack=n.stack;for(var o=i.keys(n),c=0;c<o.length;++c){var s=o[c];r.test(s)||(a[s]=n[s])}return a}return e.markAsOriginatingFromRejection(n),n}return xi=function(e,t){return function(i,r){if(null!==e){if(i){var o=a(n(i));e._attachExtraTrace(o),e._reject(o)}else if(t){for(var c=arguments.length,s=new Array(Math.max(c-1,0)),d=1;d<c;++d)s[d-1]=arguments[d];e._fulfill(s)}else e._fulfill(r);e=null}}},xi}function yr(){return ji||(ji=1,Ri=function(e,n,t,i,r,a){var o=Jt(),c=fr().TypeError,s=Jt().inherits,d=o.errorObj,u=o.tryCatch,l={};function h(e){setTimeout(function(){throw e},0)}function p(n,i){var a=0,o=n.length,c=new e(r);return function r(){if(a>=o)return c._fulfill();var s=function(e){var n=t(e);return n!==e&&"function"==typeof e._isDisposable&&"function"==typeof e._getDisposer&&e._isDisposable()&&n._setDisposable(e._getDisposer()),n}(n[a++]);if(s instanceof e&&s._isDisposable()){try{s=t(s._getDisposer().tryDispose(i),n.promise)}catch(d){return h(d)}if(s instanceof e)return s._then(r,h,null,null,null)}r()}(),c}function f(e,n,t){this._data=e,this._promise=n,this._context=t}function g(e,n,t){this.constructor$(e,n,t)}function b(e){return f.isDisposer(e)?(this.resources[this.index]._setDisposable(e),e.promise()):e}function m(e){this.length=e,this.promise=null,this[e-1]=null}f.prototype.data=function(){return this._data},f.prototype.promise=function(){return this._promise},f.prototype.resource=function(){return this.promise().isFulfilled()?this.promise().value():l},f.prototype.tryDispose=function(e){var n=this.resource(),t=this._context;void 0!==t&&t._pushContext();var i=n!==l?this.doDispose(n,e):null;return void 0!==t&&t._popContext(),this._promise._unsetDisposable(),this._data=null,i},f.isDisposer=function(e){return null!=e&&"function"==typeof e.resource&&"function"==typeof e.tryDispose},s(g,f),g.prototype.doDispose=function(e,n){return this.data().call(e,e,n)},m.prototype._resultCancelled=function(){for(var n=this.length,t=0;t<n;++t){var i=this[t];i instanceof e&&i.cancel()}},e.using=function(){var i=arguments.length;if(i<2)return n("you must pass at least 2 arguments to Promise.using");var r,c=arguments[i-1];if("function"!=typeof c)return n("expecting a function but got "+o.classString(c));var s=!0;2===i&&Array.isArray(arguments[0])?(i=(r=arguments[0]).length,s=!1):(r=arguments,i--);for(var l=new m(i),h=0;h<i;++h){var g=r[h];if(f.isDisposer(g)){var y=g;(g=g.promise())._setDisposable(y)}else{var x=t(g);x instanceof e&&(g=x._then(b,null,null,{resources:l,index:h},void 0))}l[h]=g}var D=new Array(l.length);for(h=0;h<D.length;++h)D[h]=e.resolve(l[h]).reflect();var U=e.all(D).then(function(e){for(var n=0;n<e.length;++n){var t=e[n];if(t.isRejected())return d.e=t.error(),d;if(!t.isFulfilled())return void U.cancel();e[n]=t.value()}v._pushContext(),c=u(c);var i=s?c.apply(void 0,e):c(e),r=v._popContext();return a.checkForgottenReturns(i,r,"Promise.using",v),i}),v=U.lastly(function(){var n=new e.PromiseInspection(U);return p(l,n)});return l.promise=v,v._setOnCancel(l),v},e.prototype._setDisposable=function(e){this._bitField=131072|this._bitField,this._disposer=e},e.prototype._isDisposable=function(){return(131072&this._bitField)>0},e.prototype._getDisposer=function(){return this._disposer},e.prototype._unsetDisposable=function(){this._bitField=-131073&this._bitField,this._disposer=void 0},e.prototype.disposer=function(e){if("function"==typeof e)return new g(e,this,i());throw new c}}),Ri}function xr(){return Li?Pi:(Li=1,Pi=function(e,n,t){var i=Jt(),r=e.TimeoutError;function a(e){this.handle=e}a.prototype._resultCancelled=function(){clearTimeout(this.handle)};var o=function(e){return c(+this).thenReturn(e)},c=e.delay=function(i,r){var c,s;return void 0!==r?(c=e.resolve(r)._then(o,null,null,i,void 0),t.cancellation()&&r instanceof e&&c._setOnCancel(r)):(c=new e(n),s=setTimeout(function(){c._fulfill()},+i),t.cancellation()&&c._setOnCancel(new a(s)),c._captureStackTrace()),c._setAsyncGuaranteed(),c};e.prototype.delay=function(e){return c(e,this)};function s(e){return clearTimeout(this.handle),e}function d(e){throw clearTimeout(this.handle),e}e.prototype.timeout=function(e,n){var o,c;e=+e;var u=new a(setTimeout(function(){o.isPending()&&function(e,n,t){var a;a="string"!=typeof n?n instanceof Error?n:new r("operation timed out"):new r(n),i.markAsOriginatingFromRejection(a),e._attachExtraTrace(a),e._reject(a),null!=t&&t.cancel()}(o,n,c)},e));return t.cancellation()?(c=this.then(),(o=c._then(s,d,void 0,u,void 0))._setOnCancel(u)):o=this._then(s,d,void 0,u,void 0),o}})}function Dr(){return Mi||(Mi=1,qi=function(e,n,t,i,r,a){var o=fr().TypeError,c=Jt(),s=c.errorObj,d=c.tryCatch,u=[];function l(n,i,r,o){if(a.cancellation()){var c=new e(t),s=this._finallyPromise=new e(t);this._promise=c.lastly(function(){return s}),c._captureStackTrace(),c._setOnCancel(this)}else{(this._promise=new e(t))._captureStackTrace()}this._stack=o,this._generatorFunction=n,this._receiver=i,this._generator=void 0,this._yieldHandlers="function"==typeof r?[r].concat(u):u,this._yieldedPromise=null,this._cancellationPhase=!1}c.inherits(l,r),l.prototype._isResolved=function(){return null===this._promise},l.prototype._cleanup=function(){this._promise=this._generator=null,a.cancellation()&&null!==this._finallyPromise&&(this._finallyPromise._fulfill(),this._finallyPromise=null)},l.prototype._promiseCancelled=function(){if(!this._isResolved()){var n;if(void 0!==this._generator.return)this._promise._pushContext(),n=d(this._generator.return).call(this._generator,void 0),this._promise._popContext();else{var t=new e.CancellationError("generator .return() sentinel");e.coroutine.returnSentinel=t,this._promise._attachExtraTrace(t),this._promise._pushContext(),n=d(this._generator.throw).call(this._generator,t),this._promise._popContext()}this._cancellationPhase=!0,this._yieldedPromise=null,this._continue(n)}},l.prototype._promiseFulfilled=function(e){this._yieldedPromise=null,this._promise._pushContext();var n=d(this._generator.next).call(this._generator,e);this._promise._popContext(),this._continue(n)},l.prototype._promiseRejected=function(e){this._yieldedPromise=null,this._promise._attachExtraTrace(e),this._promise._pushContext();var n=d(this._generator.throw).call(this._generator,e);this._promise._popContext(),this._continue(n)},l.prototype._resultCancelled=function(){if(this._yieldedPromise instanceof e){var n=this._yieldedPromise;this._yieldedPromise=null,n.cancel()}},l.prototype.promise=function(){return this._promise},l.prototype._run=function(){this._generator=this._generatorFunction.call(this._receiver),this._receiver=this._generatorFunction=void 0,this._promiseFulfilled(void 0)},l.prototype._continue=function(n){var t=this._promise;if(n===s)return this._cleanup(),this._cancellationPhase?t.cancel():t._rejectCallback(n.e,!1);var r=n.value;if(!0===n.done)return this._cleanup(),this._cancellationPhase?t.cancel():t._resolveCallback(r);var a=i(r,this._promise);if(a instanceof e||(a=function(n,t,r){for(var a=0;a<t.length;++a){r._pushContext();var o=d(t[a])(n);if(r._popContext(),o===s){r._pushContext();var c=e.reject(s.e);return r._popContext(),c}var u=i(o,r);if(u instanceof e)return u}return null}(a,this._yieldHandlers,this._promise),null!==a)){var c=(a=a._target())._bitField;50397184&c?33554432&c?e._async.invoke(this._promiseFulfilled,this,a._value()):16777216&c?e._async.invoke(this._promiseRejected,this,a._reason()):this._promiseCancelled():(this._yieldedPromise=a,a._proxy(this,null))}else this._promiseRejected(new o("A value %s was yielded that could not be treated as a promise\n\n    See http://goo.gl/MqrFmX\n\n".replace("%s",r)+"From coroutine:\n"+this._stack.split("\n").slice(1,-7).join("\n")))},e.coroutine=function(e,n){if("function"!=typeof e)throw new o("generatorFunction must be a function\n\n    See http://goo.gl/MqrFmX\n");var t=Object(n).yieldHandler,i=l,r=(new Error).stack;return function(){var n=e.apply(this,arguments),a=new i(void 0,void 0,t,r),o=a.promise();return a._generator=n,a._promiseFulfilled(void 0),o}},e.coroutine.addYieldHandler=function(e){if("function"!=typeof e)throw new o("expecting a function but got "+c.classString(e));u.push(e)},e.spawn=function(t){if(a.deprecated("Promise.spawn()","Promise.coroutine()"),"function"!=typeof t)return n("generatorFunction must be a function\n\n    See http://goo.gl/MqrFmX\n");var i=new l(t,this),r=i.promise();return i._run(e.spawn),r}}),qi}function Ur(){return Gi||(Gi=1,zi=function(e,n){var t={},i=Jt(),r=mr(),a=i.withAppended,o=i.maybeWrapAsError,c=i.canEvaluate,s=fr().TypeError,d={__isPromisified__:!0},u=new RegExp("^(?:"+["arity","length","name","arguments","caller","callee","prototype","__isPromisified__"].join("|")+")$"),l=function(e){return i.isIdentifier(e)&&"_"!==e.charAt(0)&&"constructor"!==e};function h(e){return!u.test(e)}function p(e){try{return!0===e.__isPromisified__}catch(n){return!1}}function f(e,n,t){var r=i.getDataPropertyOrDefault(e,n+t,d);return!!r&&p(r)}function g(e,n,t,r){for(var a=i.inheritedDataKeys(e),o=[],c=0;c<a.length;++c){var d=a[c],u=e[d],h=r===l||l(d);"function"!=typeof u||p(u)||f(e,d,n)||!r(d,u,e,h)||o.push(d,u)}return function(e,n,t){for(var i=0;i<e.length;i+=2){var r=e[i];if(t.test(r))for(var a=r.replace(t,""),o=0;o<e.length;o+=2)if(e[o]===a)throw new s("Cannot promisify an API that has normal methods with '%s'-suffix\n\n    See http://goo.gl/MqrFmX\n".replace("%s",n))}}(o,n,t),o}var b=c?function(c,s,d,u,l,h){var p=Math.max(0,function(e){return"number"==typeof e.length?Math.max(Math.min(e.length,1024),0):0}(u)-1),f=function(e){for(var n=[e],t=Math.max(0,e-1-3),i=e-1;i>=t;--i)n.push(i);for(i=e+1;i<=3;++i)n.push(i);return n}(p),g="string"==typeof c||s===t;function b(e){var n,t=(n=e,i.filledRange(n,"_arg","")).join(", "),r=e>0?", ":"";return(g?"ret = callback.call(this, {{args}}, nodeback); break;\n":void 0===s?"ret = callback({{args}}, nodeback); break;\n":"ret = callback.call(receiver, {{args}}, nodeback); break;\n").replace("{{args}}",t).replace(", ",r)}var m,y="string"==typeof c?"this != null ? this['"+c+"'] : fn":"fn",x="'use strict';                                                \n\t        var ret = function (Parameters) {                                    \n\t            'use strict';                                                    \n\t            var len = arguments.length;                                      \n\t            var promise = new Promise(INTERNAL);                             \n\t            promise._captureStackTrace();                                    \n\t            var nodeback = nodebackForPromise(promise, "+h+");   \n\t            var ret;                                                         \n\t            var callback = tryCatch([GetFunctionCode]);                      \n\t            switch(len) {                                                    \n\t                [CodeForSwitchCase]                                          \n\t            }                                                                \n\t            if (ret === errorObj) {                                          \n\t                promise._rejectCallback(maybeWrapAsError(ret.e), true, true);\n\t            }                                                                \n\t            if (!promise._isFateSealed()) promise._setAsyncGuaranteed();     \n\t            return promise;                                                  \n\t        };                                                                   \n\t        notEnumerableProp(ret, '__isPromisified__', true);                   \n\t        return ret;                                                          \n\t    ".replace("[CodeForSwitchCase]",function(){for(var e="",n=0;n<f.length;++n)e+="case "+f[n]+":"+b(f[n]);return e+="                                                             \n\t        default:                                                             \n\t            var args = new Array(len + 1);                                   \n\t            var i = 0;                                                       \n\t            for (var i = 0; i < len; ++i) {                                  \n\t               args[i] = arguments[i];                                       \n\t            }                                                                \n\t            args[i] = nodeback;                                              \n\t            [CodeForCall]                                                    \n\t            break;                                                           \n\t        ".replace("[CodeForCall]",g?"ret = callback.apply(this, args);\n":"ret = callback.apply(receiver, args);\n")}()).replace("[GetFunctionCode]",y);return x=x.replace("Parameters",(m=p,i.filledRange(Math.max(m,3),"_arg",""))),new Function("Promise","fn","receiver","withAppended","maybeWrapAsError","nodebackForPromise","tryCatch","errorObj","notEnumerableProp","INTERNAL",x)(e,u,s,a,o,r,i.tryCatch,i.errorObj,i.notEnumerableProp,n)}:function(c,s,d,u,l,h){var p=function(){return this}(),f=c;function g(){var i=s;s===t&&(i=this);var d=new e(n);d._captureStackTrace();var u="string"==typeof f&&this!==p?this[f]:c,l=r(d,h);try{u.apply(i,a(arguments,l))}catch(g){d._rejectCallback(o(g),!0,!0)}return d._isFateSealed()||d._setAsyncGuaranteed(),d}return"string"==typeof f&&(c=u),i.notEnumerableProp(g,"__isPromisified__",!0),g};function m(e,n,r,a,o){for(var c=new RegExp(n.replace(/([$])/,"\\$")+"$"),s=g(e,n,c,r),d=0,u=s.length;d<u;d+=2){var l=s[d],h=s[d+1],p=l+n;if(a===b)e[p]=b(l,t,l,h,n,o);else{var f=a(h,function(){return b(l,t,l,h,n,o)});i.notEnumerableProp(f,"__isPromisified__",!0),e[p]=f}}return i.toFastProperties(e),e}e.promisify=function(e,n){if("function"!=typeof e)throw new s("expecting a function but got "+i.classString(e));if(p(e))return e;var r=function(e,n,t){return b(e,n,void 0,e,null,t)}(e,void 0===(n=Object(n)).context?t:n.context,!!n.multiArgs);return i.copyDescriptors(e,r,h),r},e.promisifyAll=function(e,n){if("function"!=typeof e&&"object"!=typeof e)throw new s("the target of promisifyAll must be an object or a function\n\n    See http://goo.gl/MqrFmX\n");var t=!!(n=Object(n)).multiArgs,r=n.suffix;"string"!=typeof r&&(r="Async");var a=n.filter;"function"!=typeof a&&(a=l);var o=n.promisifier;if("function"!=typeof o&&(o=b),!i.isIdentifier(r))throw new RangeError("suffix must be a valid identifier\n\n    See http://goo.gl/MqrFmX\n");for(var c=i.inheritedDataKeys(e),d=0;d<c.length;++d){var u=e[c[d]];"constructor"!==c[d]&&i.isClass(u)&&(m(u.prototype,r,a,o,t),m(u,r,a,o,t))}return m(e,r,a,o,t)}}),zi}function vr(){return $i?Xi:($i=1,Xi=function(e,n,t,i){var r,a=Jt(),o=a.isObject,c=Zt();"function"==typeof Map&&(r=Map);var s=function(){var e=0,n=0;function t(t,i){this[e]=t,this[e+n]=i,e++}return function(i){n=i.size,e=0;var r=new Array(2*i.size);return i.forEach(t,r),r}}();function d(e){var n,t=!1;if(void 0!==r&&e instanceof r)n=s(e),t=!0;else{var i=c.keys(e),a=i.length;n=new Array(2*a);for(var o=0;o<a;++o){var d=i[o];n[o]=e[d],n[o+a]=d}}this.constructor$(n),this._isMap=t,this._init$(void 0,-3)}function u(n){var r,a=t(n);return o(a)?(r=a instanceof e?a._then(e.props,void 0,void 0,void 0,void 0):new d(a).promise(),a instanceof e&&r._propagateFrom(a,2),r):i("cannot await properties of a non-object\n\n    See http://goo.gl/MqrFmX\n")}a.inherits(d,n),d.prototype._init=function(){},d.prototype._promiseFulfilled=function(e,n){if(this._values[n]=e,++this._totalResolved>=this._length){var t;if(this._isMap)t=function(e){for(var n=new r,t=e.length/2|0,i=0;i<t;++i){var a=e[t+i],o=e[i];n.set(a,o)}return n}(this._values);else{t={};for(var i=this.length(),a=0,o=this.length();a<o;++a)t[this._values[a+i]]=this._values[a]}return this._resolve(t),!0}return!1},d.prototype.shouldCopyValues=function(){return!1},d.prototype.getActualLength=function(e){return e>>1},e.prototype.props=function(){return u(this)},e.props=function(e){return u(e)}})}function Tr(){return Qi?Ki:(Qi=1,Ki=function(e,n,t,i){var r=Jt();function a(o,c){var s,d=t(o);if(d instanceof e)return(s=d).then(function(e){return a(e,s)});if(null===(o=r.asArray(o)))return i("expecting an array or an iterable object but got "+r.classString(o));var u=new e(n);void 0!==c&&u._propagateFrom(c,3);for(var l=u._fulfill,h=u._reject,p=0,f=o.length;p<f;++p){var g=o[p];(void 0!==g||p in o)&&e.cast(g)._then(l,h,void 0,u,null)}return u}e.race=function(e){return a(e,void 0)},e.prototype.race=function(){return a(this,void 0)}})}function _r(){return dr||(dr=1,(e=Qt).exports=function(){var n=function(){return new h("circular promise resolution chain\n\n    See http://goo.gl/MqrFmX\n")},t=function(){return new E.PromiseInspection(this._target())},i=function(e){return E.reject(new h(e))};function r(){}var a,o={},c=Jt();a=c.isNode?function(){var e=process.domain;return void 0===e&&(e=null),e}:function(){return null},c.notEnumerableProp(E,"_getDomain",a);var s=Zt(),d=pr(),u=new d;s.defineProperty(E,"_async",{value:u});var l=fr(),h=E.TypeError=l.TypeError;E.RangeError=l.RangeError;var p=E.CancellationError=l.CancellationError;E.TimeoutError=l.TimeoutError,E.OperationalError=l.OperationalError,E.RejectionError=l.OperationalError,E.AggregateError=l.AggregateError;var f=function(){},g={},b={},m=gr()(E,f),y=(ui?di:(ui=1,di=function(e,n,t,i,r){var a=Jt();function o(t){var i=this._promise=new e(n);t instanceof e&&i._propagateFrom(t,3),i._setOnCancel(this),this._values=t,this._length=0,this._totalResolved=0,this._init(void 0,-2)}return a.isArray,a.inherits(o,r),o.prototype.length=function(){return this._length},o.prototype.promise=function(){return this._promise},o.prototype._init=function n(r,o){var c=t(this._values,this._promise);if(c instanceof e){var s=(c=c._target())._bitField;if(this._values=c,!(50397184&s))return this._promise._setAsyncGuaranteed(),c._then(n,this._reject,void 0,this,o);if(!(33554432&s))return 16777216&s?this._reject(c._reason()):this._cancel();c=c._value()}if(null!==(c=a.asArray(c)))0!==c.length?this._iterate(c):-5===o?this._resolveEmptyArray():this._resolve(function(e){switch(e){case-2:return[];case-3:return{}}}(o));else{var d=i("expecting an array or an iterable object but got "+a.classString(c)).reason();this._promise._rejectCallback(d,!1)}},o.prototype._iterate=function(n){var i=this.getActualLength(n.length);this._length=i,this._values=this.shouldCopyValues()?new Array(i):this._values;for(var r=this._promise,a=!1,o=null,c=0;c<i;++c){var s=t(n[c],r);o=s instanceof e?(s=s._target())._bitField:null,a?null!==o&&s.suppressUnhandledRejections():null!==o?50397184&o?a=33554432&o?this._promiseFulfilled(s._value(),c):16777216&o?this._promiseRejected(s._reason(),c):this._promiseCancelled(c):(s._proxy(this,c),this._values[c]=s):a=this._promiseFulfilled(s,c)}a||r._setAsyncGuaranteed()},o.prototype._isResolved=function(){return null===this._values},o.prototype._resolve=function(e){this._values=null,this._promise._fulfill(e)},o.prototype._cancel=function(){!this._isResolved()&&this._promise._isCancellable()&&(this._values=null,this._promise._cancel())},o.prototype._reject=function(e){this._values=null,this._promise._rejectCallback(e,!1)},o.prototype._promiseFulfilled=function(e,n){return this._values[n]=e,++this._totalResolved>=this._length&&(this._resolve(this._values),!0)},o.prototype._promiseCancelled=function(){return this._cancel(),!0},o.prototype._promiseRejected=function(e){return this._totalResolved++,this._reject(e),!0},o.prototype._resultCancelled=function(){if(!this._isResolved()){var n=this._values;if(this._cancel(),n instanceof e)n.cancel();else for(var t=0;t<n.length;++t)n[t]instanceof e&&n[t].cancel()}},o.prototype.shouldCopyValues=function(){return!0},o.prototype.getActualLength=function(e){return e},o}))(E,f,m,i,r),x=(hi?li:(hi=1,li=function(e){var n=!1,t=[];function i(){this._trace=new i.CapturedTrace(r())}function r(){var e=t.length-1;if(e>=0)return t[e]}return e.prototype._promiseCreated=function(){},e.prototype._pushContext=function(){},e.prototype._popContext=function(){return null},e._peekContext=e.prototype._peekContext=function(){},i.prototype._pushContext=function(){void 0!==this._trace&&(this._trace._promiseCreated=null,t.push(this._trace))},i.prototype._popContext=function(){if(void 0!==this._trace){var e=t.pop(),n=e._promiseCreated;return e._promiseCreated=null,n}return null},i.CapturedTrace=null,i.create=function(){if(n)return new i},i.deactivateLongStackTraces=function(){},i.activateLongStackTraces=function(){var t=e.prototype._pushContext,a=e.prototype._popContext,o=e._peekContext,c=e.prototype._peekContext,s=e.prototype._promiseCreated;i.deactivateLongStackTraces=function(){e.prototype._pushContext=t,e.prototype._popContext=a,e._peekContext=o,e.prototype._peekContext=c,e.prototype._promiseCreated=s,n=!1},n=!0,e.prototype._pushContext=i.prototype._pushContext,e.prototype._popContext=i.prototype._popContext,e._peekContext=e.prototype._peekContext=r,e.prototype._promiseCreated=function(){var e=this._peekContext();e&&null==e._promiseCreated&&(e._promiseCreated=this)}},i}))(E),D=x.create,U=br()(E,x);U.CapturedTrace;var v=(bi||(bi=1,gi=function(e,n){var t=Jt(),i=e.CancellationError,r=t.errorObj;function a(e,n,t){this.promise=e,this.type=n,this.handler=t,this.called=!1,this.cancelPromise=null}function o(e){this.finallyHandler=e}function c(e,n){return null!=e.cancelPromise&&(arguments.length>1?e.cancelPromise._reject(n):e.cancelPromise._cancel(),e.cancelPromise=null,!0)}function s(){return u.call(this,this.promise._target()._settledValue())}function d(e){if(!c(this,e))return r.e=e,r}function u(t){var a=this.promise,u=this.handler;if(!this.called){this.called=!0;var l=this.isFinallyHandler()?u.call(a._boundValue()):u.call(a._boundValue(),t);if(void 0!==l){a._setReturnedNonUndefined();var h=n(l,a);if(h instanceof e){if(null!=this.cancelPromise){if(h._isCancelled()){var p=new i("late cancellation observer");return a._attachExtraTrace(p),r.e=p,r}h.isPending()&&h._attachCancellationCallback(new o(this))}return h._then(s,d,void 0,this,void 0)}}}return a.isRejected()?(c(this),r.e=t,r):(c(this),t)}return a.prototype.isFinallyHandler=function(){return 0===this.type},o.prototype._resultCancelled=function(){c(this.finallyHandler)},e.prototype._passThrough=function(e,n,t,i){return"function"!=typeof e?this.then():this._then(t,i,void 0,new a(this,n,e),void 0)},e.prototype.lastly=e.prototype.finally=function(e){return this._passThrough(e,0,u,u)},e.prototype.tap=function(e){return this._passThrough(e,1,u)},a}),gi)(E,m),T=(yi?mi:(yi=1,mi=function(e){var n=Jt(),t=Zt().keys,i=n.tryCatch,r=n.errorObj;return function(a,o,c){return function(s){var d=c._boundValue();e:for(var u=0;u<a.length;++u){var l=a[u];if(l===Error||null!=l&&l.prototype instanceof Error){if(s instanceof l)return i(o).call(d,s)}else if("function"==typeof l){var h=i(l).call(d,s);if(h===r)return h;if(h)return i(o).call(d,s)}else if(n.isObject(s)){for(var p=t(l),f=0;f<p.length;++f){var g=p[f];if(l[g]!=s[g])continue e}return i(o).call(d,s)}}return e}}}))(b),_=mr(),w=c.errorObj,F=c.tryCatch;function E(e){this._bitField=0,this._fulfillmentHandler0=void 0,this._rejectionHandler0=void 0,this._promise0=void 0,this._receiver0=void 0,e!==f&&(function(e,n){if("function"!=typeof n)throw new h("expecting a function but got "+c.classString(n));if(e.constructor!==E)throw new h("the promise constructor cannot be invoked directly\n\n    See http://goo.gl/MqrFmX\n")}(this,e),this._resolveFromExecutor(e)),this._promiseCreated(),this._fireEvent("promiseCreated",this)}function W(e){this.promise._resolveCallback(e)}function C(e){this.promise._rejectCallback(e,!1)}function A(e){var n=new E(f);n._fulfillmentHandler0=e,n._rejectionHandler0=e,n._promise0=e,n._receiver0=e}return E.prototype.toString=function(){return"[object Promise]"},E.prototype.caught=E.prototype.catch=function(e){var n=arguments.length;if(n>1){var t,r=new Array(n-1),a=0;for(t=0;t<n-1;++t){var o=arguments[t];if(!c.isObject(o))return i("expecting an object but got A catch statement predicate "+c.classString(o));r[a++]=o}return r.length=a,e=arguments[t],this.then(void 0,T(r,e,this))}return this.then(void 0,e)},E.prototype.reflect=function(){return this._then(t,t,void 0,this,void 0)},E.prototype.then=function(e,n){if(U.warnings()&&arguments.length>0&&"function"!=typeof e&&"function"!=typeof n){var t=".then() only accepts functions but was passed: "+c.classString(e);arguments.length>1&&(t+=", "+c.classString(n)),this._warn(t)}return this._then(e,n,void 0,void 0,void 0)},E.prototype.done=function(e,n){this._then(e,n,void 0,void 0,void 0)._setIsFinal()},E.prototype.spread=function(e){return"function"!=typeof e?i("expecting a function but got "+c.classString(e)):this.all()._then(e,void 0,void 0,g,void 0)},E.prototype.toJSON=function(){var e={isFulfilled:!1,isRejected:!1,fulfillmentValue:void 0,rejectionReason:void 0};return this.isFulfilled()?(e.fulfillmentValue=this.value(),e.isFulfilled=!0):this.isRejected()&&(e.rejectionReason=this.reason(),e.isRejected=!0),e},E.prototype.all=function(){return arguments.length>0&&this._warn(".all() was passed arguments but it does not take any"),new y(this).promise()},E.prototype.error=function(e){return this.caught(c.originatesFromRejection,e)},E.getNewLibraryCopy=e.exports,E.is=function(e){return e instanceof E},E.fromNode=E.fromCallback=function(e){var n=new E(f);n._captureStackTrace();var t=arguments.length>1&&!!Object(arguments[1]).multiArgs,i=F(e)(_(n,t));return i===w&&n._rejectCallback(i.e,!0),n._isFateSealed()||n._setAsyncGuaranteed(),n},E.all=function(e){return new y(e).promise()},E.cast=function(e){var n=m(e);return n instanceof E||((n=new E(f))._captureStackTrace(),n._setFulfilled(),n._rejectionHandler0=e),n},E.resolve=E.fulfilled=E.cast,E.reject=E.rejected=function(e){var n=new E(f);return n._captureStackTrace(),n._rejectCallback(e,!0),n},E.setScheduler=function(e){if("function"!=typeof e)throw new h("expecting a function but got "+c.classString(e));return u.setScheduler(e)},E.prototype._then=function(e,n,t,i,r){var o=void 0!==r,s=o?r:new E(f),d=this._target(),l=d._bitField;o||(s._propagateFrom(this,3),s._captureStackTrace(),void 0===i&&2097152&this._bitField&&(i=50397184&l?this._boundValue():d===this?void 0:this._boundTo),this._fireEvent("promiseChained",this,s));var h=a();if(50397184&l){var g,b,m=d._settlePromiseCtx;33554432&l?(b=d._rejectionHandler0,g=e):16777216&l?(b=d._fulfillmentHandler0,g=n,d._unsetRejectionIsUnhandled()):(m=d._settlePromiseLateCancellationObserver,b=new p("late cancellation observer"),d._attachExtraTrace(b),g=n),u.invoke(m,d,{handler:null===h?g:"function"==typeof g&&c.domainBind(h,g),promise:s,receiver:i,value:b})}else d._addCallbacks(e,n,s,i,h);return s},E.prototype._length=function(){return 65535&this._bitField},E.prototype._isFateSealed=function(){return!!(117506048&this._bitField)},E.prototype._isFollowing=function(){return!(67108864&~this._bitField)},E.prototype._setLength=function(e){this._bitField=-65536&this._bitField|65535&e},E.prototype._setFulfilled=function(){this._bitField=33554432|this._bitField,this._fireEvent("promiseFulfilled",this)},E.prototype._setRejected=function(){this._bitField=16777216|this._bitField,this._fireEvent("promiseRejected",this)},E.prototype._setFollowing=function(){this._bitField=67108864|this._bitField,this._fireEvent("promiseResolved",this)},E.prototype._setIsFinal=function(){this._bitField=4194304|this._bitField},E.prototype._isFinal=function(){return(4194304&this._bitField)>0},E.prototype._unsetCancelled=function(){this._bitField=-65537&this._bitField},E.prototype._setCancelled=function(){this._bitField=65536|this._bitField,this._fireEvent("promiseCancelled",this)},E.prototype._setWillBeCancelled=function(){this._bitField=8388608|this._bitField},E.prototype._setAsyncGuaranteed=function(){u.hasCustomScheduler()||(this._bitField=134217728|this._bitField)},E.prototype._receiverAt=function(e){var n=0===e?this._receiver0:this[4*e-4+3];if(n!==o)return void 0===n&&this._isBound()?this._boundValue():n},E.prototype._promiseAt=function(e){return this[4*e-4+2]},E.prototype._fulfillmentHandlerAt=function(e){return this[4*e-4+0]},E.prototype._rejectionHandlerAt=function(e){return this[4*e-4+1]},E.prototype._boundValue=function(){},E.prototype._migrateCallback0=function(e){e._bitField;var n=e._fulfillmentHandler0,t=e._rejectionHandler0,i=e._promise0,r=e._receiverAt(0);void 0===r&&(r=o),this._addCallbacks(n,t,i,r,null)},E.prototype._migrateCallbackAt=function(e,n){var t=e._fulfillmentHandlerAt(n),i=e._rejectionHandlerAt(n),r=e._promiseAt(n),a=e._receiverAt(n);void 0===a&&(a=o),this._addCallbacks(t,i,r,a,null)},E.prototype._addCallbacks=function(e,n,t,i,r){var a=this._length();if(a>=65531&&(a=0,this._setLength(0)),0===a)this._promise0=t,this._receiver0=i,"function"==typeof e&&(this._fulfillmentHandler0=null===r?e:c.domainBind(r,e)),"function"==typeof n&&(this._rejectionHandler0=null===r?n:c.domainBind(r,n));else{var o=4*a-4;this[o+2]=t,this[o+3]=i,"function"==typeof e&&(this[o+0]=null===r?e:c.domainBind(r,e)),"function"==typeof n&&(this[o+1]=null===r?n:c.domainBind(r,n))}return this._setLength(a+1),a},E.prototype._proxy=function(e,n){this._addCallbacks(void 0,void 0,n,e,null)},E.prototype._resolveCallback=function(e,t){if(!(117506048&this._bitField)){if(e===this)return this._rejectCallback(n(),!1);var i=m(e,this);if(!(i instanceof E))return this._fulfill(e);t&&this._propagateFrom(i,2);var r=i._target();if(r!==this){var a=r._bitField;if(50397184&a)if(33554432&a)this._fulfill(r._value());else if(16777216&a)this._reject(r._reason());else{var o=new p("late cancellation observer");r._attachExtraTrace(o),this._reject(o)}else{var c=this._length();c>0&&r._migrateCallback0(this);for(var s=1;s<c;++s)r._migrateCallbackAt(this,s);this._setFollowing(),this._setLength(0),this._setFollowee(r)}}else this._reject(n())}},E.prototype._rejectCallback=function(e,n,t){var i=c.ensureErrorObject(e),r=i===e;if(!r&&!t&&U.warnings()){var a="a promise was rejected with a non-error: "+c.classString(e);this._warn(a,!0)}this._attachExtraTrace(i,!!n&&r),this._reject(e)},E.prototype._resolveFromExecutor=function(e){var n=this;this._captureStackTrace(),this._pushContext();var t=!0,i=this._execute(e,function(e){n._resolveCallback(e)},function(e){n._rejectCallback(e,t)});t=!1,this._popContext(),void 0!==i&&n._rejectCallback(i,!0)},E.prototype._settlePromiseFromHandler=function(e,n,t,i){var r=i._bitField;if(!(65536&r)){var a;i._pushContext(),n===g?t&&"number"==typeof t.length?a=F(e).apply(this._boundValue(),t):(a=w).e=new h("cannot .spread() a non-array: "+c.classString(t)):a=F(e).call(n,t);var o=i._popContext();65536&(r=i._bitField)||(a===b?i._reject(t):a===w?i._rejectCallback(a.e,!1):(U.checkForgottenReturns(a,o,"",i,this),i._resolveCallback(a)))}},E.prototype._target=function(){for(var e=this;e._isFollowing();)e=e._followee();return e},E.prototype._followee=function(){return this._rejectionHandler0},E.prototype._setFollowee=function(e){this._rejectionHandler0=e},E.prototype._settlePromise=function(e,n,i,a){var o=e instanceof E,c=this._bitField,s=!!(134217728&c);65536&c?(o&&e._invokeInternalOnCancel(),i instanceof v&&i.isFinallyHandler()?(i.cancelPromise=e,F(n).call(i,a)===w&&e._reject(w.e)):n===t?e._fulfill(t.call(i)):i instanceof r?i._promiseCancelled(e):o||e instanceof y?e._cancel():i.cancel()):"function"==typeof n?o?(s&&e._setAsyncGuaranteed(),this._settlePromiseFromHandler(n,i,a,e)):n.call(i,a,e):i instanceof r?i._isResolved()||(33554432&c?i._promiseFulfilled(a,e):i._promiseRejected(a,e)):o&&(s&&e._setAsyncGuaranteed(),33554432&c?e._fulfill(a):e._reject(a))},E.prototype._settlePromiseLateCancellationObserver=function(e){var n=e.handler,t=e.promise,i=e.receiver,r=e.value;"function"==typeof n?t instanceof E?this._settlePromiseFromHandler(n,i,r,t):n.call(i,r,t):t instanceof E&&t._reject(r)},E.prototype._settlePromiseCtx=function(e){this._settlePromise(e.promise,e.handler,e.receiver,e.value)},E.prototype._settlePromise0=function(e,n,t){var i=this._promise0,r=this._receiverAt(0);this._promise0=void 0,this._receiver0=void 0,this._settlePromise(i,e,r,n)},E.prototype._clearCallbackDataAtIndex=function(e){var n=4*e-4;this[n+2]=this[n+3]=this[n+0]=this[n+1]=void 0},E.prototype._fulfill=function(e){var t=this._bitField;if(!((117506048&t)>>>16)){if(e===this){var i=n();return this._attachExtraTrace(i),this._reject(i)}this._setFulfilled(),this._rejectionHandler0=e,(65535&t)>0&&(134217728&t?this._settlePromises():u.settlePromises(this))}},E.prototype._reject=function(e){var n=this._bitField;if(!((117506048&n)>>>16)){if(this._setRejected(),this._fulfillmentHandler0=e,this._isFinal())return u.fatalError(e,c.isNode);(65535&n)>0?u.settlePromises(this):this._ensurePossibleRejectionHandled()}},E.prototype._fulfillPromises=function(e,n){for(var t=1;t<e;t++){var i=this._fulfillmentHandlerAt(t),r=this._promiseAt(t),a=this._receiverAt(t);this._clearCallbackDataAtIndex(t),this._settlePromise(r,i,a,n)}},E.prototype._rejectPromises=function(e,n){for(var t=1;t<e;t++){var i=this._rejectionHandlerAt(t),r=this._promiseAt(t),a=this._receiverAt(t);this._clearCallbackDataAtIndex(t),this._settlePromise(r,i,a,n)}},E.prototype._settlePromises=function(){var e=this._bitField,n=65535&e;if(n>0){if(16842752&e){var t=this._fulfillmentHandler0;this._settlePromise0(this._rejectionHandler0,t,e),this._rejectPromises(n,t)}else{var i=this._rejectionHandler0;this._settlePromise0(this._fulfillmentHandler0,i,e),this._fulfillPromises(n,i)}this._setLength(0)}this._clearCancellationData()},E.prototype._settledValue=function(){var e=this._bitField;return 33554432&e?this._rejectionHandler0:16777216&e?this._fulfillmentHandler0:void 0},E.defer=E.pending=function(){return U.deprecated("Promise.defer","new Promise"),{promise:new E(f),resolve:W,reject:C}},c.notEnumerableProp(E,"_makeSelfResolutionError",n),(vi||(vi=1,Ui=function(e,n,t,i,r){var a=Jt(),o=a.tryCatch;e.method=function(t){if("function"!=typeof t)throw new e.TypeError("expecting a function but got "+a.classString(t));return function(){var i=new e(n);i._captureStackTrace(),i._pushContext();var a=o(t).apply(this,arguments),c=i._popContext();return r.checkForgottenReturns(a,c,"Promise.method",i),i._resolveFromSyncValue(a),i}},e.attempt=e.try=function(t){if("function"!=typeof t)return i("expecting a function but got "+a.classString(t));var c,s=new e(n);if(s._captureStackTrace(),s._pushContext(),arguments.length>1){r.deprecated("calling Promise.try with more than 1 argument");var d=arguments[1],u=arguments[2];c=a.isArray(d)?o(t).apply(u,d):o(t).call(u,d)}else c=o(t)();var l=s._popContext();return r.checkForgottenReturns(c,l,"Promise.try",s),s._resolveFromSyncValue(c),s},e.prototype._resolveFromSyncValue=function(e){e===a.errorObj?this._rejectCallback(e.e,!1):this._resolveCallback(e,!0)}}),Ui)(E,f,m,i,U),(_i?Ti:(_i=1,Ti=function(e,n,t,i){var r=!1,a=function(e,n){this._reject(n)},o=function(e,n){n.promiseRejectionQueued=!0,n.bindingPromise._then(a,a,null,this,e)},c=function(e,n){50397184&this._bitField||this._resolveCallback(n.target)},s=function(e,n){n.promiseRejectionQueued||this._reject(e)};e.prototype.bind=function(a){r||(r=!0,e.prototype._propagateFrom=i.propagateFromFunction(),e.prototype._boundValue=i.boundValueFunction());var d=t(a),u=new e(n);u._propagateFrom(this,1);var l=this._target();if(u._setBoundTo(d),d instanceof e){var h={promiseRejectionQueued:!1,promise:u,target:l,bindingPromise:d};l._then(n,o,void 0,u,h),d._then(c,s,void 0,u,h),u._setOnCancel(d)}else u._resolveCallback(l);return u},e.prototype._setBoundTo=function(e){void 0!==e?(this._bitField=2097152|this._bitField,this._boundTo=e):this._bitField=-2097153&this._bitField},e.prototype._isBound=function(){return!(2097152&~this._bitField)},e.bind=function(n,t){return e.resolve(t).bind(n)}}))(E,f,m,U),(Fi?wi:(Fi=1,wi=function(e,n,t,i){var r=Jt(),a=r.tryCatch,o=r.errorObj,c=e._async;e.prototype.break=e.prototype.cancel=function(){if(!i.cancellation())return this._warn("cancellation is disabled");for(var e=this,n=e;e._isCancellable();){if(!e._cancelBy(n)){n._isFollowing()?n._followee().cancel():n._cancelBranched();break}var t=e._cancellationParent;if(null==t||!t._isCancellable()){e._isFollowing()?e._followee().cancel():e._cancelBranched();break}e._isFollowing()&&e._followee().cancel(),e._setWillBeCancelled(),n=e,e=t}},e.prototype._branchHasCancelled=function(){this._branchesRemainingToCancel--},e.prototype._enoughBranchesHaveCancelled=function(){return void 0===this._branchesRemainingToCancel||this._branchesRemainingToCancel<=0},e.prototype._cancelBy=function(e){return e===this?(this._branchesRemainingToCancel=0,this._invokeOnCancel(),!0):(this._branchHasCancelled(),!!this._enoughBranchesHaveCancelled()&&(this._invokeOnCancel(),!0))},e.prototype._cancelBranched=function(){this._enoughBranchesHaveCancelled()&&this._cancel()},e.prototype._cancel=function(){this._isCancellable()&&(this._setCancelled(),c.invoke(this._cancelPromises,this,void 0))},e.prototype._cancelPromises=function(){this._length()>0&&this._settlePromises()},e.prototype._unsetOnCancel=function(){this._onCancelField=void 0},e.prototype._isCancellable=function(){return this.isPending()&&!this._isCancelled()},e.prototype.isCancellable=function(){return this.isPending()&&!this.isCancelled()},e.prototype._doInvokeOnCancel=function(e,n){if(r.isArray(e))for(var t=0;t<e.length;++t)this._doInvokeOnCancel(e[t],n);else if(void 0!==e)if("function"==typeof e){if(!n){var i=a(e).call(this._boundValue());i===o&&(this._attachExtraTrace(i.e),c.throwLater(i.e))}}else e._resultCancelled(this)},e.prototype._invokeOnCancel=function(){var e=this._onCancel();this._unsetOnCancel(),c.invoke(this._doInvokeOnCancel,this,e)},e.prototype._invokeInternalOnCancel=function(){this._isCancellable()&&(this._doInvokeOnCancel(this._onCancel(),!0),this._unsetOnCancel())},e.prototype._resultCancelled=function(){this.cancel()}}))(E,y,i,U),(Wi||(Wi=1,Ei=function(e){function n(){return this.value}function t(){throw this.reason}e.prototype.return=e.prototype.thenReturn=function(t){return t instanceof e&&t.suppressUnhandledRejections(),this._then(n,void 0,void 0,{value:t},void 0)},e.prototype.throw=e.prototype.thenThrow=function(e){return this._then(t,void 0,void 0,{reason:e},void 0)},e.prototype.catchThrow=function(e){if(arguments.length<=1)return this._then(void 0,t,void 0,{reason:e},void 0);var n=arguments[1];return this.caught(e,function(){throw n})},e.prototype.catchReturn=function(t){if(arguments.length<=1)return t instanceof e&&t.suppressUnhandledRejections(),this._then(void 0,n,void 0,{value:t},void 0);var i=arguments[1];return i instanceof e&&i.suppressUnhandledRejections(),this.caught(t,function(){return i})}}),Ei)(E),(Ai?Ci:(Ai=1,Ci=function(e){function n(e){void 0!==e?(e=e._target(),this._bitField=e._bitField,this._settledValueField=e._isFateSealed()?e._settledValue():void 0):(this._bitField=0,this._settledValueField=void 0)}n.prototype._settledValue=function(){return this._settledValueField};var t=n.prototype.value=function(){if(!this.isFulfilled())throw new TypeError("cannot get fulfillment value of a non-fulfilled promise\n\n    See http://goo.gl/MqrFmX\n");return this._settledValue()},i=n.prototype.error=n.prototype.reason=function(){if(!this.isRejected())throw new TypeError("cannot get rejection reason of a non-rejected promise\n\n    See http://goo.gl/MqrFmX\n");return this._settledValue()},r=n.prototype.isFulfilled=function(){return!!(33554432&this._bitField)},a=n.prototype.isRejected=function(){return!!(16777216&this._bitField)},o=n.prototype.isPending=function(){return!(50397184&this._bitField)},c=n.prototype.isResolved=function(){return!!(50331648&this._bitField)};n.prototype.isCancelled=function(){return!!(8454144&this._bitField)},e.prototype.__isCancelled=function(){return!(65536&~this._bitField)},e.prototype._isCancelled=function(){return this._target().__isCancelled()},e.prototype.isCancelled=function(){return!!(8454144&this._target()._bitField)},e.prototype.isPending=function(){return o.call(this._target())},e.prototype.isRejected=function(){return a.call(this._target())},e.prototype.isFulfilled=function(){return r.call(this._target())},e.prototype.isResolved=function(){return c.call(this._target())},e.prototype.value=function(){return t.call(this._target())},e.prototype.reason=function(){var e=this._target();return e._unsetRejectionIsUnhandled(),i.call(e)},e.prototype._value=function(){return this._settledValue()},e.prototype._reason=function(){return this._unsetRejectionIsUnhandled(),this._settledValue()},e.PromiseInspection=n}))(E),(Ni||(Ni=1,Si=function(e,n,t,i,r,a){var o,c=Jt(),s=c.canEvaluate,d=c.tryCatch,u=c.errorObj;if(s){for(var l=function(e){return new Function("value","holder","                             \n\t            'use strict';                                                    \n\t            holder.pIndex = value;                                           \n\t            holder.checkFulfillment(this);                                   \n\t            ".replace(/Index/g,e))},h=function(e){return new Function("promise","holder","                           \n\t            'use strict';                                                    \n\t            holder.pIndex = promise;                                         \n\t            ".replace(/Index/g,e))},p=function(n){for(var t=new Array(n),i=0;i<t.length;++i)t[i]="this.p"+(i+1);var a=t.join(" = ")+" = null;",o="var promise;\n"+t.map(function(e){return"                                                         \n\t                promise = "+e+";                                      \n\t                if (promise instanceof Promise) {                            \n\t                    promise.cancel();                                        \n\t                }                                                            \n\t            "}).join("\n"),c=t.join(", "),s="Holder$"+n,l="return function(tryCatch, errorObj, Promise, async) {    \n\t            'use strict';                                                    \n\t            function [TheName](fn) {                                         \n\t                [TheProperties]                                              \n\t                this.fn = fn;                                                \n\t                this.asyncNeeded = true;                                     \n\t                this.now = 0;                                                \n\t            }                                                                \n\t                                                                             \n\t            [TheName].prototype._callFunction = function(promise) {          \n\t                promise._pushContext();                                      \n\t                var ret = tryCatch(this.fn)([ThePassedArguments]);           \n\t                promise._popContext();                                       \n\t                if (ret === errorObj) {                                      \n\t                    promise._rejectCallback(ret.e, false);                   \n\t                } else {                                                     \n\t                    promise._resolveCallback(ret);                           \n\t                }                                                            \n\t            };                                                               \n\t                                                                             \n\t            [TheName].prototype.checkFulfillment = function(promise) {       \n\t                var now = ++this.now;                                        \n\t                if (now === [TheTotal]) {                                    \n\t                    if (this.asyncNeeded) {                                  \n\t                        async.invoke(this._callFunction, this, promise);     \n\t                    } else {                                                 \n\t                        this._callFunction(promise);                         \n\t                    }                                                        \n\t                                                                             \n\t                }                                                            \n\t            };                                                               \n\t                                                                             \n\t            [TheName].prototype._resultCancelled = function() {              \n\t                [CancellationCode]                                           \n\t            };                                                               \n\t                                                                             \n\t            return [TheName];                                                \n\t        }(tryCatch, errorObj, Promise, async);                               \n\t        ";return l=l.replace(/\[TheName\]/g,s).replace(/\[TheTotal\]/g,n).replace(/\[ThePassedArguments\]/g,c).replace(/\[TheProperties\]/g,a).replace(/\[CancellationCode\]/g,o),new Function("tryCatch","errorObj","Promise","async",l)(d,u,e,r)},f=[],g=[],b=[],m=0;m<8;++m)f.push(p(m+1)),g.push(l(m+1)),b.push(h(m+1));o=function(e){this._reject(e)}}e.join=function(){var r,d=arguments.length-1;if(d>0&&"function"==typeof arguments[d]&&(r=arguments[d],d<=8&&s)){(v=new e(i))._captureStackTrace();for(var u=new(0,f[d-1])(r),l=g,h=0;h<d;++h){var p=t(arguments[h],v);if(p instanceof e){var m=(p=p._target())._bitField;50397184&m?33554432&m?l[h].call(v,p._value(),u):16777216&m?v._reject(p._reason()):v._cancel():(p._then(l[h],o,void 0,v,u),b[h](p,u),u.asyncNeeded=!1)}else l[h].call(v,p,u)}if(!v._isFateSealed()){if(u.asyncNeeded){var y=a();null!==y&&(u.fn=c.domainBind(y,u.fn))}v._setAsyncGuaranteed(),v._setOnCancel(u)}return v}for(var x=arguments.length,D=new Array(x),U=0;U<x;++U)D[U]=arguments[U];r&&D.pop();var v=new n(D).promise();return void 0!==r?v.spread(r):v}}),Si)(E,y,m,f,u,a),E.Promise=E,E.version="3.4.7",(ki?Bi:(ki=1,Bi=function(e,n,t,i,r,a){var o=e._getDomain,c=Jt(),s=c.tryCatch,d=c.errorObj,u=e._async;function l(e,n,t,i){this.constructor$(e),this._promise._captureStackTrace();var a=o();this._callback=null===a?n:c.domainBind(a,n),this._preservedValues=i===r?new Array(this.length()):null,this._limit=t,this._inFlight=0,this._queue=[],u.invoke(this._asyncInit,this,void 0)}function h(n,i,r,a){if("function"!=typeof i)return t("expecting a function but got "+c.classString(i));var o=0;if(void 0!==r){if("object"!=typeof r||null===r)return e.reject(new TypeError("options argument must be an object but it is "+c.classString(r)));if("number"!=typeof r.concurrency)return e.reject(new TypeError("'concurrency' must be a number but it is "+c.classString(r.concurrency)));o=r.concurrency}return new l(n,i,o="number"==typeof o&&isFinite(o)&&o>=1?o:0,a).promise()}c.inherits(l,n),l.prototype._asyncInit=function(){this._init$(void 0,-2)},l.prototype._init=function(){},l.prototype._promiseFulfilled=function(n,t){var r=this._values,o=this.length(),c=this._preservedValues,u=this._limit;if(t<0){if(r[t=-1*t-1]=n,u>=1&&(this._inFlight--,this._drainQueue(),this._isResolved()))return!0}else{if(u>=1&&this._inFlight>=u)return r[t]=n,this._queue.push(t),!1;null!==c&&(c[t]=n);var l=this._promise,h=this._callback,p=l._boundValue();l._pushContext();var f=s(h).call(p,n,t,o),g=l._popContext();if(a.checkForgottenReturns(f,g,null!==c?"Promise.filter":"Promise.map",l),f===d)return this._reject(f.e),!0;var b=i(f,this._promise);if(b instanceof e){var m=(b=b._target())._bitField;if(!(50397184&m))return u>=1&&this._inFlight++,r[t]=b,b._proxy(this,-1*(t+1)),!1;if(!(33554432&m))return 16777216&m?(this._reject(b._reason()),!0):(this._cancel(),!0);f=b._value()}r[t]=f}return++this._totalResolved>=o&&(null!==c?this._filter(r,c):this._resolve(r),!0)},l.prototype._drainQueue=function(){for(var e=this._queue,n=this._limit,t=this._values;e.length>0&&this._inFlight<n;){if(this._isResolved())return;var i=e.pop();this._promiseFulfilled(t[i],i)}},l.prototype._filter=function(e,n){for(var t=n.length,i=new Array(t),r=0,a=0;a<t;++a)e[a]&&(i[r++]=n[a]);i.length=r,this._resolve(i)},l.prototype.preservedValues=function(){return this._preservedValues},e.prototype.map=function(e,n){return h(this,e,n,null)},e.map=function(e,n,t,i){return h(e,n,t,i)}}))(E,y,i,m,f,U),function(){if(Ii)return Oi;Ii=1;var e=Object.create;if(e){var n=e(null),t=e(null);n[" size"]=t[" size"]=0}return Oi=function(e){var i,r,a=Jt(),o=a.canEvaluate,c=a.isIdentifier,s=function(e){return new Function("ensureMethod","                                    \n\t        return function(obj) {                                               \n\t            'use strict'                                                     \n\t            var len = this.length;                                           \n\t            ensureMethod(obj, 'methodName');                                 \n\t            switch(len) {                                                    \n\t                case 1: return obj.methodName(this[0]);                      \n\t                case 2: return obj.methodName(this[0], this[1]);             \n\t                case 3: return obj.methodName(this[0], this[1], this[2]);    \n\t                case 0: return obj.methodName();                             \n\t                default:                                                     \n\t                    return obj.methodName.apply(obj, this);                  \n\t            }                                                                \n\t        };                                                                   \n\t        ".replace(/methodName/g,e))(l)},d=function(e){return new Function("obj","                                             \n\t        'use strict';                                                        \n\t        return obj.propertyName;                                             \n\t        ".replace("propertyName",e))},u=function(e,n,t){var i=t[e];if("function"!=typeof i){if(!c(e))return null;if(i=n(e),t[e]=i,t[" size"]++,t[" size"]>512){for(var r=Object.keys(t),a=0;a<256;++a)delete t[r[a]];t[" size"]=r.length-256}}return i};function l(n,t){var i;if(null!=n&&(i=n[t]),"function"!=typeof i){var r="Object "+a.classString(n)+" has no method '"+a.toString(t)+"'";throw new e.TypeError(r)}return i}function h(e){return l(e,this.pop()).apply(e,this)}function p(e){return e[this]}function f(e){var n=+this;return n<0&&(n=Math.max(0,n+e.length)),e[n]}i=function(e){return u(e,s,n)},r=function(e){return u(e,d,t)},e.prototype.call=function(e){for(var n=arguments.length,t=new Array(Math.max(n-1,0)),r=1;r<n;++r)t[r-1]=arguments[r];if(o){var a=i(e);if(null!==a)return this._then(a,void 0,void 0,t,void 0)}return t.push(e),this._then(h,void 0,void 0,t,void 0)},e.prototype.get=function(e){var n;if("number"==typeof e)n=f;else if(o){var t=r(e);n=null!==t?t:p}else n=p;return this._then(n,void 0,void 0,e,void 0)}},Oi}()(E),yr()(E,i,m,D,f,U),xr()(E,f,U),Dr()(E,i,f,m,r,U),(Hi?Vi:(Hi=1,Vi=function(e){var n=Jt(),t=e._async,i=n.tryCatch,r=n.errorObj;function a(e,a){if(!n.isArray(e))return o.call(this,e,a);var c=i(a).apply(this._boundValue(),[null].concat(e));c===r&&t.throwLater(c.e)}function o(e,n){var a=this._boundValue(),o=void 0===e?i(n).call(a,null):i(n).call(a,null,e);o===r&&t.throwLater(o.e)}function c(e,n){if(!e){var a=new Error(e+"");a.cause=e,e=a}var o=i(n).call(this._boundValue(),e);o===r&&t.throwLater(o.e)}e.prototype.asCallback=e.prototype.nodeify=function(e,n){if("function"==typeof e){var t=o;void 0!==n&&Object(n).spread&&(t=a),this._then(t,c,void 0,this,e)}return this}}))(E),Ur()(E,f),vr()(E,y,m,i),Tr()(E,f,m,i),(Zi?Yi:(Zi=1,Yi=function(e,n,t,i,r,a){var o=e._getDomain,c=Jt(),s=c.tryCatch;function d(n,t,i,a){this.constructor$(n);var s=o();this._fn=null===s?t:c.domainBind(s,t),void 0!==i&&(i=e.resolve(i))._attachCancellationCallback(this),this._initialValue=i,this._currentCancellable=null,this._eachValues=a===r?Array(this._length):0===a?null:void 0,this._promise._captureStackTrace(),this._init$(void 0,-5)}function u(e,n){this.isFulfilled()?n._resolve(e):n._reject(e)}function l(e,n,i,r){return"function"!=typeof n?t("expecting a function but got "+c.classString(n)):new d(e,n,i,r).promise()}function h(n){this.accum=n,this.array._gotAccum(n);var t=i(this.value,this.array._promise);return t instanceof e?(this.array._currentCancellable=t,t._then(p,void 0,void 0,this,void 0)):p.call(this,t)}function p(n){var t,i=this.array,r=i._promise,o=s(i._fn);r._pushContext(),(t=void 0!==i._eachValues?o.call(r._boundValue(),n,this.index,this.length):o.call(r._boundValue(),this.accum,n,this.index,this.length))instanceof e&&(i._currentCancellable=t);var c=r._popContext();return a.checkForgottenReturns(t,c,void 0!==i._eachValues?"Promise.each":"Promise.reduce",r),t}c.inherits(d,n),d.prototype._gotAccum=function(e){void 0!==this._eachValues&&null!==this._eachValues&&e!==r&&this._eachValues.push(e)},d.prototype._eachComplete=function(e){return null!==this._eachValues&&this._eachValues.push(e),this._eachValues},d.prototype._init=function(){},d.prototype._resolveEmptyArray=function(){this._resolve(void 0!==this._eachValues?this._eachValues:this._initialValue)},d.prototype.shouldCopyValues=function(){return!1},d.prototype._resolve=function(e){this._promise._resolveCallback(e),this._values=null},d.prototype._resultCancelled=function(n){if(n===this._initialValue)return this._cancel();this._isResolved()||(this._resultCancelled$(),this._currentCancellable instanceof e&&this._currentCancellable.cancel(),this._initialValue instanceof e&&this._initialValue.cancel())},d.prototype._iterate=function(n){var t,i;this._values=n;var r=n.length;if(void 0!==this._initialValue?(t=this._initialValue,i=0):(t=e.resolve(n[0]),i=1),this._currentCancellable=t,!t.isRejected())for(;i<r;++i){var a={accum:null,value:n[i],index:i,length:r,array:this};t=t._then(h,void 0,void 0,a,void 0)}void 0!==this._eachValues&&(t=t._then(this._eachComplete,void 0,void 0,this,void 0)),t._then(u,u,void 0,t,this)},e.prototype.reduce=function(e,n){return l(this,e,n,null)},e.reduce=function(e,n,t,i){return l(e,n,t,i)}}))(E,y,i,m,f,U),(er?Ji:(er=1,Ji=function(e,n,t){var i=e.PromiseInspection;function r(e){this.constructor$(e)}Jt().inherits(r,n),r.prototype._promiseResolved=function(e,n){return this._values[e]=n,++this._totalResolved>=this._length&&(this._resolve(this._values),!0)},r.prototype._promiseFulfilled=function(e,n){var t=new i;return t._bitField=33554432,t._settledValueField=e,this._promiseResolved(n,t)},r.prototype._promiseRejected=function(e,n){var t=new i;return t._bitField=16777216,t._settledValueField=e,this._promiseResolved(n,t)},e.settle=function(e){return t.deprecated(".settle()",".reflect()"),new r(e).promise()},e.prototype.settle=function(){return e.settle(this)}}))(E,y,U),(tr?nr:(tr=1,nr=function(e,n,t){var i=Jt(),r=fr().RangeError,a=fr().AggregateError,o=i.isArray,c={};function s(e){this.constructor$(e),this._howMany=0,this._unwrap=!1,this._initialized=!1}function d(e,n){if((0|n)!==n||n<0)return t("expecting a positive integer\n\n    See http://goo.gl/MqrFmX\n");var i=new s(e),r=i.promise();return i.setHowMany(n),i.init(),r}i.inherits(s,n),s.prototype._init=function(){if(this._initialized)if(0!==this._howMany){this._init$(void 0,-5);var e=o(this._values);!this._isResolved()&&e&&this._howMany>this._canPossiblyFulfill()&&this._reject(this._getRangeError(this.length()))}else this._resolve([])},s.prototype.init=function(){this._initialized=!0,this._init()},s.prototype.setUnwrap=function(){this._unwrap=!0},s.prototype.howMany=function(){return this._howMany},s.prototype.setHowMany=function(e){this._howMany=e},s.prototype._promiseFulfilled=function(e){return this._addFulfilled(e),this._fulfilled()===this.howMany()&&(this._values.length=this.howMany(),1===this.howMany()&&this._unwrap?this._resolve(this._values[0]):this._resolve(this._values),!0)},s.prototype._promiseRejected=function(e){return this._addRejected(e),this._checkOutcome()},s.prototype._promiseCancelled=function(){return this._values instanceof e||null==this._values?this._cancel():(this._addRejected(c),this._checkOutcome())},s.prototype._checkOutcome=function(){if(this.howMany()>this._canPossiblyFulfill()){for(var e=new a,n=this.length();n<this._values.length;++n)this._values[n]!==c&&e.push(this._values[n]);return e.length>0?this._reject(e):this._cancel(),!0}return!1},s.prototype._fulfilled=function(){return this._totalResolved},s.prototype._rejected=function(){return this._values.length-this.length()},s.prototype._addRejected=function(e){this._values.push(e)},s.prototype._addFulfilled=function(e){this._values[this._totalResolved++]=e},s.prototype._canPossiblyFulfill=function(){return this.length()-this._rejected()},s.prototype._getRangeError=function(e){var n="Input array must contain at least "+this._howMany+" items but contains only "+e+" items";return new r(n)},s.prototype._resolveEmptyArray=function(){this._reject(this._getRangeError(0))},e.some=function(e,n){return d(e,n)},e.prototype.some=function(e){return d(this,e)},e._SomePromiseArray=s}))(E,y,i),(rr?ir:(rr=1,ir=function(e,n){var t=e.map;e.prototype.filter=function(e,i){return t(this,e,i,n)},e.filter=function(e,i,r){return t(e,i,r,n)}}))(E,f),(or?ar:(or=1,ar=function(e,n){var t=e.reduce,i=e.all;function r(){return i(this)}e.prototype.each=function(e){return t(this,e,n,0)._then(r,void 0,void 0,this,void 0)},e.prototype.mapSeries=function(e){return t(this,e,n,n)},e.each=function(e,i){return t(e,i,n,0)._then(r,void 0,void 0,e,void 0)},e.mapSeries=function(e,i){return t(e,i,n,n)}}))(E,f),(sr?cr:(sr=1,cr=function(e){var n=e._SomePromiseArray;function t(e){var t=new n(e),i=t.promise();return t.setHowMany(1),t.setUnwrap(),t.init(),i}e.any=function(e){return t(e)},e.prototype.any=function(){return t(this)}}))(E),c.toFastProperties(E),c.toFastProperties(E.prototype),A({a:1}),A({b:2}),A({c:3}),A(1),A(function(){}),A(void 0),A(!1),A(new E(f)),U.setBounds(d.firstLineError,c.lastLineError),E}),Qt.exports;var e}function wr(){if(ur)return Kt;ur=1;var e=Ht,n=_r()();return Kt.defer=function(){var e,t,i=new n.Promise(function(n,i){e=n,t=i});return{resolve:e,reject:t,promise:i}},Kt.when=n.resolve,Kt.resolve=n.resolve,Kt.all=n.all,Kt.props=n.props,Kt.reject=n.reject,Kt.promisify=n.promisify,Kt.mapSeries=n.mapSeries,Kt.attempt=n.attempt,Kt.nfcall=function(e){var t=Array.prototype.slice.call(arguments,1);return n.promisify(e).apply(null,t)},n.prototype.fail=n.prototype.caught,n.prototype.also=function(t){return this.then(function(i){var r=e.extend({},i,t(i));return n.props(r)})},Kt}var Fr,Er={};function Wr(){if(Fr)return Er;Fr=1;var e=Ht,n=Er.types={document:"document",paragraph:"paragraph",run:"run",text:"text",tab:"tab",checkbox:"checkbox",hyperlink:"hyperlink",noteReference:"noteReference",image:"image",note:"note",commentReference:"commentReference",comment:"comment",table:"table",tableRow:"tableRow",tableCell:"tableCell",break:"break",bookmarkStart:"bookmarkStart"};var t={baseline:"baseline",superscript:"superscript",subscript:"subscript"};function i(n){this._notes=e.indexBy(n,function(e){return r(e.noteType,e.noteId)})}function r(e,n){return e+"-"+n}function a(e){return{type:n.break,breakType:e}}return i.prototype.resolve=function(e){return this.findNoteByKey(r(e.noteType,e.noteId))},i.prototype.findNoteByKey=function(e){return this._notes[e]||null},Er.document=Er.Document=function(e,t){return{type:n.document,children:e,notes:(t=t||{}).notes||new i({}),comments:t.comments||[]}},Er.paragraph=Er.Paragraph=function(e,t){var i=(t=t||{}).indent||{};return{type:n.paragraph,children:e,styleId:t.styleId||null,styleName:t.styleName||null,numbering:t.numbering||null,alignment:t.alignment||null,indent:{start:i.start||null,end:i.end||null,firstLine:i.firstLine||null,hanging:i.hanging||null}}},Er.run=Er.Run=function(e,i){return{type:n.run,children:e,styleId:(i=i||{}).styleId||null,styleName:i.styleName||null,isBold:!!i.isBold,isUnderline:!!i.isUnderline,isItalic:!!i.isItalic,isStrikethrough:!!i.isStrikethrough,isAllCaps:!!i.isAllCaps,isSmallCaps:!!i.isSmallCaps,verticalAlignment:i.verticalAlignment||t.baseline,font:i.font||null,fontSize:i.fontSize||null,highlight:i.highlight||null}},Er.text=Er.Text=function(e){return{type:n.text,value:e}},Er.tab=Er.Tab=function(){return{type:n.tab}},Er.checkbox=Er.Checkbox=function(e){return{type:n.checkbox,checked:e.checked}},Er.Hyperlink=function(e,t){return{type:n.hyperlink,children:e,href:t.href,anchor:t.anchor,targetFrame:t.targetFrame}},Er.noteReference=Er.NoteReference=function(e){return{type:n.noteReference,noteType:e.noteType,noteId:e.noteId}},Er.Notes=i,Er.Note=function(e){return{type:n.note,noteType:e.noteType,noteId:e.noteId,body:e.body}},Er.commentReference=function(e){return{type:n.commentReference,commentId:e.commentId}},Er.comment=function(e){return{type:n.comment,commentId:e.commentId,body:e.body,authorName:e.authorName,authorInitials:e.authorInitials}},Er.Image=function(e){return{type:n.image,read:function(n){return n?e.readImage(n):e.readImage().then(function(e){return Buffer.from(e)})},readAsArrayBuffer:function(){return e.readImage()},readAsBase64String:function(){return e.readImage("base64")},readAsBuffer:function(){return e.readImage().then(function(e){return Buffer.from(e)})},altText:e.altText,contentType:e.contentType}},Er.Table=function(e,t){return{type:n.table,children:e,styleId:(t=t||{}).styleId||null,styleName:t.styleName||null}},Er.TableRow=function(e,t){return{type:n.tableRow,children:e,isHeader:(t=t||{}).isHeader||!1}},Er.TableCell=function(e,t){return{type:n.tableCell,children:e,colSpan:null==(t=t||{}).colSpan?1:t.colSpan,rowSpan:null==t.rowSpan?1:t.rowSpan}},Er.lineBreak=a("line"),Er.pageBreak=a("page"),Er.columnBreak=a("column"),Er.BookmarkStart=function(e){return{type:n.bookmarkStart,name:e.name}},Er.verticalAlignment=t,Er}var Cr,Ar={};function Sr(){if(Cr)return Ar;Cr=1;var e=Ht;function n(e,n){this.value=e,this.messages=n||[]}function t(n){var t=[];return e.flatten(e.pluck(n,"messages"),!0).forEach(function(n){(function(n,t){return void 0!==e.find(n,i.bind(null,t))})(t,n)||t.push(n)}),t}function i(e,n){return e.type===n.type&&e.message===n.message}return Ar.Result=n,Ar.success=function(e){return new n(e,[])},Ar.warning=function(e){return{type:"warning",message:e}},Ar.error=function(e){return{type:"error",message:e.message,error:e}},n.prototype.map=function(e){return new n(e(this.value),this.messages)},n.prototype.flatMap=function(e){var i=e(this.value);return new n(i.value,t([this,i]))},n.prototype.flatMapThen=function(e){var i=this;return e(this.value).then(function(e){return new n(e.value,t([i,e]))})},n.combine=function(i){return new n(e.flatten(e.pluck(i,"value")),t(i))},Ar}var Nr,Br,kr={},Or={};function Ir(){if(Br)return kr;Br=1;var e=function(){if(Nr)return Or;Nr=1,Or.byteLength=function(e){var n=a(e),t=n[0],i=n[1];return 3*(t+i)/4-i},Or.toByteArray=function(e){var i,r,o=a(e),c=o[0],s=o[1],d=new t(function(e,n,t){return 3*(n+t)/4-t}(0,c,s)),u=0,l=s>0?c-4:c;for(r=0;r<l;r+=4)i=n[e.charCodeAt(r)]<<18|n[e.charCodeAt(r+1)]<<12|n[e.charCodeAt(r+2)]<<6|n[e.charCodeAt(r+3)],d[u++]=i>>16&255,d[u++]=i>>8&255,d[u++]=255&i;return 2===s&&(i=n[e.charCodeAt(r)]<<2|n[e.charCodeAt(r+1)]>>4,d[u++]=255&i),1===s&&(i=n[e.charCodeAt(r)]<<10|n[e.charCodeAt(r+1)]<<4|n[e.charCodeAt(r+2)]>>2,d[u++]=i>>8&255,d[u++]=255&i),d},Or.fromByteArray=function(n){for(var t,i=n.length,r=i%3,a=[],o=16383,s=0,d=i-r;s<d;s+=o)a.push(c(n,s,s+o>d?d:s+o));return 1===r?(t=n[i-1],a.push(e[t>>2]+e[t<<4&63]+"==")):2===r&&(t=(n[i-2]<<8)+n[i-1],a.push(e[t>>10]+e[t>>4&63]+e[t<<2&63]+"=")),a.join("")};for(var e=[],n=[],t="undefined"!=typeof Uint8Array?Uint8Array:Array,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",r=0;r<64;++r)e[r]=i[r],n[i.charCodeAt(r)]=r;function a(e){var n=e.length;if(n%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var t=e.indexOf("=");return-1===t&&(t=n),[t,t===n?0:4-t%4]}function o(n){return e[n>>18&63]+e[n>>12&63]+e[n>>6&63]+e[63&n]}function c(e,n,t){for(var i,r=[],a=n;a<t;a+=3)i=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(255&e[a+2]),r.push(o(i));return r.join("")}return n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63,Or}(),n=r();return kr.openArrayBuffer=function(t){return n.loadAsync(t).then(function(n){return{exists:function(e){return null!==n.file(e)},read:function(t,i){return n.file(t).async("uint8array").then(function(n){return"base64"===i?e.fromByteArray(n):i?new TextDecoder(i).decode(n):n})},write:function(e,t){n.file(e,t)},toArrayBuffer:function(){return n.generateAsync({type:"arraybuffer"})}}})},kr.splitPath=function(e){var n=e.lastIndexOf("/");return-1===n?{dirname:"",basename:e}:{dirname:e.substring(0,n),basename:e.substring(n+1)}},kr.joinPath=function(){var e=Array.prototype.filter.call(arguments,function(e){return e}),n=[];return e.forEach(function(e){/^\//.test(e)?n=[e]:n.push(e)}),n.join("/")},kr}var Rr,jr={},Pr={},Lr={};function qr(){if(Rr)return Lr;Rr=1;var e=Ht;Lr.Element=t,Lr.element=function(e,n,i){return new t(e,n,i)},Lr.text=function(e){return{type:"text",value:e}};var n=Lr.emptyElement={first:function(){return null},firstOrEmpty:function(){return n},attributes:{},children:[]};function t(e,n,t){this.type="element",this.name=e,this.attributes=n||{},this.children=t||[]}t.prototype.first=function(n){return e.find(this.children,function(e){return e.name===n})},t.prototype.firstOrEmpty=function(e){return this.first(e)||n},t.prototype.getElementsByTagName=function(n){return r(e.filter(this.children,function(e){return e.name===n}))},t.prototype.text=function(){if(0===this.children.length)return"";if(1!==this.children.length||"text"!==this.children[0].type)throw new Error("Not implemented");return this.children[0].value};var i={getElementsByTagName:function(n){return r(e.flatten(this.map(function(e){return e.getElementsByTagName(n)},!0)))}};function r(n){return e.extend(n,i)}return Lr}var Mr,Vr,Hr={},zr={},Gr={},Xr={},$r={};function Kr(){if(Mr)return $r;function e(e,n){return void 0===n&&(n=Object),n&&"function"==typeof n.freeze?n.freeze(e):e}Mr=1;var n=e({HTML:"text/html",isHTML:function(e){return e===n.HTML},XML_APPLICATION:"application/xml",XML_TEXT:"text/xml",XML_XHTML_APPLICATION:"application/xhtml+xml",XML_SVG_IMAGE:"image/svg+xml"}),t=e({HTML:"http://www.w3.org/1999/xhtml",isHTML:function(e){return e===t.HTML},SVG:"http://www.w3.org/2000/svg",XML:"http://www.w3.org/XML/1998/namespace",XMLNS:"http://www.w3.org/2000/xmlns/"});return $r.assign=function(e,n){if(null===e||"object"!=typeof e)throw new TypeError("target is not an object");for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&(e[t]=n[t]);return e},$r.find=function(e,n,t){if(void 0===t&&(t=Array.prototype),e&&"function"==typeof t.find)return t.find.call(e,n);for(var i=0;i<e.length;i++)if(Object.prototype.hasOwnProperty.call(e,i)){var r=e[i];if(n.call(void 0,r,i,e))return r}},$r.freeze=e,$r.MIME_TYPE=n,$r.NAMESPACE=t,$r}function Qr(){if(Vr)return Xr;Vr=1;var e=Kr(),n=e.find,t=e.NAMESPACE;function i(e){return""!==e}function r(e,n){return e.hasOwnProperty(n)||(e[n]=!0),e}function a(e){if(!e)return[];var n=function(e){return e?e.split(/[\t\n\f\r ]+/).filter(i):[]}(e);return Object.keys(n.reduce(r,{}))}function o(e,n){for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[t]=e[t])}function c(e,n){var t=e.prototype;if(!(t instanceof n)){let i=function(){};i.prototype=n.prototype,i=new i,o(t,i),e.prototype=t=i}t.constructor!=e&&("function"!=typeof e&&console.error("unknown Class:"+e),t.constructor=e)}var s={},d=s.ELEMENT_NODE=1,u=s.ATTRIBUTE_NODE=2,l=s.TEXT_NODE=3,h=s.CDATA_SECTION_NODE=4,p=s.ENTITY_REFERENCE_NODE=5,f=s.ENTITY_NODE=6,g=s.PROCESSING_INSTRUCTION_NODE=7,b=s.COMMENT_NODE=8,m=s.DOCUMENT_NODE=9,y=s.DOCUMENT_TYPE_NODE=10,x=s.DOCUMENT_FRAGMENT_NODE=11,D=s.NOTATION_NODE=12,U={},v={};U.INDEX_SIZE_ERR=(v[1]="Index size error",1),U.DOMSTRING_SIZE_ERR=(v[2]="DOMString size error",2);var T=U.HIERARCHY_REQUEST_ERR=(v[3]="Hierarchy request error",3);U.WRONG_DOCUMENT_ERR=(v[4]="Wrong document",4),U.INVALID_CHARACTER_ERR=(v[5]="Invalid character",5),U.NO_DATA_ALLOWED_ERR=(v[6]="No data allowed",6),U.NO_MODIFICATION_ALLOWED_ERR=(v[7]="No modification allowed",7);var _=U.NOT_FOUND_ERR=(v[8]="Not found",8);U.NOT_SUPPORTED_ERR=(v[9]="Not supported",9);var w=U.INUSE_ATTRIBUTE_ERR=(v[10]="Attribute in use",10);function F(e,n){if(n instanceof Error)var t=n;else t=this,Error.call(this,v[e]),this.message=v[e],Error.captureStackTrace&&Error.captureStackTrace(this,F);return t.code=e,n&&(this.message=this.message+": "+n),t}function E(){}function W(e,n){this._node=e,this._refresh=n,C(this)}function C(e){var n=e._node._inc||e._node.ownerDocument._inc;if(e._inc!==n){var t=e._refresh(e._node);if(be(e,"length",t.length),!e.$$length||t.length<e.$$length)for(var i=t.length;i in e;i++)Object.prototype.hasOwnProperty.call(e,i)&&delete e[i];o(t,e),e._inc=n}}function A(){}function S(e,n){for(var t=e.length;t--;)if(e[t]===n)return t}function N(e,n,i,r){if(r?n[S(n,r)]=i:n[n.length++]=i,e){i.ownerElement=e;var a=e.ownerDocument;a&&(r&&P(a,e,r),function(e,n,i){e&&e._inc++;var r=i.namespaceURI;r===t.XMLNS&&(n._nsMap[i.prefix?i.localName:""]=i.value)}(a,e,i))}}function B(e,n,t){var i=S(n,t);if(!(i>=0))throw new F(_,new Error(e.tagName+"@"+t));for(var r=n.length-1;i<r;)n[i]=n[++i];if(n.length=r,e){var a=e.ownerDocument;a&&(P(a,e,t),t.ownerElement=null)}}function k(){}function O(){}function I(e){return("<"==e?"&lt;":">"==e&&"&gt;")||"&"==e&&"&amp;"||'"'==e&&"&quot;"||"&#"+e.charCodeAt()+";"}function R(e,n){if(n(e))return!0;if(e=e.firstChild)do{if(R(e,n))return!0}while(e=e.nextSibling)}function j(){this.ownerDocument=this}function P(e,n,i,r){e&&e._inc++,i.namespaceURI===t.XMLNS&&delete n._nsMap[i.prefix?i.localName:""]}function L(e,n,t){if(e&&e._inc){e._inc++;var i=n.childNodes;if(t)i[i.length++]=t;else{for(var r=n.firstChild,a=0;r;)i[a++]=r,r=r.nextSibling;i.length=a,delete i[i.length]}}}function q(e,n){var t=n.previousSibling,i=n.nextSibling;return t?t.nextSibling=i:e.firstChild=i,i?i.previousSibling=t:e.lastChild=t,n.parentNode=null,n.previousSibling=null,n.nextSibling=null,L(e.ownerDocument,e),n}function M(e){return e&&e.nodeType===O.DOCUMENT_TYPE_NODE}function V(e){return e&&e.nodeType===O.ELEMENT_NODE}function H(e){return e&&e.nodeType===O.TEXT_NODE}function z(e,t){var i=e.childNodes||[];if(n(i,V)||M(t))return!1;var r=n(i,M);return!(t&&r&&i.indexOf(r)>i.indexOf(t))}function G(e,t){var i=e.childNodes||[];if(n(i,function(e){return V(e)&&e!==t}))return!1;var r=n(i,M);return!(t&&r&&i.indexOf(r)>i.indexOf(t))}function X(e,n,t){if(!function(e){return e&&(e.nodeType===O.DOCUMENT_NODE||e.nodeType===O.DOCUMENT_FRAGMENT_NODE||e.nodeType===O.ELEMENT_NODE)}(e))throw new F(T,"Unexpected parent node type "+e.nodeType);if(t&&t.parentNode!==e)throw new F(_,"child not in parent");if(!function(e){return e&&(V(e)||H(e)||M(e)||e.nodeType===O.DOCUMENT_FRAGMENT_NODE||e.nodeType===O.COMMENT_NODE||e.nodeType===O.PROCESSING_INSTRUCTION_NODE)}(n)||M(n)&&e.nodeType!==O.DOCUMENT_NODE)throw new F(T,"Unexpected node type "+n.nodeType+" for parent node type "+e.nodeType)}function $(e,t,i){var r=e.childNodes||[],a=t.childNodes||[];if(t.nodeType===O.DOCUMENT_FRAGMENT_NODE){var o=a.filter(V);if(o.length>1||n(a,H))throw new F(T,"More than one element or text in fragment");if(1===o.length&&!z(e,i))throw new F(T,"Element in fragment can not be inserted before doctype")}if(V(t)&&!z(e,i))throw new F(T,"Only one element can be added and only after doctype");if(M(t)){if(n(r,M))throw new F(T,"Only one doctype is allowed");var c=n(r,V);if(i&&r.indexOf(c)<r.indexOf(i))throw new F(T,"Doctype can only be inserted before an element");if(!i&&c)throw new F(T,"Doctype can not be appended since element is present")}}function K(e,t,i){var r=e.childNodes||[],a=t.childNodes||[];if(t.nodeType===O.DOCUMENT_FRAGMENT_NODE){var o=a.filter(V);if(o.length>1||n(a,H))throw new F(T,"More than one element or text in fragment");if(1===o.length&&!G(e,i))throw new F(T,"Element in fragment can not be inserted before doctype")}if(V(t)&&!G(e,i))throw new F(T,"Only one element can be added and only after doctype");if(M(t)){if(n(r,function(e){return M(e)&&e!==i}))throw new F(T,"Only one doctype is allowed");var c=n(r,V);if(i&&r.indexOf(c)<r.indexOf(i))throw new F(T,"Doctype can only be inserted before an element")}}function Q(e,n,t,i){X(e,n,t),e.nodeType===O.DOCUMENT_NODE&&(i||$)(e,n,t);var r=n.parentNode;if(r&&r.removeChild(n),n.nodeType===x){var a=n.firstChild;if(null==a)return n;var o=n.lastChild}else a=o=n;var c=t?t.previousSibling:e.lastChild;a.previousSibling=c,o.nextSibling=t,c?c.nextSibling=a:e.firstChild=a,null==t?e.lastChild=o:t.previousSibling=o;do{a.parentNode=e}while(a!==o&&(a=a.nextSibling));return L(e.ownerDocument||e,e),n.nodeType==x&&(n.firstChild=n.lastChild=null),n}function Y(){this._nsMap={}}function Z(){}function J(){}function ee(){}function ne(){}function te(){}function ie(){}function re(){}function ae(){}function oe(){}function ce(){}function se(){}function de(){}function ue(e,n){var t=[],i=9==this.nodeType&&this.documentElement||this,r=i.prefix,a=i.namespaceURI;if(a&&null==r&&null==(r=i.lookupPrefix(a)))var o=[{namespace:a,prefix:null}];return pe(this,t,e,n,o),t.join("")}function le(e,n,i){var r=e.prefix||"",a=e.namespaceURI;if(!a)return!1;if("xml"===r&&a===t.XML||a===t.XMLNS)return!1;for(var o=i.length;o--;){var c=i[o];if(c.prefix===r)return c.namespace!==a}return!0}function he(e,n,t){e.push(" ",n,'="',t.replace(/[<>&"\t\n\r]/g,I),'"')}function pe(e,n,i,r,a){if(a||(a=[]),r){if(!(e=r(e)))return;if("string"==typeof e)return void n.push(e)}switch(e.nodeType){case d:var o=e.attributes,c=o.length,s=e.firstChild,f=e.tagName,D=f;if(!(i=t.isHTML(e.namespaceURI)||i)&&!e.prefix&&e.namespaceURI){for(var U,v=0;v<o.length;v++)if("xmlns"===o.item(v).name){U=o.item(v).value;break}if(!U)for(var T=a.length-1;T>=0;T--){if(""===(_=a[T]).prefix&&_.namespace===e.namespaceURI){U=_.namespace;break}}if(U!==e.namespaceURI)for(T=a.length-1;T>=0;T--){var _;if((_=a[T]).namespace===e.namespaceURI){_.prefix&&(D=_.prefix+":"+f);break}}}n.push("<",D);for(var w=0;w<c;w++){"xmlns"==(F=o.item(w)).prefix?a.push({prefix:F.localName,namespace:F.value}):"xmlns"==F.nodeName&&a.push({prefix:"",namespace:F.value})}for(w=0;w<c;w++){var F,E,W;if(le(F=o.item(w),0,a))he(n,(E=F.prefix||"")?"xmlns:"+E:"xmlns",W=F.namespaceURI),a.push({prefix:E,namespace:W});pe(F,n,i,r,a)}if(f===D&&le(e,0,a))he(n,(E=e.prefix||"")?"xmlns:"+E:"xmlns",W=e.namespaceURI),a.push({prefix:E,namespace:W});if(s||i&&!/^(?:meta|link|img|br|hr|input)$/i.test(f)){if(n.push(">"),i&&/^script$/i.test(f))for(;s;)s.data?n.push(s.data):pe(s,n,i,r,a.slice()),s=s.nextSibling;else for(;s;)pe(s,n,i,r,a.slice()),s=s.nextSibling;n.push("</",D,">")}else n.push("/>");return;case m:case x:for(s=e.firstChild;s;)pe(s,n,i,r,a.slice()),s=s.nextSibling;return;case u:return he(n,e.name,e.value);case l:return n.push(e.data.replace(/[<&>]/g,I));case h:return n.push("<![CDATA[",e.data,"]]>");case b:return n.push("\x3c!--",e.data,"--\x3e");case y:var C=e.publicId,A=e.systemId;if(n.push("<!DOCTYPE ",e.name),C)n.push(" PUBLIC ",C),A&&"."!=A&&n.push(" ",A),n.push(">");else if(A&&"."!=A)n.push(" SYSTEM ",A,">");else{var S=e.internalSubset;S&&n.push(" [",S,"]"),n.push(">")}return;case g:return n.push("<?",e.target," ",e.data,"?>");case p:return n.push("&",e.nodeName,";");default:n.push("??",e.nodeName)}}function fe(e,n,t){var i;switch(n.nodeType){case d:(i=n.cloneNode(!1)).ownerDocument=e;case x:break;case u:t=!0}if(i||(i=n.cloneNode(!1)),i.ownerDocument=e,i.parentNode=null,t)for(var r=n.firstChild;r;)i.appendChild(fe(e,r,t)),r=r.nextSibling;return i}function ge(e,n,t){var i=new n.constructor;for(var r in n)if(Object.prototype.hasOwnProperty.call(n,r)){var a=n[r];"object"!=typeof a&&a!=i[r]&&(i[r]=a)}switch(n.childNodes&&(i.childNodes=new E),i.ownerDocument=e,i.nodeType){case d:var o=n.attributes,c=i.attributes=new A,s=o.length;c._ownerElement=i;for(var l=0;l<s;l++)i.setAttributeNode(ge(e,o.item(l),!0));break;case u:t=!0}if(t)for(var h=n.firstChild;h;)i.appendChild(ge(e,h,t)),h=h.nextSibling;return i}function be(e,n,t){e[n]=t}U.INVALID_STATE_ERR=(v[11]="Invalid state",11),U.SYNTAX_ERR=(v[12]="Syntax error",12),U.INVALID_MODIFICATION_ERR=(v[13]="Invalid modification",13),U.NAMESPACE_ERR=(v[14]="Invalid namespace",14),U.INVALID_ACCESS_ERR=(v[15]="Invalid access",15),F.prototype=Error.prototype,o(U,F),E.prototype={length:0,item:function(e){return e>=0&&e<this.length?this[e]:null},toString:function(e,n){for(var t=[],i=0;i<this.length;i++)pe(this[i],t,e,n);return t.join("")},filter:function(e){return Array.prototype.filter.call(this,e)},indexOf:function(e){return Array.prototype.indexOf.call(this,e)}},W.prototype.item=function(e){return C(this),this[e]||null},c(W,E),A.prototype={length:0,item:E.prototype.item,getNamedItem:function(e){for(var n=this.length;n--;){var t=this[n];if(t.nodeName==e)return t}},setNamedItem:function(e){var n=e.ownerElement;if(n&&n!=this._ownerElement)throw new F(w);var t=this.getNamedItem(e.nodeName);return N(this._ownerElement,this,e,t),t},setNamedItemNS:function(e){var n,t=e.ownerElement;if(t&&t!=this._ownerElement)throw new F(w);return n=this.getNamedItemNS(e.namespaceURI,e.localName),N(this._ownerElement,this,e,n),n},removeNamedItem:function(e){var n=this.getNamedItem(e);return B(this._ownerElement,this,n),n},removeNamedItemNS:function(e,n){var t=this.getNamedItemNS(e,n);return B(this._ownerElement,this,t),t},getNamedItemNS:function(e,n){for(var t=this.length;t--;){var i=this[t];if(i.localName==n&&i.namespaceURI==e)return i}return null}},k.prototype={hasFeature:function(e,n){return!0},createDocument:function(e,n,t){var i=new j;if(i.implementation=this,i.childNodes=new E,i.doctype=t||null,t&&i.appendChild(t),n){var r=i.createElementNS(e,n);i.appendChild(r)}return i},createDocumentType:function(e,n,t){var i=new ie;return i.name=e,i.nodeName=e,i.publicId=n||"",i.systemId=t||"",i}},O.prototype={firstChild:null,lastChild:null,previousSibling:null,nextSibling:null,attributes:null,parentNode:null,childNodes:null,ownerDocument:null,nodeValue:null,namespaceURI:null,prefix:null,localName:null,insertBefore:function(e,n){return Q(this,e,n)},replaceChild:function(e,n){Q(this,e,n,K),n&&this.removeChild(n)},removeChild:function(e){return q(this,e)},appendChild:function(e){return this.insertBefore(e,null)},hasChildNodes:function(){return null!=this.firstChild},cloneNode:function(e){return ge(this.ownerDocument||this,this,e)},normalize:function(){for(var e=this.firstChild;e;){var n=e.nextSibling;n&&n.nodeType==l&&e.nodeType==l?(this.removeChild(n),e.appendData(n.data)):(e.normalize(),e=n)}},isSupported:function(e,n){return this.ownerDocument.implementation.hasFeature(e,n)},hasAttributes:function(){return this.attributes.length>0},lookupPrefix:function(e){for(var n=this;n;){var t=n._nsMap;if(t)for(var i in t)if(Object.prototype.hasOwnProperty.call(t,i)&&t[i]===e)return i;n=n.nodeType==u?n.ownerDocument:n.parentNode}return null},lookupNamespaceURI:function(e){for(var n=this;n;){var t=n._nsMap;if(t&&Object.prototype.hasOwnProperty.call(t,e))return t[e];n=n.nodeType==u?n.ownerDocument:n.parentNode}return null},isDefaultNamespace:function(e){return null==this.lookupPrefix(e)}},o(s,O),o(s,O.prototype),j.prototype={nodeName:"#document",nodeType:m,doctype:null,documentElement:null,_inc:1,insertBefore:function(e,n){if(e.nodeType==x){for(var t=e.firstChild;t;){var i=t.nextSibling;this.insertBefore(t,n),t=i}return e}return Q(this,e,n),e.ownerDocument=this,null===this.documentElement&&e.nodeType===d&&(this.documentElement=e),e},removeChild:function(e){return this.documentElement==e&&(this.documentElement=null),q(this,e)},replaceChild:function(e,n){Q(this,e,n,K),e.ownerDocument=this,n&&this.removeChild(n),V(e)&&(this.documentElement=e)},importNode:function(e,n){return fe(this,e,n)},getElementById:function(e){var n=null;return R(this.documentElement,function(t){if(t.nodeType==d&&t.getAttribute("id")==e)return n=t,!0}),n},getElementsByClassName:function(e){var n=a(e);return new W(this,function(t){var i=[];return n.length>0&&R(t.documentElement,function(r){if(r!==t&&r.nodeType===d){var o=r.getAttribute("class");if(o){var c=e===o;if(!c){var s=a(o);c=n.every((u=s,function(e){return u&&-1!==u.indexOf(e)}))}c&&i.push(r)}}var u}),i})},createElement:function(e){var n=new Y;return n.ownerDocument=this,n.nodeName=e,n.tagName=e,n.localName=e,n.childNodes=new E,(n.attributes=new A)._ownerElement=n,n},createDocumentFragment:function(){var e=new ce;return e.ownerDocument=this,e.childNodes=new E,e},createTextNode:function(e){var n=new ee;return n.ownerDocument=this,n.appendData(e),n},createComment:function(e){var n=new ne;return n.ownerDocument=this,n.appendData(e),n},createCDATASection:function(e){var n=new te;return n.ownerDocument=this,n.appendData(e),n},createProcessingInstruction:function(e,n){var t=new se;return t.ownerDocument=this,t.tagName=t.nodeName=t.target=e,t.nodeValue=t.data=n,t},createAttribute:function(e){var n=new Z;return n.ownerDocument=this,n.name=e,n.nodeName=e,n.localName=e,n.specified=!0,n},createEntityReference:function(e){var n=new oe;return n.ownerDocument=this,n.nodeName=e,n},createElementNS:function(e,n){var t=new Y,i=n.split(":"),r=t.attributes=new A;return t.childNodes=new E,t.ownerDocument=this,t.nodeName=n,t.tagName=n,t.namespaceURI=e,2==i.length?(t.prefix=i[0],t.localName=i[1]):t.localName=n,r._ownerElement=t,t},createAttributeNS:function(e,n){var t=new Z,i=n.split(":");return t.ownerDocument=this,t.nodeName=n,t.name=n,t.namespaceURI=e,t.specified=!0,2==i.length?(t.prefix=i[0],t.localName=i[1]):t.localName=n,t}},c(j,O),Y.prototype={nodeType:d,hasAttribute:function(e){return null!=this.getAttributeNode(e)},getAttribute:function(e){var n=this.getAttributeNode(e);return n&&n.value||""},getAttributeNode:function(e){return this.attributes.getNamedItem(e)},setAttribute:function(e,n){var t=this.ownerDocument.createAttribute(e);t.value=t.nodeValue=""+n,this.setAttributeNode(t)},removeAttribute:function(e){var n=this.getAttributeNode(e);n&&this.removeAttributeNode(n)},appendChild:function(e){return e.nodeType===x?this.insertBefore(e,null):function(e,n){return n.parentNode&&n.parentNode.removeChild(n),n.parentNode=e,n.previousSibling=e.lastChild,n.nextSibling=null,n.previousSibling?n.previousSibling.nextSibling=n:e.firstChild=n,e.lastChild=n,L(e.ownerDocument,e,n),n}(this,e)},setAttributeNode:function(e){return this.attributes.setNamedItem(e)},setAttributeNodeNS:function(e){return this.attributes.setNamedItemNS(e)},removeAttributeNode:function(e){return this.attributes.removeNamedItem(e.nodeName)},removeAttributeNS:function(e,n){var t=this.getAttributeNodeNS(e,n);t&&this.removeAttributeNode(t)},hasAttributeNS:function(e,n){return null!=this.getAttributeNodeNS(e,n)},getAttributeNS:function(e,n){var t=this.getAttributeNodeNS(e,n);return t&&t.value||""},setAttributeNS:function(e,n,t){var i=this.ownerDocument.createAttributeNS(e,n);i.value=i.nodeValue=""+t,this.setAttributeNode(i)},getAttributeNodeNS:function(e,n){return this.attributes.getNamedItemNS(e,n)},getElementsByTagName:function(e){return new W(this,function(n){var t=[];return R(n,function(i){i===n||i.nodeType!=d||"*"!==e&&i.tagName!=e||t.push(i)}),t})},getElementsByTagNameNS:function(e,n){return new W(this,function(t){var i=[];return R(t,function(r){r===t||r.nodeType!==d||"*"!==e&&r.namespaceURI!==e||"*"!==n&&r.localName!=n||i.push(r)}),i})}},j.prototype.getElementsByTagName=Y.prototype.getElementsByTagName,j.prototype.getElementsByTagNameNS=Y.prototype.getElementsByTagNameNS,c(Y,O),Z.prototype.nodeType=u,c(Z,O),J.prototype={data:"",substringData:function(e,n){return this.data.substring(e,e+n)},appendData:function(e){e=this.data+e,this.nodeValue=this.data=e,this.length=e.length},insertData:function(e,n){this.replaceData(e,0,n)},appendChild:function(e){throw new Error(v[T])},deleteData:function(e,n){this.replaceData(e,n,"")},replaceData:function(e,n,t){t=this.data.substring(0,e)+t+this.data.substring(e+n),this.nodeValue=this.data=t,this.length=t.length}},c(J,O),ee.prototype={nodeName:"#text",nodeType:l,splitText:function(e){var n=this.data,t=n.substring(e);n=n.substring(0,e),this.data=this.nodeValue=n,this.length=n.length;var i=this.ownerDocument.createTextNode(t);return this.parentNode&&this.parentNode.insertBefore(i,this.nextSibling),i}},c(ee,J),ne.prototype={nodeName:"#comment",nodeType:b},c(ne,J),te.prototype={nodeName:"#cdata-section",nodeType:h},c(te,J),ie.prototype.nodeType=y,c(ie,O),re.prototype.nodeType=D,c(re,O),ae.prototype.nodeType=f,c(ae,O),oe.prototype.nodeType=p,c(oe,O),ce.prototype.nodeName="#document-fragment",ce.prototype.nodeType=x,c(ce,O),se.prototype.nodeType=g,c(se,O),de.prototype.serializeToString=function(e,n,t){return ue.call(e,n,t)},O.prototype.toString=ue;try{if(Object.defineProperty){let e=function(n){switch(n.nodeType){case d:case x:var t=[];for(n=n.firstChild;n;)7!==n.nodeType&&8!==n.nodeType&&t.push(e(n)),n=n.nextSibling;return t.join("");default:return n.nodeValue}};Object.defineProperty(W.prototype,"length",{get:function(){return C(this),this.$$length}}),Object.defineProperty(O.prototype,"textContent",{get:function(){return e(this)},set:function(e){switch(this.nodeType){case d:case x:for(;this.firstChild;)this.removeChild(this.firstChild);(e||String(e))&&this.appendChild(this.ownerDocument.createTextNode(e));break;default:this.data=e,this.value=e,this.nodeValue=e}}}),be=function(e,n,t){e["$$"+n]=t}}}catch(me){}return Xr.DocumentType=ie,Xr.DOMException=F,Xr.DOMImplementation=k,Xr.Element=Y,Xr.Node=O,Xr.NodeList=E,Xr.XMLSerializer=de,Xr}var Yr,Zr={},Jr={};var ea,na,ta,ia,ra,aa={};function oa(){if(ea)return aa;ea=1;var e=Kr().NAMESPACE,n=/[A-Z_a-z\xC0-\xD6\xD8-\xF6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,t=new RegExp("[\\-\\.0-9"+n.source.slice(1,-1)+"\\u00B7\\u0300-\\u036F\\u203F-\\u2040]"),i=new RegExp("^"+n.source+t.source+"*(?::"+n.source+t.source+"*)?$");function r(e,n){this.message=e,this.locator=n,Error.captureStackTrace&&Error.captureStackTrace(this,r)}function a(){}function o(e,n){return n.lineNumber=e.lineNumber,n.columnNumber=e.columnNumber,n}function c(n,t,i,r,a,o){function c(e,n,t){i.attributeNames.hasOwnProperty(e)&&o.fatalError("Attribute "+e+" redefined"),i.addValue(e,n.replace(/[\t\n\r]/g," ").replace(/&#?\w+;/g,a),t)}for(var s,d=++t,u=0;;){var l=n.charAt(d);switch(l){case"=":if(1===u)s=n.slice(t,d),u=3;else{if(2!==u)throw new Error("attribute equal must after attrName");u=3}break;case"'":case'"':if(3===u||1===u){if(1===u&&(o.warning('attribute value must after "="'),s=n.slice(t,d)),t=d+1,!((d=n.indexOf(l,t))>0))throw new Error("attribute value no end '"+l+"' match");c(s,h=n.slice(t,d),t-1),u=5}else{if(4!=u)throw new Error('attribute value must after "="');c(s,h=n.slice(t,d),t),o.warning('attribute "'+s+'" missed start quot('+l+")!!"),t=d+1,u=5}break;case"/":switch(u){case 0:i.setTagName(n.slice(t,d));case 5:case 6:case 7:u=7,i.closed=!0;case 4:case 1:break;case 2:i.closed=!0;break;default:throw new Error("attribute invalid close char('/')")}break;case"":return o.error("unexpected end of input"),0==u&&i.setTagName(n.slice(t,d)),d;case">":switch(u){case 0:i.setTagName(n.slice(t,d));case 5:case 6:case 7:break;case 4:case 1:"/"===(h=n.slice(t,d)).slice(-1)&&(i.closed=!0,h=h.slice(0,-1));case 2:2===u&&(h=s),4==u?(o.warning('attribute "'+h+'" missed quot(")!'),c(s,h,t)):(e.isHTML(r[""])&&h.match(/^(?:disabled|checked|selected)$/i)||o.warning('attribute "'+h+'" missed value!! "'+h+'" instead!!'),c(h,h,t));break;case 3:throw new Error("attribute value missed!!")}return d;case"":l=" ";default:if(l<=" ")switch(u){case 0:i.setTagName(n.slice(t,d)),u=6;break;case 1:s=n.slice(t,d),u=2;break;case 4:var h=n.slice(t,d);o.warning('attribute "'+h+'" missed quot(")!!'),c(s,h,t);case 5:u=6}else switch(u){case 2:i.tagName,e.isHTML(r[""])&&s.match(/^(?:disabled|checked|selected)$/i)||o.warning('attribute "'+s+'" missed value!! "'+s+'" instead2!!'),c(s,s,t),t=d,u=1;break;case 5:o.warning('attribute space is required"'+s+'"!!');case 6:u=1,t=d;break;case 3:u=4,t=d;break;case 7:throw new Error("elements closed character '/' and '>' must be connected to")}}d++}}function s(n,t,i){for(var r=n.tagName,a=null,o=n.length;o--;){var c=n[o],s=c.qName,d=c.value;if((f=s.indexOf(":"))>0)var u=c.prefix=s.slice(0,f),h=s.slice(f+1),p="xmlns"===u&&h;else h=s,u=null,p="xmlns"===s&&"";c.localName=h,!1!==p&&(null==a&&(a={},l(i,i={})),i[p]=a[p]=d,c.uri=e.XMLNS,t.startPrefixMapping(p,d))}for(o=n.length;o--;){(u=(c=n[o]).prefix)&&("xml"===u&&(c.uri=e.XML),"xmlns"!==u&&(c.uri=i[u||""]))}var f;(f=r.indexOf(":"))>0?(u=n.prefix=r.slice(0,f),h=n.localName=r.slice(f+1)):(u=null,h=n.localName=r);var g=n.uri=i[u||""];if(t.startElement(g,h,r,n),!n.closed)return n.currentNSMap=i,n.localNSMap=a,!0;if(t.endElement(g,h,r),a)for(u in a)Object.prototype.hasOwnProperty.call(a,u)&&t.endPrefixMapping(u)}function d(e,n,t,i,r){if(/^(?:script|textarea)$/i.test(t)){var a=e.indexOf("</"+t+">",n),o=e.substring(n+1,a);if(/[&<]/.test(o))return/^script$/i.test(t)?(r.characters(o,0,o.length),a):(o=o.replace(/&#?\w+;/g,i),r.characters(o,0,o.length),a)}return n+1}function u(e,n,t,i){var r=i[t];return null==r&&((r=e.lastIndexOf("</"+t+">"))<n&&(r=e.lastIndexOf("</"+t)),i[t]=r),r<n}function l(e,n){for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[t]=e[t])}function h(e,n,t,i){if("-"===e.charAt(n+2))return"-"===e.charAt(n+3)?(r=e.indexOf("--\x3e",n+4))>n?(t.comment(e,n+4,r-n-4),r+3):(i.error("Unclosed comment"),-1):-1;if("CDATA["==e.substr(n+3,6)){var r=e.indexOf("]]>",n+9);return t.startCDATA(),t.characters(e,n+9,r-n-9),t.endCDATA(),r+3}var a=function(e,n){var t,i=[],r=/'[^']+'|"[^"]+"|[^\s<>\/=]+=?|(\/?\s*>|<)/g;r.lastIndex=n,r.exec(e);for(;t=r.exec(e);)if(i.push(t),t[1])return i}(e,n),o=a.length;if(o>1&&/!doctype/i.test(a[0][0])){var c=a[1][0],s=!1,d=!1;o>3&&(/^public$/i.test(a[2][0])?(s=a[3][0],d=o>4&&a[4][0]):/^system$/i.test(a[2][0])&&(d=a[3][0]));var u=a[o-1];return t.startDTD(c,s,d),t.endDTD(),u.index+u[0].length}return-1}function p(e,n,t){var i=e.indexOf("?>",n);if(i){var r=e.substring(n,i).match(/^<\?(\S*)\s*([\s\S]*?)\s*$/);return r?(r[0].length,t.processingInstruction(r[1],r[2]),i+2):-1}return-1}function f(){this.attributeNames={}}return r.prototype=new Error,r.prototype.name=r.name,a.prototype={parse:function(n,t,i){var a=this.domBuilder;a.startDocument(),l(t,t={}),function(n,t,i,a,l){function g(e){if(e>65535){var n=55296+((e-=65536)>>10),t=56320+(1023&e);return String.fromCharCode(n,t)}return String.fromCharCode(e)}function b(e){var n=e.slice(1,-1);return Object.hasOwnProperty.call(i,n)?i[n]:"#"===n.charAt(0)?g(parseInt(n.substr(1).replace("x","0x"))):(l.error("entity not found:"+e),e)}function m(e){if(e>w){var t=n.substring(w,e).replace(/&#?\w+;/g,b);v&&y(w),a.characters(t,0,e-w),w=e}}function y(e,t){for(;e>=D&&(t=U.exec(n));)x=t.index,D=x+t[0].length,v.lineNumber++;v.columnNumber=e-x+1}var x=0,D=0,U=/.*(?:\r\n?|\n)|.*$/g,v=a.locator,T=[{currentNSMap:t}],_={},w=0;for(;;){try{var F=n.indexOf("<",w);if(F<0){if(!n.substr(w).match(/^\s*$/)){var E=a.doc,W=E.createTextNode(n.substr(w));E.appendChild(W),a.currentElement=W}return}switch(F>w&&m(F),n.charAt(F+1)){case"/":var C=n.indexOf(">",F+3),A=n.substring(F+2,C).replace(/[ \t\n\r]+$/g,""),S=T.pop();C<0?(A=n.substring(F+2).replace(/[\s<].*/,""),l.error("end tag name: "+A+" is not complete:"+S.tagName),C=F+1+A.length):A.match(/\s</)&&(A=A.replace(/[\s<].*/,""),l.error("end tag name: "+A+" maybe not complete"),C=F+1+A.length);var N=S.localNSMap,B=S.tagName==A;if(B||S.tagName&&S.tagName.toLowerCase()==A.toLowerCase()){if(a.endElement(S.uri,S.localName,A),N)for(var k in N)Object.prototype.hasOwnProperty.call(N,k)&&a.endPrefixMapping(k);B||l.fatalError("end tag name: "+A+" is not match the current start tagName:"+S.tagName)}else T.push(S);C++;break;case"?":v&&y(F),C=p(n,F,a);break;case"!":v&&y(F),C=h(n,F,a,l);break;default:v&&y(F);var O=new f,I=T[T.length-1].currentNSMap,R=(C=c(n,F,O,I,b,l),O.length);if(!O.closed&&u(n,C,O.tagName,_)&&(O.closed=!0,i.nbsp||l.warning("unclosed xml attribute")),v&&R){for(var j=o(v,{}),P=0;P<R;P++){var L=O[P];y(L.offset),L.locator=o(v,{})}a.locator=j,s(O,a,I)&&T.push(O),a.locator=v}else s(O,a,I)&&T.push(O);e.isHTML(O.uri)&&!O.closed?C=d(n,C,O.tagName,b,a):C++}}catch(q){if(q instanceof r)throw q;l.error("element parse error: "+q),C=-1}C>w?w=C:m(Math.max(F,w)+1)}}(n,t,i,a,this.errorHandler),a.endDocument()}},f.prototype={setTagName:function(e){if(!i.test(e))throw new Error("invalid tagName:"+e);this.tagName=e},addValue:function(e,n,t){if(!i.test(e))throw new Error("invalid attribute:"+e);this.attributeNames[e]=this.length,this[this.length++]={qName:e,value:n,offset:t}},length:0,getLocalName:function(e){return this[e].localName},getLocator:function(e){return this[e].locator},getQName:function(e){return this[e].qName},getURI:function(e){return this[e].uri},getValue:function(e){return this[e].value}},aa.XMLReader=a,aa.ParseError=r,aa}function ca(){if(na)return Zr;na=1;var e,n,t=Kr(),i=Qr(),r=(Yr||(Yr=1,e=Jr,n=Kr().freeze,e.XML_ENTITIES=n({amp:"&",apos:"'",gt:">",lt:"<",quot:'"'}),e.HTML_ENTITIES=n({Aacute:"Á",aacute:"á",Abreve:"Ă",abreve:"ă",ac:"∾",acd:"∿",acE:"∾̳",Acirc:"Â",acirc:"â",acute:"´",Acy:"А",acy:"а",AElig:"Æ",aelig:"æ",af:"⁡",Afr:"𝔄",afr:"𝔞",Agrave:"À",agrave:"à",alefsym:"ℵ",aleph:"ℵ",Alpha:"Α",alpha:"α",Amacr:"Ā",amacr:"ā",amalg:"⨿",AMP:"&",amp:"&",And:"⩓",and:"∧",andand:"⩕",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",ange:"⦤",angle:"∠",angmsd:"∡",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angst:"Å",angzarr:"⍼",Aogon:"Ą",aogon:"ą",Aopf:"𝔸",aopf:"𝕒",ap:"≈",apacir:"⩯",apE:"⩰",ape:"≊",apid:"≋",apos:"'",ApplyFunction:"⁡",approx:"≈",approxeq:"≊",Aring:"Å",aring:"å",Ascr:"𝒜",ascr:"𝒶",Assign:"≔",ast:"*",asymp:"≈",asympeq:"≍",Atilde:"Ã",atilde:"ã",Auml:"Ä",auml:"ä",awconint:"∳",awint:"⨑",backcong:"≌",backepsilon:"϶",backprime:"‵",backsim:"∽",backsimeq:"⋍",Backslash:"∖",Barv:"⫧",barvee:"⊽",Barwed:"⌆",barwed:"⌅",barwedge:"⌅",bbrk:"⎵",bbrktbrk:"⎶",bcong:"≌",Bcy:"Б",bcy:"б",bdquo:"„",becaus:"∵",Because:"∵",because:"∵",bemptyv:"⦰",bepsi:"϶",bernou:"ℬ",Bernoullis:"ℬ",Beta:"Β",beta:"β",beth:"ℶ",between:"≬",Bfr:"𝔅",bfr:"𝔟",bigcap:"⋂",bigcirc:"◯",bigcup:"⋃",bigodot:"⨀",bigoplus:"⨁",bigotimes:"⨂",bigsqcup:"⨆",bigstar:"★",bigtriangledown:"▽",bigtriangleup:"△",biguplus:"⨄",bigvee:"⋁",bigwedge:"⋀",bkarow:"⤍",blacklozenge:"⧫",blacksquare:"▪",blacktriangle:"▴",blacktriangledown:"▾",blacktriangleleft:"◂",blacktriangleright:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bNot:"⫭",bnot:"⌐",Bopf:"𝔹",bopf:"𝕓",bot:"⊥",bottom:"⊥",bowtie:"⋈",boxbox:"⧉",boxDL:"╗",boxDl:"╖",boxdL:"╕",boxdl:"┐",boxDR:"╔",boxDr:"╓",boxdR:"╒",boxdr:"┌",boxH:"═",boxh:"─",boxHD:"╦",boxHd:"╤",boxhD:"╥",boxhd:"┬",boxHU:"╩",boxHu:"╧",boxhU:"╨",boxhu:"┴",boxminus:"⊟",boxplus:"⊞",boxtimes:"⊠",boxUL:"╝",boxUl:"╜",boxuL:"╛",boxul:"┘",boxUR:"╚",boxUr:"╙",boxuR:"╘",boxur:"└",boxV:"║",boxv:"│",boxVH:"╬",boxVh:"╫",boxvH:"╪",boxvh:"┼",boxVL:"╣",boxVl:"╢",boxvL:"╡",boxvl:"┤",boxVR:"╠",boxVr:"╟",boxvR:"╞",boxvr:"├",bprime:"‵",Breve:"˘",breve:"˘",brvbar:"¦",Bscr:"ℬ",bscr:"𝒷",bsemi:"⁏",bsim:"∽",bsime:"⋍",bsol:"\\",bsolb:"⧅",bsolhsub:"⟈",bull:"•",bullet:"•",bump:"≎",bumpE:"⪮",bumpe:"≏",Bumpeq:"≎",bumpeq:"≏",Cacute:"Ć",cacute:"ć",Cap:"⋒",cap:"∩",capand:"⩄",capbrcup:"⩉",capcap:"⩋",capcup:"⩇",capdot:"⩀",CapitalDifferentialD:"ⅅ",caps:"∩︀",caret:"⁁",caron:"ˇ",Cayleys:"ℭ",ccaps:"⩍",Ccaron:"Č",ccaron:"č",Ccedil:"Ç",ccedil:"ç",Ccirc:"Ĉ",ccirc:"ĉ",Cconint:"∰",ccups:"⩌",ccupssm:"⩐",Cdot:"Ċ",cdot:"ċ",cedil:"¸",Cedilla:"¸",cemptyv:"⦲",cent:"¢",CenterDot:"·",centerdot:"·",Cfr:"ℭ",cfr:"𝔠",CHcy:"Ч",chcy:"ч",check:"✓",checkmark:"✓",Chi:"Χ",chi:"χ",cir:"○",circ:"ˆ",circeq:"≗",circlearrowleft:"↺",circlearrowright:"↻",circledast:"⊛",circledcirc:"⊚",circleddash:"⊝",CircleDot:"⊙",circledR:"®",circledS:"Ⓢ",CircleMinus:"⊖",CirclePlus:"⊕",CircleTimes:"⊗",cirE:"⧃",cire:"≗",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",ClockwiseContourIntegral:"∲",CloseCurlyDoubleQuote:"”",CloseCurlyQuote:"’",clubs:"♣",clubsuit:"♣",Colon:"∷",colon:":",Colone:"⩴",colone:"≔",coloneq:"≔",comma:",",commat:"@",comp:"∁",compfn:"∘",complement:"∁",complexes:"ℂ",cong:"≅",congdot:"⩭",Congruent:"≡",Conint:"∯",conint:"∮",ContourIntegral:"∮",Copf:"ℂ",copf:"𝕔",coprod:"∐",Coproduct:"∐",COPY:"©",copy:"©",copysr:"℗",CounterClockwiseContourIntegral:"∳",crarr:"↵",Cross:"⨯",cross:"✗",Cscr:"𝒞",cscr:"𝒸",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",cuesc:"⋟",cularr:"↶",cularrp:"⤽",Cup:"⋓",cup:"∪",cupbrcap:"⩈",CupCap:"≍",cupcap:"⩆",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curarrm:"⤼",curlyeqprec:"⋞",curlyeqsucc:"⋟",curlyvee:"⋎",curlywedge:"⋏",curren:"¤",curvearrowleft:"↶",curvearrowright:"↷",cuvee:"⋎",cuwed:"⋏",cwconint:"∲",cwint:"∱",cylcty:"⌭",Dagger:"‡",dagger:"†",daleth:"ℸ",Darr:"↡",dArr:"⇓",darr:"↓",dash:"‐",Dashv:"⫤",dashv:"⊣",dbkarow:"⤏",dblac:"˝",Dcaron:"Ď",dcaron:"ď",Dcy:"Д",dcy:"д",DD:"ⅅ",dd:"ⅆ",ddagger:"‡",ddarr:"⇊",DDotrahd:"⤑",ddotseq:"⩷",deg:"°",Del:"∇",Delta:"Δ",delta:"δ",demptyv:"⦱",dfisht:"⥿",Dfr:"𝔇",dfr:"𝔡",dHar:"⥥",dharl:"⇃",dharr:"⇂",DiacriticalAcute:"´",DiacriticalDot:"˙",DiacriticalDoubleAcute:"˝",DiacriticalGrave:"`",DiacriticalTilde:"˜",diam:"⋄",Diamond:"⋄",diamond:"⋄",diamondsuit:"♦",diams:"♦",die:"¨",DifferentialD:"ⅆ",digamma:"ϝ",disin:"⋲",div:"÷",divide:"÷",divideontimes:"⋇",divonx:"⋇",DJcy:"Ђ",djcy:"ђ",dlcorn:"⌞",dlcrop:"⌍",dollar:"$",Dopf:"𝔻",dopf:"𝕕",Dot:"¨",dot:"˙",DotDot:"⃜",doteq:"≐",doteqdot:"≑",DotEqual:"≐",dotminus:"∸",dotplus:"∔",dotsquare:"⊡",doublebarwedge:"⌆",DoubleContourIntegral:"∯",DoubleDot:"¨",DoubleDownArrow:"⇓",DoubleLeftArrow:"⇐",DoubleLeftRightArrow:"⇔",DoubleLeftTee:"⫤",DoubleLongLeftArrow:"⟸",DoubleLongLeftRightArrow:"⟺",DoubleLongRightArrow:"⟹",DoubleRightArrow:"⇒",DoubleRightTee:"⊨",DoubleUpArrow:"⇑",DoubleUpDownArrow:"⇕",DoubleVerticalBar:"∥",DownArrow:"↓",Downarrow:"⇓",downarrow:"↓",DownArrowBar:"⤓",DownArrowUpArrow:"⇵",DownBreve:"̑",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVector:"↽",DownLeftVectorBar:"⥖",DownRightTeeVector:"⥟",DownRightVector:"⇁",DownRightVectorBar:"⥗",DownTee:"⊤",DownTeeArrow:"↧",drbkarow:"⤐",drcorn:"⌟",drcrop:"⌌",Dscr:"𝒟",dscr:"𝒹",DScy:"Ѕ",dscy:"ѕ",dsol:"⧶",Dstrok:"Đ",dstrok:"đ",dtdot:"⋱",dtri:"▿",dtrif:"▾",duarr:"⇵",duhar:"⥯",dwangle:"⦦",DZcy:"Џ",dzcy:"џ",dzigrarr:"⟿",Eacute:"É",eacute:"é",easter:"⩮",Ecaron:"Ě",ecaron:"ě",ecir:"≖",Ecirc:"Ê",ecirc:"ê",ecolon:"≕",Ecy:"Э",ecy:"э",eDDot:"⩷",Edot:"Ė",eDot:"≑",edot:"ė",ee:"ⅇ",efDot:"≒",Efr:"𝔈",efr:"𝔢",eg:"⪚",Egrave:"È",egrave:"è",egs:"⪖",egsdot:"⪘",el:"⪙",Element:"∈",elinters:"⏧",ell:"ℓ",els:"⪕",elsdot:"⪗",Emacr:"Ē",emacr:"ē",empty:"∅",emptyset:"∅",EmptySmallSquare:"◻",emptyv:"∅",EmptyVerySmallSquare:"▫",emsp:" ",emsp13:" ",emsp14:" ",ENG:"Ŋ",eng:"ŋ",ensp:" ",Eogon:"Ę",eogon:"ę",Eopf:"𝔼",eopf:"𝕖",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",Epsilon:"Ε",epsilon:"ε",epsiv:"ϵ",eqcirc:"≖",eqcolon:"≕",eqsim:"≂",eqslantgtr:"⪖",eqslantless:"⪕",Equal:"⩵",equals:"=",EqualTilde:"≂",equest:"≟",Equilibrium:"⇌",equiv:"≡",equivDD:"⩸",eqvparsl:"⧥",erarr:"⥱",erDot:"≓",Escr:"ℰ",escr:"ℯ",esdot:"≐",Esim:"⩳",esim:"≂",Eta:"Η",eta:"η",ETH:"Ð",eth:"ð",Euml:"Ë",euml:"ë",euro:"€",excl:"!",exist:"∃",Exists:"∃",expectation:"ℰ",ExponentialE:"ⅇ",exponentiale:"ⅇ",fallingdotseq:"≒",Fcy:"Ф",fcy:"ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",Ffr:"𝔉",ffr:"𝔣",filig:"ﬁ",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",Fopf:"𝔽",fopf:"𝕗",ForAll:"∀",forall:"∀",fork:"⋔",forkv:"⫙",Fouriertrf:"ℱ",fpartint:"⨍",frac12:"½",frac13:"⅓",frac14:"¼",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac34:"¾",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",Fscr:"ℱ",fscr:"𝒻",gacute:"ǵ",Gamma:"Γ",gamma:"γ",Gammad:"Ϝ",gammad:"ϝ",gap:"⪆",Gbreve:"Ğ",gbreve:"ğ",Gcedil:"Ģ",Gcirc:"Ĝ",gcirc:"ĝ",Gcy:"Г",gcy:"г",Gdot:"Ġ",gdot:"ġ",gE:"≧",ge:"≥",gEl:"⪌",gel:"⋛",geq:"≥",geqq:"≧",geqslant:"⩾",ges:"⩾",gescc:"⪩",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",Gfr:"𝔊",gfr:"𝔤",Gg:"⋙",gg:"≫",ggg:"⋙",gimel:"ℷ",GJcy:"Ѓ",gjcy:"ѓ",gl:"≷",gla:"⪥",glE:"⪒",glj:"⪤",gnap:"⪊",gnapprox:"⪊",gnE:"≩",gne:"⪈",gneq:"⪈",gneqq:"≩",gnsim:"⋧",Gopf:"𝔾",gopf:"𝕘",grave:"`",GreaterEqual:"≥",GreaterEqualLess:"⋛",GreaterFullEqual:"≧",GreaterGreater:"⪢",GreaterLess:"≷",GreaterSlantEqual:"⩾",GreaterTilde:"≳",Gscr:"𝒢",gscr:"ℊ",gsim:"≳",gsime:"⪎",gsiml:"⪐",Gt:"≫",GT:">",gt:">",gtcc:"⪧",gtcir:"⩺",gtdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrapprox:"⪆",gtrarr:"⥸",gtrdot:"⋗",gtreqless:"⋛",gtreqqless:"⪌",gtrless:"≷",gtrsim:"≳",gvertneqq:"≩︀",gvnE:"≩︀",Hacek:"ˇ",hairsp:" ",half:"½",hamilt:"ℋ",HARDcy:"Ъ",hardcy:"ъ",hArr:"⇔",harr:"↔",harrcir:"⥈",harrw:"↭",Hat:"^",hbar:"ℏ",Hcirc:"Ĥ",hcirc:"ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",hercon:"⊹",Hfr:"ℌ",hfr:"𝔥",HilbertSpace:"ℋ",hksearow:"⤥",hkswarow:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",hookrightarrow:"↪",Hopf:"ℍ",hopf:"𝕙",horbar:"―",HorizontalLine:"─",Hscr:"ℋ",hscr:"𝒽",hslash:"ℏ",Hstrok:"Ħ",hstrok:"ħ",HumpDownHump:"≎",HumpEqual:"≏",hybull:"⁃",hyphen:"‐",Iacute:"Í",iacute:"í",ic:"⁣",Icirc:"Î",icirc:"î",Icy:"И",icy:"и",Idot:"İ",IEcy:"Е",iecy:"е",iexcl:"¡",iff:"⇔",Ifr:"ℑ",ifr:"𝔦",Igrave:"Ì",igrave:"ì",ii:"ⅈ",iiiint:"⨌",iiint:"∭",iinfin:"⧜",iiota:"℩",IJlig:"Ĳ",ijlig:"ĳ",Im:"ℑ",Imacr:"Ī",imacr:"ī",image:"ℑ",ImaginaryI:"ⅈ",imagline:"ℐ",imagpart:"ℑ",imath:"ı",imof:"⊷",imped:"Ƶ",Implies:"⇒",in:"∈",incare:"℅",infin:"∞",infintie:"⧝",inodot:"ı",Int:"∬",int:"∫",intcal:"⊺",integers:"ℤ",Integral:"∫",intercal:"⊺",Intersection:"⋂",intlarhk:"⨗",intprod:"⨼",InvisibleComma:"⁣",InvisibleTimes:"⁢",IOcy:"Ё",iocy:"ё",Iogon:"Į",iogon:"į",Iopf:"𝕀",iopf:"𝕚",Iota:"Ι",iota:"ι",iprod:"⨼",iquest:"¿",Iscr:"ℐ",iscr:"𝒾",isin:"∈",isindot:"⋵",isinE:"⋹",isins:"⋴",isinsv:"⋳",isinv:"∈",it:"⁢",Itilde:"Ĩ",itilde:"ĩ",Iukcy:"І",iukcy:"і",Iuml:"Ï",iuml:"ï",Jcirc:"Ĵ",jcirc:"ĵ",Jcy:"Й",jcy:"й",Jfr:"𝔍",jfr:"𝔧",jmath:"ȷ",Jopf:"𝕁",jopf:"𝕛",Jscr:"𝒥",jscr:"𝒿",Jsercy:"Ј",jsercy:"ј",Jukcy:"Є",jukcy:"є",Kappa:"Κ",kappa:"κ",kappav:"ϰ",Kcedil:"Ķ",kcedil:"ķ",Kcy:"К",kcy:"к",Kfr:"𝔎",kfr:"𝔨",kgreen:"ĸ",KHcy:"Х",khcy:"х",KJcy:"Ќ",kjcy:"ќ",Kopf:"𝕂",kopf:"𝕜",Kscr:"𝒦",kscr:"𝓀",lAarr:"⇚",Lacute:"Ĺ",lacute:"ĺ",laemptyv:"⦴",lagran:"ℒ",Lambda:"Λ",lambda:"λ",Lang:"⟪",lang:"⟨",langd:"⦑",langle:"⟨",lap:"⪅",Laplacetrf:"ℒ",laquo:"«",Larr:"↞",lArr:"⇐",larr:"←",larrb:"⇤",larrbfs:"⤟",larrfs:"⤝",larrhk:"↩",larrlp:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",lat:"⪫",lAtail:"⤛",latail:"⤙",late:"⪭",lates:"⪭︀",lBarr:"⤎",lbarr:"⤌",lbbrk:"❲",lbrace:"{",lbrack:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",Lcaron:"Ľ",lcaron:"ľ",Lcedil:"Ļ",lcedil:"ļ",lceil:"⌈",lcub:"{",Lcy:"Л",lcy:"л",ldca:"⤶",ldquo:"“",ldquor:"„",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",lE:"≦",le:"≤",LeftAngleBracket:"⟨",LeftArrow:"←",Leftarrow:"⇐",leftarrow:"←",LeftArrowBar:"⇤",LeftArrowRightArrow:"⇆",leftarrowtail:"↢",LeftCeiling:"⌈",LeftDoubleBracket:"⟦",LeftDownTeeVector:"⥡",LeftDownVector:"⇃",LeftDownVectorBar:"⥙",LeftFloor:"⌊",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",LeftRightArrow:"↔",Leftrightarrow:"⇔",leftrightarrow:"↔",leftrightarrows:"⇆",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",LeftRightVector:"⥎",LeftTee:"⊣",LeftTeeArrow:"↤",LeftTeeVector:"⥚",leftthreetimes:"⋋",LeftTriangle:"⊲",LeftTriangleBar:"⧏",LeftTriangleEqual:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVector:"↿",LeftUpVectorBar:"⥘",LeftVector:"↼",LeftVectorBar:"⥒",lEg:"⪋",leg:"⋚",leq:"≤",leqq:"≦",leqslant:"⩽",les:"⩽",lescc:"⪨",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessapprox:"⪅",lessdot:"⋖",lesseqgtr:"⋚",lesseqqgtr:"⪋",LessEqualGreater:"⋚",LessFullEqual:"≦",LessGreater:"≶",lessgtr:"≶",LessLess:"⪡",lesssim:"≲",LessSlantEqual:"⩽",LessTilde:"≲",lfisht:"⥼",lfloor:"⌊",Lfr:"𝔏",lfr:"𝔩",lg:"≶",lgE:"⪑",lHar:"⥢",lhard:"↽",lharu:"↼",lharul:"⥪",lhblk:"▄",LJcy:"Љ",ljcy:"љ",Ll:"⋘",ll:"≪",llarr:"⇇",llcorner:"⌞",Lleftarrow:"⇚",llhard:"⥫",lltri:"◺",Lmidot:"Ŀ",lmidot:"ŀ",lmoust:"⎰",lmoustache:"⎰",lnap:"⪉",lnapprox:"⪉",lnE:"≨",lne:"⪇",lneq:"⪇",lneqq:"≨",lnsim:"⋦",loang:"⟬",loarr:"⇽",lobrk:"⟦",LongLeftArrow:"⟵",Longleftarrow:"⟸",longleftarrow:"⟵",LongLeftRightArrow:"⟷",Longleftrightarrow:"⟺",longleftrightarrow:"⟷",longmapsto:"⟼",LongRightArrow:"⟶",Longrightarrow:"⟹",longrightarrow:"⟶",looparrowleft:"↫",looparrowright:"↬",lopar:"⦅",Lopf:"𝕃",lopf:"𝕝",loplus:"⨭",lotimes:"⨴",lowast:"∗",lowbar:"_",LowerLeftArrow:"↙",LowerRightArrow:"↘",loz:"◊",lozenge:"◊",lozf:"⧫",lpar:"(",lparlt:"⦓",lrarr:"⇆",lrcorner:"⌟",lrhar:"⇋",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",Lscr:"ℒ",lscr:"𝓁",Lsh:"↰",lsh:"↰",lsim:"≲",lsime:"⪍",lsimg:"⪏",lsqb:"[",lsquo:"‘",lsquor:"‚",Lstrok:"Ł",lstrok:"ł",Lt:"≪",LT:"<",lt:"<",ltcc:"⪦",ltcir:"⩹",ltdot:"⋖",lthree:"⋋",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltri:"◃",ltrie:"⊴",ltrif:"◂",ltrPar:"⦖",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",macr:"¯",male:"♂",malt:"✠",maltese:"✠",Map:"⤅",map:"↦",mapsto:"↦",mapstodown:"↧",mapstoleft:"↤",mapstoup:"↥",marker:"▮",mcomma:"⨩",Mcy:"М",mcy:"м",mdash:"—",mDDot:"∺",measuredangle:"∡",MediumSpace:" ",Mellintrf:"ℳ",Mfr:"𝔐",mfr:"𝔪",mho:"℧",micro:"µ",mid:"∣",midast:"*",midcir:"⫰",middot:"·",minus:"−",minusb:"⊟",minusd:"∸",minusdu:"⨪",MinusPlus:"∓",mlcp:"⫛",mldr:"…",mnplus:"∓",models:"⊧",Mopf:"𝕄",mopf:"𝕞",mp:"∓",Mscr:"ℳ",mscr:"𝓂",mstpos:"∾",Mu:"Μ",mu:"μ",multimap:"⊸",mumap:"⊸",nabla:"∇",Nacute:"Ń",nacute:"ń",nang:"∠⃒",nap:"≉",napE:"⩰̸",napid:"≋̸",napos:"ŉ",napprox:"≉",natur:"♮",natural:"♮",naturals:"ℕ",nbsp:" ",nbump:"≎̸",nbumpe:"≏̸",ncap:"⩃",Ncaron:"Ň",ncaron:"ň",Ncedil:"Ņ",ncedil:"ņ",ncong:"≇",ncongdot:"⩭̸",ncup:"⩂",Ncy:"Н",ncy:"н",ndash:"–",ne:"≠",nearhk:"⤤",neArr:"⇗",nearr:"↗",nearrow:"↗",nedot:"≐̸",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",nequiv:"≢",nesear:"⤨",nesim:"≂̸",NestedGreaterGreater:"≫",NestedLessLess:"≪",NewLine:"\n",nexist:"∄",nexists:"∄",Nfr:"𝔑",nfr:"𝔫",ngE:"≧̸",nge:"≱",ngeq:"≱",ngeqq:"≧̸",ngeqslant:"⩾̸",nges:"⩾̸",nGg:"⋙̸",ngsim:"≵",nGt:"≫⃒",ngt:"≯",ngtr:"≯",nGtv:"≫̸",nhArr:"⇎",nharr:"↮",nhpar:"⫲",ni:"∋",nis:"⋼",nisd:"⋺",niv:"∋",NJcy:"Њ",njcy:"њ",nlArr:"⇍",nlarr:"↚",nldr:"‥",nlE:"≦̸",nle:"≰",nLeftarrow:"⇍",nleftarrow:"↚",nLeftrightarrow:"⇎",nleftrightarrow:"↮",nleq:"≰",nleqq:"≦̸",nleqslant:"⩽̸",nles:"⩽̸",nless:"≮",nLl:"⋘̸",nlsim:"≴",nLt:"≪⃒",nlt:"≮",nltri:"⋪",nltrie:"⋬",nLtv:"≪̸",nmid:"∤",NoBreak:"⁠",NonBreakingSpace:" ",Nopf:"ℕ",nopf:"𝕟",Not:"⫬",not:"¬",NotCongruent:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",NotElement:"∉",NotEqual:"≠",NotEqualTilde:"≂̸",NotExists:"∄",NotGreater:"≯",NotGreaterEqual:"≱",NotGreaterFullEqual:"≧̸",NotGreaterGreater:"≫̸",NotGreaterLess:"≹",NotGreaterSlantEqual:"⩾̸",NotGreaterTilde:"≵",NotHumpDownHump:"≎̸",NotHumpEqual:"≏̸",notin:"∉",notindot:"⋵̸",notinE:"⋹̸",notinva:"∉",notinvb:"⋷",notinvc:"⋶",NotLeftTriangle:"⋪",NotLeftTriangleBar:"⧏̸",NotLeftTriangleEqual:"⋬",NotLess:"≮",NotLessEqual:"≰",NotLessGreater:"≸",NotLessLess:"≪̸",NotLessSlantEqual:"⩽̸",NotLessTilde:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",notni:"∌",notniva:"∌",notnivb:"⋾",notnivc:"⋽",NotPrecedes:"⊀",NotPrecedesEqual:"⪯̸",NotPrecedesSlantEqual:"⋠",NotReverseElement:"∌",NotRightTriangle:"⋫",NotRightTriangleBar:"⧐̸",NotRightTriangleEqual:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",NotSubset:"⊂⃒",NotSubsetEqual:"⊈",NotSucceeds:"⊁",NotSucceedsEqual:"⪰̸",NotSucceedsSlantEqual:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",NotSupersetEqual:"⊉",NotTilde:"≁",NotTildeEqual:"≄",NotTildeFullEqual:"≇",NotTildeTilde:"≉",NotVerticalBar:"∤",npar:"∦",nparallel:"∦",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",npr:"⊀",nprcue:"⋠",npre:"⪯̸",nprec:"⊀",npreceq:"⪯̸",nrArr:"⇏",nrarr:"↛",nrarrc:"⤳̸",nrarrw:"↝̸",nRightarrow:"⇏",nrightarrow:"↛",nrtri:"⋫",nrtrie:"⋭",nsc:"⊁",nsccue:"⋡",nsce:"⪰̸",Nscr:"𝒩",nscr:"𝓃",nshortmid:"∤",nshortparallel:"∦",nsim:"≁",nsime:"≄",nsimeq:"≄",nsmid:"∤",nspar:"∦",nsqsube:"⋢",nsqsupe:"⋣",nsub:"⊄",nsubE:"⫅̸",nsube:"⊈",nsubset:"⊂⃒",nsubseteq:"⊈",nsubseteqq:"⫅̸",nsucc:"⊁",nsucceq:"⪰̸",nsup:"⊅",nsupE:"⫆̸",nsupe:"⊉",nsupset:"⊃⃒",nsupseteq:"⊉",nsupseteqq:"⫆̸",ntgl:"≹",Ntilde:"Ñ",ntilde:"ñ",ntlg:"≸",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",Nu:"Ν",nu:"ν",num:"#",numero:"№",numsp:" ",nvap:"≍⃒",nVDash:"⊯",nVdash:"⊮",nvDash:"⊭",nvdash:"⊬",nvge:"≥⃒",nvgt:">⃒",nvHarr:"⤄",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwarhk:"⤣",nwArr:"⇖",nwarr:"↖",nwarrow:"↖",nwnear:"⤧",Oacute:"Ó",oacute:"ó",oast:"⊛",ocir:"⊚",Ocirc:"Ô",ocirc:"ô",Ocy:"О",ocy:"о",odash:"⊝",Odblac:"Ő",odblac:"ő",odiv:"⨸",odot:"⊙",odsold:"⦼",OElig:"Œ",oelig:"œ",ofcir:"⦿",Ofr:"𝔒",ofr:"𝔬",ogon:"˛",Ograve:"Ò",ograve:"ò",ogt:"⧁",ohbar:"⦵",ohm:"Ω",oint:"∮",olarr:"↺",olcir:"⦾",olcross:"⦻",oline:"‾",olt:"⧀",Omacr:"Ō",omacr:"ō",Omega:"Ω",omega:"ω",Omicron:"Ο",omicron:"ο",omid:"⦶",ominus:"⊖",Oopf:"𝕆",oopf:"𝕠",opar:"⦷",OpenCurlyDoubleQuote:"“",OpenCurlyQuote:"‘",operp:"⦹",oplus:"⊕",Or:"⩔",or:"∨",orarr:"↻",ord:"⩝",order:"ℴ",orderof:"ℴ",ordf:"ª",ordm:"º",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oS:"Ⓢ",Oscr:"𝒪",oscr:"ℴ",Oslash:"Ø",oslash:"ø",osol:"⊘",Otilde:"Õ",otilde:"õ",Otimes:"⨷",otimes:"⊗",otimesas:"⨶",Ouml:"Ö",ouml:"ö",ovbar:"⌽",OverBar:"‾",OverBrace:"⏞",OverBracket:"⎴",OverParenthesis:"⏜",par:"∥",para:"¶",parallel:"∥",parsim:"⫳",parsl:"⫽",part:"∂",PartialD:"∂",Pcy:"П",pcy:"п",percnt:"%",period:".",permil:"‰",perp:"⊥",pertenk:"‱",Pfr:"𝔓",pfr:"𝔭",Phi:"Φ",phi:"φ",phiv:"ϕ",phmmat:"ℳ",phone:"☎",Pi:"Π",pi:"π",pitchfork:"⋔",piv:"ϖ",planck:"ℏ",planckh:"ℎ",plankv:"ℏ",plus:"+",plusacir:"⨣",plusb:"⊞",pluscir:"⨢",plusdo:"∔",plusdu:"⨥",pluse:"⩲",PlusMinus:"±",plusmn:"±",plussim:"⨦",plustwo:"⨧",pm:"±",Poincareplane:"ℌ",pointint:"⨕",Popf:"ℙ",popf:"𝕡",pound:"£",Pr:"⪻",pr:"≺",prap:"⪷",prcue:"≼",prE:"⪳",pre:"⪯",prec:"≺",precapprox:"⪷",preccurlyeq:"≼",Precedes:"≺",PrecedesEqual:"⪯",PrecedesSlantEqual:"≼",PrecedesTilde:"≾",preceq:"⪯",precnapprox:"⪹",precneqq:"⪵",precnsim:"⋨",precsim:"≾",Prime:"″",prime:"′",primes:"ℙ",prnap:"⪹",prnE:"⪵",prnsim:"⋨",prod:"∏",Product:"∏",profalar:"⌮",profline:"⌒",profsurf:"⌓",prop:"∝",Proportion:"∷",Proportional:"∝",propto:"∝",prsim:"≾",prurel:"⊰",Pscr:"𝒫",pscr:"𝓅",Psi:"Ψ",psi:"ψ",puncsp:" ",Qfr:"𝔔",qfr:"𝔮",qint:"⨌",Qopf:"ℚ",qopf:"𝕢",qprime:"⁗",Qscr:"𝒬",qscr:"𝓆",quaternions:"ℍ",quatint:"⨖",quest:"?",questeq:"≟",QUOT:'"',quot:'"',rAarr:"⇛",race:"∽̱",Racute:"Ŕ",racute:"ŕ",radic:"√",raemptyv:"⦳",Rang:"⟫",rang:"⟩",rangd:"⦒",range:"⦥",rangle:"⟩",raquo:"»",Rarr:"↠",rArr:"⇒",rarr:"→",rarrap:"⥵",rarrb:"⇥",rarrbfs:"⤠",rarrc:"⤳",rarrfs:"⤞",rarrhk:"↪",rarrlp:"↬",rarrpl:"⥅",rarrsim:"⥴",Rarrtl:"⤖",rarrtl:"↣",rarrw:"↝",rAtail:"⤜",ratail:"⤚",ratio:"∶",rationals:"ℚ",RBarr:"⤐",rBarr:"⤏",rbarr:"⤍",rbbrk:"❳",rbrace:"}",rbrack:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",Rcaron:"Ř",rcaron:"ř",Rcedil:"Ŗ",rcedil:"ŗ",rceil:"⌉",rcub:"}",Rcy:"Р",rcy:"р",rdca:"⤷",rdldhar:"⥩",rdquo:"”",rdquor:"”",rdsh:"↳",Re:"ℜ",real:"ℜ",realine:"ℛ",realpart:"ℜ",reals:"ℝ",rect:"▭",REG:"®",reg:"®",ReverseElement:"∋",ReverseEquilibrium:"⇋",ReverseUpEquilibrium:"⥯",rfisht:"⥽",rfloor:"⌋",Rfr:"ℜ",rfr:"𝔯",rHar:"⥤",rhard:"⇁",rharu:"⇀",rharul:"⥬",Rho:"Ρ",rho:"ρ",rhov:"ϱ",RightAngleBracket:"⟩",RightArrow:"→",Rightarrow:"⇒",rightarrow:"→",RightArrowBar:"⇥",RightArrowLeftArrow:"⇄",rightarrowtail:"↣",RightCeiling:"⌉",RightDoubleBracket:"⟧",RightDownTeeVector:"⥝",RightDownVector:"⇂",RightDownVectorBar:"⥕",RightFloor:"⌋",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇄",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"↝",RightTee:"⊢",RightTeeArrow:"↦",RightTeeVector:"⥛",rightthreetimes:"⋌",RightTriangle:"⊳",RightTriangleBar:"⧐",RightTriangleEqual:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVector:"↾",RightUpVectorBar:"⥔",RightVector:"⇀",RightVectorBar:"⥓",ring:"˚",risingdotseq:"≓",rlarr:"⇄",rlhar:"⇌",rlm:"‏",rmoust:"⎱",rmoustache:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",robrk:"⟧",ropar:"⦆",Ropf:"ℝ",ropf:"𝕣",roplus:"⨮",rotimes:"⨵",RoundImplies:"⥰",rpar:")",rpargt:"⦔",rppolint:"⨒",rrarr:"⇉",Rrightarrow:"⇛",rsaquo:"›",Rscr:"ℛ",rscr:"𝓇",Rsh:"↱",rsh:"↱",rsqb:"]",rsquo:"’",rsquor:"’",rthree:"⋌",rtimes:"⋊",rtri:"▹",rtrie:"⊵",rtrif:"▸",rtriltri:"⧎",RuleDelayed:"⧴",ruluhar:"⥨",rx:"℞",Sacute:"Ś",sacute:"ś",sbquo:"‚",Sc:"⪼",sc:"≻",scap:"⪸",Scaron:"Š",scaron:"š",sccue:"≽",scE:"⪴",sce:"⪰",Scedil:"Ş",scedil:"ş",Scirc:"Ŝ",scirc:"ŝ",scnap:"⪺",scnE:"⪶",scnsim:"⋩",scpolint:"⨓",scsim:"≿",Scy:"С",scy:"с",sdot:"⋅",sdotb:"⊡",sdote:"⩦",searhk:"⤥",seArr:"⇘",searr:"↘",searrow:"↘",sect:"§",semi:";",seswar:"⤩",setminus:"∖",setmn:"∖",sext:"✶",Sfr:"𝔖",sfr:"𝔰",sfrown:"⌢",sharp:"♯",SHCHcy:"Щ",shchcy:"щ",SHcy:"Ш",shcy:"ш",ShortDownArrow:"↓",ShortLeftArrow:"←",shortmid:"∣",shortparallel:"∥",ShortRightArrow:"→",ShortUpArrow:"↑",shy:"­",Sigma:"Σ",sigma:"σ",sigmaf:"ς",sigmav:"ς",sim:"∼",simdot:"⩪",sime:"≃",simeq:"≃",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",slarr:"←",SmallCircle:"∘",smallsetminus:"∖",smashp:"⨳",smeparsl:"⧤",smid:"∣",smile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",SOFTcy:"Ь",softcy:"ь",sol:"/",solb:"⧄",solbar:"⌿",Sopf:"𝕊",sopf:"𝕤",spades:"♠",spadesuit:"♠",spar:"∥",sqcap:"⊓",sqcaps:"⊓︀",sqcup:"⊔",sqcups:"⊔︀",Sqrt:"√",sqsub:"⊏",sqsube:"⊑",sqsubset:"⊏",sqsubseteq:"⊑",sqsup:"⊐",sqsupe:"⊒",sqsupset:"⊐",sqsupseteq:"⊒",squ:"□",Square:"□",square:"□",SquareIntersection:"⊓",SquareSubset:"⊏",SquareSubsetEqual:"⊑",SquareSuperset:"⊐",SquareSupersetEqual:"⊒",SquareUnion:"⊔",squarf:"▪",squf:"▪",srarr:"→",Sscr:"𝒮",sscr:"𝓈",ssetmn:"∖",ssmile:"⌣",sstarf:"⋆",Star:"⋆",star:"☆",starf:"★",straightepsilon:"ϵ",straightphi:"ϕ",strns:"¯",Sub:"⋐",sub:"⊂",subdot:"⪽",subE:"⫅",sube:"⊆",subedot:"⫃",submult:"⫁",subnE:"⫋",subne:"⊊",subplus:"⪿",subrarr:"⥹",Subset:"⋐",subset:"⊂",subseteq:"⊆",subseteqq:"⫅",SubsetEqual:"⊆",subsetneq:"⊊",subsetneqq:"⫋",subsim:"⫇",subsub:"⫕",subsup:"⫓",succ:"≻",succapprox:"⪸",succcurlyeq:"≽",Succeeds:"≻",SucceedsEqual:"⪰",SucceedsSlantEqual:"≽",SucceedsTilde:"≿",succeq:"⪰",succnapprox:"⪺",succneqq:"⪶",succnsim:"⋩",succsim:"≿",SuchThat:"∋",Sum:"∑",sum:"∑",sung:"♪",Sup:"⋑",sup:"⊃",sup1:"¹",sup2:"²",sup3:"³",supdot:"⪾",supdsub:"⫘",supE:"⫆",supe:"⊇",supedot:"⫄",Superset:"⊃",SupersetEqual:"⊇",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supnE:"⫌",supne:"⊋",supplus:"⫀",Supset:"⋑",supset:"⊃",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",supsim:"⫈",supsub:"⫔",supsup:"⫖",swarhk:"⤦",swArr:"⇙",swarr:"↙",swarrow:"↙",swnwar:"⤪",szlig:"ß",Tab:"\t",target:"⌖",Tau:"Τ",tau:"τ",tbrk:"⎴",Tcaron:"Ť",tcaron:"ť",Tcedil:"Ţ",tcedil:"ţ",Tcy:"Т",tcy:"т",tdot:"⃛",telrec:"⌕",Tfr:"𝔗",tfr:"𝔱",there4:"∴",Therefore:"∴",therefore:"∴",Theta:"Θ",theta:"θ",thetasym:"ϑ",thetav:"ϑ",thickapprox:"≈",thicksim:"∼",ThickSpace:"  ",thinsp:" ",ThinSpace:" ",thkap:"≈",thksim:"∼",THORN:"Þ",thorn:"þ",Tilde:"∼",tilde:"˜",TildeEqual:"≃",TildeFullEqual:"≅",TildeTilde:"≈",times:"×",timesb:"⊠",timesbar:"⨱",timesd:"⨰",tint:"∭",toea:"⤨",top:"⊤",topbot:"⌶",topcir:"⫱",Topf:"𝕋",topf:"𝕥",topfork:"⫚",tosa:"⤩",tprime:"‴",TRADE:"™",trade:"™",triangle:"▵",triangledown:"▿",triangleleft:"◃",trianglelefteq:"⊴",triangleq:"≜",triangleright:"▹",trianglerighteq:"⊵",tridot:"◬",trie:"≜",triminus:"⨺",TripleDot:"⃛",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",Tscr:"𝒯",tscr:"𝓉",TScy:"Ц",tscy:"ц",TSHcy:"Ћ",tshcy:"ћ",Tstrok:"Ŧ",tstrok:"ŧ",twixt:"≬",twoheadleftarrow:"↞",twoheadrightarrow:"↠",Uacute:"Ú",uacute:"ú",Uarr:"↟",uArr:"⇑",uarr:"↑",Uarrocir:"⥉",Ubrcy:"Ў",ubrcy:"ў",Ubreve:"Ŭ",ubreve:"ŭ",Ucirc:"Û",ucirc:"û",Ucy:"У",ucy:"у",udarr:"⇅",Udblac:"Ű",udblac:"ű",udhar:"⥮",ufisht:"⥾",Ufr:"𝔘",ufr:"𝔲",Ugrave:"Ù",ugrave:"ù",uHar:"⥣",uharl:"↿",uharr:"↾",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",Umacr:"Ū",umacr:"ū",uml:"¨",UnderBar:"_",UnderBrace:"⏟",UnderBracket:"⎵",UnderParenthesis:"⏝",Union:"⋃",UnionPlus:"⊎",Uogon:"Ų",uogon:"ų",Uopf:"𝕌",uopf:"𝕦",UpArrow:"↑",Uparrow:"⇑",uparrow:"↑",UpArrowBar:"⤒",UpArrowDownArrow:"⇅",UpDownArrow:"↕",Updownarrow:"⇕",updownarrow:"↕",UpEquilibrium:"⥮",upharpoonleft:"↿",upharpoonright:"↾",uplus:"⊎",UpperLeftArrow:"↖",UpperRightArrow:"↗",Upsi:"ϒ",upsi:"υ",upsih:"ϒ",Upsilon:"Υ",upsilon:"υ",UpTee:"⊥",UpTeeArrow:"↥",upuparrows:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",Uring:"Ů",uring:"ů",urtri:"◹",Uscr:"𝒰",uscr:"𝓊",utdot:"⋰",Utilde:"Ũ",utilde:"ũ",utri:"▵",utrif:"▴",uuarr:"⇈",Uuml:"Ü",uuml:"ü",uwangle:"⦧",vangrt:"⦜",varepsilon:"ϵ",varkappa:"ϰ",varnothing:"∅",varphi:"ϕ",varpi:"ϖ",varpropto:"∝",vArr:"⇕",varr:"↕",varrho:"ϱ",varsigma:"ς",varsubsetneq:"⊊︀",varsubsetneqq:"⫋︀",varsupsetneq:"⊋︀",varsupsetneqq:"⫌︀",vartheta:"ϑ",vartriangleleft:"⊲",vartriangleright:"⊳",Vbar:"⫫",vBar:"⫨",vBarv:"⫩",Vcy:"В",vcy:"в",VDash:"⊫",Vdash:"⊩",vDash:"⊨",vdash:"⊢",Vdashl:"⫦",Vee:"⋁",vee:"∨",veebar:"⊻",veeeq:"≚",vellip:"⋮",Verbar:"‖",verbar:"|",Vert:"‖",vert:"|",VerticalBar:"∣",VerticalLine:"|",VerticalSeparator:"❘",VerticalTilde:"≀",VeryThinSpace:" ",Vfr:"𝔙",vfr:"𝔳",vltri:"⊲",vnsub:"⊂⃒",vnsup:"⊃⃒",Vopf:"𝕍",vopf:"𝕧",vprop:"∝",vrtri:"⊳",Vscr:"𝒱",vscr:"𝓋",vsubnE:"⫋︀",vsubne:"⊊︀",vsupnE:"⫌︀",vsupne:"⊋︀",Vvdash:"⊪",vzigzag:"⦚",Wcirc:"Ŵ",wcirc:"ŵ",wedbar:"⩟",Wedge:"⋀",wedge:"∧",wedgeq:"≙",weierp:"℘",Wfr:"𝔚",wfr:"𝔴",Wopf:"𝕎",wopf:"𝕨",wp:"℘",wr:"≀",wreath:"≀",Wscr:"𝒲",wscr:"𝓌",xcap:"⋂",xcirc:"◯",xcup:"⋃",xdtri:"▽",Xfr:"𝔛",xfr:"𝔵",xhArr:"⟺",xharr:"⟷",Xi:"Ξ",xi:"ξ",xlArr:"⟸",xlarr:"⟵",xmap:"⟼",xnis:"⋻",xodot:"⨀",Xopf:"𝕏",xopf:"𝕩",xoplus:"⨁",xotime:"⨂",xrArr:"⟹",xrarr:"⟶",Xscr:"𝒳",xscr:"𝓍",xsqcup:"⨆",xuplus:"⨄",xutri:"△",xvee:"⋁",xwedge:"⋀",Yacute:"Ý",yacute:"ý",YAcy:"Я",yacy:"я",Ycirc:"Ŷ",ycirc:"ŷ",Ycy:"Ы",ycy:"ы",yen:"¥",Yfr:"𝔜",yfr:"𝔶",YIcy:"Ї",yicy:"ї",Yopf:"𝕐",yopf:"𝕪",Yscr:"𝒴",yscr:"𝓎",YUcy:"Ю",yucy:"ю",Yuml:"Ÿ",yuml:"ÿ",Zacute:"Ź",zacute:"ź",Zcaron:"Ž",zcaron:"ž",Zcy:"З",zcy:"з",Zdot:"Ż",zdot:"ż",zeetrf:"ℨ",ZeroWidthSpace:"​",Zeta:"Ζ",zeta:"ζ",Zfr:"ℨ",zfr:"𝔷",ZHcy:"Ж",zhcy:"ж",zigrarr:"⇝",Zopf:"ℤ",zopf:"𝕫",Zscr:"𝒵",zscr:"𝓏",zwj:"‍",zwnj:"‌"}),e.entityMap=e.HTML_ENTITIES),Jr),a=oa(),o=i.DOMImplementation,c=t.NAMESPACE,s=a.ParseError,d=a.XMLReader;function u(e){return e.replace(/\r[\n\u0085]/g,"\n").replace(/[\r\u0085\u2028]/g,"\n")}function l(e){this.options=e||{locator:{}}}function h(){this.cdata=!1}function p(e,n){n.lineNumber=e.lineNumber,n.columnNumber=e.columnNumber}function f(e){if(e)return"\n@"+(e.systemId||"")+"#[line:"+e.lineNumber+",col:"+e.columnNumber+"]"}function g(e,n,t){return"string"==typeof e?e.substr(n,t):e.length>=n+t||n?new java.lang.String(e,n,t)+"":e}function b(e,n){e.currentElement?e.currentElement.appendChild(n):e.doc.appendChild(n)}return l.prototype.parseFromString=function(e,n){var t=this.options,i=new d,a=t.domBuilder||new h,o=t.errorHandler,s=t.locator,l=t.xmlns||{},p=/\/x?html?$/.test(n),g=p?r.HTML_ENTITIES:r.XML_ENTITIES;s&&a.setDocumentLocator(s),i.errorHandler=function(e,n,t){if(!e){if(n instanceof h)return n;e=n}var i={},r=e instanceof Function;function a(n){var a=e[n];!a&&r&&(a=2==e.length?function(t){e(n,t)}:e),i[n]=a&&function(e){a("[xmldom "+n+"]\t"+e+f(t))}||function(){}}return t=t||{},a("warning"),a("error"),a("fatalError"),i}(o,a,s),i.domBuilder=t.domBuilder||a,p&&(l[""]=c.HTML),l.xml=l.xml||c.XML;var b=t.normalizeLineEndings||u;return e&&"string"==typeof e?i.parse(b(e),l,g):i.errorHandler.error("invalid doc source"),a.doc},h.prototype={startDocument:function(){this.doc=(new o).createDocument(null,null,null),this.locator&&(this.doc.documentURI=this.locator.systemId)},startElement:function(e,n,t,i){var r=this.doc,a=r.createElementNS(e,t||n),o=i.length;b(this,a),this.currentElement=a,this.locator&&p(this.locator,a);for(var c=0;c<o;c++){e=i.getURI(c);var s=i.getValue(c),d=(t=i.getQName(c),r.createAttributeNS(e,t));this.locator&&p(i.getLocator(c),d),d.value=d.nodeValue=s,a.setAttributeNode(d)}},endElement:function(e,n,t){var i=this.currentElement;i.tagName,this.currentElement=i.parentNode},startPrefixMapping:function(e,n){},endPrefixMapping:function(e){},processingInstruction:function(e,n){var t=this.doc.createProcessingInstruction(e,n);this.locator&&p(this.locator,t),b(this,t)},ignorableWhitespace:function(e,n,t){},characters:function(e,n,t){if(e=g.apply(this,arguments)){if(this.cdata)var i=this.doc.createCDATASection(e);else i=this.doc.createTextNode(e);this.currentElement?this.currentElement.appendChild(i):/^\s*$/.test(e)&&this.doc.appendChild(i),this.locator&&p(this.locator,i)}},skippedEntity:function(e){},endDocument:function(){this.doc.normalize()},setDocumentLocator:function(e){(this.locator=e)&&(e.lineNumber=0)},comment:function(e,n,t){e=g.apply(this,arguments);var i=this.doc.createComment(e);this.locator&&p(this.locator,i),b(this,i)},startCDATA:function(){this.cdata=!0},endCDATA:function(){this.cdata=!1},startDTD:function(e,n,t){var i=this.doc.implementation;if(i&&i.createDocumentType){var r=i.createDocumentType(e,n,t);this.locator&&p(this.locator,r),b(this,r),this.doc.doctype=r}},warning:function(e){console.warn("[xmldom warning]\t"+e,f(this.locator))},error:function(e){console.error("[xmldom error]\t"+e,f(this.locator))},fatalError:function(e){throw new s(e,this.locator)}},"endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl".replace(/\w+/g,function(e){h.prototype[e]=function(){return null}}),Zr.__DOMHandler=h,Zr.normalizeLineEndings=u,Zr.DOMParser=l,Zr}function sa(){if(ia)return zr;ia=1;var e=function(){if(ta)return Gr;ta=1;var e=Qr();return Gr.DOMImplementation=e.DOMImplementation,Gr.XMLSerializer=e.XMLSerializer,Gr.DOMParser=ca().DOMParser,Gr}(),n=Qr();return zr.parseFromString=function(n){var t=null,i=new e.DOMParser({errorHandler:function(e,n){t={level:e,message:n}}}).parseFromString(n);if(null===t)return i;throw new Error(t.level+": "+t.message)},zr.Node=n.Node,zr}function da(){if(ra)return Hr;ra=1;var e=wr(),n=Ht,t=sa(),i=qr(),r=i.Element;Hr.readString=function(o,c){c=c||{};try{var s=t.parseFromString(o,"text/xml")}catch(l){return e.reject(l)}if("parsererror"===s.documentElement.tagName)return e.resolve(new Error(s.documentElement.textContent));function d(e){switch(e.nodeType){case a.ELEMENT_NODE:return function(e){var t=u(e),i=[];n.forEach(e.childNodes,function(e){var n=d(e);n&&i.push(n)});var a={};return n.forEach(e.attributes,function(e){a[u(e)]=e.value}),new r(t,a,i)}(e);case a.TEXT_NODE:return i.text(e.nodeValue)}}function u(e){if(e.namespaceURI){var n=c[e.namespaceURI];return(n?n+":":"{"+e.namespaceURI+"}")+e.localName}return e.localName}return e.resolve(d(s.documentElement))};var a=t.Node;return Hr}var ua,la={},ha={},pa={};function fa(){return ua||(ua=1,function(){var e,n,t,i,r,a,o,c=[].slice,s={}.hasOwnProperty;e=function(){var e,n,t,i,a,o;if(o=arguments[0],a=2<=arguments.length?c.call(arguments,1):[],r(Object.assign))Object.assign.apply(null,arguments);else for(e=0,t=a.length;e<t;e++)if(null!=(i=a[e]))for(n in i)s.call(i,n)&&(o[n]=i[n]);return o},r=function(e){return!!e&&"[object Function]"===Object.prototype.toString.call(e)},a=function(e){var n;return!!e&&("function"==(n=typeof e)||"object"===n)},t=function(e){return r(Array.isArray)?Array.isArray(e):"[object Array]"===Object.prototype.toString.call(e)},i=function(e){var n;if(t(e))return!e.length;for(n in e)if(s.call(e,n))return!1;return!0},o=function(e){var n,t;return a(e)&&(t=Object.getPrototypeOf(e))&&(n=t.constructor)&&"function"==typeof n&&n instanceof n&&Function.prototype.toString.call(n)===Function.prototype.toString.call(Object)},n=function(e){return r(e.valueOf)?e.valueOf():e},pa.assign=e,pa.isFunction=r,pa.isObject=a,pa.isArray=t,pa.isEmpty=i,pa.isPlainObject=o,pa.getValue=n}.call(pa)),pa}var ga,ba={exports:{}},ma={exports:{}},ya={exports:{}},xa={exports:{}},Da=xa.exports;function Ua(){return ga||(ga=1,function(){xa.exports=function(){function e(e,n,t){if(this.options=e.options,this.stringify=e.stringify,this.parent=e,null==n)throw new Error("Missing attribute name. "+this.debugInfo(n));if(null==t)throw new Error("Missing attribute value. "+this.debugInfo(n));this.name=this.stringify.attName(n),this.value=this.stringify.attValue(t)}return e.prototype.clone=function(){return Object.create(this)},e.prototype.toString=function(e){return this.options.writer.set(e).attribute(this)},e.prototype.debugInfo=function(e){return null==(e=e||this.name)?"parent: <"+this.parent.name+">":"attribute: {"+e+"}, parent: <"+this.parent.name+">"},e}()}.call(Da)),xa.exports}var va,Ta=ya.exports;function _a(){return va||(va=1,function(){var e,n,t,i,r,a,o={}.hasOwnProperty;a=fa(),r=a.isObject,i=a.isFunction,t=a.getValue,n=vo(),e=Ua(),ya.exports=function(n){function a(e,n,t){if(a.__super__.constructor.call(this,e),null==n)throw new Error("Missing element name. "+this.debugInfo());this.name=this.stringify.eleName(n),this.attributes={},null!=t&&this.attribute(t),e.isDocument&&(this.isRoot=!0,this.documentObject=e,e.rootObject=this)}return function(e,n){for(var t in n)o.call(n,t)&&(e[t]=n[t]);function i(){this.constructor=e}i.prototype=n.prototype,e.prototype=new i,e.__super__=n.prototype}(a,n),a.prototype.clone=function(){var e,n,t,i;for(n in(t=Object.create(this)).isRoot&&(t.documentObject=null),t.attributes={},i=this.attributes)o.call(i,n)&&(e=i[n],t.attributes[n]=e.clone());return t.children=[],this.children.forEach(function(e){var n;return(n=e.clone()).parent=t,t.children.push(n)}),t},a.prototype.attribute=function(n,a){var c,s;if(null!=n&&(n=t(n)),r(n))for(c in n)o.call(n,c)&&(s=n[c],this.attribute(c,s));else i(a)&&(a=a.apply()),this.options.skipNullAttributes&&null==a||(this.attributes[n]=new e(this,n,a));return this},a.prototype.removeAttribute=function(e){var n,i,r;if(null==e)throw new Error("Missing attribute name. "+this.debugInfo());if(e=t(e),Array.isArray(e))for(i=0,r=e.length;i<r;i++)n=e[i],delete this.attributes[n];else delete this.attributes[e];return this},a.prototype.toString=function(e){return this.options.writer.set(e).element(this)},a.prototype.att=function(e,n){return this.attribute(e,n)},a.prototype.a=function(e,n){return this.attribute(e,n)},a}(n)}.call(Ta)),ya.exports}var wa,Fa={exports:{}},Ea=Fa.exports;function Wa(){return wa||(wa=1,function(){var e,n={}.hasOwnProperty;e=vo(),Fa.exports=function(e){function t(e,n){if(t.__super__.constructor.call(this,e),null==n)throw new Error("Missing CDATA text. "+this.debugInfo());this.text=this.stringify.cdata(n)}return function(e,t){for(var i in t)n.call(t,i)&&(e[i]=t[i]);function r(){this.constructor=e}r.prototype=t.prototype,e.prototype=new r,e.__super__=t.prototype}(t,e),t.prototype.clone=function(){return Object.create(this)},t.prototype.toString=function(e){return this.options.writer.set(e).cdata(this)},t}(e)}.call(Ea)),Fa.exports}var Ca,Aa={exports:{}},Sa=Aa.exports;function Na(){return Ca||(Ca=1,function(){var e,n={}.hasOwnProperty;e=vo(),Aa.exports=function(e){function t(e,n){if(t.__super__.constructor.call(this,e),null==n)throw new Error("Missing comment text. "+this.debugInfo());this.text=this.stringify.comment(n)}return function(e,t){for(var i in t)n.call(t,i)&&(e[i]=t[i]);function r(){this.constructor=e}r.prototype=t.prototype,e.prototype=new r,e.__super__=t.prototype}(t,e),t.prototype.clone=function(){return Object.create(this)},t.prototype.toString=function(e){return this.options.writer.set(e).comment(this)},t}(e)}.call(Sa)),Aa.exports}var Ba,ka={exports:{}},Oa=ka.exports;function Ia(){return Ba||(Ba=1,function(){var e,n,t={}.hasOwnProperty;n=fa().isObject,e=vo(),ka.exports=function(e){function i(e,t,r,a){var o;i.__super__.constructor.call(this,e),n(t)&&(t=(o=t).version,r=o.encoding,a=o.standalone),t||(t="1.0"),this.version=this.stringify.xmlVersion(t),null!=r&&(this.encoding=this.stringify.xmlEncoding(r)),null!=a&&(this.standalone=this.stringify.xmlStandalone(a))}return function(e,n){for(var i in n)t.call(n,i)&&(e[i]=n[i]);function r(){this.constructor=e}r.prototype=n.prototype,e.prototype=new r,e.__super__=n.prototype}(i,e),i.prototype.toString=function(e){return this.options.writer.set(e).declaration(this)},i}(e)}.call(Oa)),ka.exports}var Ra,ja={exports:{}},Pa={exports:{}},La=Pa.exports;function qa(){return Ra||(Ra=1,function(){var e,n={}.hasOwnProperty;e=vo(),Pa.exports=function(e){function t(e,n,i,r,a,o){if(t.__super__.constructor.call(this,e),null==n)throw new Error("Missing DTD element name. "+this.debugInfo());if(null==i)throw new Error("Missing DTD attribute name. "+this.debugInfo(n));if(!r)throw new Error("Missing DTD attribute type. "+this.debugInfo(n));if(!a)throw new Error("Missing DTD attribute default. "+this.debugInfo(n));if(0!==a.indexOf("#")&&(a="#"+a),!a.match(/^(#REQUIRED|#IMPLIED|#FIXED|#DEFAULT)$/))throw new Error("Invalid default value type; expected: #REQUIRED, #IMPLIED, #FIXED or #DEFAULT. "+this.debugInfo(n));if(o&&!a.match(/^(#FIXED|#DEFAULT)$/))throw new Error("Default value only applies to #FIXED or #DEFAULT. "+this.debugInfo(n));this.elementName=this.stringify.eleName(n),this.attributeName=this.stringify.attName(i),this.attributeType=this.stringify.dtdAttType(r),this.defaultValue=this.stringify.dtdAttDefault(o),this.defaultValueType=a}return function(e,t){for(var i in t)n.call(t,i)&&(e[i]=t[i]);function r(){this.constructor=e}r.prototype=t.prototype,e.prototype=new r,e.__super__=t.prototype}(t,e),t.prototype.toString=function(e){return this.options.writer.set(e).dtdAttList(this)},t}(e)}.call(La)),Pa.exports}var Ma,Va={exports:{}},Ha=Va.exports;function za(){return Ma||(Ma=1,function(){var e,n,t={}.hasOwnProperty;n=fa().isObject,e=vo(),Va.exports=function(e){function i(e,t,r,a){if(i.__super__.constructor.call(this,e),null==r)throw new Error("Missing DTD entity name. "+this.debugInfo(r));if(null==a)throw new Error("Missing DTD entity value. "+this.debugInfo(r));if(this.pe=!!t,this.name=this.stringify.eleName(r),n(a)){if(!a.pubID&&!a.sysID)throw new Error("Public and/or system identifiers are required for an external entity. "+this.debugInfo(r));if(a.pubID&&!a.sysID)throw new Error("System identifier is required for a public external entity. "+this.debugInfo(r));if(null!=a.pubID&&(this.pubID=this.stringify.dtdPubID(a.pubID)),null!=a.sysID&&(this.sysID=this.stringify.dtdSysID(a.sysID)),null!=a.nData&&(this.nData=this.stringify.dtdNData(a.nData)),this.pe&&this.nData)throw new Error("Notation declaration is not allowed in a parameter entity. "+this.debugInfo(r))}else this.value=this.stringify.dtdEntityValue(a)}return function(e,n){for(var i in n)t.call(n,i)&&(e[i]=n[i]);function r(){this.constructor=e}r.prototype=n.prototype,e.prototype=new r,e.__super__=n.prototype}(i,e),i.prototype.toString=function(e){return this.options.writer.set(e).dtdEntity(this)},i}(e)}.call(Ha)),Va.exports}var Ga,Xa={exports:{}},$a=Xa.exports;function Ka(){return Ga||(Ga=1,function(){var e,n={}.hasOwnProperty;e=vo(),Xa.exports=function(e){function t(e,n,i){if(t.__super__.constructor.call(this,e),null==n)throw new Error("Missing DTD element name. "+this.debugInfo());i||(i="(#PCDATA)"),Array.isArray(i)&&(i="("+i.join(",")+")"),this.name=this.stringify.eleName(n),this.value=this.stringify.dtdElementValue(i)}return function(e,t){for(var i in t)n.call(t,i)&&(e[i]=t[i]);function r(){this.constructor=e}r.prototype=t.prototype,e.prototype=new r,e.__super__=t.prototype}(t,e),t.prototype.toString=function(e){return this.options.writer.set(e).dtdElement(this)},t}(e)}.call($a)),Xa.exports}var Qa,Ya={exports:{}},Za=Ya.exports;function Ja(){return Qa||(Qa=1,function(){var e,n={}.hasOwnProperty;e=vo(),Ya.exports=function(e){function t(e,n,i){if(t.__super__.constructor.call(this,e),null==n)throw new Error("Missing DTD notation name. "+this.debugInfo(n));if(!i.pubID&&!i.sysID)throw new Error("Public or system identifiers are required for an external entity. "+this.debugInfo(n));this.name=this.stringify.eleName(n),null!=i.pubID&&(this.pubID=this.stringify.dtdPubID(i.pubID)),null!=i.sysID&&(this.sysID=this.stringify.dtdSysID(i.sysID))}return function(e,t){for(var i in t)n.call(t,i)&&(e[i]=t[i]);function r(){this.constructor=e}r.prototype=t.prototype,e.prototype=new r,e.__super__=t.prototype}(t,e),t.prototype.toString=function(e){return this.options.writer.set(e).dtdNotation(this)},t}(e)}.call(Za)),Ya.exports}var eo,no=ja.exports;function to(){return eo||(eo=1,function(){var e,n,t,i,r,a,o={}.hasOwnProperty;a=fa().isObject,r=vo(),e=qa(),t=za(),n=Ka(),i=Ja(),ja.exports=function(r){function c(e,n,t){var i,r;c.__super__.constructor.call(this,e),this.name="!DOCTYPE",this.documentObject=e,a(n)&&(n=(i=n).pubID,t=i.sysID),null==t&&(t=(r=[n,t])[0],n=r[1]),null!=n&&(this.pubID=this.stringify.dtdPubID(n)),null!=t&&(this.sysID=this.stringify.dtdSysID(t))}return function(e,n){for(var t in n)o.call(n,t)&&(e[t]=n[t]);function i(){this.constructor=e}i.prototype=n.prototype,e.prototype=new i,e.__super__=n.prototype}(c,r),c.prototype.element=function(e,t){var i;return i=new n(this,e,t),this.children.push(i),this},c.prototype.attList=function(n,t,i,r,a){var o;return o=new e(this,n,t,i,r,a),this.children.push(o),this},c.prototype.entity=function(e,n){var i;return i=new t(this,!1,e,n),this.children.push(i),this},c.prototype.pEntity=function(e,n){var i;return i=new t(this,!0,e,n),this.children.push(i),this},c.prototype.notation=function(e,n){var t;return t=new i(this,e,n),this.children.push(t),this},c.prototype.toString=function(e){return this.options.writer.set(e).docType(this)},c.prototype.ele=function(e,n){return this.element(e,n)},c.prototype.att=function(e,n,t,i,r){return this.attList(e,n,t,i,r)},c.prototype.ent=function(e,n){return this.entity(e,n)},c.prototype.pent=function(e,n){return this.pEntity(e,n)},c.prototype.not=function(e,n){return this.notation(e,n)},c.prototype.up=function(){return this.root()||this.documentObject},c}(r)}.call(no)),ja.exports}var io,ro={exports:{}},ao=ro.exports;function oo(){return io||(io=1,function(){var e,n={}.hasOwnProperty;e=vo(),ro.exports=function(e){function t(e,n){if(t.__super__.constructor.call(this,e),null==n)throw new Error("Missing raw text. "+this.debugInfo());this.value=this.stringify.raw(n)}return function(e,t){for(var i in t)n.call(t,i)&&(e[i]=t[i]);function r(){this.constructor=e}r.prototype=t.prototype,e.prototype=new r,e.__super__=t.prototype}(t,e),t.prototype.clone=function(){return Object.create(this)},t.prototype.toString=function(e){return this.options.writer.set(e).raw(this)},t}(e)}.call(ao)),ro.exports}var co,so={exports:{}},uo=so.exports;function lo(){return co||(co=1,function(){var e,n={}.hasOwnProperty;e=vo(),so.exports=function(e){function t(e,n){if(t.__super__.constructor.call(this,e),null==n)throw new Error("Missing element text. "+this.debugInfo());this.value=this.stringify.eleText(n)}return function(e,t){for(var i in t)n.call(t,i)&&(e[i]=t[i]);function r(){this.constructor=e}r.prototype=t.prototype,e.prototype=new r,e.__super__=t.prototype}(t,e),t.prototype.clone=function(){return Object.create(this)},t.prototype.toString=function(e){return this.options.writer.set(e).text(this)},t}(e)}.call(uo)),so.exports}var ho,po={exports:{}},fo=po.exports;function go(){return ho||(ho=1,function(){var e,n={}.hasOwnProperty;e=vo(),po.exports=function(e){function t(e,n,i){if(t.__super__.constructor.call(this,e),null==n)throw new Error("Missing instruction target. "+this.debugInfo());this.target=this.stringify.insTarget(n),i&&(this.value=this.stringify.insValue(i))}return function(e,t){for(var i in t)n.call(t,i)&&(e[i]=t[i]);function r(){this.constructor=e}r.prototype=t.prototype,e.prototype=new r,e.__super__=t.prototype}(t,e),t.prototype.clone=function(){return Object.create(this)},t.prototype.toString=function(e){return this.options.writer.set(e).processingInstruction(this)},t}(e)}.call(fo)),po.exports}var bo,mo={exports:{}},yo=mo.exports;function xo(){return bo||(bo=1,function(){var e,n={}.hasOwnProperty;e=vo(),mo.exports=function(e){function t(e){t.__super__.constructor.call(this,e),this.isDummy=!0}return function(e,t){for(var i in t)n.call(t,i)&&(e[i]=t[i]);function r(){this.constructor=e}r.prototype=t.prototype,e.prototype=new r,e.__super__=t.prototype}(t,e),t.prototype.clone=function(){return Object.create(this)},t.prototype.toString=function(e){return""},t}(e)}.call(yo)),mo.exports}var Do,Uo=ma.exports;function vo(){return Do||(Do=1,function(){var e,n,t,i,r,a,o,c,s,d,u,l,h,p,f={}.hasOwnProperty;p=fa(),h=p.isObject,l=p.isFunction,u=p.isEmpty,d=p.getValue,a=null,e=null,n=null,t=null,i=null,c=null,s=null,o=null,r=null,ma.exports=function(){function p(d){this.parent=d,this.parent&&(this.options=this.parent.options,this.stringify=this.parent.stringify),this.children=[],a||(a=_a(),e=Wa(),n=Na(),t=Ia(),i=to(),c=oo(),s=lo(),o=go(),r=xo())}return p.prototype.element=function(e,n,t){var i,r,a,o,c,s,p,g,b,m,y;if(s=null,null===n&&null==t&&(n=(b=[{},null])[0],t=b[1]),null==n&&(n={}),n=d(n),h(n)||(t=(m=[n,t])[0],n=m[1]),null!=e&&(e=d(e)),Array.isArray(e))for(a=0,p=e.length;a<p;a++)r=e[a],s=this.element(r);else if(l(e))s=this.element(e.apply());else if(h(e)){for(c in e)if(f.call(e,c))if(y=e[c],l(y)&&(y=y.apply()),h(y)&&u(y)&&(y=null),!this.options.ignoreDecorators&&this.stringify.convertAttKey&&0===c.indexOf(this.stringify.convertAttKey))s=this.attribute(c.substr(this.stringify.convertAttKey.length),y);else if(!this.options.separateArrayItems&&Array.isArray(y))for(o=0,g=y.length;o<g;o++)r=y[o],(i={})[c]=r,s=this.element(i);else h(y)?(s=this.element(c)).element(y):s=this.element(c,y)}else s=this.options.skipNullNodes&&null===t?this.dummy():!this.options.ignoreDecorators&&this.stringify.convertTextKey&&0===e.indexOf(this.stringify.convertTextKey)?this.text(t):!this.options.ignoreDecorators&&this.stringify.convertCDataKey&&0===e.indexOf(this.stringify.convertCDataKey)?this.cdata(t):!this.options.ignoreDecorators&&this.stringify.convertCommentKey&&0===e.indexOf(this.stringify.convertCommentKey)?this.comment(t):!this.options.ignoreDecorators&&this.stringify.convertRawKey&&0===e.indexOf(this.stringify.convertRawKey)?this.raw(t):!this.options.ignoreDecorators&&this.stringify.convertPIKey&&0===e.indexOf(this.stringify.convertPIKey)?this.instruction(e.substr(this.stringify.convertPIKey.length),t):this.node(e,n,t);if(null==s)throw new Error("Could not create any elements with: "+e+". "+this.debugInfo());return s},p.prototype.insertBefore=function(e,n,t){var i,r,a;if(this.isRoot)throw new Error("Cannot insert elements at root level. "+this.debugInfo(e));return r=this.parent.children.indexOf(this),a=this.parent.children.splice(r),i=this.parent.element(e,n,t),Array.prototype.push.apply(this.parent.children,a),i},p.prototype.insertAfter=function(e,n,t){var i,r,a;if(this.isRoot)throw new Error("Cannot insert elements at root level. "+this.debugInfo(e));return r=this.parent.children.indexOf(this),a=this.parent.children.splice(r+1),i=this.parent.element(e,n,t),Array.prototype.push.apply(this.parent.children,a),i},p.prototype.remove=function(){var e;if(this.isRoot)throw new Error("Cannot remove the root element. "+this.debugInfo());return e=this.parent.children.indexOf(this),[].splice.apply(this.parent.children,[e,e-e+1].concat([])),this.parent},p.prototype.node=function(e,n,t){var i,r;return null!=e&&(e=d(e)),n||(n={}),n=d(n),h(n)||(t=(r=[n,t])[0],n=r[1]),i=new a(this,e,n),null!=t&&i.text(t),this.children.push(i),i},p.prototype.text=function(e){var n;return n=new s(this,e),this.children.push(n),this},p.prototype.cdata=function(n){var t;return t=new e(this,n),this.children.push(t),this},p.prototype.comment=function(e){var t;return t=new n(this,e),this.children.push(t),this},p.prototype.commentBefore=function(e){var n,t;return n=this.parent.children.indexOf(this),t=this.parent.children.splice(n),this.parent.comment(e),Array.prototype.push.apply(this.parent.children,t),this},p.prototype.commentAfter=function(e){var n,t;return n=this.parent.children.indexOf(this),t=this.parent.children.splice(n+1),this.parent.comment(e),Array.prototype.push.apply(this.parent.children,t),this},p.prototype.raw=function(e){var n;return n=new c(this,e),this.children.push(n),this},p.prototype.dummy=function(){var e;return e=new r(this),this.children.push(e),e},p.prototype.instruction=function(e,n){var t,i,r,a,c;if(null!=e&&(e=d(e)),null!=n&&(n=d(n)),Array.isArray(e))for(a=0,c=e.length;a<c;a++)t=e[a],this.instruction(t);else if(h(e))for(t in e)f.call(e,t)&&(i=e[t],this.instruction(t,i));else l(n)&&(n=n.apply()),r=new o(this,e,n),this.children.push(r);return this},p.prototype.instructionBefore=function(e,n){var t,i;return t=this.parent.children.indexOf(this),i=this.parent.children.splice(t),this.parent.instruction(e,n),Array.prototype.push.apply(this.parent.children,i),this},p.prototype.instructionAfter=function(e,n){var t,i;return t=this.parent.children.indexOf(this),i=this.parent.children.splice(t+1),this.parent.instruction(e,n),Array.prototype.push.apply(this.parent.children,i),this},p.prototype.declaration=function(e,n,i){var r,a;return r=this.document(),a=new t(r,e,n,i),r.children[0]instanceof t?r.children[0]=a:r.children.unshift(a),r.root()||r},p.prototype.doctype=function(e,n){var t,r,a,o,c,s,d,u,l;for(t=this.document(),r=new i(t,e,n),a=o=0,s=(u=t.children).length;o<s;a=++o)if(u[a]instanceof i)return t.children[a]=r,r;for(a=c=0,d=(l=t.children).length;c<d;a=++c)if(l[a].isRoot)return t.children.splice(a,0,r),r;return t.children.push(r),r},p.prototype.up=function(){if(this.isRoot)throw new Error("The root node has no parent. Use doc() if you need to get the document object.");return this.parent},p.prototype.root=function(){var e;for(e=this;e;){if(e.isDocument)return e.rootObject;if(e.isRoot)return e;e=e.parent}},p.prototype.document=function(){var e;for(e=this;e;){if(e.isDocument)return e;e=e.parent}},p.prototype.end=function(e){return this.document().end(e)},p.prototype.prev=function(){var e;for(e=this.parent.children.indexOf(this);e>0&&this.parent.children[e-1].isDummy;)e-=1;if(e<1)throw new Error("Already at the first node. "+this.debugInfo());return this.parent.children[e-1]},p.prototype.next=function(){var e;for(e=this.parent.children.indexOf(this);e<this.parent.children.length-1&&this.parent.children[e+1].isDummy;)e+=1;if(-1===e||e===this.parent.children.length-1)throw new Error("Already at the last node. "+this.debugInfo());return this.parent.children[e+1]},p.prototype.importDocument=function(e){var n;return(n=e.root().clone()).parent=this,n.isRoot=!1,this.children.push(n),this},p.prototype.debugInfo=function(e){var n,t;return null!=(e=e||this.name)||(null!=(n=this.parent)?n.name:void 0)?null==e?"parent: <"+this.parent.name+">":(null!=(t=this.parent)?t.name:void 0)?"node: <"+e+">, parent: <"+this.parent.name+">":"node: <"+e+">":""},p.prototype.ele=function(e,n,t){return this.element(e,n,t)},p.prototype.nod=function(e,n,t){return this.node(e,n,t)},p.prototype.txt=function(e){return this.text(e)},p.prototype.dat=function(e){return this.cdata(e)},p.prototype.com=function(e){return this.comment(e)},p.prototype.ins=function(e,n){return this.instruction(e,n)},p.prototype.doc=function(){return this.document()},p.prototype.dec=function(e,n,t){return this.declaration(e,n,t)},p.prototype.dtd=function(e,n){return this.doctype(e,n)},p.prototype.e=function(e,n,t){return this.element(e,n,t)},p.prototype.n=function(e,n,t){return this.node(e,n,t)},p.prototype.t=function(e){return this.text(e)},p.prototype.d=function(e){return this.cdata(e)},p.prototype.c=function(e){return this.comment(e)},p.prototype.r=function(e){return this.raw(e)},p.prototype.i=function(e,n){return this.instruction(e,n)},p.prototype.u=function(){return this.up()},p.prototype.importXMLBuilder=function(e){return this.importDocument(e)},p}()}.call(Uo)),ma.exports}var To,_o={exports:{}},wo=_o.exports;function Fo(){return To||(To=1,function(){var e={}.hasOwnProperty;_o.exports=function(){function n(n){var t,i,r,a,o;for(r in this.assertLegalChar=(t=this.assertLegalChar,i=this,function(){return t.apply(i,arguments)}),n||(n={}),this.noDoubleEncoding=n.noDoubleEncoding,a=n.stringify||{})e.call(a,r)&&(o=a[r],this[r]=o)}return n.prototype.eleName=function(e){return e=""+e||"",this.assertLegalChar(e)},n.prototype.eleText=function(e){return e=""+e||"",this.assertLegalChar(this.elEscape(e))},n.prototype.cdata=function(e){return e=(e=""+e||"").replace("]]>","]]]]><![CDATA[>"),this.assertLegalChar(e)},n.prototype.comment=function(e){if((e=""+e||"").match(/--/))throw new Error("Comment text cannot contain double-hypen: "+e);return this.assertLegalChar(e)},n.prototype.raw=function(e){return""+e||""},n.prototype.attName=function(e){return""+e||""},n.prototype.attValue=function(e){return e=""+e||"",this.attEscape(e)},n.prototype.insTarget=function(e){return""+e||""},n.prototype.insValue=function(e){if((e=""+e||"").match(/\?>/))throw new Error("Invalid processing instruction value: "+e);return e},n.prototype.xmlVersion=function(e){if(!(e=""+e||"").match(/1\.[0-9]+/))throw new Error("Invalid version number: "+e);return e},n.prototype.xmlEncoding=function(e){if(!(e=""+e||"").match(/^[A-Za-z](?:[A-Za-z0-9._-])*$/))throw new Error("Invalid encoding: "+e);return e},n.prototype.xmlStandalone=function(e){return e?"yes":"no"},n.prototype.dtdPubID=function(e){return""+e||""},n.prototype.dtdSysID=function(e){return""+e||""},n.prototype.dtdElementValue=function(e){return""+e||""},n.prototype.dtdAttType=function(e){return""+e||""},n.prototype.dtdAttDefault=function(e){return null!=e?""+e||"":e},n.prototype.dtdEntityValue=function(e){return""+e||""},n.prototype.dtdNData=function(e){return""+e||""},n.prototype.convertAttKey="@",n.prototype.convertPIKey="?",n.prototype.convertTextKey="#text",n.prototype.convertCDataKey="#cdata",n.prototype.convertCommentKey="#comment",n.prototype.convertRawKey="#raw",n.prototype.assertLegalChar=function(e){var n;if(n=e.match(/[\0\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/))throw new Error("Invalid character in string: "+e+" at index "+n.index);return e},n.prototype.elEscape=function(e){var n;return n=this.noDoubleEncoding?/(?!&\S+;)&/g:/&/g,e.replace(n,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\r/g,"&#xD;")},n.prototype.attEscape=function(e){var n;return n=this.noDoubleEncoding?/(?!&\S+;)&/g:/&/g,e.replace(n,"&amp;").replace(/</g,"&lt;").replace(/"/g,"&quot;").replace(/\t/g,"&#x9;").replace(/\n/g,"&#xA;").replace(/\r/g,"&#xD;")},n}()}.call(wo)),_o.exports}var Eo,Wo={exports:{}},Co={exports:{}},Ao=Co.exports;function So(){return Eo||(Eo=1,function(){var e={}.hasOwnProperty;Co.exports=function(){function n(n){var t,i,r,a,o,c,s,d,u;for(t in n||(n={}),this.pretty=n.pretty||!1,this.allowEmpty=null!=(i=n.allowEmpty)&&i,this.pretty?(this.indent=null!=(r=n.indent)?r:"  ",this.newline=null!=(a=n.newline)?a:"\n",this.offset=null!=(o=n.offset)?o:0,this.dontprettytextnodes=null!=(c=n.dontprettytextnodes)?c:0):(this.indent="",this.newline="",this.offset=0,this.dontprettytextnodes=0),this.spacebeforeslash=null!=(s=n.spacebeforeslash)?s:"",!0===this.spacebeforeslash&&(this.spacebeforeslash=" "),this.newlinedefault=this.newline,this.prettydefault=this.pretty,d=n.writer||{})e.call(d,t)&&(u=d[t],this[t]=u)}return n.prototype.set=function(n){var t,i,r;for(t in n||(n={}),"pretty"in n&&(this.pretty=n.pretty),"allowEmpty"in n&&(this.allowEmpty=n.allowEmpty),this.pretty?(this.indent="indent"in n?n.indent:"  ",this.newline="newline"in n?n.newline:"\n",this.offset="offset"in n?n.offset:0,this.dontprettytextnodes="dontprettytextnodes"in n?n.dontprettytextnodes:0):(this.indent="",this.newline="",this.offset=0,this.dontprettytextnodes=0),this.spacebeforeslash="spacebeforeslash"in n?n.spacebeforeslash:"",!0===this.spacebeforeslash&&(this.spacebeforeslash=" "),this.newlinedefault=this.newline,this.prettydefault=this.pretty,i=n.writer||{})e.call(i,t)&&(r=i[t],this[t]=r);return this},n.prototype.space=function(e){var n;return this.pretty&&(n=(e||0)+this.offset+1)>0?new Array(n).join(this.indent):""},n}()}.call(Ao)),Co.exports}var No,Bo=Wo.exports;function ko(){return No||(No=1,function(){var e,n,t,i,r,a,o,c,s,d,u,l,h,p,f={}.hasOwnProperty;o=Ia(),c=to(),e=Wa(),n=Na(),d=_a(),l=oo(),h=lo(),u=go(),s=xo(),t=qa(),i=Ka(),r=za(),a=Ja(),p=So(),Wo.exports=function(p){function g(e){g.__super__.constructor.call(this,e)}return function(e,n){for(var t in n)f.call(n,t)&&(e[t]=n[t]);function i(){this.constructor=e}i.prototype=n.prototype,e.prototype=new i,e.__super__=n.prototype}(g,p),g.prototype.document=function(e){var t,i,r,a,d;for(this.textispresent=!1,a="",i=0,r=(d=e.children).length;i<r;i++)(t=d[i])instanceof s||(a+=function(){switch(!1){case!(t instanceof o):return this.declaration(t);case!(t instanceof c):return this.docType(t);case!(t instanceof n):return this.comment(t);case!(t instanceof u):return this.processingInstruction(t);default:return this.element(t,0)}}.call(this));return this.pretty&&a.slice(-this.newline.length)===this.newline&&(a=a.slice(0,-this.newline.length)),a},g.prototype.attribute=function(e){return" "+e.name+'="'+e.value+'"'},g.prototype.cdata=function(e,n){return this.space(n)+"<![CDATA["+e.text+"]]>"+this.newline},g.prototype.comment=function(e,n){return this.space(n)+"\x3c!-- "+e.text+" --\x3e"+this.newline},g.prototype.declaration=function(e,n){var t;return t=this.space(n),t+='<?xml version="'+e.version+'"',null!=e.encoding&&(t+=' encoding="'+e.encoding+'"'),null!=e.standalone&&(t+=' standalone="'+e.standalone+'"'),t+=this.spacebeforeslash+"?>",t+=this.newline},g.prototype.docType=function(o,c){var s,d,l,h,p;if(c||(c=0),h=this.space(c),h+="<!DOCTYPE "+o.root().name,o.pubID&&o.sysID?h+=' PUBLIC "'+o.pubID+'" "'+o.sysID+'"':o.sysID&&(h+=' SYSTEM "'+o.sysID+'"'),o.children.length>0){for(h+=" [",h+=this.newline,d=0,l=(p=o.children).length;d<l;d++)s=p[d],h+=function(){switch(!1){case!(s instanceof t):return this.dtdAttList(s,c+1);case!(s instanceof i):return this.dtdElement(s,c+1);case!(s instanceof r):return this.dtdEntity(s,c+1);case!(s instanceof a):return this.dtdNotation(s,c+1);case!(s instanceof e):return this.cdata(s,c+1);case!(s instanceof n):return this.comment(s,c+1);case!(s instanceof u):return this.processingInstruction(s,c+1);default:throw new Error("Unknown DTD node type: "+s.constructor.name)}}.call(this);h+="]"}return h+=this.spacebeforeslash+">",h+=this.newline},g.prototype.element=function(t,i){var r,a,o,c,p,g,b,m,y,x,D,U,v;for(b in i||(i=0),v=!1,this.textispresent?(this.newline="",this.pretty=!1):(this.newline=this.newlinedefault,this.pretty=this.prettydefault),m="",m+=(U=this.space(i))+"<"+t.name,y=t.attributes)f.call(y,b)&&(r=y[b],m+=this.attribute(r));if(0===t.children.length||t.children.every(function(e){return""===e.value}))this.allowEmpty?m+="></"+t.name+">"+this.newline:m+=this.spacebeforeslash+"/>"+this.newline;else if(this.pretty&&1===t.children.length&&null!=t.children[0].value)m+=">",m+=t.children[0].value,m+="</"+t.name+">"+this.newline;else{if(this.dontprettytextnodes)for(o=0,p=(x=t.children).length;o<p;o++)if(null!=(a=x[o]).value){this.textispresent++,v=!0;break}for(this.textispresent&&(this.newline="",this.pretty=!1,U=this.space(i)),m+=">"+this.newline,c=0,g=(D=t.children).length;c<g;c++)a=D[c],m+=function(){switch(!1){case!(a instanceof e):return this.cdata(a,i+1);case!(a instanceof n):return this.comment(a,i+1);case!(a instanceof d):return this.element(a,i+1);case!(a instanceof l):return this.raw(a,i+1);case!(a instanceof h):return this.text(a,i+1);case!(a instanceof u):return this.processingInstruction(a,i+1);case!(a instanceof s):return"";default:throw new Error("Unknown XML node type: "+a.constructor.name)}}.call(this);v&&this.textispresent--,this.textispresent||(this.newline=this.newlinedefault,this.pretty=this.prettydefault),m+=U+"</"+t.name+">"+this.newline}return m},g.prototype.processingInstruction=function(e,n){var t;return t=this.space(n)+"<?"+e.target,e.value&&(t+=" "+e.value),t+=this.spacebeforeslash+"?>"+this.newline},g.prototype.raw=function(e,n){return this.space(n)+e.value+this.newline},g.prototype.text=function(e,n){return this.space(n)+e.value+this.newline},g.prototype.dtdAttList=function(e,n){var t;return t=this.space(n)+"<!ATTLIST "+e.elementName+" "+e.attributeName+" "+e.attributeType,"#DEFAULT"!==e.defaultValueType&&(t+=" "+e.defaultValueType),e.defaultValue&&(t+=' "'+e.defaultValue+'"'),t+=this.spacebeforeslash+">"+this.newline},g.prototype.dtdElement=function(e,n){return this.space(n)+"<!ELEMENT "+e.name+" "+e.value+this.spacebeforeslash+">"+this.newline},g.prototype.dtdEntity=function(e,n){var t;return t=this.space(n)+"<!ENTITY",e.pe&&(t+=" %"),t+=" "+e.name,e.value?t+=' "'+e.value+'"':(e.pubID&&e.sysID?t+=' PUBLIC "'+e.pubID+'" "'+e.sysID+'"':e.sysID&&(t+=' SYSTEM "'+e.sysID+'"'),e.nData&&(t+=" NDATA "+e.nData)),t+=this.spacebeforeslash+">"+this.newline},g.prototype.dtdNotation=function(e,n){var t;return t=this.space(n)+"<!NOTATION "+e.name,e.pubID&&e.sysID?t+=' PUBLIC "'+e.pubID+'" "'+e.sysID+'"':e.pubID?t+=' PUBLIC "'+e.pubID+'"':e.sysID&&(t+=' SYSTEM "'+e.sysID+'"'),t+=this.spacebeforeslash+">"+this.newline},g.prototype.openNode=function(e,n){var t,i,r,a;if(n||(n=0),e instanceof d){for(i in r=this.space(n)+"<"+e.name,a=e.attributes)f.call(a,i)&&(t=a[i],r+=this.attribute(t));return r+=(e.children?">":"/>")+this.newline}return r=this.space(n)+"<!DOCTYPE "+e.rootNodeName,e.pubID&&e.sysID?r+=' PUBLIC "'+e.pubID+'" "'+e.sysID+'"':e.sysID&&(r+=' SYSTEM "'+e.sysID+'"'),r+=(e.children?" [":">")+this.newline},g.prototype.closeNode=function(e,n){switch(n||(n=0),!1){case!(e instanceof d):return this.space(n)+"</"+e.name+">"+this.newline;case!(e instanceof c):return this.space(n)+"]>"+this.newline}},g}(p)}.call(Bo)),Wo.exports}var Oo,Io=ba.exports;function Ro(){return Oo||(Oo=1,function(){var e,n,t,i,r={}.hasOwnProperty;i=fa().isPlainObject,e=vo(),t=Fo(),n=ko(),ba.exports=function(e){function a(e){a.__super__.constructor.call(this,null),this.name="?xml",e||(e={}),e.writer||(e.writer=new n),this.options=e,this.stringify=new t(e),this.isDocument=!0}return function(e,n){for(var t in n)r.call(n,t)&&(e[t]=n[t]);function i(){this.constructor=e}i.prototype=n.prototype,e.prototype=new i,e.__super__=n.prototype}(a,e),a.prototype.end=function(e){var n;return e?i(e)&&(n=e,e=this.options.writer.set(n)):e=this.options.writer,e.document(this)},a.prototype.toString=function(e){return this.options.writer.set(e).document(this)},a}(e)}.call(Io)),ba.exports}var jo,Po={exports:{}},Lo=Po.exports;var qo,Mo,Vo,Ho,zo,Go={exports:{}},Xo=Go.exports;function $o(){return qo||(qo=1,function(){var e,n,t,i,r,a,o,c,s,d,u,l,h,p,f={}.hasOwnProperty;o=Ia(),c=to(),e=Wa(),n=Na(),d=_a(),l=oo(),h=lo(),u=go(),s=xo(),t=qa(),i=Ka(),r=za(),a=Ja(),p=So(),Go.exports=function(p){function g(e,n){g.__super__.constructor.call(this,n),this.stream=e}return function(e,n){for(var t in n)f.call(n,t)&&(e[t]=n[t]);function i(){this.constructor=e}i.prototype=n.prototype,e.prototype=new i,e.__super__=n.prototype}(g,p),g.prototype.document=function(e){var t,i,r,a,d,l,h,p;for(i=0,a=(l=e.children).length;i<a;i++)(t=l[i]).isLastRootNode=!1;for(e.children[e.children.length-1].isLastRootNode=!0,p=[],r=0,d=(h=e.children).length;r<d;r++)if(!((t=h[r])instanceof s))switch(!1){case!(t instanceof o):p.push(this.declaration(t));break;case!(t instanceof c):p.push(this.docType(t));break;case!(t instanceof n):p.push(this.comment(t));break;case!(t instanceof u):p.push(this.processingInstruction(t));break;default:p.push(this.element(t))}return p},g.prototype.attribute=function(e){return this.stream.write(" "+e.name+'="'+e.value+'"')},g.prototype.cdata=function(e,n){return this.stream.write(this.space(n)+"<![CDATA["+e.text+"]]>"+this.endline(e))},g.prototype.comment=function(e,n){return this.stream.write(this.space(n)+"\x3c!-- "+e.text+" --\x3e"+this.endline(e))},g.prototype.declaration=function(e,n){return this.stream.write(this.space(n)),this.stream.write('<?xml version="'+e.version+'"'),null!=e.encoding&&this.stream.write(' encoding="'+e.encoding+'"'),null!=e.standalone&&this.stream.write(' standalone="'+e.standalone+'"'),this.stream.write(this.spacebeforeslash+"?>"),this.stream.write(this.endline(e))},g.prototype.docType=function(o,c){var s,d,l,h;if(c||(c=0),this.stream.write(this.space(c)),this.stream.write("<!DOCTYPE "+o.root().name),o.pubID&&o.sysID?this.stream.write(' PUBLIC "'+o.pubID+'" "'+o.sysID+'"'):o.sysID&&this.stream.write(' SYSTEM "'+o.sysID+'"'),o.children.length>0){for(this.stream.write(" ["),this.stream.write(this.endline(o)),d=0,l=(h=o.children).length;d<l;d++)switch(s=h[d],!1){case!(s instanceof t):this.dtdAttList(s,c+1);break;case!(s instanceof i):this.dtdElement(s,c+1);break;case!(s instanceof r):this.dtdEntity(s,c+1);break;case!(s instanceof a):this.dtdNotation(s,c+1);break;case!(s instanceof e):this.cdata(s,c+1);break;case!(s instanceof n):this.comment(s,c+1);break;case!(s instanceof u):this.processingInstruction(s,c+1);break;default:throw new Error("Unknown DTD node type: "+s.constructor.name)}this.stream.write("]")}return this.stream.write(this.spacebeforeslash+">"),this.stream.write(this.endline(o))},g.prototype.element=function(t,i){var r,a,o,c,p,g,b,m;for(p in i||(i=0),m=this.space(i),this.stream.write(m+"<"+t.name),g=t.attributes)f.call(g,p)&&(r=g[p],this.attribute(r));if(0===t.children.length||t.children.every(function(e){return""===e.value}))this.allowEmpty?this.stream.write("></"+t.name+">"):this.stream.write(this.spacebeforeslash+"/>");else if(this.pretty&&1===t.children.length&&null!=t.children[0].value)this.stream.write(">"),this.stream.write(t.children[0].value),this.stream.write("</"+t.name+">");else{for(this.stream.write(">"+this.newline),o=0,c=(b=t.children).length;o<c;o++)switch(a=b[o],!1){case!(a instanceof e):this.cdata(a,i+1);break;case!(a instanceof n):this.comment(a,i+1);break;case!(a instanceof d):this.element(a,i+1);break;case!(a instanceof l):this.raw(a,i+1);break;case!(a instanceof h):this.text(a,i+1);break;case!(a instanceof u):this.processingInstruction(a,i+1);break;case!(a instanceof s):break;default:throw new Error("Unknown XML node type: "+a.constructor.name)}this.stream.write(m+"</"+t.name+">")}return this.stream.write(this.endline(t))},g.prototype.processingInstruction=function(e,n){return this.stream.write(this.space(n)+"<?"+e.target),e.value&&this.stream.write(" "+e.value),this.stream.write(this.spacebeforeslash+"?>"+this.endline(e))},g.prototype.raw=function(e,n){return this.stream.write(this.space(n)+e.value+this.endline(e))},g.prototype.text=function(e,n){return this.stream.write(this.space(n)+e.value+this.endline(e))},g.prototype.dtdAttList=function(e,n){return this.stream.write(this.space(n)+"<!ATTLIST "+e.elementName+" "+e.attributeName+" "+e.attributeType),"#DEFAULT"!==e.defaultValueType&&this.stream.write(" "+e.defaultValueType),e.defaultValue&&this.stream.write(' "'+e.defaultValue+'"'),this.stream.write(this.spacebeforeslash+">"+this.endline(e))},g.prototype.dtdElement=function(e,n){return this.stream.write(this.space(n)+"<!ELEMENT "+e.name+" "+e.value),this.stream.write(this.spacebeforeslash+">"+this.endline(e))},g.prototype.dtdEntity=function(e,n){return this.stream.write(this.space(n)+"<!ENTITY"),e.pe&&this.stream.write(" %"),this.stream.write(" "+e.name),e.value?this.stream.write(' "'+e.value+'"'):(e.pubID&&e.sysID?this.stream.write(' PUBLIC "'+e.pubID+'" "'+e.sysID+'"'):e.sysID&&this.stream.write(' SYSTEM "'+e.sysID+'"'),e.nData&&this.stream.write(" NDATA "+e.nData)),this.stream.write(this.spacebeforeslash+">"+this.endline(e))},g.prototype.dtdNotation=function(e,n){return this.stream.write(this.space(n)+"<!NOTATION "+e.name),e.pubID&&e.sysID?this.stream.write(' PUBLIC "'+e.pubID+'" "'+e.sysID+'"'):e.pubID?this.stream.write(' PUBLIC "'+e.pubID+'"'):e.sysID&&this.stream.write(' SYSTEM "'+e.sysID+'"'),this.stream.write(this.spacebeforeslash+">"+this.endline(e))},g.prototype.endline=function(e){return e.isLastRootNode?"":this.newline},g}(p)}.call(Xo)),Go.exports}function Ko(){return Mo||(Mo=1,function(){var e,n,t,i,r,a,o;o=fa(),r=o.assign,a=o.isFunction,e=Ro(),jo||(jo=1,function(){var e,n,t,i,r,a,o,c,s,d,u,l,h,p,f,g,b,m,y,x,D={}.hasOwnProperty;x=fa(),m=x.isObject,b=x.isFunction,y=x.isPlainObject,g=x.getValue,d=_a(),n=Wa(),t=Na(),l=oo(),f=lo(),u=go(),c=Ia(),s=to(),i=qa(),a=za(),r=Ka(),o=Ja(),e=Ua(),p=Fo(),h=ko(),Po.exports=function(){function x(e,n,t){var i;this.name="?xml",e||(e={}),e.writer?y(e.writer)&&(i=e.writer,e.writer=new h(i)):e.writer=new h(e),this.options=e,this.writer=e.writer,this.stringify=new p(e),this.onDataCallback=n||function(){},this.onEndCallback=t||function(){},this.currentNode=null,this.currentLevel=-1,this.openTags={},this.documentStarted=!1,this.documentCompleted=!1,this.root=null}return x.prototype.node=function(e,n,t){var i,r;if(null==e)throw new Error("Missing node name.");if(this.root&&-1===this.currentLevel)throw new Error("Document can only have one root node. "+this.debugInfo(e));return this.openCurrent(),e=g(e),null===n&&null==t&&(n=(i=[{},null])[0],t=i[1]),null==n&&(n={}),n=g(n),m(n)||(t=(r=[n,t])[0],n=r[1]),this.currentNode=new d(this,e,n),this.currentNode.children=!1,this.currentLevel++,this.openTags[this.currentLevel]=this.currentNode,null!=t&&this.text(t),this},x.prototype.element=function(e,n,t){return this.currentNode&&this.currentNode instanceof s?this.dtdElement.apply(this,arguments):this.node(e,n,t)},x.prototype.attribute=function(n,t){var i,r;if(!this.currentNode||this.currentNode.children)throw new Error("att() can only be used immediately after an ele() call in callback mode. "+this.debugInfo(n));if(null!=n&&(n=g(n)),m(n))for(i in n)D.call(n,i)&&(r=n[i],this.attribute(i,r));else b(t)&&(t=t.apply()),this.options.skipNullAttributes&&null==t||(this.currentNode.attributes[n]=new e(this,n,t));return this},x.prototype.text=function(e){var n;return this.openCurrent(),n=new f(this,e),this.onData(this.writer.text(n,this.currentLevel+1),this.currentLevel+1),this},x.prototype.cdata=function(e){var t;return this.openCurrent(),t=new n(this,e),this.onData(this.writer.cdata(t,this.currentLevel+1),this.currentLevel+1),this},x.prototype.comment=function(e){var n;return this.openCurrent(),n=new t(this,e),this.onData(this.writer.comment(n,this.currentLevel+1),this.currentLevel+1),this},x.prototype.raw=function(e){var n;return this.openCurrent(),n=new l(this,e),this.onData(this.writer.raw(n,this.currentLevel+1),this.currentLevel+1),this},x.prototype.instruction=function(e,n){var t,i,r,a,o;if(this.openCurrent(),null!=e&&(e=g(e)),null!=n&&(n=g(n)),Array.isArray(e))for(t=0,a=e.length;t<a;t++)i=e[t],this.instruction(i);else if(m(e))for(i in e)D.call(e,i)&&(r=e[i],this.instruction(i,r));else b(n)&&(n=n.apply()),o=new u(this,e,n),this.onData(this.writer.processingInstruction(o,this.currentLevel+1),this.currentLevel+1);return this},x.prototype.declaration=function(e,n,t){var i;if(this.openCurrent(),this.documentStarted)throw new Error("declaration() must be the first node.");return i=new c(this,e,n,t),this.onData(this.writer.declaration(i,this.currentLevel+1),this.currentLevel+1),this},x.prototype.doctype=function(e,n,t){if(this.openCurrent(),null==e)throw new Error("Missing root node name.");if(this.root)throw new Error("dtd() must come before the root node.");return this.currentNode=new s(this,n,t),this.currentNode.rootNodeName=e,this.currentNode.children=!1,this.currentLevel++,this.openTags[this.currentLevel]=this.currentNode,this},x.prototype.dtdElement=function(e,n){var t;return this.openCurrent(),t=new r(this,e,n),this.onData(this.writer.dtdElement(t,this.currentLevel+1),this.currentLevel+1),this},x.prototype.attList=function(e,n,t,r,a){var o;return this.openCurrent(),o=new i(this,e,n,t,r,a),this.onData(this.writer.dtdAttList(o,this.currentLevel+1),this.currentLevel+1),this},x.prototype.entity=function(e,n){var t;return this.openCurrent(),t=new a(this,!1,e,n),this.onData(this.writer.dtdEntity(t,this.currentLevel+1),this.currentLevel+1),this},x.prototype.pEntity=function(e,n){var t;return this.openCurrent(),t=new a(this,!0,e,n),this.onData(this.writer.dtdEntity(t,this.currentLevel+1),this.currentLevel+1),this},x.prototype.notation=function(e,n){var t;return this.openCurrent(),t=new o(this,e,n),this.onData(this.writer.dtdNotation(t,this.currentLevel+1),this.currentLevel+1),this},x.prototype.up=function(){if(this.currentLevel<0)throw new Error("The document node has no parent.");return this.currentNode?(this.currentNode.children?this.closeNode(this.currentNode):this.openNode(this.currentNode),this.currentNode=null):this.closeNode(this.openTags[this.currentLevel]),delete this.openTags[this.currentLevel],this.currentLevel--,this},x.prototype.end=function(){for(;this.currentLevel>=0;)this.up();return this.onEnd()},x.prototype.openCurrent=function(){if(this.currentNode)return this.currentNode.children=!0,this.openNode(this.currentNode)},x.prototype.openNode=function(e){if(!e.isOpen)return!this.root&&0===this.currentLevel&&e instanceof d&&(this.root=e),this.onData(this.writer.openNode(e,this.currentLevel),this.currentLevel),e.isOpen=!0},x.prototype.closeNode=function(e){if(!e.isClosed)return this.onData(this.writer.closeNode(e,this.currentLevel),this.currentLevel),e.isClosed=!0},x.prototype.onData=function(e,n){return this.documentStarted=!0,this.onDataCallback(e,n+1)},x.prototype.onEnd=function(){return this.documentCompleted=!0,this.onEndCallback()},x.prototype.debugInfo=function(e){return null==e?"":"node: <"+e+">"},x.prototype.ele=function(){return this.element.apply(this,arguments)},x.prototype.nod=function(e,n,t){return this.node(e,n,t)},x.prototype.txt=function(e){return this.text(e)},x.prototype.dat=function(e){return this.cdata(e)},x.prototype.com=function(e){return this.comment(e)},x.prototype.ins=function(e,n){return this.instruction(e,n)},x.prototype.dec=function(e,n,t){return this.declaration(e,n,t)},x.prototype.dtd=function(e,n,t){return this.doctype(e,n,t)},x.prototype.e=function(e,n,t){return this.element(e,n,t)},x.prototype.n=function(e,n,t){return this.node(e,n,t)},x.prototype.t=function(e){return this.text(e)},x.prototype.d=function(e){return this.cdata(e)},x.prototype.c=function(e){return this.comment(e)},x.prototype.r=function(e){return this.raw(e)},x.prototype.i=function(e,n){return this.instruction(e,n)},x.prototype.att=function(){return this.currentNode&&this.currentNode instanceof s?this.attList.apply(this,arguments):this.attribute.apply(this,arguments)},x.prototype.a=function(){return this.currentNode&&this.currentNode instanceof s?this.attList.apply(this,arguments):this.attribute.apply(this,arguments)},x.prototype.ent=function(e,n){return this.entity(e,n)},x.prototype.pent=function(e,n){return this.pEntity(e,n)},x.prototype.not=function(e,n){return this.notation(e,n)},x}()}.call(Lo)),n=Po.exports,i=ko(),t=$o(),ha.create=function(n,t,i,a){var o,c;if(null==n)throw new Error("Root element needs a name.");return a=r({},t,i,a),c=(o=new e(a)).element(n),a.headless||(o.declaration(a),null==a.pubID&&null==a.sysID||o.doctype(a)),c},ha.begin=function(t,i,r){var o;return a(t)&&(i=(o=[t,i])[0],r=o[1],t={}),i?new n(t,i,r):new e(t)},ha.stringWriter=function(e){return new i(e)},ha.streamWriter=function(e,n){return new t(e,n)}}.call(ha)),ha}function Qo(){if(Ho)return Pr;Ho=1;var e=qr();return Pr.Element=e.Element,Pr.element=e.element,Pr.emptyElement=e.emptyElement,Pr.text=e.text,Pr.readString=da().readString,Pr.writeString=function(){if(Vo)return la;Vo=1;var e=Ht,n=Ko();function t(e,n){e.text(n.value)}return la.writeString=function(i,r){var a,o,c=e.invert(r),s={element:function(e,n){var t=e.element(u(n.name),n.attributes);n.children.forEach(function(e){d(t,e)})},text:t};function d(e,n){return s[n.type](e,n)}function u(e){var n=/^\{(.*)\}(.*)$/.exec(e);if(n){var t=c[n[1]];return t+(""===t?"":":")+n[2]}return e}return a=i,o=n.create(u(a.name),{version:"1.0",encoding:"UTF-8",standalone:!0}),e.forEach(r,function(e,n){var t="xmlns"+(""===n?"":":"+n);o.attribute(t,e)}),a.children.forEach(function(e){d(o,e)}),o.end()},la}().writeString,Pr}var Yo,Zo,Jo={},ec={},nc={};function tc(){if(Zo)return ec;Zo=1;var e=ec&&ec.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(ec,"__esModule",{value:!0}),ec.hex=ec.dec=ec.codePoint=void 0;for(var n=e((Yo||(Yo=1,Object.defineProperty(nc,"__esModule",{value:!0}),nc.default=[{"Typeface name":"Symbol","Dingbat dec":"32","Dingbat hex":"20","Unicode dec":"32","Unicode hex":"20"},{"Typeface name":"Symbol","Dingbat dec":"33","Dingbat hex":"21","Unicode dec":"33","Unicode hex":"21"},{"Typeface name":"Symbol","Dingbat dec":"34","Dingbat hex":"22","Unicode dec":"8704","Unicode hex":"2200"},{"Typeface name":"Symbol","Dingbat dec":"35","Dingbat hex":"23","Unicode dec":"35","Unicode hex":"23"},{"Typeface name":"Symbol","Dingbat dec":"36","Dingbat hex":"24","Unicode dec":"8707","Unicode hex":"2203"},{"Typeface name":"Symbol","Dingbat dec":"37","Dingbat hex":"25","Unicode dec":"37","Unicode hex":"25"},{"Typeface name":"Symbol","Dingbat dec":"38","Dingbat hex":"26","Unicode dec":"38","Unicode hex":"26"},{"Typeface name":"Symbol","Dingbat dec":"39","Dingbat hex":"27","Unicode dec":"8717","Unicode hex":"220D"},{"Typeface name":"Symbol","Dingbat dec":"40","Dingbat hex":"28","Unicode dec":"40","Unicode hex":"28"},{"Typeface name":"Symbol","Dingbat dec":"41","Dingbat hex":"29","Unicode dec":"41","Unicode hex":"29"},{"Typeface name":"Symbol","Dingbat dec":"42","Dingbat hex":"2A","Unicode dec":"42","Unicode hex":"2A"},{"Typeface name":"Symbol","Dingbat dec":"43","Dingbat hex":"2B","Unicode dec":"43","Unicode hex":"2B"},{"Typeface name":"Symbol","Dingbat dec":"44","Dingbat hex":"2C","Unicode dec":"44","Unicode hex":"2C"},{"Typeface name":"Symbol","Dingbat dec":"45","Dingbat hex":"2D","Unicode dec":"8722","Unicode hex":"2212"},{"Typeface name":"Symbol","Dingbat dec":"46","Dingbat hex":"2E","Unicode dec":"46","Unicode hex":"2E"},{"Typeface name":"Symbol","Dingbat dec":"47","Dingbat hex":"2F","Unicode dec":"47","Unicode hex":"2F"},{"Typeface name":"Symbol","Dingbat dec":"48","Dingbat hex":"30","Unicode dec":"48","Unicode hex":"30"},{"Typeface name":"Symbol","Dingbat dec":"49","Dingbat hex":"31","Unicode dec":"49","Unicode hex":"31"},{"Typeface name":"Symbol","Dingbat dec":"50","Dingbat hex":"32","Unicode dec":"50","Unicode hex":"32"},{"Typeface name":"Symbol","Dingbat dec":"51","Dingbat hex":"33","Unicode dec":"51","Unicode hex":"33"},{"Typeface name":"Symbol","Dingbat dec":"52","Dingbat hex":"34","Unicode dec":"52","Unicode hex":"34"},{"Typeface name":"Symbol","Dingbat dec":"53","Dingbat hex":"35","Unicode dec":"53","Unicode hex":"35"},{"Typeface name":"Symbol","Dingbat dec":"54","Dingbat hex":"36","Unicode dec":"54","Unicode hex":"36"},{"Typeface name":"Symbol","Dingbat dec":"55","Dingbat hex":"37","Unicode dec":"55","Unicode hex":"37"},{"Typeface name":"Symbol","Dingbat dec":"56","Dingbat hex":"38","Unicode dec":"56","Unicode hex":"38"},{"Typeface name":"Symbol","Dingbat dec":"57","Dingbat hex":"39","Unicode dec":"57","Unicode hex":"39"},{"Typeface name":"Symbol","Dingbat dec":"58","Dingbat hex":"3A","Unicode dec":"58","Unicode hex":"3A"},{"Typeface name":"Symbol","Dingbat dec":"59","Dingbat hex":"3B","Unicode dec":"59","Unicode hex":"3B"},{"Typeface name":"Symbol","Dingbat dec":"60","Dingbat hex":"3C","Unicode dec":"60","Unicode hex":"3C"},{"Typeface name":"Symbol","Dingbat dec":"61","Dingbat hex":"3D","Unicode dec":"61","Unicode hex":"3D"},{"Typeface name":"Symbol","Dingbat dec":"62","Dingbat hex":"3E","Unicode dec":"62","Unicode hex":"3E"},{"Typeface name":"Symbol","Dingbat dec":"63","Dingbat hex":"3F","Unicode dec":"63","Unicode hex":"3F"},{"Typeface name":"Symbol","Dingbat dec":"64","Dingbat hex":"40","Unicode dec":"8773","Unicode hex":"2245"},{"Typeface name":"Symbol","Dingbat dec":"65","Dingbat hex":"41","Unicode dec":"913","Unicode hex":"391"},{"Typeface name":"Symbol","Dingbat dec":"66","Dingbat hex":"42","Unicode dec":"914","Unicode hex":"392"},{"Typeface name":"Symbol","Dingbat dec":"67","Dingbat hex":"43","Unicode dec":"935","Unicode hex":"3A7"},{"Typeface name":"Symbol","Dingbat dec":"68","Dingbat hex":"44","Unicode dec":"916","Unicode hex":"394"},{"Typeface name":"Symbol","Dingbat dec":"69","Dingbat hex":"45","Unicode dec":"917","Unicode hex":"395"},{"Typeface name":"Symbol","Dingbat dec":"70","Dingbat hex":"46","Unicode dec":"934","Unicode hex":"3A6"},{"Typeface name":"Symbol","Dingbat dec":"71","Dingbat hex":"47","Unicode dec":"915","Unicode hex":"393"},{"Typeface name":"Symbol","Dingbat dec":"72","Dingbat hex":"48","Unicode dec":"919","Unicode hex":"397"},{"Typeface name":"Symbol","Dingbat dec":"73","Dingbat hex":"49","Unicode dec":"921","Unicode hex":"399"},{"Typeface name":"Symbol","Dingbat dec":"74","Dingbat hex":"4A","Unicode dec":"977","Unicode hex":"3D1"},{"Typeface name":"Symbol","Dingbat dec":"75","Dingbat hex":"4B","Unicode dec":"922","Unicode hex":"39A"},{"Typeface name":"Symbol","Dingbat dec":"76","Dingbat hex":"4C","Unicode dec":"923","Unicode hex":"39B"},{"Typeface name":"Symbol","Dingbat dec":"77","Dingbat hex":"4D","Unicode dec":"924","Unicode hex":"39C"},{"Typeface name":"Symbol","Dingbat dec":"78","Dingbat hex":"4E","Unicode dec":"925","Unicode hex":"39D"},{"Typeface name":"Symbol","Dingbat dec":"79","Dingbat hex":"4F","Unicode dec":"927","Unicode hex":"39F"},{"Typeface name":"Symbol","Dingbat dec":"80","Dingbat hex":"50","Unicode dec":"928","Unicode hex":"3A0"},{"Typeface name":"Symbol","Dingbat dec":"81","Dingbat hex":"51","Unicode dec":"920","Unicode hex":"398"},{"Typeface name":"Symbol","Dingbat dec":"82","Dingbat hex":"52","Unicode dec":"929","Unicode hex":"3A1"},{"Typeface name":"Symbol","Dingbat dec":"83","Dingbat hex":"53","Unicode dec":"931","Unicode hex":"3A3"},{"Typeface name":"Symbol","Dingbat dec":"84","Dingbat hex":"54","Unicode dec":"932","Unicode hex":"3A4"},{"Typeface name":"Symbol","Dingbat dec":"85","Dingbat hex":"55","Unicode dec":"933","Unicode hex":"3A5"},{"Typeface name":"Symbol","Dingbat dec":"86","Dingbat hex":"56","Unicode dec":"962","Unicode hex":"3C2"},{"Typeface name":"Symbol","Dingbat dec":"87","Dingbat hex":"57","Unicode dec":"937","Unicode hex":"3A9"},{"Typeface name":"Symbol","Dingbat dec":"88","Dingbat hex":"58","Unicode dec":"926","Unicode hex":"39E"},{"Typeface name":"Symbol","Dingbat dec":"89","Dingbat hex":"59","Unicode dec":"936","Unicode hex":"3A8"},{"Typeface name":"Symbol","Dingbat dec":"90","Dingbat hex":"5A","Unicode dec":"918","Unicode hex":"396"},{"Typeface name":"Symbol","Dingbat dec":"91","Dingbat hex":"5B","Unicode dec":"91","Unicode hex":"5B"},{"Typeface name":"Symbol","Dingbat dec":"92","Dingbat hex":"5C","Unicode dec":"8756","Unicode hex":"2234"},{"Typeface name":"Symbol","Dingbat dec":"93","Dingbat hex":"5D","Unicode dec":"93","Unicode hex":"5D"},{"Typeface name":"Symbol","Dingbat dec":"94","Dingbat hex":"5E","Unicode dec":"8869","Unicode hex":"22A5"},{"Typeface name":"Symbol","Dingbat dec":"95","Dingbat hex":"5F","Unicode dec":"95","Unicode hex":"5F"},{"Typeface name":"Symbol","Dingbat dec":"96","Dingbat hex":"60","Unicode dec":"8254","Unicode hex":"203E"},{"Typeface name":"Symbol","Dingbat dec":"97","Dingbat hex":"61","Unicode dec":"945","Unicode hex":"3B1"},{"Typeface name":"Symbol","Dingbat dec":"98","Dingbat hex":"62","Unicode dec":"946","Unicode hex":"3B2"},{"Typeface name":"Symbol","Dingbat dec":"99","Dingbat hex":"63","Unicode dec":"967","Unicode hex":"3C7"},{"Typeface name":"Symbol","Dingbat dec":"100","Dingbat hex":"64","Unicode dec":"948","Unicode hex":"3B4"},{"Typeface name":"Symbol","Dingbat dec":"101","Dingbat hex":"65","Unicode dec":"949","Unicode hex":"3B5"},{"Typeface name":"Symbol","Dingbat dec":"102","Dingbat hex":"66","Unicode dec":"966","Unicode hex":"3C6"},{"Typeface name":"Symbol","Dingbat dec":"103","Dingbat hex":"67","Unicode dec":"947","Unicode hex":"3B3"},{"Typeface name":"Symbol","Dingbat dec":"104","Dingbat hex":"68","Unicode dec":"951","Unicode hex":"3B7"},{"Typeface name":"Symbol","Dingbat dec":"105","Dingbat hex":"69","Unicode dec":"953","Unicode hex":"3B9"},{"Typeface name":"Symbol","Dingbat dec":"106","Dingbat hex":"6A","Unicode dec":"981","Unicode hex":"3D5"},{"Typeface name":"Symbol","Dingbat dec":"107","Dingbat hex":"6B","Unicode dec":"954","Unicode hex":"3BA"},{"Typeface name":"Symbol","Dingbat dec":"108","Dingbat hex":"6C","Unicode dec":"955","Unicode hex":"3BB"},{"Typeface name":"Symbol","Dingbat dec":"109","Dingbat hex":"6D","Unicode dec":"956","Unicode hex":"3BC"},{"Typeface name":"Symbol","Dingbat dec":"110","Dingbat hex":"6E","Unicode dec":"957","Unicode hex":"3BD"},{"Typeface name":"Symbol","Dingbat dec":"111","Dingbat hex":"6F","Unicode dec":"959","Unicode hex":"3BF"},{"Typeface name":"Symbol","Dingbat dec":"112","Dingbat hex":"70","Unicode dec":"960","Unicode hex":"3C0"},{"Typeface name":"Symbol","Dingbat dec":"113","Dingbat hex":"71","Unicode dec":"952","Unicode hex":"3B8"},{"Typeface name":"Symbol","Dingbat dec":"114","Dingbat hex":"72","Unicode dec":"961","Unicode hex":"3C1"},{"Typeface name":"Symbol","Dingbat dec":"115","Dingbat hex":"73","Unicode dec":"963","Unicode hex":"3C3"},{"Typeface name":"Symbol","Dingbat dec":"116","Dingbat hex":"74","Unicode dec":"964","Unicode hex":"3C4"},{"Typeface name":"Symbol","Dingbat dec":"117","Dingbat hex":"75","Unicode dec":"965","Unicode hex":"3C5"},{"Typeface name":"Symbol","Dingbat dec":"118","Dingbat hex":"76","Unicode dec":"982","Unicode hex":"3D6"},{"Typeface name":"Symbol","Dingbat dec":"119","Dingbat hex":"77","Unicode dec":"969","Unicode hex":"3C9"},{"Typeface name":"Symbol","Dingbat dec":"120","Dingbat hex":"78","Unicode dec":"958","Unicode hex":"3BE"},{"Typeface name":"Symbol","Dingbat dec":"121","Dingbat hex":"79","Unicode dec":"968","Unicode hex":"3C8"},{"Typeface name":"Symbol","Dingbat dec":"122","Dingbat hex":"7A","Unicode dec":"950","Unicode hex":"3B6"},{"Typeface name":"Symbol","Dingbat dec":"123","Dingbat hex":"7B","Unicode dec":"123","Unicode hex":"7B"},{"Typeface name":"Symbol","Dingbat dec":"124","Dingbat hex":"7C","Unicode dec":"124","Unicode hex":"7C"},{"Typeface name":"Symbol","Dingbat dec":"125","Dingbat hex":"7D","Unicode dec":"125","Unicode hex":"7D"},{"Typeface name":"Symbol","Dingbat dec":"126","Dingbat hex":"7E","Unicode dec":"126","Unicode hex":"7E"},{"Typeface name":"Symbol","Dingbat dec":"160","Dingbat hex":"A0","Unicode dec":"8364","Unicode hex":"20AC"},{"Typeface name":"Symbol","Dingbat dec":"161","Dingbat hex":"A1","Unicode dec":"978","Unicode hex":"3D2"},{"Typeface name":"Symbol","Dingbat dec":"162","Dingbat hex":"A2","Unicode dec":"8242","Unicode hex":"2032"},{"Typeface name":"Symbol","Dingbat dec":"163","Dingbat hex":"A3","Unicode dec":"8804","Unicode hex":"2264"},{"Typeface name":"Symbol","Dingbat dec":"164","Dingbat hex":"A4","Unicode dec":"8260","Unicode hex":"2044"},{"Typeface name":"Symbol","Dingbat dec":"165","Dingbat hex":"A5","Unicode dec":"8734","Unicode hex":"221E"},{"Typeface name":"Symbol","Dingbat dec":"166","Dingbat hex":"A6","Unicode dec":"402","Unicode hex":"192"},{"Typeface name":"Symbol","Dingbat dec":"167","Dingbat hex":"A7","Unicode dec":"9827","Unicode hex":"2663"},{"Typeface name":"Symbol","Dingbat dec":"168","Dingbat hex":"A8","Unicode dec":"9830","Unicode hex":"2666"},{"Typeface name":"Symbol","Dingbat dec":"169","Dingbat hex":"A9","Unicode dec":"9829","Unicode hex":"2665"},{"Typeface name":"Symbol","Dingbat dec":"170","Dingbat hex":"AA","Unicode dec":"9824","Unicode hex":"2660"},{"Typeface name":"Symbol","Dingbat dec":"171","Dingbat hex":"AB","Unicode dec":"8596","Unicode hex":"2194"},{"Typeface name":"Symbol","Dingbat dec":"172","Dingbat hex":"AC","Unicode dec":"8592","Unicode hex":"2190"},{"Typeface name":"Symbol","Dingbat dec":"173","Dingbat hex":"AD","Unicode dec":"8593","Unicode hex":"2191"},{"Typeface name":"Symbol","Dingbat dec":"174","Dingbat hex":"AE","Unicode dec":"8594","Unicode hex":"2192"},{"Typeface name":"Symbol","Dingbat dec":"175","Dingbat hex":"AF","Unicode dec":"8595","Unicode hex":"2193"},{"Typeface name":"Symbol","Dingbat dec":"176","Dingbat hex":"B0","Unicode dec":"176","Unicode hex":"B0"},{"Typeface name":"Symbol","Dingbat dec":"177","Dingbat hex":"B1","Unicode dec":"177","Unicode hex":"B1"},{"Typeface name":"Symbol","Dingbat dec":"178","Dingbat hex":"B2","Unicode dec":"8243","Unicode hex":"2033"},{"Typeface name":"Symbol","Dingbat dec":"179","Dingbat hex":"B3","Unicode dec":"8805","Unicode hex":"2265"},{"Typeface name":"Symbol","Dingbat dec":"180","Dingbat hex":"B4","Unicode dec":"215","Unicode hex":"D7"},{"Typeface name":"Symbol","Dingbat dec":"181","Dingbat hex":"B5","Unicode dec":"8733","Unicode hex":"221D"},{"Typeface name":"Symbol","Dingbat dec":"182","Dingbat hex":"B6","Unicode dec":"8706","Unicode hex":"2202"},{"Typeface name":"Symbol","Dingbat dec":"183","Dingbat hex":"B7","Unicode dec":"8226","Unicode hex":"2022"},{"Typeface name":"Symbol","Dingbat dec":"184","Dingbat hex":"B8","Unicode dec":"247","Unicode hex":"F7"},{"Typeface name":"Symbol","Dingbat dec":"185","Dingbat hex":"B9","Unicode dec":"8800","Unicode hex":"2260"},{"Typeface name":"Symbol","Dingbat dec":"186","Dingbat hex":"BA","Unicode dec":"8801","Unicode hex":"2261"},{"Typeface name":"Symbol","Dingbat dec":"187","Dingbat hex":"BB","Unicode dec":"8776","Unicode hex":"2248"},{"Typeface name":"Symbol","Dingbat dec":"188","Dingbat hex":"BC","Unicode dec":"8230","Unicode hex":"2026"},{"Typeface name":"Symbol","Dingbat dec":"189","Dingbat hex":"BD","Unicode dec":"9168","Unicode hex":"23D0"},{"Typeface name":"Symbol","Dingbat dec":"190","Dingbat hex":"BE","Unicode dec":"9135","Unicode hex":"23AF"},{"Typeface name":"Symbol","Dingbat dec":"191","Dingbat hex":"BF","Unicode dec":"8629","Unicode hex":"21B5"},{"Typeface name":"Symbol","Dingbat dec":"192","Dingbat hex":"C0","Unicode dec":"8501","Unicode hex":"2135"},{"Typeface name":"Symbol","Dingbat dec":"193","Dingbat hex":"C1","Unicode dec":"8465","Unicode hex":"2111"},{"Typeface name":"Symbol","Dingbat dec":"194","Dingbat hex":"C2","Unicode dec":"8476","Unicode hex":"211C"},{"Typeface name":"Symbol","Dingbat dec":"195","Dingbat hex":"C3","Unicode dec":"8472","Unicode hex":"2118"},{"Typeface name":"Symbol","Dingbat dec":"196","Dingbat hex":"C4","Unicode dec":"8855","Unicode hex":"2297"},{"Typeface name":"Symbol","Dingbat dec":"197","Dingbat hex":"C5","Unicode dec":"8853","Unicode hex":"2295"},{"Typeface name":"Symbol","Dingbat dec":"198","Dingbat hex":"C6","Unicode dec":"8709","Unicode hex":"2205"},{"Typeface name":"Symbol","Dingbat dec":"199","Dingbat hex":"C7","Unicode dec":"8745","Unicode hex":"2229"},{"Typeface name":"Symbol","Dingbat dec":"200","Dingbat hex":"C8","Unicode dec":"8746","Unicode hex":"222A"},{"Typeface name":"Symbol","Dingbat dec":"201","Dingbat hex":"C9","Unicode dec":"8835","Unicode hex":"2283"},{"Typeface name":"Symbol","Dingbat dec":"202","Dingbat hex":"CA","Unicode dec":"8839","Unicode hex":"2287"},{"Typeface name":"Symbol","Dingbat dec":"203","Dingbat hex":"CB","Unicode dec":"8836","Unicode hex":"2284"},{"Typeface name":"Symbol","Dingbat dec":"204","Dingbat hex":"CC","Unicode dec":"8834","Unicode hex":"2282"},{"Typeface name":"Symbol","Dingbat dec":"205","Dingbat hex":"CD","Unicode dec":"8838","Unicode hex":"2286"},{"Typeface name":"Symbol","Dingbat dec":"206","Dingbat hex":"CE","Unicode dec":"8712","Unicode hex":"2208"},{"Typeface name":"Symbol","Dingbat dec":"207","Dingbat hex":"CF","Unicode dec":"8713","Unicode hex":"2209"},{"Typeface name":"Symbol","Dingbat dec":"208","Dingbat hex":"D0","Unicode dec":"8736","Unicode hex":"2220"},{"Typeface name":"Symbol","Dingbat dec":"209","Dingbat hex":"D1","Unicode dec":"8711","Unicode hex":"2207"},{"Typeface name":"Symbol","Dingbat dec":"210","Dingbat hex":"D2","Unicode dec":"174","Unicode hex":"AE"},{"Typeface name":"Symbol","Dingbat dec":"211","Dingbat hex":"D3","Unicode dec":"169","Unicode hex":"A9"},{"Typeface name":"Symbol","Dingbat dec":"212","Dingbat hex":"D4","Unicode dec":"8482","Unicode hex":"2122"},{"Typeface name":"Symbol","Dingbat dec":"213","Dingbat hex":"D5","Unicode dec":"8719","Unicode hex":"220F"},{"Typeface name":"Symbol","Dingbat dec":"214","Dingbat hex":"D6","Unicode dec":"8730","Unicode hex":"221A"},{"Typeface name":"Symbol","Dingbat dec":"215","Dingbat hex":"D7","Unicode dec":"8901","Unicode hex":"22C5"},{"Typeface name":"Symbol","Dingbat dec":"216","Dingbat hex":"D8","Unicode dec":"172","Unicode hex":"AC"},{"Typeface name":"Symbol","Dingbat dec":"217","Dingbat hex":"D9","Unicode dec":"8743","Unicode hex":"2227"},{"Typeface name":"Symbol","Dingbat dec":"218","Dingbat hex":"DA","Unicode dec":"8744","Unicode hex":"2228"},{"Typeface name":"Symbol","Dingbat dec":"219","Dingbat hex":"DB","Unicode dec":"8660","Unicode hex":"21D4"},{"Typeface name":"Symbol","Dingbat dec":"220","Dingbat hex":"DC","Unicode dec":"8656","Unicode hex":"21D0"},{"Typeface name":"Symbol","Dingbat dec":"221","Dingbat hex":"DD","Unicode dec":"8657","Unicode hex":"21D1"},{"Typeface name":"Symbol","Dingbat dec":"222","Dingbat hex":"DE","Unicode dec":"8658","Unicode hex":"21D2"},{"Typeface name":"Symbol","Dingbat dec":"223","Dingbat hex":"DF","Unicode dec":"8659","Unicode hex":"21D3"},{"Typeface name":"Symbol","Dingbat dec":"224","Dingbat hex":"E0","Unicode dec":"9674","Unicode hex":"25CA"},{"Typeface name":"Symbol","Dingbat dec":"225","Dingbat hex":"E1","Unicode dec":"12296","Unicode hex":"3008"},{"Typeface name":"Symbol","Dingbat dec":"226","Dingbat hex":"E2","Unicode dec":"174","Unicode hex":"AE"},{"Typeface name":"Symbol","Dingbat dec":"227","Dingbat hex":"E3","Unicode dec":"169","Unicode hex":"A9"},{"Typeface name":"Symbol","Dingbat dec":"228","Dingbat hex":"E4","Unicode dec":"8482","Unicode hex":"2122"},{"Typeface name":"Symbol","Dingbat dec":"229","Dingbat hex":"E5","Unicode dec":"8721","Unicode hex":"2211"},{"Typeface name":"Symbol","Dingbat dec":"230","Dingbat hex":"E6","Unicode dec":"9115","Unicode hex":"239B"},{"Typeface name":"Symbol","Dingbat dec":"231","Dingbat hex":"E7","Unicode dec":"9116","Unicode hex":"239C"},{"Typeface name":"Symbol","Dingbat dec":"232","Dingbat hex":"E8","Unicode dec":"9117","Unicode hex":"239D"},{"Typeface name":"Symbol","Dingbat dec":"233","Dingbat hex":"E9","Unicode dec":"9121","Unicode hex":"23A1"},{"Typeface name":"Symbol","Dingbat dec":"234","Dingbat hex":"EA","Unicode dec":"9122","Unicode hex":"23A2"},{"Typeface name":"Symbol","Dingbat dec":"235","Dingbat hex":"EB","Unicode dec":"9123","Unicode hex":"23A3"},{"Typeface name":"Symbol","Dingbat dec":"236","Dingbat hex":"EC","Unicode dec":"9127","Unicode hex":"23A7"},{"Typeface name":"Symbol","Dingbat dec":"237","Dingbat hex":"ED","Unicode dec":"9128","Unicode hex":"23A8"},{"Typeface name":"Symbol","Dingbat dec":"238","Dingbat hex":"EE","Unicode dec":"9129","Unicode hex":"23A9"},{"Typeface name":"Symbol","Dingbat dec":"239","Dingbat hex":"EF","Unicode dec":"9130","Unicode hex":"23AA"},{"Typeface name":"Symbol","Dingbat dec":"240","Dingbat hex":"F0","Unicode dec":"63743","Unicode hex":"F8FF"},{"Typeface name":"Symbol","Dingbat dec":"241","Dingbat hex":"F1","Unicode dec":"12297","Unicode hex":"3009"},{"Typeface name":"Symbol","Dingbat dec":"242","Dingbat hex":"F2","Unicode dec":"8747","Unicode hex":"222B"},{"Typeface name":"Symbol","Dingbat dec":"243","Dingbat hex":"F3","Unicode dec":"8992","Unicode hex":"2320"},{"Typeface name":"Symbol","Dingbat dec":"244","Dingbat hex":"F4","Unicode dec":"9134","Unicode hex":"23AE"},{"Typeface name":"Symbol","Dingbat dec":"245","Dingbat hex":"F5","Unicode dec":"8993","Unicode hex":"2321"},{"Typeface name":"Symbol","Dingbat dec":"246","Dingbat hex":"F6","Unicode dec":"9118","Unicode hex":"239E"},{"Typeface name":"Symbol","Dingbat dec":"247","Dingbat hex":"F7","Unicode dec":"9119","Unicode hex":"239F"},{"Typeface name":"Symbol","Dingbat dec":"248","Dingbat hex":"F8","Unicode dec":"9120","Unicode hex":"23A0"},{"Typeface name":"Symbol","Dingbat dec":"249","Dingbat hex":"F9","Unicode dec":"9124","Unicode hex":"23A4"},{"Typeface name":"Symbol","Dingbat dec":"250","Dingbat hex":"FA","Unicode dec":"9125","Unicode hex":"23A5"},{"Typeface name":"Symbol","Dingbat dec":"251","Dingbat hex":"FB","Unicode dec":"9126","Unicode hex":"23A6"},{"Typeface name":"Symbol","Dingbat dec":"252","Dingbat hex":"FC","Unicode dec":"9131","Unicode hex":"23AB"},{"Typeface name":"Symbol","Dingbat dec":"253","Dingbat hex":"FD","Unicode dec":"9132","Unicode hex":"23AC"},{"Typeface name":"Symbol","Dingbat dec":"254","Dingbat hex":"FE","Unicode dec":"9133","Unicode hex":"23AD"},{"Typeface name":"Webdings","Dingbat dec":"32","Dingbat hex":"20","Unicode dec":"32","Unicode hex":"20"},{"Typeface name":"Webdings","Dingbat dec":"33","Dingbat hex":"21","Unicode dec":"128375","Unicode hex":"1F577"},{"Typeface name":"Webdings","Dingbat dec":"34","Dingbat hex":"22","Unicode dec":"128376","Unicode hex":"1F578"},{"Typeface name":"Webdings","Dingbat dec":"35","Dingbat hex":"23","Unicode dec":"128370","Unicode hex":"1F572"},{"Typeface name":"Webdings","Dingbat dec":"36","Dingbat hex":"24","Unicode dec":"128374","Unicode hex":"1F576"},{"Typeface name":"Webdings","Dingbat dec":"37","Dingbat hex":"25","Unicode dec":"127942","Unicode hex":"1F3C6"},{"Typeface name":"Webdings","Dingbat dec":"38","Dingbat hex":"26","Unicode dec":"127894","Unicode hex":"1F396"},{"Typeface name":"Webdings","Dingbat dec":"39","Dingbat hex":"27","Unicode dec":"128391","Unicode hex":"1F587"},{"Typeface name":"Webdings","Dingbat dec":"40","Dingbat hex":"28","Unicode dec":"128488","Unicode hex":"1F5E8"},{"Typeface name":"Webdings","Dingbat dec":"41","Dingbat hex":"29","Unicode dec":"128489","Unicode hex":"1F5E9"},{"Typeface name":"Webdings","Dingbat dec":"42","Dingbat hex":"2A","Unicode dec":"128496","Unicode hex":"1F5F0"},{"Typeface name":"Webdings","Dingbat dec":"43","Dingbat hex":"2B","Unicode dec":"128497","Unicode hex":"1F5F1"},{"Typeface name":"Webdings","Dingbat dec":"44","Dingbat hex":"2C","Unicode dec":"127798","Unicode hex":"1F336"},{"Typeface name":"Webdings","Dingbat dec":"45","Dingbat hex":"2D","Unicode dec":"127895","Unicode hex":"1F397"},{"Typeface name":"Webdings","Dingbat dec":"46","Dingbat hex":"2E","Unicode dec":"128638","Unicode hex":"1F67E"},{"Typeface name":"Webdings","Dingbat dec":"47","Dingbat hex":"2F","Unicode dec":"128636","Unicode hex":"1F67C"},{"Typeface name":"Webdings","Dingbat dec":"48","Dingbat hex":"30","Unicode dec":"128469","Unicode hex":"1F5D5"},{"Typeface name":"Webdings","Dingbat dec":"49","Dingbat hex":"31","Unicode dec":"128470","Unicode hex":"1F5D6"},{"Typeface name":"Webdings","Dingbat dec":"50","Dingbat hex":"32","Unicode dec":"128471","Unicode hex":"1F5D7"},{"Typeface name":"Webdings","Dingbat dec":"51","Dingbat hex":"33","Unicode dec":"9204","Unicode hex":"23F4"},{"Typeface name":"Webdings","Dingbat dec":"52","Dingbat hex":"34","Unicode dec":"9205","Unicode hex":"23F5"},{"Typeface name":"Webdings","Dingbat dec":"53","Dingbat hex":"35","Unicode dec":"9206","Unicode hex":"23F6"},{"Typeface name":"Webdings","Dingbat dec":"54","Dingbat hex":"36","Unicode dec":"9207","Unicode hex":"23F7"},{"Typeface name":"Webdings","Dingbat dec":"55","Dingbat hex":"37","Unicode dec":"9194","Unicode hex":"23EA"},{"Typeface name":"Webdings","Dingbat dec":"56","Dingbat hex":"38","Unicode dec":"9193","Unicode hex":"23E9"},{"Typeface name":"Webdings","Dingbat dec":"57","Dingbat hex":"39","Unicode dec":"9198","Unicode hex":"23EE"},{"Typeface name":"Webdings","Dingbat dec":"58","Dingbat hex":"3A","Unicode dec":"9197","Unicode hex":"23ED"},{"Typeface name":"Webdings","Dingbat dec":"59","Dingbat hex":"3B","Unicode dec":"9208","Unicode hex":"23F8"},{"Typeface name":"Webdings","Dingbat dec":"60","Dingbat hex":"3C","Unicode dec":"9209","Unicode hex":"23F9"},{"Typeface name":"Webdings","Dingbat dec":"61","Dingbat hex":"3D","Unicode dec":"9210","Unicode hex":"23FA"},{"Typeface name":"Webdings","Dingbat dec":"62","Dingbat hex":"3E","Unicode dec":"128474","Unicode hex":"1F5DA"},{"Typeface name":"Webdings","Dingbat dec":"63","Dingbat hex":"3F","Unicode dec":"128499","Unicode hex":"1F5F3"},{"Typeface name":"Webdings","Dingbat dec":"64","Dingbat hex":"40","Unicode dec":"128736","Unicode hex":"1F6E0"},{"Typeface name":"Webdings","Dingbat dec":"65","Dingbat hex":"41","Unicode dec":"127959","Unicode hex":"1F3D7"},{"Typeface name":"Webdings","Dingbat dec":"66","Dingbat hex":"42","Unicode dec":"127960","Unicode hex":"1F3D8"},{"Typeface name":"Webdings","Dingbat dec":"67","Dingbat hex":"43","Unicode dec":"127961","Unicode hex":"1F3D9"},{"Typeface name":"Webdings","Dingbat dec":"68","Dingbat hex":"44","Unicode dec":"127962","Unicode hex":"1F3DA"},{"Typeface name":"Webdings","Dingbat dec":"69","Dingbat hex":"45","Unicode dec":"127964","Unicode hex":"1F3DC"},{"Typeface name":"Webdings","Dingbat dec":"70","Dingbat hex":"46","Unicode dec":"127981","Unicode hex":"1F3ED"},{"Typeface name":"Webdings","Dingbat dec":"71","Dingbat hex":"47","Unicode dec":"127963","Unicode hex":"1F3DB"},{"Typeface name":"Webdings","Dingbat dec":"72","Dingbat hex":"48","Unicode dec":"127968","Unicode hex":"1F3E0"},{"Typeface name":"Webdings","Dingbat dec":"73","Dingbat hex":"49","Unicode dec":"127958","Unicode hex":"1F3D6"},{"Typeface name":"Webdings","Dingbat dec":"74","Dingbat hex":"4A","Unicode dec":"127965","Unicode hex":"1F3DD"},{"Typeface name":"Webdings","Dingbat dec":"75","Dingbat hex":"4B","Unicode dec":"128739","Unicode hex":"1F6E3"},{"Typeface name":"Webdings","Dingbat dec":"76","Dingbat hex":"4C","Unicode dec":"128269","Unicode hex":"1F50D"},{"Typeface name":"Webdings","Dingbat dec":"77","Dingbat hex":"4D","Unicode dec":"127956","Unicode hex":"1F3D4"},{"Typeface name":"Webdings","Dingbat dec":"78","Dingbat hex":"4E","Unicode dec":"128065","Unicode hex":"1F441"},{"Typeface name":"Webdings","Dingbat dec":"79","Dingbat hex":"4F","Unicode dec":"128066","Unicode hex":"1F442"},{"Typeface name":"Webdings","Dingbat dec":"80","Dingbat hex":"50","Unicode dec":"127966","Unicode hex":"1F3DE"},{"Typeface name":"Webdings","Dingbat dec":"81","Dingbat hex":"51","Unicode dec":"127957","Unicode hex":"1F3D5"},{"Typeface name":"Webdings","Dingbat dec":"82","Dingbat hex":"52","Unicode dec":"128740","Unicode hex":"1F6E4"},{"Typeface name":"Webdings","Dingbat dec":"83","Dingbat hex":"53","Unicode dec":"127967","Unicode hex":"1F3DF"},{"Typeface name":"Webdings","Dingbat dec":"84","Dingbat hex":"54","Unicode dec":"128755","Unicode hex":"1F6F3"},{"Typeface name":"Webdings","Dingbat dec":"85","Dingbat hex":"55","Unicode dec":"128364","Unicode hex":"1F56C"},{"Typeface name":"Webdings","Dingbat dec":"86","Dingbat hex":"56","Unicode dec":"128363","Unicode hex":"1F56B"},{"Typeface name":"Webdings","Dingbat dec":"87","Dingbat hex":"57","Unicode dec":"128360","Unicode hex":"1F568"},{"Typeface name":"Webdings","Dingbat dec":"88","Dingbat hex":"58","Unicode dec":"128264","Unicode hex":"1F508"},{"Typeface name":"Webdings","Dingbat dec":"89","Dingbat hex":"59","Unicode dec":"127892","Unicode hex":"1F394"},{"Typeface name":"Webdings","Dingbat dec":"90","Dingbat hex":"5A","Unicode dec":"127893","Unicode hex":"1F395"},{"Typeface name":"Webdings","Dingbat dec":"91","Dingbat hex":"5B","Unicode dec":"128492","Unicode hex":"1F5EC"},{"Typeface name":"Webdings","Dingbat dec":"92","Dingbat hex":"5C","Unicode dec":"128637","Unicode hex":"1F67D"},{"Typeface name":"Webdings","Dingbat dec":"93","Dingbat hex":"5D","Unicode dec":"128493","Unicode hex":"1F5ED"},{"Typeface name":"Webdings","Dingbat dec":"94","Dingbat hex":"5E","Unicode dec":"128490","Unicode hex":"1F5EA"},{"Typeface name":"Webdings","Dingbat dec":"95","Dingbat hex":"5F","Unicode dec":"128491","Unicode hex":"1F5EB"},{"Typeface name":"Webdings","Dingbat dec":"96","Dingbat hex":"60","Unicode dec":"11156","Unicode hex":"2B94"},{"Typeface name":"Webdings","Dingbat dec":"97","Dingbat hex":"61","Unicode dec":"10004","Unicode hex":"2714"},{"Typeface name":"Webdings","Dingbat dec":"98","Dingbat hex":"62","Unicode dec":"128690","Unicode hex":"1F6B2"},{"Typeface name":"Webdings","Dingbat dec":"99","Dingbat hex":"63","Unicode dec":"11036","Unicode hex":"2B1C"},{"Typeface name":"Webdings","Dingbat dec":"100","Dingbat hex":"64","Unicode dec":"128737","Unicode hex":"1F6E1"},{"Typeface name":"Webdings","Dingbat dec":"101","Dingbat hex":"65","Unicode dec":"128230","Unicode hex":"1F4E6"},{"Typeface name":"Webdings","Dingbat dec":"102","Dingbat hex":"66","Unicode dec":"128753","Unicode hex":"1F6F1"},{"Typeface name":"Webdings","Dingbat dec":"103","Dingbat hex":"67","Unicode dec":"11035","Unicode hex":"2B1B"},{"Typeface name":"Webdings","Dingbat dec":"104","Dingbat hex":"68","Unicode dec":"128657","Unicode hex":"1F691"},{"Typeface name":"Webdings","Dingbat dec":"105","Dingbat hex":"69","Unicode dec":"128712","Unicode hex":"1F6C8"},{"Typeface name":"Webdings","Dingbat dec":"106","Dingbat hex":"6A","Unicode dec":"128745","Unicode hex":"1F6E9"},{"Typeface name":"Webdings","Dingbat dec":"107","Dingbat hex":"6B","Unicode dec":"128752","Unicode hex":"1F6F0"},{"Typeface name":"Webdings","Dingbat dec":"108","Dingbat hex":"6C","Unicode dec":"128968","Unicode hex":"1F7C8"},{"Typeface name":"Webdings","Dingbat dec":"109","Dingbat hex":"6D","Unicode dec":"128372","Unicode hex":"1F574"},{"Typeface name":"Webdings","Dingbat dec":"110","Dingbat hex":"6E","Unicode dec":"11044","Unicode hex":"2B24"},{"Typeface name":"Webdings","Dingbat dec":"111","Dingbat hex":"6F","Unicode dec":"128741","Unicode hex":"1F6E5"},{"Typeface name":"Webdings","Dingbat dec":"112","Dingbat hex":"70","Unicode dec":"128660","Unicode hex":"1F694"},{"Typeface name":"Webdings","Dingbat dec":"113","Dingbat hex":"71","Unicode dec":"128472","Unicode hex":"1F5D8"},{"Typeface name":"Webdings","Dingbat dec":"114","Dingbat hex":"72","Unicode dec":"128473","Unicode hex":"1F5D9"},{"Typeface name":"Webdings","Dingbat dec":"115","Dingbat hex":"73","Unicode dec":"10067","Unicode hex":"2753"},{"Typeface name":"Webdings","Dingbat dec":"116","Dingbat hex":"74","Unicode dec":"128754","Unicode hex":"1F6F2"},{"Typeface name":"Webdings","Dingbat dec":"117","Dingbat hex":"75","Unicode dec":"128647","Unicode hex":"1F687"},{"Typeface name":"Webdings","Dingbat dec":"118","Dingbat hex":"76","Unicode dec":"128653","Unicode hex":"1F68D"},{"Typeface name":"Webdings","Dingbat dec":"119","Dingbat hex":"77","Unicode dec":"9971","Unicode hex":"26F3"},{"Typeface name":"Webdings","Dingbat dec":"120","Dingbat hex":"78","Unicode dec":"10680","Unicode hex":"29B8"},{"Typeface name":"Webdings","Dingbat dec":"121","Dingbat hex":"79","Unicode dec":"8854","Unicode hex":"2296"},{"Typeface name":"Webdings","Dingbat dec":"122","Dingbat hex":"7A","Unicode dec":"128685","Unicode hex":"1F6AD"},{"Typeface name":"Webdings","Dingbat dec":"123","Dingbat hex":"7B","Unicode dec":"128494","Unicode hex":"1F5EE"},{"Typeface name":"Webdings","Dingbat dec":"124","Dingbat hex":"7C","Unicode dec":"9168","Unicode hex":"23D0"},{"Typeface name":"Webdings","Dingbat dec":"125","Dingbat hex":"7D","Unicode dec":"128495","Unicode hex":"1F5EF"},{"Typeface name":"Webdings","Dingbat dec":"126","Dingbat hex":"7E","Unicode dec":"128498","Unicode hex":"1F5F2"},{"Typeface name":"Webdings","Dingbat dec":"128","Dingbat hex":"80","Unicode dec":"128697","Unicode hex":"1F6B9"},{"Typeface name":"Webdings","Dingbat dec":"129","Dingbat hex":"81","Unicode dec":"128698","Unicode hex":"1F6BA"},{"Typeface name":"Webdings","Dingbat dec":"130","Dingbat hex":"82","Unicode dec":"128713","Unicode hex":"1F6C9"},{"Typeface name":"Webdings","Dingbat dec":"131","Dingbat hex":"83","Unicode dec":"128714","Unicode hex":"1F6CA"},{"Typeface name":"Webdings","Dingbat dec":"132","Dingbat hex":"84","Unicode dec":"128700","Unicode hex":"1F6BC"},{"Typeface name":"Webdings","Dingbat dec":"133","Dingbat hex":"85","Unicode dec":"128125","Unicode hex":"1F47D"},{"Typeface name":"Webdings","Dingbat dec":"134","Dingbat hex":"86","Unicode dec":"127947","Unicode hex":"1F3CB"},{"Typeface name":"Webdings","Dingbat dec":"135","Dingbat hex":"87","Unicode dec":"9975","Unicode hex":"26F7"},{"Typeface name":"Webdings","Dingbat dec":"136","Dingbat hex":"88","Unicode dec":"127938","Unicode hex":"1F3C2"},{"Typeface name":"Webdings","Dingbat dec":"137","Dingbat hex":"89","Unicode dec":"127948","Unicode hex":"1F3CC"},{"Typeface name":"Webdings","Dingbat dec":"138","Dingbat hex":"8A","Unicode dec":"127946","Unicode hex":"1F3CA"},{"Typeface name":"Webdings","Dingbat dec":"139","Dingbat hex":"8B","Unicode dec":"127940","Unicode hex":"1F3C4"},{"Typeface name":"Webdings","Dingbat dec":"140","Dingbat hex":"8C","Unicode dec":"127949","Unicode hex":"1F3CD"},{"Typeface name":"Webdings","Dingbat dec":"141","Dingbat hex":"8D","Unicode dec":"127950","Unicode hex":"1F3CE"},{"Typeface name":"Webdings","Dingbat dec":"142","Dingbat hex":"8E","Unicode dec":"128664","Unicode hex":"1F698"},{"Typeface name":"Webdings","Dingbat dec":"143","Dingbat hex":"8F","Unicode dec":"128480","Unicode hex":"1F5E0"},{"Typeface name":"Webdings","Dingbat dec":"144","Dingbat hex":"90","Unicode dec":"128738","Unicode hex":"1F6E2"},{"Typeface name":"Webdings","Dingbat dec":"145","Dingbat hex":"91","Unicode dec":"128176","Unicode hex":"1F4B0"},{"Typeface name":"Webdings","Dingbat dec":"146","Dingbat hex":"92","Unicode dec":"127991","Unicode hex":"1F3F7"},{"Typeface name":"Webdings","Dingbat dec":"147","Dingbat hex":"93","Unicode dec":"128179","Unicode hex":"1F4B3"},{"Typeface name":"Webdings","Dingbat dec":"148","Dingbat hex":"94","Unicode dec":"128106","Unicode hex":"1F46A"},{"Typeface name":"Webdings","Dingbat dec":"149","Dingbat hex":"95","Unicode dec":"128481","Unicode hex":"1F5E1"},{"Typeface name":"Webdings","Dingbat dec":"150","Dingbat hex":"96","Unicode dec":"128482","Unicode hex":"1F5E2"},{"Typeface name":"Webdings","Dingbat dec":"151","Dingbat hex":"97","Unicode dec":"128483","Unicode hex":"1F5E3"},{"Typeface name":"Webdings","Dingbat dec":"152","Dingbat hex":"98","Unicode dec":"10031","Unicode hex":"272F"},{"Typeface name":"Webdings","Dingbat dec":"153","Dingbat hex":"99","Unicode dec":"128388","Unicode hex":"1F584"},{"Typeface name":"Webdings","Dingbat dec":"154","Dingbat hex":"9A","Unicode dec":"128389","Unicode hex":"1F585"},{"Typeface name":"Webdings","Dingbat dec":"155","Dingbat hex":"9B","Unicode dec":"128387","Unicode hex":"1F583"},{"Typeface name":"Webdings","Dingbat dec":"156","Dingbat hex":"9C","Unicode dec":"128390","Unicode hex":"1F586"},{"Typeface name":"Webdings","Dingbat dec":"157","Dingbat hex":"9D","Unicode dec":"128441","Unicode hex":"1F5B9"},{"Typeface name":"Webdings","Dingbat dec":"158","Dingbat hex":"9E","Unicode dec":"128442","Unicode hex":"1F5BA"},{"Typeface name":"Webdings","Dingbat dec":"159","Dingbat hex":"9F","Unicode dec":"128443","Unicode hex":"1F5BB"},{"Typeface name":"Webdings","Dingbat dec":"160","Dingbat hex":"A0","Unicode dec":"128373","Unicode hex":"1F575"},{"Typeface name":"Webdings","Dingbat dec":"161","Dingbat hex":"A1","Unicode dec":"128368","Unicode hex":"1F570"},{"Typeface name":"Webdings","Dingbat dec":"162","Dingbat hex":"A2","Unicode dec":"128445","Unicode hex":"1F5BD"},{"Typeface name":"Webdings","Dingbat dec":"163","Dingbat hex":"A3","Unicode dec":"128446","Unicode hex":"1F5BE"},{"Typeface name":"Webdings","Dingbat dec":"164","Dingbat hex":"A4","Unicode dec":"128203","Unicode hex":"1F4CB"},{"Typeface name":"Webdings","Dingbat dec":"165","Dingbat hex":"A5","Unicode dec":"128466","Unicode hex":"1F5D2"},{"Typeface name":"Webdings","Dingbat dec":"166","Dingbat hex":"A6","Unicode dec":"128467","Unicode hex":"1F5D3"},{"Typeface name":"Webdings","Dingbat dec":"167","Dingbat hex":"A7","Unicode dec":"128366","Unicode hex":"1F56E"},{"Typeface name":"Webdings","Dingbat dec":"168","Dingbat hex":"A8","Unicode dec":"128218","Unicode hex":"1F4DA"},{"Typeface name":"Webdings","Dingbat dec":"169","Dingbat hex":"A9","Unicode dec":"128478","Unicode hex":"1F5DE"},{"Typeface name":"Webdings","Dingbat dec":"170","Dingbat hex":"AA","Unicode dec":"128479","Unicode hex":"1F5DF"},{"Typeface name":"Webdings","Dingbat dec":"171","Dingbat hex":"AB","Unicode dec":"128451","Unicode hex":"1F5C3"},{"Typeface name":"Webdings","Dingbat dec":"172","Dingbat hex":"AC","Unicode dec":"128450","Unicode hex":"1F5C2"},{"Typeface name":"Webdings","Dingbat dec":"173","Dingbat hex":"AD","Unicode dec":"128444","Unicode hex":"1F5BC"},{"Typeface name":"Webdings","Dingbat dec":"174","Dingbat hex":"AE","Unicode dec":"127917","Unicode hex":"1F3AD"},{"Typeface name":"Webdings","Dingbat dec":"175","Dingbat hex":"AF","Unicode dec":"127900","Unicode hex":"1F39C"},{"Typeface name":"Webdings","Dingbat dec":"176","Dingbat hex":"B0","Unicode dec":"127896","Unicode hex":"1F398"},{"Typeface name":"Webdings","Dingbat dec":"177","Dingbat hex":"B1","Unicode dec":"127897","Unicode hex":"1F399"},{"Typeface name":"Webdings","Dingbat dec":"178","Dingbat hex":"B2","Unicode dec":"127911","Unicode hex":"1F3A7"},{"Typeface name":"Webdings","Dingbat dec":"179","Dingbat hex":"B3","Unicode dec":"128191","Unicode hex":"1F4BF"},{"Typeface name":"Webdings","Dingbat dec":"180","Dingbat hex":"B4","Unicode dec":"127902","Unicode hex":"1F39E"},{"Typeface name":"Webdings","Dingbat dec":"181","Dingbat hex":"B5","Unicode dec":"128247","Unicode hex":"1F4F7"},{"Typeface name":"Webdings","Dingbat dec":"182","Dingbat hex":"B6","Unicode dec":"127903","Unicode hex":"1F39F"},{"Typeface name":"Webdings","Dingbat dec":"183","Dingbat hex":"B7","Unicode dec":"127916","Unicode hex":"1F3AC"},{"Typeface name":"Webdings","Dingbat dec":"184","Dingbat hex":"B8","Unicode dec":"128253","Unicode hex":"1F4FD"},{"Typeface name":"Webdings","Dingbat dec":"185","Dingbat hex":"B9","Unicode dec":"128249","Unicode hex":"1F4F9"},{"Typeface name":"Webdings","Dingbat dec":"186","Dingbat hex":"BA","Unicode dec":"128254","Unicode hex":"1F4FE"},{"Typeface name":"Webdings","Dingbat dec":"187","Dingbat hex":"BB","Unicode dec":"128251","Unicode hex":"1F4FB"},{"Typeface name":"Webdings","Dingbat dec":"188","Dingbat hex":"BC","Unicode dec":"127898","Unicode hex":"1F39A"},{"Typeface name":"Webdings","Dingbat dec":"189","Dingbat hex":"BD","Unicode dec":"127899","Unicode hex":"1F39B"},{"Typeface name":"Webdings","Dingbat dec":"190","Dingbat hex":"BE","Unicode dec":"128250","Unicode hex":"1F4FA"},{"Typeface name":"Webdings","Dingbat dec":"191","Dingbat hex":"BF","Unicode dec":"128187","Unicode hex":"1F4BB"},{"Typeface name":"Webdings","Dingbat dec":"192","Dingbat hex":"C0","Unicode dec":"128421","Unicode hex":"1F5A5"},{"Typeface name":"Webdings","Dingbat dec":"193","Dingbat hex":"C1","Unicode dec":"128422","Unicode hex":"1F5A6"},{"Typeface name":"Webdings","Dingbat dec":"194","Dingbat hex":"C2","Unicode dec":"128423","Unicode hex":"1F5A7"},{"Typeface name":"Webdings","Dingbat dec":"195","Dingbat hex":"C3","Unicode dec":"128377","Unicode hex":"1F579"},{"Typeface name":"Webdings","Dingbat dec":"196","Dingbat hex":"C4","Unicode dec":"127918","Unicode hex":"1F3AE"},{"Typeface name":"Webdings","Dingbat dec":"197","Dingbat hex":"C5","Unicode dec":"128379","Unicode hex":"1F57B"},{"Typeface name":"Webdings","Dingbat dec":"198","Dingbat hex":"C6","Unicode dec":"128380","Unicode hex":"1F57C"},{"Typeface name":"Webdings","Dingbat dec":"199","Dingbat hex":"C7","Unicode dec":"128223","Unicode hex":"1F4DF"},{"Typeface name":"Webdings","Dingbat dec":"200","Dingbat hex":"C8","Unicode dec":"128385","Unicode hex":"1F581"},{"Typeface name":"Webdings","Dingbat dec":"201","Dingbat hex":"C9","Unicode dec":"128384","Unicode hex":"1F580"},{"Typeface name":"Webdings","Dingbat dec":"202","Dingbat hex":"CA","Unicode dec":"128424","Unicode hex":"1F5A8"},{"Typeface name":"Webdings","Dingbat dec":"203","Dingbat hex":"CB","Unicode dec":"128425","Unicode hex":"1F5A9"},{"Typeface name":"Webdings","Dingbat dec":"204","Dingbat hex":"CC","Unicode dec":"128447","Unicode hex":"1F5BF"},{"Typeface name":"Webdings","Dingbat dec":"205","Dingbat hex":"CD","Unicode dec":"128426","Unicode hex":"1F5AA"},{"Typeface name":"Webdings","Dingbat dec":"206","Dingbat hex":"CE","Unicode dec":"128476","Unicode hex":"1F5DC"},{"Typeface name":"Webdings","Dingbat dec":"207","Dingbat hex":"CF","Unicode dec":"128274","Unicode hex":"1F512"},{"Typeface name":"Webdings","Dingbat dec":"208","Dingbat hex":"D0","Unicode dec":"128275","Unicode hex":"1F513"},{"Typeface name":"Webdings","Dingbat dec":"209","Dingbat hex":"D1","Unicode dec":"128477","Unicode hex":"1F5DD"},{"Typeface name":"Webdings","Dingbat dec":"210","Dingbat hex":"D2","Unicode dec":"128229","Unicode hex":"1F4E5"},{"Typeface name":"Webdings","Dingbat dec":"211","Dingbat hex":"D3","Unicode dec":"128228","Unicode hex":"1F4E4"},{"Typeface name":"Webdings","Dingbat dec":"212","Dingbat hex":"D4","Unicode dec":"128371","Unicode hex":"1F573"},{"Typeface name":"Webdings","Dingbat dec":"213","Dingbat hex":"D5","Unicode dec":"127779","Unicode hex":"1F323"},{"Typeface name":"Webdings","Dingbat dec":"214","Dingbat hex":"D6","Unicode dec":"127780","Unicode hex":"1F324"},{"Typeface name":"Webdings","Dingbat dec":"215","Dingbat hex":"D7","Unicode dec":"127781","Unicode hex":"1F325"},{"Typeface name":"Webdings","Dingbat dec":"216","Dingbat hex":"D8","Unicode dec":"127782","Unicode hex":"1F326"},{"Typeface name":"Webdings","Dingbat dec":"217","Dingbat hex":"D9","Unicode dec":"9729","Unicode hex":"2601"},{"Typeface name":"Webdings","Dingbat dec":"218","Dingbat hex":"DA","Unicode dec":"127784","Unicode hex":"1F328"},{"Typeface name":"Webdings","Dingbat dec":"219","Dingbat hex":"DB","Unicode dec":"127783","Unicode hex":"1F327"},{"Typeface name":"Webdings","Dingbat dec":"220","Dingbat hex":"DC","Unicode dec":"127785","Unicode hex":"1F329"},{"Typeface name":"Webdings","Dingbat dec":"221","Dingbat hex":"DD","Unicode dec":"127786","Unicode hex":"1F32A"},{"Typeface name":"Webdings","Dingbat dec":"222","Dingbat hex":"DE","Unicode dec":"127788","Unicode hex":"1F32C"},{"Typeface name":"Webdings","Dingbat dec":"223","Dingbat hex":"DF","Unicode dec":"127787","Unicode hex":"1F32B"},{"Typeface name":"Webdings","Dingbat dec":"224","Dingbat hex":"E0","Unicode dec":"127772","Unicode hex":"1F31C"},{"Typeface name":"Webdings","Dingbat dec":"225","Dingbat hex":"E1","Unicode dec":"127777","Unicode hex":"1F321"},{"Typeface name":"Webdings","Dingbat dec":"226","Dingbat hex":"E2","Unicode dec":"128715","Unicode hex":"1F6CB"},{"Typeface name":"Webdings","Dingbat dec":"227","Dingbat hex":"E3","Unicode dec":"128719","Unicode hex":"1F6CF"},{"Typeface name":"Webdings","Dingbat dec":"228","Dingbat hex":"E4","Unicode dec":"127869","Unicode hex":"1F37D"},{"Typeface name":"Webdings","Dingbat dec":"229","Dingbat hex":"E5","Unicode dec":"127864","Unicode hex":"1F378"},{"Typeface name":"Webdings","Dingbat dec":"230","Dingbat hex":"E6","Unicode dec":"128718","Unicode hex":"1F6CE"},{"Typeface name":"Webdings","Dingbat dec":"231","Dingbat hex":"E7","Unicode dec":"128717","Unicode hex":"1F6CD"},{"Typeface name":"Webdings","Dingbat dec":"232","Dingbat hex":"E8","Unicode dec":"9413","Unicode hex":"24C5"},{"Typeface name":"Webdings","Dingbat dec":"233","Dingbat hex":"E9","Unicode dec":"9855","Unicode hex":"267F"},{"Typeface name":"Webdings","Dingbat dec":"234","Dingbat hex":"EA","Unicode dec":"128710","Unicode hex":"1F6C6"},{"Typeface name":"Webdings","Dingbat dec":"235","Dingbat hex":"EB","Unicode dec":"128392","Unicode hex":"1F588"},{"Typeface name":"Webdings","Dingbat dec":"236","Dingbat hex":"EC","Unicode dec":"127891","Unicode hex":"1F393"},{"Typeface name":"Webdings","Dingbat dec":"237","Dingbat hex":"ED","Unicode dec":"128484","Unicode hex":"1F5E4"},{"Typeface name":"Webdings","Dingbat dec":"238","Dingbat hex":"EE","Unicode dec":"128485","Unicode hex":"1F5E5"},{"Typeface name":"Webdings","Dingbat dec":"239","Dingbat hex":"EF","Unicode dec":"128486","Unicode hex":"1F5E6"},{"Typeface name":"Webdings","Dingbat dec":"240","Dingbat hex":"F0","Unicode dec":"128487","Unicode hex":"1F5E7"},{"Typeface name":"Webdings","Dingbat dec":"241","Dingbat hex":"F1","Unicode dec":"128746","Unicode hex":"1F6EA"},{"Typeface name":"Webdings","Dingbat dec":"242","Dingbat hex":"F2","Unicode dec":"128063","Unicode hex":"1F43F"},{"Typeface name":"Webdings","Dingbat dec":"243","Dingbat hex":"F3","Unicode dec":"128038","Unicode hex":"1F426"},{"Typeface name":"Webdings","Dingbat dec":"244","Dingbat hex":"F4","Unicode dec":"128031","Unicode hex":"1F41F"},{"Typeface name":"Webdings","Dingbat dec":"245","Dingbat hex":"F5","Unicode dec":"128021","Unicode hex":"1F415"},{"Typeface name":"Webdings","Dingbat dec":"246","Dingbat hex":"F6","Unicode dec":"128008","Unicode hex":"1F408"},{"Typeface name":"Webdings","Dingbat dec":"247","Dingbat hex":"F7","Unicode dec":"128620","Unicode hex":"1F66C"},{"Typeface name":"Webdings","Dingbat dec":"248","Dingbat hex":"F8","Unicode dec":"128622","Unicode hex":"1F66E"},{"Typeface name":"Webdings","Dingbat dec":"249","Dingbat hex":"F9","Unicode dec":"128621","Unicode hex":"1F66D"},{"Typeface name":"Webdings","Dingbat dec":"250","Dingbat hex":"FA","Unicode dec":"128623","Unicode hex":"1F66F"},{"Typeface name":"Webdings","Dingbat dec":"251","Dingbat hex":"FB","Unicode dec":"128506","Unicode hex":"1F5FA"},{"Typeface name":"Webdings","Dingbat dec":"252","Dingbat hex":"FC","Unicode dec":"127757","Unicode hex":"1F30D"},{"Typeface name":"Webdings","Dingbat dec":"253","Dingbat hex":"FD","Unicode dec":"127759","Unicode hex":"1F30F"},{"Typeface name":"Webdings","Dingbat dec":"254","Dingbat hex":"FE","Unicode dec":"127758","Unicode hex":"1F30E"},{"Typeface name":"Webdings","Dingbat dec":"255","Dingbat hex":"FF","Unicode dec":"128330","Unicode hex":"1F54A"},{"Typeface name":"Wingdings","Dingbat dec":"32","Dingbat hex":"20","Unicode dec":"32","Unicode hex":"20"},{"Typeface name":"Wingdings","Dingbat dec":"33","Dingbat hex":"21","Unicode dec":"128393","Unicode hex":"1F589"},{"Typeface name":"Wingdings","Dingbat dec":"34","Dingbat hex":"22","Unicode dec":"9986","Unicode hex":"2702"},{"Typeface name":"Wingdings","Dingbat dec":"35","Dingbat hex":"23","Unicode dec":"9985","Unicode hex":"2701"},{"Typeface name":"Wingdings","Dingbat dec":"36","Dingbat hex":"24","Unicode dec":"128083","Unicode hex":"1F453"},{"Typeface name":"Wingdings","Dingbat dec":"37","Dingbat hex":"25","Unicode dec":"128365","Unicode hex":"1F56D"},{"Typeface name":"Wingdings","Dingbat dec":"38","Dingbat hex":"26","Unicode dec":"128366","Unicode hex":"1F56E"},{"Typeface name":"Wingdings","Dingbat dec":"39","Dingbat hex":"27","Unicode dec":"128367","Unicode hex":"1F56F"},{"Typeface name":"Wingdings","Dingbat dec":"40","Dingbat hex":"28","Unicode dec":"128383","Unicode hex":"1F57F"},{"Typeface name":"Wingdings","Dingbat dec":"41","Dingbat hex":"29","Unicode dec":"9990","Unicode hex":"2706"},{"Typeface name":"Wingdings","Dingbat dec":"42","Dingbat hex":"2A","Unicode dec":"128386","Unicode hex":"1F582"},{"Typeface name":"Wingdings","Dingbat dec":"43","Dingbat hex":"2B","Unicode dec":"128387","Unicode hex":"1F583"},{"Typeface name":"Wingdings","Dingbat dec":"44","Dingbat hex":"2C","Unicode dec":"128234","Unicode hex":"1F4EA"},{"Typeface name":"Wingdings","Dingbat dec":"45","Dingbat hex":"2D","Unicode dec":"128235","Unicode hex":"1F4EB"},{"Typeface name":"Wingdings","Dingbat dec":"46","Dingbat hex":"2E","Unicode dec":"128236","Unicode hex":"1F4EC"},{"Typeface name":"Wingdings","Dingbat dec":"47","Dingbat hex":"2F","Unicode dec":"128237","Unicode hex":"1F4ED"},{"Typeface name":"Wingdings","Dingbat dec":"48","Dingbat hex":"30","Unicode dec":"128448","Unicode hex":"1F5C0"},{"Typeface name":"Wingdings","Dingbat dec":"49","Dingbat hex":"31","Unicode dec":"128449","Unicode hex":"1F5C1"},{"Typeface name":"Wingdings","Dingbat dec":"50","Dingbat hex":"32","Unicode dec":"128462","Unicode hex":"1F5CE"},{"Typeface name":"Wingdings","Dingbat dec":"51","Dingbat hex":"33","Unicode dec":"128463","Unicode hex":"1F5CF"},{"Typeface name":"Wingdings","Dingbat dec":"52","Dingbat hex":"34","Unicode dec":"128464","Unicode hex":"1F5D0"},{"Typeface name":"Wingdings","Dingbat dec":"53","Dingbat hex":"35","Unicode dec":"128452","Unicode hex":"1F5C4"},{"Typeface name":"Wingdings","Dingbat dec":"54","Dingbat hex":"36","Unicode dec":"8987","Unicode hex":"231B"},{"Typeface name":"Wingdings","Dingbat dec":"55","Dingbat hex":"37","Unicode dec":"128430","Unicode hex":"1F5AE"},{"Typeface name":"Wingdings","Dingbat dec":"56","Dingbat hex":"38","Unicode dec":"128432","Unicode hex":"1F5B0"},{"Typeface name":"Wingdings","Dingbat dec":"57","Dingbat hex":"39","Unicode dec":"128434","Unicode hex":"1F5B2"},{"Typeface name":"Wingdings","Dingbat dec":"58","Dingbat hex":"3A","Unicode dec":"128435","Unicode hex":"1F5B3"},{"Typeface name":"Wingdings","Dingbat dec":"59","Dingbat hex":"3B","Unicode dec":"128436","Unicode hex":"1F5B4"},{"Typeface name":"Wingdings","Dingbat dec":"60","Dingbat hex":"3C","Unicode dec":"128427","Unicode hex":"1F5AB"},{"Typeface name":"Wingdings","Dingbat dec":"61","Dingbat hex":"3D","Unicode dec":"128428","Unicode hex":"1F5AC"},{"Typeface name":"Wingdings","Dingbat dec":"62","Dingbat hex":"3E","Unicode dec":"9991","Unicode hex":"2707"},{"Typeface name":"Wingdings","Dingbat dec":"63","Dingbat hex":"3F","Unicode dec":"9997","Unicode hex":"270D"},{"Typeface name":"Wingdings","Dingbat dec":"64","Dingbat hex":"40","Unicode dec":"128398","Unicode hex":"1F58E"},{"Typeface name":"Wingdings","Dingbat dec":"65","Dingbat hex":"41","Unicode dec":"9996","Unicode hex":"270C"},{"Typeface name":"Wingdings","Dingbat dec":"66","Dingbat hex":"42","Unicode dec":"128399","Unicode hex":"1F58F"},{"Typeface name":"Wingdings","Dingbat dec":"67","Dingbat hex":"43","Unicode dec":"128077","Unicode hex":"1F44D"},{"Typeface name":"Wingdings","Dingbat dec":"68","Dingbat hex":"44","Unicode dec":"128078","Unicode hex":"1F44E"},{"Typeface name":"Wingdings","Dingbat dec":"69","Dingbat hex":"45","Unicode dec":"9756","Unicode hex":"261C"},{"Typeface name":"Wingdings","Dingbat dec":"70","Dingbat hex":"46","Unicode dec":"9758","Unicode hex":"261E"},{"Typeface name":"Wingdings","Dingbat dec":"71","Dingbat hex":"47","Unicode dec":"9757","Unicode hex":"261D"},{"Typeface name":"Wingdings","Dingbat dec":"72","Dingbat hex":"48","Unicode dec":"9759","Unicode hex":"261F"},{"Typeface name":"Wingdings","Dingbat dec":"73","Dingbat hex":"49","Unicode dec":"128400","Unicode hex":"1F590"},{"Typeface name":"Wingdings","Dingbat dec":"74","Dingbat hex":"4A","Unicode dec":"9786","Unicode hex":"263A"},{"Typeface name":"Wingdings","Dingbat dec":"75","Dingbat hex":"4B","Unicode dec":"128528","Unicode hex":"1F610"},{"Typeface name":"Wingdings","Dingbat dec":"76","Dingbat hex":"4C","Unicode dec":"9785","Unicode hex":"2639"},{"Typeface name":"Wingdings","Dingbat dec":"77","Dingbat hex":"4D","Unicode dec":"128163","Unicode hex":"1F4A3"},{"Typeface name":"Wingdings","Dingbat dec":"78","Dingbat hex":"4E","Unicode dec":"128369","Unicode hex":"1F571"},{"Typeface name":"Wingdings","Dingbat dec":"79","Dingbat hex":"4F","Unicode dec":"127987","Unicode hex":"1F3F3"},{"Typeface name":"Wingdings","Dingbat dec":"80","Dingbat hex":"50","Unicode dec":"127985","Unicode hex":"1F3F1"},{"Typeface name":"Wingdings","Dingbat dec":"81","Dingbat hex":"51","Unicode dec":"9992","Unicode hex":"2708"},{"Typeface name":"Wingdings","Dingbat dec":"82","Dingbat hex":"52","Unicode dec":"9788","Unicode hex":"263C"},{"Typeface name":"Wingdings","Dingbat dec":"83","Dingbat hex":"53","Unicode dec":"127778","Unicode hex":"1F322"},{"Typeface name":"Wingdings","Dingbat dec":"84","Dingbat hex":"54","Unicode dec":"10052","Unicode hex":"2744"},{"Typeface name":"Wingdings","Dingbat dec":"85","Dingbat hex":"55","Unicode dec":"128326","Unicode hex":"1F546"},{"Typeface name":"Wingdings","Dingbat dec":"86","Dingbat hex":"56","Unicode dec":"10014","Unicode hex":"271E"},{"Typeface name":"Wingdings","Dingbat dec":"87","Dingbat hex":"57","Unicode dec":"128328","Unicode hex":"1F548"},{"Typeface name":"Wingdings","Dingbat dec":"88","Dingbat hex":"58","Unicode dec":"10016","Unicode hex":"2720"},{"Typeface name":"Wingdings","Dingbat dec":"89","Dingbat hex":"59","Unicode dec":"10017","Unicode hex":"2721"},{"Typeface name":"Wingdings","Dingbat dec":"90","Dingbat hex":"5A","Unicode dec":"9770","Unicode hex":"262A"},{"Typeface name":"Wingdings","Dingbat dec":"91","Dingbat hex":"5B","Unicode dec":"9775","Unicode hex":"262F"},{"Typeface name":"Wingdings","Dingbat dec":"92","Dingbat hex":"5C","Unicode dec":"128329","Unicode hex":"1F549"},{"Typeface name":"Wingdings","Dingbat dec":"93","Dingbat hex":"5D","Unicode dec":"9784","Unicode hex":"2638"},{"Typeface name":"Wingdings","Dingbat dec":"94","Dingbat hex":"5E","Unicode dec":"9800","Unicode hex":"2648"},{"Typeface name":"Wingdings","Dingbat dec":"95","Dingbat hex":"5F","Unicode dec":"9801","Unicode hex":"2649"},{"Typeface name":"Wingdings","Dingbat dec":"96","Dingbat hex":"60","Unicode dec":"9802","Unicode hex":"264A"},{"Typeface name":"Wingdings","Dingbat dec":"97","Dingbat hex":"61","Unicode dec":"9803","Unicode hex":"264B"},{"Typeface name":"Wingdings","Dingbat dec":"98","Dingbat hex":"62","Unicode dec":"9804","Unicode hex":"264C"},{"Typeface name":"Wingdings","Dingbat dec":"99","Dingbat hex":"63","Unicode dec":"9805","Unicode hex":"264D"},{"Typeface name":"Wingdings","Dingbat dec":"100","Dingbat hex":"64","Unicode dec":"9806","Unicode hex":"264E"},{"Typeface name":"Wingdings","Dingbat dec":"101","Dingbat hex":"65","Unicode dec":"9807","Unicode hex":"264F"},{"Typeface name":"Wingdings","Dingbat dec":"102","Dingbat hex":"66","Unicode dec":"9808","Unicode hex":"2650"},{"Typeface name":"Wingdings","Dingbat dec":"103","Dingbat hex":"67","Unicode dec":"9809","Unicode hex":"2651"},{"Typeface name":"Wingdings","Dingbat dec":"104","Dingbat hex":"68","Unicode dec":"9810","Unicode hex":"2652"},{"Typeface name":"Wingdings","Dingbat dec":"105","Dingbat hex":"69","Unicode dec":"9811","Unicode hex":"2653"},{"Typeface name":"Wingdings","Dingbat dec":"106","Dingbat hex":"6A","Unicode dec":"128624","Unicode hex":"1F670"},{"Typeface name":"Wingdings","Dingbat dec":"107","Dingbat hex":"6B","Unicode dec":"128629","Unicode hex":"1F675"},{"Typeface name":"Wingdings","Dingbat dec":"108","Dingbat hex":"6C","Unicode dec":"9899","Unicode hex":"26AB"},{"Typeface name":"Wingdings","Dingbat dec":"109","Dingbat hex":"6D","Unicode dec":"128318","Unicode hex":"1F53E"},{"Typeface name":"Wingdings","Dingbat dec":"110","Dingbat hex":"6E","Unicode dec":"9724","Unicode hex":"25FC"},{"Typeface name":"Wingdings","Dingbat dec":"111","Dingbat hex":"6F","Unicode dec":"128911","Unicode hex":"1F78F"},{"Typeface name":"Wingdings","Dingbat dec":"112","Dingbat hex":"70","Unicode dec":"128912","Unicode hex":"1F790"},{"Typeface name":"Wingdings","Dingbat dec":"113","Dingbat hex":"71","Unicode dec":"10065","Unicode hex":"2751"},{"Typeface name":"Wingdings","Dingbat dec":"114","Dingbat hex":"72","Unicode dec":"10066","Unicode hex":"2752"},{"Typeface name":"Wingdings","Dingbat dec":"115","Dingbat hex":"73","Unicode dec":"128927","Unicode hex":"1F79F"},{"Typeface name":"Wingdings","Dingbat dec":"116","Dingbat hex":"74","Unicode dec":"10731","Unicode hex":"29EB"},{"Typeface name":"Wingdings","Dingbat dec":"117","Dingbat hex":"75","Unicode dec":"9670","Unicode hex":"25C6"},{"Typeface name":"Wingdings","Dingbat dec":"118","Dingbat hex":"76","Unicode dec":"10070","Unicode hex":"2756"},{"Typeface name":"Wingdings","Dingbat dec":"119","Dingbat hex":"77","Unicode dec":"11049","Unicode hex":"2B29"},{"Typeface name":"Wingdings","Dingbat dec":"120","Dingbat hex":"78","Unicode dec":"8999","Unicode hex":"2327"},{"Typeface name":"Wingdings","Dingbat dec":"121","Dingbat hex":"79","Unicode dec":"11193","Unicode hex":"2BB9"},{"Typeface name":"Wingdings","Dingbat dec":"122","Dingbat hex":"7A","Unicode dec":"8984","Unicode hex":"2318"},{"Typeface name":"Wingdings","Dingbat dec":"123","Dingbat hex":"7B","Unicode dec":"127989","Unicode hex":"1F3F5"},{"Typeface name":"Wingdings","Dingbat dec":"124","Dingbat hex":"7C","Unicode dec":"127990","Unicode hex":"1F3F6"},{"Typeface name":"Wingdings","Dingbat dec":"125","Dingbat hex":"7D","Unicode dec":"128630","Unicode hex":"1F676"},{"Typeface name":"Wingdings","Dingbat dec":"126","Dingbat hex":"7E","Unicode dec":"128631","Unicode hex":"1F677"},{"Typeface name":"Wingdings","Dingbat dec":"127","Dingbat hex":"7F","Unicode dec":"9647","Unicode hex":"25AF"},{"Typeface name":"Wingdings","Dingbat dec":"128","Dingbat hex":"80","Unicode dec":"127243","Unicode hex":"1F10B"},{"Typeface name":"Wingdings","Dingbat dec":"129","Dingbat hex":"81","Unicode dec":"10112","Unicode hex":"2780"},{"Typeface name":"Wingdings","Dingbat dec":"130","Dingbat hex":"82","Unicode dec":"10113","Unicode hex":"2781"},{"Typeface name":"Wingdings","Dingbat dec":"131","Dingbat hex":"83","Unicode dec":"10114","Unicode hex":"2782"},{"Typeface name":"Wingdings","Dingbat dec":"132","Dingbat hex":"84","Unicode dec":"10115","Unicode hex":"2783"},{"Typeface name":"Wingdings","Dingbat dec":"133","Dingbat hex":"85","Unicode dec":"10116","Unicode hex":"2784"},{"Typeface name":"Wingdings","Dingbat dec":"134","Dingbat hex":"86","Unicode dec":"10117","Unicode hex":"2785"},{"Typeface name":"Wingdings","Dingbat dec":"135","Dingbat hex":"87","Unicode dec":"10118","Unicode hex":"2786"},{"Typeface name":"Wingdings","Dingbat dec":"136","Dingbat hex":"88","Unicode dec":"10119","Unicode hex":"2787"},{"Typeface name":"Wingdings","Dingbat dec":"137","Dingbat hex":"89","Unicode dec":"10120","Unicode hex":"2788"},{"Typeface name":"Wingdings","Dingbat dec":"138","Dingbat hex":"8A","Unicode dec":"10121","Unicode hex":"2789"},{"Typeface name":"Wingdings","Dingbat dec":"139","Dingbat hex":"8B","Unicode dec":"127244","Unicode hex":"1F10C"},{"Typeface name":"Wingdings","Dingbat dec":"140","Dingbat hex":"8C","Unicode dec":"10122","Unicode hex":"278A"},{"Typeface name":"Wingdings","Dingbat dec":"141","Dingbat hex":"8D","Unicode dec":"10123","Unicode hex":"278B"},{"Typeface name":"Wingdings","Dingbat dec":"142","Dingbat hex":"8E","Unicode dec":"10124","Unicode hex":"278C"},{"Typeface name":"Wingdings","Dingbat dec":"143","Dingbat hex":"8F","Unicode dec":"10125","Unicode hex":"278D"},{"Typeface name":"Wingdings","Dingbat dec":"144","Dingbat hex":"90","Unicode dec":"10126","Unicode hex":"278E"},{"Typeface name":"Wingdings","Dingbat dec":"145","Dingbat hex":"91","Unicode dec":"10127","Unicode hex":"278F"},{"Typeface name":"Wingdings","Dingbat dec":"146","Dingbat hex":"92","Unicode dec":"10128","Unicode hex":"2790"},{"Typeface name":"Wingdings","Dingbat dec":"147","Dingbat hex":"93","Unicode dec":"10129","Unicode hex":"2791"},{"Typeface name":"Wingdings","Dingbat dec":"148","Dingbat hex":"94","Unicode dec":"10130","Unicode hex":"2792"},{"Typeface name":"Wingdings","Dingbat dec":"149","Dingbat hex":"95","Unicode dec":"10131","Unicode hex":"2793"},{"Typeface name":"Wingdings","Dingbat dec":"150","Dingbat hex":"96","Unicode dec":"128610","Unicode hex":"1F662"},{"Typeface name":"Wingdings","Dingbat dec":"151","Dingbat hex":"97","Unicode dec":"128608","Unicode hex":"1F660"},{"Typeface name":"Wingdings","Dingbat dec":"152","Dingbat hex":"98","Unicode dec":"128609","Unicode hex":"1F661"},{"Typeface name":"Wingdings","Dingbat dec":"153","Dingbat hex":"99","Unicode dec":"128611","Unicode hex":"1F663"},{"Typeface name":"Wingdings","Dingbat dec":"154","Dingbat hex":"9A","Unicode dec":"128606","Unicode hex":"1F65E"},{"Typeface name":"Wingdings","Dingbat dec":"155","Dingbat hex":"9B","Unicode dec":"128604","Unicode hex":"1F65C"},{"Typeface name":"Wingdings","Dingbat dec":"156","Dingbat hex":"9C","Unicode dec":"128605","Unicode hex":"1F65D"},{"Typeface name":"Wingdings","Dingbat dec":"157","Dingbat hex":"9D","Unicode dec":"128607","Unicode hex":"1F65F"},{"Typeface name":"Wingdings","Dingbat dec":"158","Dingbat hex":"9E","Unicode dec":"8729","Unicode hex":"2219"},{"Typeface name":"Wingdings","Dingbat dec":"159","Dingbat hex":"9F","Unicode dec":"8226","Unicode hex":"2022"},{"Typeface name":"Wingdings","Dingbat dec":"160","Dingbat hex":"A0","Unicode dec":"11037","Unicode hex":"2B1D"},{"Typeface name":"Wingdings","Dingbat dec":"161","Dingbat hex":"A1","Unicode dec":"11096","Unicode hex":"2B58"},{"Typeface name":"Wingdings","Dingbat dec":"162","Dingbat hex":"A2","Unicode dec":"128902","Unicode hex":"1F786"},{"Typeface name":"Wingdings","Dingbat dec":"163","Dingbat hex":"A3","Unicode dec":"128904","Unicode hex":"1F788"},{"Typeface name":"Wingdings","Dingbat dec":"164","Dingbat hex":"A4","Unicode dec":"128906","Unicode hex":"1F78A"},{"Typeface name":"Wingdings","Dingbat dec":"165","Dingbat hex":"A5","Unicode dec":"128907","Unicode hex":"1F78B"},{"Typeface name":"Wingdings","Dingbat dec":"166","Dingbat hex":"A6","Unicode dec":"128319","Unicode hex":"1F53F"},{"Typeface name":"Wingdings","Dingbat dec":"167","Dingbat hex":"A7","Unicode dec":"9642","Unicode hex":"25AA"},{"Typeface name":"Wingdings","Dingbat dec":"168","Dingbat hex":"A8","Unicode dec":"128910","Unicode hex":"1F78E"},{"Typeface name":"Wingdings","Dingbat dec":"169","Dingbat hex":"A9","Unicode dec":"128961","Unicode hex":"1F7C1"},{"Typeface name":"Wingdings","Dingbat dec":"170","Dingbat hex":"AA","Unicode dec":"128965","Unicode hex":"1F7C5"},{"Typeface name":"Wingdings","Dingbat dec":"171","Dingbat hex":"AB","Unicode dec":"9733","Unicode hex":"2605"},{"Typeface name":"Wingdings","Dingbat dec":"172","Dingbat hex":"AC","Unicode dec":"128971","Unicode hex":"1F7CB"},{"Typeface name":"Wingdings","Dingbat dec":"173","Dingbat hex":"AD","Unicode dec":"128975","Unicode hex":"1F7CF"},{"Typeface name":"Wingdings","Dingbat dec":"174","Dingbat hex":"AE","Unicode dec":"128979","Unicode hex":"1F7D3"},{"Typeface name":"Wingdings","Dingbat dec":"175","Dingbat hex":"AF","Unicode dec":"128977","Unicode hex":"1F7D1"},{"Typeface name":"Wingdings","Dingbat dec":"176","Dingbat hex":"B0","Unicode dec":"11216","Unicode hex":"2BD0"},{"Typeface name":"Wingdings","Dingbat dec":"177","Dingbat hex":"B1","Unicode dec":"8982","Unicode hex":"2316"},{"Typeface name":"Wingdings","Dingbat dec":"178","Dingbat hex":"B2","Unicode dec":"11214","Unicode hex":"2BCE"},{"Typeface name":"Wingdings","Dingbat dec":"179","Dingbat hex":"B3","Unicode dec":"11215","Unicode hex":"2BCF"},{"Typeface name":"Wingdings","Dingbat dec":"180","Dingbat hex":"B4","Unicode dec":"11217","Unicode hex":"2BD1"},{"Typeface name":"Wingdings","Dingbat dec":"181","Dingbat hex":"B5","Unicode dec":"10026","Unicode hex":"272A"},{"Typeface name":"Wingdings","Dingbat dec":"182","Dingbat hex":"B6","Unicode dec":"10032","Unicode hex":"2730"},{"Typeface name":"Wingdings","Dingbat dec":"183","Dingbat hex":"B7","Unicode dec":"128336","Unicode hex":"1F550"},{"Typeface name":"Wingdings","Dingbat dec":"184","Dingbat hex":"B8","Unicode dec":"128337","Unicode hex":"1F551"},{"Typeface name":"Wingdings","Dingbat dec":"185","Dingbat hex":"B9","Unicode dec":"128338","Unicode hex":"1F552"},{"Typeface name":"Wingdings","Dingbat dec":"186","Dingbat hex":"BA","Unicode dec":"128339","Unicode hex":"1F553"},{"Typeface name":"Wingdings","Dingbat dec":"187","Dingbat hex":"BB","Unicode dec":"128340","Unicode hex":"1F554"},{"Typeface name":"Wingdings","Dingbat dec":"188","Dingbat hex":"BC","Unicode dec":"128341","Unicode hex":"1F555"},{"Typeface name":"Wingdings","Dingbat dec":"189","Dingbat hex":"BD","Unicode dec":"128342","Unicode hex":"1F556"},{"Typeface name":"Wingdings","Dingbat dec":"190","Dingbat hex":"BE","Unicode dec":"128343","Unicode hex":"1F557"},{"Typeface name":"Wingdings","Dingbat dec":"191","Dingbat hex":"BF","Unicode dec":"128344","Unicode hex":"1F558"},{"Typeface name":"Wingdings","Dingbat dec":"192","Dingbat hex":"C0","Unicode dec":"128345","Unicode hex":"1F559"},{"Typeface name":"Wingdings","Dingbat dec":"193","Dingbat hex":"C1","Unicode dec":"128346","Unicode hex":"1F55A"},{"Typeface name":"Wingdings","Dingbat dec":"194","Dingbat hex":"C2","Unicode dec":"128347","Unicode hex":"1F55B"},{"Typeface name":"Wingdings","Dingbat dec":"195","Dingbat hex":"C3","Unicode dec":"11184","Unicode hex":"2BB0"},{"Typeface name":"Wingdings","Dingbat dec":"196","Dingbat hex":"C4","Unicode dec":"11185","Unicode hex":"2BB1"},{"Typeface name":"Wingdings","Dingbat dec":"197","Dingbat hex":"C5","Unicode dec":"11186","Unicode hex":"2BB2"},{"Typeface name":"Wingdings","Dingbat dec":"198","Dingbat hex":"C6","Unicode dec":"11187","Unicode hex":"2BB3"},{"Typeface name":"Wingdings","Dingbat dec":"199","Dingbat hex":"C7","Unicode dec":"11188","Unicode hex":"2BB4"},{"Typeface name":"Wingdings","Dingbat dec":"200","Dingbat hex":"C8","Unicode dec":"11189","Unicode hex":"2BB5"},{"Typeface name":"Wingdings","Dingbat dec":"201","Dingbat hex":"C9","Unicode dec":"11190","Unicode hex":"2BB6"},{"Typeface name":"Wingdings","Dingbat dec":"202","Dingbat hex":"CA","Unicode dec":"11191","Unicode hex":"2BB7"},{"Typeface name":"Wingdings","Dingbat dec":"203","Dingbat hex":"CB","Unicode dec":"128618","Unicode hex":"1F66A"},{"Typeface name":"Wingdings","Dingbat dec":"204","Dingbat hex":"CC","Unicode dec":"128619","Unicode hex":"1F66B"},{"Typeface name":"Wingdings","Dingbat dec":"205","Dingbat hex":"CD","Unicode dec":"128597","Unicode hex":"1F655"},{"Typeface name":"Wingdings","Dingbat dec":"206","Dingbat hex":"CE","Unicode dec":"128596","Unicode hex":"1F654"},{"Typeface name":"Wingdings","Dingbat dec":"207","Dingbat hex":"CF","Unicode dec":"128599","Unicode hex":"1F657"},{"Typeface name":"Wingdings","Dingbat dec":"208","Dingbat hex":"D0","Unicode dec":"128598","Unicode hex":"1F656"},{"Typeface name":"Wingdings","Dingbat dec":"209","Dingbat hex":"D1","Unicode dec":"128592","Unicode hex":"1F650"},{"Typeface name":"Wingdings","Dingbat dec":"210","Dingbat hex":"D2","Unicode dec":"128593","Unicode hex":"1F651"},{"Typeface name":"Wingdings","Dingbat dec":"211","Dingbat hex":"D3","Unicode dec":"128594","Unicode hex":"1F652"},{"Typeface name":"Wingdings","Dingbat dec":"212","Dingbat hex":"D4","Unicode dec":"128595","Unicode hex":"1F653"},{"Typeface name":"Wingdings","Dingbat dec":"213","Dingbat hex":"D5","Unicode dec":"9003","Unicode hex":"232B"},{"Typeface name":"Wingdings","Dingbat dec":"214","Dingbat hex":"D6","Unicode dec":"8998","Unicode hex":"2326"},{"Typeface name":"Wingdings","Dingbat dec":"215","Dingbat hex":"D7","Unicode dec":"11160","Unicode hex":"2B98"},{"Typeface name":"Wingdings","Dingbat dec":"216","Dingbat hex":"D8","Unicode dec":"11162","Unicode hex":"2B9A"},{"Typeface name":"Wingdings","Dingbat dec":"217","Dingbat hex":"D9","Unicode dec":"11161","Unicode hex":"2B99"},{"Typeface name":"Wingdings","Dingbat dec":"218","Dingbat hex":"DA","Unicode dec":"11163","Unicode hex":"2B9B"},{"Typeface name":"Wingdings","Dingbat dec":"219","Dingbat hex":"DB","Unicode dec":"11144","Unicode hex":"2B88"},{"Typeface name":"Wingdings","Dingbat dec":"220","Dingbat hex":"DC","Unicode dec":"11146","Unicode hex":"2B8A"},{"Typeface name":"Wingdings","Dingbat dec":"221","Dingbat hex":"DD","Unicode dec":"11145","Unicode hex":"2B89"},{"Typeface name":"Wingdings","Dingbat dec":"222","Dingbat hex":"DE","Unicode dec":"11147","Unicode hex":"2B8B"},{"Typeface name":"Wingdings","Dingbat dec":"223","Dingbat hex":"DF","Unicode dec":"129128","Unicode hex":"1F868"},{"Typeface name":"Wingdings","Dingbat dec":"224","Dingbat hex":"E0","Unicode dec":"129130","Unicode hex":"1F86A"},{"Typeface name":"Wingdings","Dingbat dec":"225","Dingbat hex":"E1","Unicode dec":"129129","Unicode hex":"1F869"},{"Typeface name":"Wingdings","Dingbat dec":"226","Dingbat hex":"E2","Unicode dec":"129131","Unicode hex":"1F86B"},{"Typeface name":"Wingdings","Dingbat dec":"227","Dingbat hex":"E3","Unicode dec":"129132","Unicode hex":"1F86C"},{"Typeface name":"Wingdings","Dingbat dec":"228","Dingbat hex":"E4","Unicode dec":"129133","Unicode hex":"1F86D"},{"Typeface name":"Wingdings","Dingbat dec":"229","Dingbat hex":"E5","Unicode dec":"129135","Unicode hex":"1F86F"},{"Typeface name":"Wingdings","Dingbat dec":"230","Dingbat hex":"E6","Unicode dec":"129134","Unicode hex":"1F86E"},{"Typeface name":"Wingdings","Dingbat dec":"231","Dingbat hex":"E7","Unicode dec":"129144","Unicode hex":"1F878"},{"Typeface name":"Wingdings","Dingbat dec":"232","Dingbat hex":"E8","Unicode dec":"129146","Unicode hex":"1F87A"},{"Typeface name":"Wingdings","Dingbat dec":"233","Dingbat hex":"E9","Unicode dec":"129145","Unicode hex":"1F879"},{"Typeface name":"Wingdings","Dingbat dec":"234","Dingbat hex":"EA","Unicode dec":"129147","Unicode hex":"1F87B"},{"Typeface name":"Wingdings","Dingbat dec":"235","Dingbat hex":"EB","Unicode dec":"129148","Unicode hex":"1F87C"},{"Typeface name":"Wingdings","Dingbat dec":"236","Dingbat hex":"EC","Unicode dec":"129149","Unicode hex":"1F87D"},{"Typeface name":"Wingdings","Dingbat dec":"237","Dingbat hex":"ED","Unicode dec":"129151","Unicode hex":"1F87F"},{"Typeface name":"Wingdings","Dingbat dec":"238","Dingbat hex":"EE","Unicode dec":"129150","Unicode hex":"1F87E"},{"Typeface name":"Wingdings","Dingbat dec":"239","Dingbat hex":"EF","Unicode dec":"8678","Unicode hex":"21E6"},{"Typeface name":"Wingdings","Dingbat dec":"240","Dingbat hex":"F0","Unicode dec":"8680","Unicode hex":"21E8"},{"Typeface name":"Wingdings","Dingbat dec":"241","Dingbat hex":"F1","Unicode dec":"8679","Unicode hex":"21E7"},{"Typeface name":"Wingdings","Dingbat dec":"242","Dingbat hex":"F2","Unicode dec":"8681","Unicode hex":"21E9"},{"Typeface name":"Wingdings","Dingbat dec":"243","Dingbat hex":"F3","Unicode dec":"11012","Unicode hex":"2B04"},{"Typeface name":"Wingdings","Dingbat dec":"244","Dingbat hex":"F4","Unicode dec":"8691","Unicode hex":"21F3"},{"Typeface name":"Wingdings","Dingbat dec":"245","Dingbat hex":"F5","Unicode dec":"11009","Unicode hex":"2B01"},{"Typeface name":"Wingdings","Dingbat dec":"246","Dingbat hex":"F6","Unicode dec":"11008","Unicode hex":"2B00"},{"Typeface name":"Wingdings","Dingbat dec":"247","Dingbat hex":"F7","Unicode dec":"11011","Unicode hex":"2B03"},{"Typeface name":"Wingdings","Dingbat dec":"248","Dingbat hex":"F8","Unicode dec":"11010","Unicode hex":"2B02"},{"Typeface name":"Wingdings","Dingbat dec":"249","Dingbat hex":"F9","Unicode dec":"129196","Unicode hex":"1F8AC"},{"Typeface name":"Wingdings","Dingbat dec":"250","Dingbat hex":"FA","Unicode dec":"129197","Unicode hex":"1F8AD"},{"Typeface name":"Wingdings","Dingbat dec":"251","Dingbat hex":"FB","Unicode dec":"128502","Unicode hex":"1F5F6"},{"Typeface name":"Wingdings","Dingbat dec":"252","Dingbat hex":"FC","Unicode dec":"10003","Unicode hex":"2713"},{"Typeface name":"Wingdings","Dingbat dec":"253","Dingbat hex":"FD","Unicode dec":"128503","Unicode hex":"1F5F7"},{"Typeface name":"Wingdings","Dingbat dec":"254","Dingbat hex":"FE","Unicode dec":"128505","Unicode hex":"1F5F9"},{"Typeface name":"Wingdings 2","Dingbat dec":"32","Dingbat hex":"20","Unicode dec":"32","Unicode hex":"20"},{"Typeface name":"Wingdings 2","Dingbat dec":"33","Dingbat hex":"21","Unicode dec":"128394","Unicode hex":"1F58A"},{"Typeface name":"Wingdings 2","Dingbat dec":"34","Dingbat hex":"22","Unicode dec":"128395","Unicode hex":"1F58B"},{"Typeface name":"Wingdings 2","Dingbat dec":"35","Dingbat hex":"23","Unicode dec":"128396","Unicode hex":"1F58C"},{"Typeface name":"Wingdings 2","Dingbat dec":"36","Dingbat hex":"24","Unicode dec":"128397","Unicode hex":"1F58D"},{"Typeface name":"Wingdings 2","Dingbat dec":"37","Dingbat hex":"25","Unicode dec":"9988","Unicode hex":"2704"},{"Typeface name":"Wingdings 2","Dingbat dec":"38","Dingbat hex":"26","Unicode dec":"9984","Unicode hex":"2700"},{"Typeface name":"Wingdings 2","Dingbat dec":"39","Dingbat hex":"27","Unicode dec":"128382","Unicode hex":"1F57E"},{"Typeface name":"Wingdings 2","Dingbat dec":"40","Dingbat hex":"28","Unicode dec":"128381","Unicode hex":"1F57D"},{"Typeface name":"Wingdings 2","Dingbat dec":"41","Dingbat hex":"29","Unicode dec":"128453","Unicode hex":"1F5C5"},{"Typeface name":"Wingdings 2","Dingbat dec":"42","Dingbat hex":"2A","Unicode dec":"128454","Unicode hex":"1F5C6"},{"Typeface name":"Wingdings 2","Dingbat dec":"43","Dingbat hex":"2B","Unicode dec":"128455","Unicode hex":"1F5C7"},{"Typeface name":"Wingdings 2","Dingbat dec":"44","Dingbat hex":"2C","Unicode dec":"128456","Unicode hex":"1F5C8"},{"Typeface name":"Wingdings 2","Dingbat dec":"45","Dingbat hex":"2D","Unicode dec":"128457","Unicode hex":"1F5C9"},{"Typeface name":"Wingdings 2","Dingbat dec":"46","Dingbat hex":"2E","Unicode dec":"128458","Unicode hex":"1F5CA"},{"Typeface name":"Wingdings 2","Dingbat dec":"47","Dingbat hex":"2F","Unicode dec":"128459","Unicode hex":"1F5CB"},{"Typeface name":"Wingdings 2","Dingbat dec":"48","Dingbat hex":"30","Unicode dec":"128460","Unicode hex":"1F5CC"},{"Typeface name":"Wingdings 2","Dingbat dec":"49","Dingbat hex":"31","Unicode dec":"128461","Unicode hex":"1F5CD"},{"Typeface name":"Wingdings 2","Dingbat dec":"50","Dingbat hex":"32","Unicode dec":"128203","Unicode hex":"1F4CB"},{"Typeface name":"Wingdings 2","Dingbat dec":"51","Dingbat hex":"33","Unicode dec":"128465","Unicode hex":"1F5D1"},{"Typeface name":"Wingdings 2","Dingbat dec":"52","Dingbat hex":"34","Unicode dec":"128468","Unicode hex":"1F5D4"},{"Typeface name":"Wingdings 2","Dingbat dec":"53","Dingbat hex":"35","Unicode dec":"128437","Unicode hex":"1F5B5"},{"Typeface name":"Wingdings 2","Dingbat dec":"54","Dingbat hex":"36","Unicode dec":"128438","Unicode hex":"1F5B6"},{"Typeface name":"Wingdings 2","Dingbat dec":"55","Dingbat hex":"37","Unicode dec":"128439","Unicode hex":"1F5B7"},{"Typeface name":"Wingdings 2","Dingbat dec":"56","Dingbat hex":"38","Unicode dec":"128440","Unicode hex":"1F5B8"},{"Typeface name":"Wingdings 2","Dingbat dec":"57","Dingbat hex":"39","Unicode dec":"128429","Unicode hex":"1F5AD"},{"Typeface name":"Wingdings 2","Dingbat dec":"58","Dingbat hex":"3A","Unicode dec":"128431","Unicode hex":"1F5AF"},{"Typeface name":"Wingdings 2","Dingbat dec":"59","Dingbat hex":"3B","Unicode dec":"128433","Unicode hex":"1F5B1"},{"Typeface name":"Wingdings 2","Dingbat dec":"60","Dingbat hex":"3C","Unicode dec":"128402","Unicode hex":"1F592"},{"Typeface name":"Wingdings 2","Dingbat dec":"61","Dingbat hex":"3D","Unicode dec":"128403","Unicode hex":"1F593"},{"Typeface name":"Wingdings 2","Dingbat dec":"62","Dingbat hex":"3E","Unicode dec":"128408","Unicode hex":"1F598"},{"Typeface name":"Wingdings 2","Dingbat dec":"63","Dingbat hex":"3F","Unicode dec":"128409","Unicode hex":"1F599"},{"Typeface name":"Wingdings 2","Dingbat dec":"64","Dingbat hex":"40","Unicode dec":"128410","Unicode hex":"1F59A"},{"Typeface name":"Wingdings 2","Dingbat dec":"65","Dingbat hex":"41","Unicode dec":"128411","Unicode hex":"1F59B"},{"Typeface name":"Wingdings 2","Dingbat dec":"66","Dingbat hex":"42","Unicode dec":"128072","Unicode hex":"1F448"},{"Typeface name":"Wingdings 2","Dingbat dec":"67","Dingbat hex":"43","Unicode dec":"128073","Unicode hex":"1F449"},{"Typeface name":"Wingdings 2","Dingbat dec":"68","Dingbat hex":"44","Unicode dec":"128412","Unicode hex":"1F59C"},{"Typeface name":"Wingdings 2","Dingbat dec":"69","Dingbat hex":"45","Unicode dec":"128413","Unicode hex":"1F59D"},{"Typeface name":"Wingdings 2","Dingbat dec":"70","Dingbat hex":"46","Unicode dec":"128414","Unicode hex":"1F59E"},{"Typeface name":"Wingdings 2","Dingbat dec":"71","Dingbat hex":"47","Unicode dec":"128415","Unicode hex":"1F59F"},{"Typeface name":"Wingdings 2","Dingbat dec":"72","Dingbat hex":"48","Unicode dec":"128416","Unicode hex":"1F5A0"},{"Typeface name":"Wingdings 2","Dingbat dec":"73","Dingbat hex":"49","Unicode dec":"128417","Unicode hex":"1F5A1"},{"Typeface name":"Wingdings 2","Dingbat dec":"74","Dingbat hex":"4A","Unicode dec":"128070","Unicode hex":"1F446"},{"Typeface name":"Wingdings 2","Dingbat dec":"75","Dingbat hex":"4B","Unicode dec":"128071","Unicode hex":"1F447"},{"Typeface name":"Wingdings 2","Dingbat dec":"76","Dingbat hex":"4C","Unicode dec":"128418","Unicode hex":"1F5A2"},{"Typeface name":"Wingdings 2","Dingbat dec":"77","Dingbat hex":"4D","Unicode dec":"128419","Unicode hex":"1F5A3"},{"Typeface name":"Wingdings 2","Dingbat dec":"78","Dingbat hex":"4E","Unicode dec":"128401","Unicode hex":"1F591"},{"Typeface name":"Wingdings 2","Dingbat dec":"79","Dingbat hex":"4F","Unicode dec":"128500","Unicode hex":"1F5F4"},{"Typeface name":"Wingdings 2","Dingbat dec":"80","Dingbat hex":"50","Unicode dec":"128504","Unicode hex":"1F5F8"},{"Typeface name":"Wingdings 2","Dingbat dec":"81","Dingbat hex":"51","Unicode dec":"128501","Unicode hex":"1F5F5"},{"Typeface name":"Wingdings 2","Dingbat dec":"82","Dingbat hex":"52","Unicode dec":"9745","Unicode hex":"2611"},{"Typeface name":"Wingdings 2","Dingbat dec":"83","Dingbat hex":"53","Unicode dec":"11197","Unicode hex":"2BBD"},{"Typeface name":"Wingdings 2","Dingbat dec":"84","Dingbat hex":"54","Unicode dec":"9746","Unicode hex":"2612"},{"Typeface name":"Wingdings 2","Dingbat dec":"85","Dingbat hex":"55","Unicode dec":"11198","Unicode hex":"2BBE"},{"Typeface name":"Wingdings 2","Dingbat dec":"86","Dingbat hex":"56","Unicode dec":"11199","Unicode hex":"2BBF"},{"Typeface name":"Wingdings 2","Dingbat dec":"87","Dingbat hex":"57","Unicode dec":"128711","Unicode hex":"1F6C7"},{"Typeface name":"Wingdings 2","Dingbat dec":"88","Dingbat hex":"58","Unicode dec":"10680","Unicode hex":"29B8"},{"Typeface name":"Wingdings 2","Dingbat dec":"89","Dingbat hex":"59","Unicode dec":"128625","Unicode hex":"1F671"},{"Typeface name":"Wingdings 2","Dingbat dec":"90","Dingbat hex":"5A","Unicode dec":"128628","Unicode hex":"1F674"},{"Typeface name":"Wingdings 2","Dingbat dec":"91","Dingbat hex":"5B","Unicode dec":"128626","Unicode hex":"1F672"},{"Typeface name":"Wingdings 2","Dingbat dec":"92","Dingbat hex":"5C","Unicode dec":"128627","Unicode hex":"1F673"},{"Typeface name":"Wingdings 2","Dingbat dec":"93","Dingbat hex":"5D","Unicode dec":"8253","Unicode hex":"203D"},{"Typeface name":"Wingdings 2","Dingbat dec":"94","Dingbat hex":"5E","Unicode dec":"128633","Unicode hex":"1F679"},{"Typeface name":"Wingdings 2","Dingbat dec":"95","Dingbat hex":"5F","Unicode dec":"128634","Unicode hex":"1F67A"},{"Typeface name":"Wingdings 2","Dingbat dec":"96","Dingbat hex":"60","Unicode dec":"128635","Unicode hex":"1F67B"},{"Typeface name":"Wingdings 2","Dingbat dec":"97","Dingbat hex":"61","Unicode dec":"128614","Unicode hex":"1F666"},{"Typeface name":"Wingdings 2","Dingbat dec":"98","Dingbat hex":"62","Unicode dec":"128612","Unicode hex":"1F664"},{"Typeface name":"Wingdings 2","Dingbat dec":"99","Dingbat hex":"63","Unicode dec":"128613","Unicode hex":"1F665"},{"Typeface name":"Wingdings 2","Dingbat dec":"100","Dingbat hex":"64","Unicode dec":"128615","Unicode hex":"1F667"},{"Typeface name":"Wingdings 2","Dingbat dec":"101","Dingbat hex":"65","Unicode dec":"128602","Unicode hex":"1F65A"},{"Typeface name":"Wingdings 2","Dingbat dec":"102","Dingbat hex":"66","Unicode dec":"128600","Unicode hex":"1F658"},{"Typeface name":"Wingdings 2","Dingbat dec":"103","Dingbat hex":"67","Unicode dec":"128601","Unicode hex":"1F659"},{"Typeface name":"Wingdings 2","Dingbat dec":"104","Dingbat hex":"68","Unicode dec":"128603","Unicode hex":"1F65B"},{"Typeface name":"Wingdings 2","Dingbat dec":"105","Dingbat hex":"69","Unicode dec":"9450","Unicode hex":"24EA"},{"Typeface name":"Wingdings 2","Dingbat dec":"106","Dingbat hex":"6A","Unicode dec":"9312","Unicode hex":"2460"},{"Typeface name":"Wingdings 2","Dingbat dec":"107","Dingbat hex":"6B","Unicode dec":"9313","Unicode hex":"2461"},{"Typeface name":"Wingdings 2","Dingbat dec":"108","Dingbat hex":"6C","Unicode dec":"9314","Unicode hex":"2462"},{"Typeface name":"Wingdings 2","Dingbat dec":"109","Dingbat hex":"6D","Unicode dec":"9315","Unicode hex":"2463"},{"Typeface name":"Wingdings 2","Dingbat dec":"110","Dingbat hex":"6E","Unicode dec":"9316","Unicode hex":"2464"},{"Typeface name":"Wingdings 2","Dingbat dec":"111","Dingbat hex":"6F","Unicode dec":"9317","Unicode hex":"2465"},{"Typeface name":"Wingdings 2","Dingbat dec":"112","Dingbat hex":"70","Unicode dec":"9318","Unicode hex":"2466"},{"Typeface name":"Wingdings 2","Dingbat dec":"113","Dingbat hex":"71","Unicode dec":"9319","Unicode hex":"2467"},{"Typeface name":"Wingdings 2","Dingbat dec":"114","Dingbat hex":"72","Unicode dec":"9320","Unicode hex":"2468"},{"Typeface name":"Wingdings 2","Dingbat dec":"115","Dingbat hex":"73","Unicode dec":"9321","Unicode hex":"2469"},{"Typeface name":"Wingdings 2","Dingbat dec":"116","Dingbat hex":"74","Unicode dec":"9471","Unicode hex":"24FF"},{"Typeface name":"Wingdings 2","Dingbat dec":"117","Dingbat hex":"75","Unicode dec":"10102","Unicode hex":"2776"},{"Typeface name":"Wingdings 2","Dingbat dec":"118","Dingbat hex":"76","Unicode dec":"10103","Unicode hex":"2777"},{"Typeface name":"Wingdings 2","Dingbat dec":"119","Dingbat hex":"77","Unicode dec":"10104","Unicode hex":"2778"},{"Typeface name":"Wingdings 2","Dingbat dec":"120","Dingbat hex":"78","Unicode dec":"10105","Unicode hex":"2779"},{"Typeface name":"Wingdings 2","Dingbat dec":"121","Dingbat hex":"79","Unicode dec":"10106","Unicode hex":"277A"},{"Typeface name":"Wingdings 2","Dingbat dec":"122","Dingbat hex":"7A","Unicode dec":"10107","Unicode hex":"277B"},{"Typeface name":"Wingdings 2","Dingbat dec":"123","Dingbat hex":"7B","Unicode dec":"10108","Unicode hex":"277C"},{"Typeface name":"Wingdings 2","Dingbat dec":"124","Dingbat hex":"7C","Unicode dec":"10109","Unicode hex":"277D"},{"Typeface name":"Wingdings 2","Dingbat dec":"125","Dingbat hex":"7D","Unicode dec":"10110","Unicode hex":"277E"},{"Typeface name":"Wingdings 2","Dingbat dec":"126","Dingbat hex":"7E","Unicode dec":"10111","Unicode hex":"277F"},{"Typeface name":"Wingdings 2","Dingbat dec":"128","Dingbat hex":"80","Unicode dec":"9737","Unicode hex":"2609"},{"Typeface name":"Wingdings 2","Dingbat dec":"129","Dingbat hex":"81","Unicode dec":"127765","Unicode hex":"1F315"},{"Typeface name":"Wingdings 2","Dingbat dec":"130","Dingbat hex":"82","Unicode dec":"9789","Unicode hex":"263D"},{"Typeface name":"Wingdings 2","Dingbat dec":"131","Dingbat hex":"83","Unicode dec":"9790","Unicode hex":"263E"},{"Typeface name":"Wingdings 2","Dingbat dec":"132","Dingbat hex":"84","Unicode dec":"11839","Unicode hex":"2E3F"},{"Typeface name":"Wingdings 2","Dingbat dec":"133","Dingbat hex":"85","Unicode dec":"10013","Unicode hex":"271D"},{"Typeface name":"Wingdings 2","Dingbat dec":"134","Dingbat hex":"86","Unicode dec":"128327","Unicode hex":"1F547"},{"Typeface name":"Wingdings 2","Dingbat dec":"135","Dingbat hex":"87","Unicode dec":"128348","Unicode hex":"1F55C"},{"Typeface name":"Wingdings 2","Dingbat dec":"136","Dingbat hex":"88","Unicode dec":"128349","Unicode hex":"1F55D"},{"Typeface name":"Wingdings 2","Dingbat dec":"137","Dingbat hex":"89","Unicode dec":"128350","Unicode hex":"1F55E"},{"Typeface name":"Wingdings 2","Dingbat dec":"138","Dingbat hex":"8A","Unicode dec":"128351","Unicode hex":"1F55F"},{"Typeface name":"Wingdings 2","Dingbat dec":"139","Dingbat hex":"8B","Unicode dec":"128352","Unicode hex":"1F560"},{"Typeface name":"Wingdings 2","Dingbat dec":"140","Dingbat hex":"8C","Unicode dec":"128353","Unicode hex":"1F561"},{"Typeface name":"Wingdings 2","Dingbat dec":"141","Dingbat hex":"8D","Unicode dec":"128354","Unicode hex":"1F562"},{"Typeface name":"Wingdings 2","Dingbat dec":"142","Dingbat hex":"8E","Unicode dec":"128355","Unicode hex":"1F563"},{"Typeface name":"Wingdings 2","Dingbat dec":"143","Dingbat hex":"8F","Unicode dec":"128356","Unicode hex":"1F564"},{"Typeface name":"Wingdings 2","Dingbat dec":"144","Dingbat hex":"90","Unicode dec":"128357","Unicode hex":"1F565"},{"Typeface name":"Wingdings 2","Dingbat dec":"145","Dingbat hex":"91","Unicode dec":"128358","Unicode hex":"1F566"},{"Typeface name":"Wingdings 2","Dingbat dec":"146","Dingbat hex":"92","Unicode dec":"128359","Unicode hex":"1F567"},{"Typeface name":"Wingdings 2","Dingbat dec":"147","Dingbat hex":"93","Unicode dec":"128616","Unicode hex":"1F668"},{"Typeface name":"Wingdings 2","Dingbat dec":"148","Dingbat hex":"94","Unicode dec":"128617","Unicode hex":"1F669"},{"Typeface name":"Wingdings 2","Dingbat dec":"149","Dingbat hex":"95","Unicode dec":"8901","Unicode hex":"22C5"},{"Typeface name":"Wingdings 2","Dingbat dec":"150","Dingbat hex":"96","Unicode dec":"128900","Unicode hex":"1F784"},{"Typeface name":"Wingdings 2","Dingbat dec":"151","Dingbat hex":"97","Unicode dec":"10625","Unicode hex":"2981"},{"Typeface name":"Wingdings 2","Dingbat dec":"152","Dingbat hex":"98","Unicode dec":"9679","Unicode hex":"25CF"},{"Typeface name":"Wingdings 2","Dingbat dec":"153","Dingbat hex":"99","Unicode dec":"9675","Unicode hex":"25CB"},{"Typeface name":"Wingdings 2","Dingbat dec":"154","Dingbat hex":"9A","Unicode dec":"128901","Unicode hex":"1F785"},{"Typeface name":"Wingdings 2","Dingbat dec":"155","Dingbat hex":"9B","Unicode dec":"128903","Unicode hex":"1F787"},{"Typeface name":"Wingdings 2","Dingbat dec":"156","Dingbat hex":"9C","Unicode dec":"128905","Unicode hex":"1F789"},{"Typeface name":"Wingdings 2","Dingbat dec":"157","Dingbat hex":"9D","Unicode dec":"8857","Unicode hex":"2299"},{"Typeface name":"Wingdings 2","Dingbat dec":"158","Dingbat hex":"9E","Unicode dec":"10687","Unicode hex":"29BF"},{"Typeface name":"Wingdings 2","Dingbat dec":"159","Dingbat hex":"9F","Unicode dec":"128908","Unicode hex":"1F78C"},{"Typeface name":"Wingdings 2","Dingbat dec":"160","Dingbat hex":"A0","Unicode dec":"128909","Unicode hex":"1F78D"},{"Typeface name":"Wingdings 2","Dingbat dec":"161","Dingbat hex":"A1","Unicode dec":"9726","Unicode hex":"25FE"},{"Typeface name":"Wingdings 2","Dingbat dec":"162","Dingbat hex":"A2","Unicode dec":"9632","Unicode hex":"25A0"},{"Typeface name":"Wingdings 2","Dingbat dec":"163","Dingbat hex":"A3","Unicode dec":"9633","Unicode hex":"25A1"},{"Typeface name":"Wingdings 2","Dingbat dec":"164","Dingbat hex":"A4","Unicode dec":"128913","Unicode hex":"1F791"},{"Typeface name":"Wingdings 2","Dingbat dec":"165","Dingbat hex":"A5","Unicode dec":"128914","Unicode hex":"1F792"},{"Typeface name":"Wingdings 2","Dingbat dec":"166","Dingbat hex":"A6","Unicode dec":"128915","Unicode hex":"1F793"},{"Typeface name":"Wingdings 2","Dingbat dec":"167","Dingbat hex":"A7","Unicode dec":"128916","Unicode hex":"1F794"},{"Typeface name":"Wingdings 2","Dingbat dec":"168","Dingbat hex":"A8","Unicode dec":"9635","Unicode hex":"25A3"},{"Typeface name":"Wingdings 2","Dingbat dec":"169","Dingbat hex":"A9","Unicode dec":"128917","Unicode hex":"1F795"},{"Typeface name":"Wingdings 2","Dingbat dec":"170","Dingbat hex":"AA","Unicode dec":"128918","Unicode hex":"1F796"},{"Typeface name":"Wingdings 2","Dingbat dec":"171","Dingbat hex":"AB","Unicode dec":"128919","Unicode hex":"1F797"},{"Typeface name":"Wingdings 2","Dingbat dec":"172","Dingbat hex":"AC","Unicode dec":"128920","Unicode hex":"1F798"},{"Typeface name":"Wingdings 2","Dingbat dec":"173","Dingbat hex":"AD","Unicode dec":"11049","Unicode hex":"2B29"},{"Typeface name":"Wingdings 2","Dingbat dec":"174","Dingbat hex":"AE","Unicode dec":"11045","Unicode hex":"2B25"},{"Typeface name":"Wingdings 2","Dingbat dec":"175","Dingbat hex":"AF","Unicode dec":"9671","Unicode hex":"25C7"},{"Typeface name":"Wingdings 2","Dingbat dec":"176","Dingbat hex":"B0","Unicode dec":"128922","Unicode hex":"1F79A"},{"Typeface name":"Wingdings 2","Dingbat dec":"177","Dingbat hex":"B1","Unicode dec":"9672","Unicode hex":"25C8"},{"Typeface name":"Wingdings 2","Dingbat dec":"178","Dingbat hex":"B2","Unicode dec":"128923","Unicode hex":"1F79B"},{"Typeface name":"Wingdings 2","Dingbat dec":"179","Dingbat hex":"B3","Unicode dec":"128924","Unicode hex":"1F79C"},{"Typeface name":"Wingdings 2","Dingbat dec":"180","Dingbat hex":"B4","Unicode dec":"128925","Unicode hex":"1F79D"},{"Typeface name":"Wingdings 2","Dingbat dec":"181","Dingbat hex":"B5","Unicode dec":"128926","Unicode hex":"1F79E"},{"Typeface name":"Wingdings 2","Dingbat dec":"182","Dingbat hex":"B6","Unicode dec":"11050","Unicode hex":"2B2A"},{"Typeface name":"Wingdings 2","Dingbat dec":"183","Dingbat hex":"B7","Unicode dec":"11047","Unicode hex":"2B27"},{"Typeface name":"Wingdings 2","Dingbat dec":"184","Dingbat hex":"B8","Unicode dec":"9674","Unicode hex":"25CA"},{"Typeface name":"Wingdings 2","Dingbat dec":"185","Dingbat hex":"B9","Unicode dec":"128928","Unicode hex":"1F7A0"},{"Typeface name":"Wingdings 2","Dingbat dec":"186","Dingbat hex":"BA","Unicode dec":"9686","Unicode hex":"25D6"},{"Typeface name":"Wingdings 2","Dingbat dec":"187","Dingbat hex":"BB","Unicode dec":"9687","Unicode hex":"25D7"},{"Typeface name":"Wingdings 2","Dingbat dec":"188","Dingbat hex":"BC","Unicode dec":"11210","Unicode hex":"2BCA"},{"Typeface name":"Wingdings 2","Dingbat dec":"189","Dingbat hex":"BD","Unicode dec":"11211","Unicode hex":"2BCB"},{"Typeface name":"Wingdings 2","Dingbat dec":"190","Dingbat hex":"BE","Unicode dec":"11200","Unicode hex":"2BC0"},{"Typeface name":"Wingdings 2","Dingbat dec":"191","Dingbat hex":"BF","Unicode dec":"11201","Unicode hex":"2BC1"},{"Typeface name":"Wingdings 2","Dingbat dec":"192","Dingbat hex":"C0","Unicode dec":"11039","Unicode hex":"2B1F"},{"Typeface name":"Wingdings 2","Dingbat dec":"193","Dingbat hex":"C1","Unicode dec":"11202","Unicode hex":"2BC2"},{"Typeface name":"Wingdings 2","Dingbat dec":"194","Dingbat hex":"C2","Unicode dec":"11043","Unicode hex":"2B23"},{"Typeface name":"Wingdings 2","Dingbat dec":"195","Dingbat hex":"C3","Unicode dec":"11042","Unicode hex":"2B22"},{"Typeface name":"Wingdings 2","Dingbat dec":"196","Dingbat hex":"C4","Unicode dec":"11203","Unicode hex":"2BC3"},{"Typeface name":"Wingdings 2","Dingbat dec":"197","Dingbat hex":"C5","Unicode dec":"11204","Unicode hex":"2BC4"},{"Typeface name":"Wingdings 2","Dingbat dec":"198","Dingbat hex":"C6","Unicode dec":"128929","Unicode hex":"1F7A1"},{"Typeface name":"Wingdings 2","Dingbat dec":"199","Dingbat hex":"C7","Unicode dec":"128930","Unicode hex":"1F7A2"},{"Typeface name":"Wingdings 2","Dingbat dec":"200","Dingbat hex":"C8","Unicode dec":"128931","Unicode hex":"1F7A3"},{"Typeface name":"Wingdings 2","Dingbat dec":"201","Dingbat hex":"C9","Unicode dec":"128932","Unicode hex":"1F7A4"},{"Typeface name":"Wingdings 2","Dingbat dec":"202","Dingbat hex":"CA","Unicode dec":"128933","Unicode hex":"1F7A5"},{"Typeface name":"Wingdings 2","Dingbat dec":"203","Dingbat hex":"CB","Unicode dec":"128934","Unicode hex":"1F7A6"},{"Typeface name":"Wingdings 2","Dingbat dec":"204","Dingbat hex":"CC","Unicode dec":"128935","Unicode hex":"1F7A7"},{"Typeface name":"Wingdings 2","Dingbat dec":"205","Dingbat hex":"CD","Unicode dec":"128936","Unicode hex":"1F7A8"},{"Typeface name":"Wingdings 2","Dingbat dec":"206","Dingbat hex":"CE","Unicode dec":"128937","Unicode hex":"1F7A9"},{"Typeface name":"Wingdings 2","Dingbat dec":"207","Dingbat hex":"CF","Unicode dec":"128938","Unicode hex":"1F7AA"},{"Typeface name":"Wingdings 2","Dingbat dec":"208","Dingbat hex":"D0","Unicode dec":"128939","Unicode hex":"1F7AB"},{"Typeface name":"Wingdings 2","Dingbat dec":"209","Dingbat hex":"D1","Unicode dec":"128940","Unicode hex":"1F7AC"},{"Typeface name":"Wingdings 2","Dingbat dec":"210","Dingbat hex":"D2","Unicode dec":"128941","Unicode hex":"1F7AD"},{"Typeface name":"Wingdings 2","Dingbat dec":"211","Dingbat hex":"D3","Unicode dec":"128942","Unicode hex":"1F7AE"},{"Typeface name":"Wingdings 2","Dingbat dec":"212","Dingbat hex":"D4","Unicode dec":"128943","Unicode hex":"1F7AF"},{"Typeface name":"Wingdings 2","Dingbat dec":"213","Dingbat hex":"D5","Unicode dec":"128944","Unicode hex":"1F7B0"},{"Typeface name":"Wingdings 2","Dingbat dec":"214","Dingbat hex":"D6","Unicode dec":"128945","Unicode hex":"1F7B1"},{"Typeface name":"Wingdings 2","Dingbat dec":"215","Dingbat hex":"D7","Unicode dec":"128946","Unicode hex":"1F7B2"},{"Typeface name":"Wingdings 2","Dingbat dec":"216","Dingbat hex":"D8","Unicode dec":"128947","Unicode hex":"1F7B3"},{"Typeface name":"Wingdings 2","Dingbat dec":"217","Dingbat hex":"D9","Unicode dec":"128948","Unicode hex":"1F7B4"},{"Typeface name":"Wingdings 2","Dingbat dec":"218","Dingbat hex":"DA","Unicode dec":"128949","Unicode hex":"1F7B5"},{"Typeface name":"Wingdings 2","Dingbat dec":"219","Dingbat hex":"DB","Unicode dec":"128950","Unicode hex":"1F7B6"},{"Typeface name":"Wingdings 2","Dingbat dec":"220","Dingbat hex":"DC","Unicode dec":"128951","Unicode hex":"1F7B7"},{"Typeface name":"Wingdings 2","Dingbat dec":"221","Dingbat hex":"DD","Unicode dec":"128952","Unicode hex":"1F7B8"},{"Typeface name":"Wingdings 2","Dingbat dec":"222","Dingbat hex":"DE","Unicode dec":"128953","Unicode hex":"1F7B9"},{"Typeface name":"Wingdings 2","Dingbat dec":"223","Dingbat hex":"DF","Unicode dec":"128954","Unicode hex":"1F7BA"},{"Typeface name":"Wingdings 2","Dingbat dec":"224","Dingbat hex":"E0","Unicode dec":"128955","Unicode hex":"1F7BB"},{"Typeface name":"Wingdings 2","Dingbat dec":"225","Dingbat hex":"E1","Unicode dec":"128956","Unicode hex":"1F7BC"},{"Typeface name":"Wingdings 2","Dingbat dec":"226","Dingbat hex":"E2","Unicode dec":"128957","Unicode hex":"1F7BD"},{"Typeface name":"Wingdings 2","Dingbat dec":"227","Dingbat hex":"E3","Unicode dec":"128958","Unicode hex":"1F7BE"},{"Typeface name":"Wingdings 2","Dingbat dec":"228","Dingbat hex":"E4","Unicode dec":"128959","Unicode hex":"1F7BF"},{"Typeface name":"Wingdings 2","Dingbat dec":"229","Dingbat hex":"E5","Unicode dec":"128960","Unicode hex":"1F7C0"},{"Typeface name":"Wingdings 2","Dingbat dec":"230","Dingbat hex":"E6","Unicode dec":"128962","Unicode hex":"1F7C2"},{"Typeface name":"Wingdings 2","Dingbat dec":"231","Dingbat hex":"E7","Unicode dec":"128964","Unicode hex":"1F7C4"},{"Typeface name":"Wingdings 2","Dingbat dec":"232","Dingbat hex":"E8","Unicode dec":"128966","Unicode hex":"1F7C6"},{"Typeface name":"Wingdings 2","Dingbat dec":"233","Dingbat hex":"E9","Unicode dec":"128969","Unicode hex":"1F7C9"},{"Typeface name":"Wingdings 2","Dingbat dec":"234","Dingbat hex":"EA","Unicode dec":"128970","Unicode hex":"1F7CA"},{"Typeface name":"Wingdings 2","Dingbat dec":"235","Dingbat hex":"EB","Unicode dec":"10038","Unicode hex":"2736"},{"Typeface name":"Wingdings 2","Dingbat dec":"236","Dingbat hex":"EC","Unicode dec":"128972","Unicode hex":"1F7CC"},{"Typeface name":"Wingdings 2","Dingbat dec":"237","Dingbat hex":"ED","Unicode dec":"128974","Unicode hex":"1F7CE"},{"Typeface name":"Wingdings 2","Dingbat dec":"238","Dingbat hex":"EE","Unicode dec":"128976","Unicode hex":"1F7D0"},{"Typeface name":"Wingdings 2","Dingbat dec":"239","Dingbat hex":"EF","Unicode dec":"128978","Unicode hex":"1F7D2"},{"Typeface name":"Wingdings 2","Dingbat dec":"240","Dingbat hex":"F0","Unicode dec":"10041","Unicode hex":"2739"},{"Typeface name":"Wingdings 2","Dingbat dec":"241","Dingbat hex":"F1","Unicode dec":"128963","Unicode hex":"1F7C3"},{"Typeface name":"Wingdings 2","Dingbat dec":"242","Dingbat hex":"F2","Unicode dec":"128967","Unicode hex":"1F7C7"},{"Typeface name":"Wingdings 2","Dingbat dec":"243","Dingbat hex":"F3","Unicode dec":"10031","Unicode hex":"272F"},{"Typeface name":"Wingdings 2","Dingbat dec":"244","Dingbat hex":"F4","Unicode dec":"128973","Unicode hex":"1F7CD"},{"Typeface name":"Wingdings 2","Dingbat dec":"245","Dingbat hex":"F5","Unicode dec":"128980","Unicode hex":"1F7D4"},{"Typeface name":"Wingdings 2","Dingbat dec":"246","Dingbat hex":"F6","Unicode dec":"11212","Unicode hex":"2BCC"},{"Typeface name":"Wingdings 2","Dingbat dec":"247","Dingbat hex":"F7","Unicode dec":"11213","Unicode hex":"2BCD"},{"Typeface name":"Wingdings 2","Dingbat dec":"248","Dingbat hex":"F8","Unicode dec":"8251","Unicode hex":"203B"},{"Typeface name":"Wingdings 2","Dingbat dec":"249","Dingbat hex":"F9","Unicode dec":"8258","Unicode hex":"2042"},{"Typeface name":"Wingdings 3","Dingbat dec":"32","Dingbat hex":"20","Unicode dec":"32","Unicode hex":"20"},{"Typeface name":"Wingdings 3","Dingbat dec":"33","Dingbat hex":"21","Unicode dec":"11104","Unicode hex":"2B60"},{"Typeface name":"Wingdings 3","Dingbat dec":"34","Dingbat hex":"22","Unicode dec":"11106","Unicode hex":"2B62"},{"Typeface name":"Wingdings 3","Dingbat dec":"35","Dingbat hex":"23","Unicode dec":"11105","Unicode hex":"2B61"},{"Typeface name":"Wingdings 3","Dingbat dec":"36","Dingbat hex":"24","Unicode dec":"11107","Unicode hex":"2B63"},{"Typeface name":"Wingdings 3","Dingbat dec":"37","Dingbat hex":"25","Unicode dec":"11110","Unicode hex":"2B66"},{"Typeface name":"Wingdings 3","Dingbat dec":"38","Dingbat hex":"26","Unicode dec":"11111","Unicode hex":"2B67"},{"Typeface name":"Wingdings 3","Dingbat dec":"39","Dingbat hex":"27","Unicode dec":"11113","Unicode hex":"2B69"},{"Typeface name":"Wingdings 3","Dingbat dec":"40","Dingbat hex":"28","Unicode dec":"11112","Unicode hex":"2B68"},{"Typeface name":"Wingdings 3","Dingbat dec":"41","Dingbat hex":"29","Unicode dec":"11120","Unicode hex":"2B70"},{"Typeface name":"Wingdings 3","Dingbat dec":"42","Dingbat hex":"2A","Unicode dec":"11122","Unicode hex":"2B72"},{"Typeface name":"Wingdings 3","Dingbat dec":"43","Dingbat hex":"2B","Unicode dec":"11121","Unicode hex":"2B71"},{"Typeface name":"Wingdings 3","Dingbat dec":"44","Dingbat hex":"2C","Unicode dec":"11123","Unicode hex":"2B73"},{"Typeface name":"Wingdings 3","Dingbat dec":"45","Dingbat hex":"2D","Unicode dec":"11126","Unicode hex":"2B76"},{"Typeface name":"Wingdings 3","Dingbat dec":"46","Dingbat hex":"2E","Unicode dec":"11128","Unicode hex":"2B78"},{"Typeface name":"Wingdings 3","Dingbat dec":"47","Dingbat hex":"2F","Unicode dec":"11131","Unicode hex":"2B7B"},{"Typeface name":"Wingdings 3","Dingbat dec":"48","Dingbat hex":"30","Unicode dec":"11133","Unicode hex":"2B7D"},{"Typeface name":"Wingdings 3","Dingbat dec":"49","Dingbat hex":"31","Unicode dec":"11108","Unicode hex":"2B64"},{"Typeface name":"Wingdings 3","Dingbat dec":"50","Dingbat hex":"32","Unicode dec":"11109","Unicode hex":"2B65"},{"Typeface name":"Wingdings 3","Dingbat dec":"51","Dingbat hex":"33","Unicode dec":"11114","Unicode hex":"2B6A"},{"Typeface name":"Wingdings 3","Dingbat dec":"52","Dingbat hex":"34","Unicode dec":"11116","Unicode hex":"2B6C"},{"Typeface name":"Wingdings 3","Dingbat dec":"53","Dingbat hex":"35","Unicode dec":"11115","Unicode hex":"2B6B"},{"Typeface name":"Wingdings 3","Dingbat dec":"54","Dingbat hex":"36","Unicode dec":"11117","Unicode hex":"2B6D"},{"Typeface name":"Wingdings 3","Dingbat dec":"55","Dingbat hex":"37","Unicode dec":"11085","Unicode hex":"2B4D"},{"Typeface name":"Wingdings 3","Dingbat dec":"56","Dingbat hex":"38","Unicode dec":"11168","Unicode hex":"2BA0"},{"Typeface name":"Wingdings 3","Dingbat dec":"57","Dingbat hex":"39","Unicode dec":"11169","Unicode hex":"2BA1"},{"Typeface name":"Wingdings 3","Dingbat dec":"58","Dingbat hex":"3A","Unicode dec":"11170","Unicode hex":"2BA2"},{"Typeface name":"Wingdings 3","Dingbat dec":"59","Dingbat hex":"3B","Unicode dec":"11171","Unicode hex":"2BA3"},{"Typeface name":"Wingdings 3","Dingbat dec":"60","Dingbat hex":"3C","Unicode dec":"11172","Unicode hex":"2BA4"},{"Typeface name":"Wingdings 3","Dingbat dec":"61","Dingbat hex":"3D","Unicode dec":"11173","Unicode hex":"2BA5"},{"Typeface name":"Wingdings 3","Dingbat dec":"62","Dingbat hex":"3E","Unicode dec":"11174","Unicode hex":"2BA6"},{"Typeface name":"Wingdings 3","Dingbat dec":"63","Dingbat hex":"3F","Unicode dec":"11175","Unicode hex":"2BA7"},{"Typeface name":"Wingdings 3","Dingbat dec":"64","Dingbat hex":"40","Unicode dec":"11152","Unicode hex":"2B90"},{"Typeface name":"Wingdings 3","Dingbat dec":"65","Dingbat hex":"41","Unicode dec":"11153","Unicode hex":"2B91"},{"Typeface name":"Wingdings 3","Dingbat dec":"66","Dingbat hex":"42","Unicode dec":"11154","Unicode hex":"2B92"},{"Typeface name":"Wingdings 3","Dingbat dec":"67","Dingbat hex":"43","Unicode dec":"11155","Unicode hex":"2B93"},{"Typeface name":"Wingdings 3","Dingbat dec":"68","Dingbat hex":"44","Unicode dec":"11136","Unicode hex":"2B80"},{"Typeface name":"Wingdings 3","Dingbat dec":"69","Dingbat hex":"45","Unicode dec":"11139","Unicode hex":"2B83"},{"Typeface name":"Wingdings 3","Dingbat dec":"70","Dingbat hex":"46","Unicode dec":"11134","Unicode hex":"2B7E"},{"Typeface name":"Wingdings 3","Dingbat dec":"71","Dingbat hex":"47","Unicode dec":"11135","Unicode hex":"2B7F"},{"Typeface name":"Wingdings 3","Dingbat dec":"72","Dingbat hex":"48","Unicode dec":"11140","Unicode hex":"2B84"},{"Typeface name":"Wingdings 3","Dingbat dec":"73","Dingbat hex":"49","Unicode dec":"11142","Unicode hex":"2B86"},{"Typeface name":"Wingdings 3","Dingbat dec":"74","Dingbat hex":"4A","Unicode dec":"11141","Unicode hex":"2B85"},{"Typeface name":"Wingdings 3","Dingbat dec":"75","Dingbat hex":"4B","Unicode dec":"11143","Unicode hex":"2B87"},{"Typeface name":"Wingdings 3","Dingbat dec":"76","Dingbat hex":"4C","Unicode dec":"11151","Unicode hex":"2B8F"},{"Typeface name":"Wingdings 3","Dingbat dec":"77","Dingbat hex":"4D","Unicode dec":"11149","Unicode hex":"2B8D"},{"Typeface name":"Wingdings 3","Dingbat dec":"78","Dingbat hex":"4E","Unicode dec":"11150","Unicode hex":"2B8E"},{"Typeface name":"Wingdings 3","Dingbat dec":"79","Dingbat hex":"4F","Unicode dec":"11148","Unicode hex":"2B8C"},{"Typeface name":"Wingdings 3","Dingbat dec":"80","Dingbat hex":"50","Unicode dec":"11118","Unicode hex":"2B6E"},{"Typeface name":"Wingdings 3","Dingbat dec":"81","Dingbat hex":"51","Unicode dec":"11119","Unicode hex":"2B6F"},{"Typeface name":"Wingdings 3","Dingbat dec":"82","Dingbat hex":"52","Unicode dec":"9099","Unicode hex":"238B"},{"Typeface name":"Wingdings 3","Dingbat dec":"83","Dingbat hex":"53","Unicode dec":"8996","Unicode hex":"2324"},{"Typeface name":"Wingdings 3","Dingbat dec":"84","Dingbat hex":"54","Unicode dec":"8963","Unicode hex":"2303"},{"Typeface name":"Wingdings 3","Dingbat dec":"85","Dingbat hex":"55","Unicode dec":"8997","Unicode hex":"2325"},{"Typeface name":"Wingdings 3","Dingbat dec":"86","Dingbat hex":"56","Unicode dec":"9251","Unicode hex":"2423"},{"Typeface name":"Wingdings 3","Dingbat dec":"87","Dingbat hex":"57","Unicode dec":"9085","Unicode hex":"237D"},{"Typeface name":"Wingdings 3","Dingbat dec":"88","Dingbat hex":"58","Unicode dec":"8682","Unicode hex":"21EA"},{"Typeface name":"Wingdings 3","Dingbat dec":"89","Dingbat hex":"59","Unicode dec":"11192","Unicode hex":"2BB8"},{"Typeface name":"Wingdings 3","Dingbat dec":"90","Dingbat hex":"5A","Unicode dec":"129184","Unicode hex":"1F8A0"},{"Typeface name":"Wingdings 3","Dingbat dec":"91","Dingbat hex":"5B","Unicode dec":"129185","Unicode hex":"1F8A1"},{"Typeface name":"Wingdings 3","Dingbat dec":"92","Dingbat hex":"5C","Unicode dec":"129186","Unicode hex":"1F8A2"},{"Typeface name":"Wingdings 3","Dingbat dec":"93","Dingbat hex":"5D","Unicode dec":"129187","Unicode hex":"1F8A3"},{"Typeface name":"Wingdings 3","Dingbat dec":"94","Dingbat hex":"5E","Unicode dec":"129188","Unicode hex":"1F8A4"},{"Typeface name":"Wingdings 3","Dingbat dec":"95","Dingbat hex":"5F","Unicode dec":"129189","Unicode hex":"1F8A5"},{"Typeface name":"Wingdings 3","Dingbat dec":"96","Dingbat hex":"60","Unicode dec":"129190","Unicode hex":"1F8A6"},{"Typeface name":"Wingdings 3","Dingbat dec":"97","Dingbat hex":"61","Unicode dec":"129191","Unicode hex":"1F8A7"},{"Typeface name":"Wingdings 3","Dingbat dec":"98","Dingbat hex":"62","Unicode dec":"129192","Unicode hex":"1F8A8"},{"Typeface name":"Wingdings 3","Dingbat dec":"99","Dingbat hex":"63","Unicode dec":"129193","Unicode hex":"1F8A9"},{"Typeface name":"Wingdings 3","Dingbat dec":"100","Dingbat hex":"64","Unicode dec":"129194","Unicode hex":"1F8AA"},{"Typeface name":"Wingdings 3","Dingbat dec":"101","Dingbat hex":"65","Unicode dec":"129195","Unicode hex":"1F8AB"},{"Typeface name":"Wingdings 3","Dingbat dec":"102","Dingbat hex":"66","Unicode dec":"129104","Unicode hex":"1F850"},{"Typeface name":"Wingdings 3","Dingbat dec":"103","Dingbat hex":"67","Unicode dec":"129106","Unicode hex":"1F852"},{"Typeface name":"Wingdings 3","Dingbat dec":"104","Dingbat hex":"68","Unicode dec":"129105","Unicode hex":"1F851"},{"Typeface name":"Wingdings 3","Dingbat dec":"105","Dingbat hex":"69","Unicode dec":"129107","Unicode hex":"1F853"},{"Typeface name":"Wingdings 3","Dingbat dec":"106","Dingbat hex":"6A","Unicode dec":"129108","Unicode hex":"1F854"},{"Typeface name":"Wingdings 3","Dingbat dec":"107","Dingbat hex":"6B","Unicode dec":"129109","Unicode hex":"1F855"},{"Typeface name":"Wingdings 3","Dingbat dec":"108","Dingbat hex":"6C","Unicode dec":"129111","Unicode hex":"1F857"},{"Typeface name":"Wingdings 3","Dingbat dec":"109","Dingbat hex":"6D","Unicode dec":"129110","Unicode hex":"1F856"},{"Typeface name":"Wingdings 3","Dingbat dec":"110","Dingbat hex":"6E","Unicode dec":"129112","Unicode hex":"1F858"},{"Typeface name":"Wingdings 3","Dingbat dec":"111","Dingbat hex":"6F","Unicode dec":"129113","Unicode hex":"1F859"},{"Typeface name":"Wingdings 3","Dingbat dec":"112","Dingbat hex":"70","Unicode dec":"9650","Unicode hex":"25B2"},{"Typeface name":"Wingdings 3","Dingbat dec":"113","Dingbat hex":"71","Unicode dec":"9660","Unicode hex":"25BC"},{"Typeface name":"Wingdings 3","Dingbat dec":"114","Dingbat hex":"72","Unicode dec":"9651","Unicode hex":"25B3"},{"Typeface name":"Wingdings 3","Dingbat dec":"115","Dingbat hex":"73","Unicode dec":"9661","Unicode hex":"25BD"},{"Typeface name":"Wingdings 3","Dingbat dec":"116","Dingbat hex":"74","Unicode dec":"9664","Unicode hex":"25C0"},{"Typeface name":"Wingdings 3","Dingbat dec":"117","Dingbat hex":"75","Unicode dec":"9654","Unicode hex":"25B6"},{"Typeface name":"Wingdings 3","Dingbat dec":"118","Dingbat hex":"76","Unicode dec":"9665","Unicode hex":"25C1"},{"Typeface name":"Wingdings 3","Dingbat dec":"119","Dingbat hex":"77","Unicode dec":"9655","Unicode hex":"25B7"},{"Typeface name":"Wingdings 3","Dingbat dec":"120","Dingbat hex":"78","Unicode dec":"9699","Unicode hex":"25E3"},{"Typeface name":"Wingdings 3","Dingbat dec":"121","Dingbat hex":"79","Unicode dec":"9698","Unicode hex":"25E2"},{"Typeface name":"Wingdings 3","Dingbat dec":"122","Dingbat hex":"7A","Unicode dec":"9700","Unicode hex":"25E4"},{"Typeface name":"Wingdings 3","Dingbat dec":"123","Dingbat hex":"7B","Unicode dec":"9701","Unicode hex":"25E5"},{"Typeface name":"Wingdings 3","Dingbat dec":"124","Dingbat hex":"7C","Unicode dec":"128896","Unicode hex":"1F780"},{"Typeface name":"Wingdings 3","Dingbat dec":"125","Dingbat hex":"7D","Unicode dec":"128898","Unicode hex":"1F782"},{"Typeface name":"Wingdings 3","Dingbat dec":"126","Dingbat hex":"7E","Unicode dec":"128897","Unicode hex":"1F781"},{"Typeface name":"Wingdings 3","Dingbat dec":"128","Dingbat hex":"80","Unicode dec":"128899","Unicode hex":"1F783"},{"Typeface name":"Wingdings 3","Dingbat dec":"129","Dingbat hex":"81","Unicode dec":"11205","Unicode hex":"2BC5"},{"Typeface name":"Wingdings 3","Dingbat dec":"130","Dingbat hex":"82","Unicode dec":"11206","Unicode hex":"2BC6"},{"Typeface name":"Wingdings 3","Dingbat dec":"131","Dingbat hex":"83","Unicode dec":"11207","Unicode hex":"2BC7"},{"Typeface name":"Wingdings 3","Dingbat dec":"132","Dingbat hex":"84","Unicode dec":"11208","Unicode hex":"2BC8"},{"Typeface name":"Wingdings 3","Dingbat dec":"133","Dingbat hex":"85","Unicode dec":"11164","Unicode hex":"2B9C"},{"Typeface name":"Wingdings 3","Dingbat dec":"134","Dingbat hex":"86","Unicode dec":"11166","Unicode hex":"2B9E"},{"Typeface name":"Wingdings 3","Dingbat dec":"135","Dingbat hex":"87","Unicode dec":"11165","Unicode hex":"2B9D"},{"Typeface name":"Wingdings 3","Dingbat dec":"136","Dingbat hex":"88","Unicode dec":"11167","Unicode hex":"2B9F"},{"Typeface name":"Wingdings 3","Dingbat dec":"137","Dingbat hex":"89","Unicode dec":"129040","Unicode hex":"1F810"},{"Typeface name":"Wingdings 3","Dingbat dec":"138","Dingbat hex":"8A","Unicode dec":"129042","Unicode hex":"1F812"},{"Typeface name":"Wingdings 3","Dingbat dec":"139","Dingbat hex":"8B","Unicode dec":"129041","Unicode hex":"1F811"},{"Typeface name":"Wingdings 3","Dingbat dec":"140","Dingbat hex":"8C","Unicode dec":"129043","Unicode hex":"1F813"},{"Typeface name":"Wingdings 3","Dingbat dec":"141","Dingbat hex":"8D","Unicode dec":"129044","Unicode hex":"1F814"},{"Typeface name":"Wingdings 3","Dingbat dec":"142","Dingbat hex":"8E","Unicode dec":"129046","Unicode hex":"1F816"},{"Typeface name":"Wingdings 3","Dingbat dec":"143","Dingbat hex":"8F","Unicode dec":"129045","Unicode hex":"1F815"},{"Typeface name":"Wingdings 3","Dingbat dec":"144","Dingbat hex":"90","Unicode dec":"129047","Unicode hex":"1F817"},{"Typeface name":"Wingdings 3","Dingbat dec":"145","Dingbat hex":"91","Unicode dec":"129048","Unicode hex":"1F818"},{"Typeface name":"Wingdings 3","Dingbat dec":"146","Dingbat hex":"92","Unicode dec":"129050","Unicode hex":"1F81A"},{"Typeface name":"Wingdings 3","Dingbat dec":"147","Dingbat hex":"93","Unicode dec":"129049","Unicode hex":"1F819"},{"Typeface name":"Wingdings 3","Dingbat dec":"148","Dingbat hex":"94","Unicode dec":"129051","Unicode hex":"1F81B"},{"Typeface name":"Wingdings 3","Dingbat dec":"149","Dingbat hex":"95","Unicode dec":"129052","Unicode hex":"1F81C"},{"Typeface name":"Wingdings 3","Dingbat dec":"150","Dingbat hex":"96","Unicode dec":"129054","Unicode hex":"1F81E"},{"Typeface name":"Wingdings 3","Dingbat dec":"151","Dingbat hex":"97","Unicode dec":"129053","Unicode hex":"1F81D"},{"Typeface name":"Wingdings 3","Dingbat dec":"152","Dingbat hex":"98","Unicode dec":"129055","Unicode hex":"1F81F"},{"Typeface name":"Wingdings 3","Dingbat dec":"153","Dingbat hex":"99","Unicode dec":"129024","Unicode hex":"1F800"},{"Typeface name":"Wingdings 3","Dingbat dec":"154","Dingbat hex":"9A","Unicode dec":"129026","Unicode hex":"1F802"},{"Typeface name":"Wingdings 3","Dingbat dec":"155","Dingbat hex":"9B","Unicode dec":"129025","Unicode hex":"1F801"},{"Typeface name":"Wingdings 3","Dingbat dec":"156","Dingbat hex":"9C","Unicode dec":"129027","Unicode hex":"1F803"},{"Typeface name":"Wingdings 3","Dingbat dec":"157","Dingbat hex":"9D","Unicode dec":"129028","Unicode hex":"1F804"},{"Typeface name":"Wingdings 3","Dingbat dec":"158","Dingbat hex":"9E","Unicode dec":"129030","Unicode hex":"1F806"},{"Typeface name":"Wingdings 3","Dingbat dec":"159","Dingbat hex":"9F","Unicode dec":"129029","Unicode hex":"1F805"},{"Typeface name":"Wingdings 3","Dingbat dec":"160","Dingbat hex":"A0","Unicode dec":"129031","Unicode hex":"1F807"},{"Typeface name":"Wingdings 3","Dingbat dec":"161","Dingbat hex":"A1","Unicode dec":"129032","Unicode hex":"1F808"},{"Typeface name":"Wingdings 3","Dingbat dec":"162","Dingbat hex":"A2","Unicode dec":"129034","Unicode hex":"1F80A"},{"Typeface name":"Wingdings 3","Dingbat dec":"163","Dingbat hex":"A3","Unicode dec":"129033","Unicode hex":"1F809"},{"Typeface name":"Wingdings 3","Dingbat dec":"164","Dingbat hex":"A4","Unicode dec":"129035","Unicode hex":"1F80B"},{"Typeface name":"Wingdings 3","Dingbat dec":"165","Dingbat hex":"A5","Unicode dec":"129056","Unicode hex":"1F820"},{"Typeface name":"Wingdings 3","Dingbat dec":"166","Dingbat hex":"A6","Unicode dec":"129058","Unicode hex":"1F822"},{"Typeface name":"Wingdings 3","Dingbat dec":"167","Dingbat hex":"A7","Unicode dec":"129060","Unicode hex":"1F824"},{"Typeface name":"Wingdings 3","Dingbat dec":"168","Dingbat hex":"A8","Unicode dec":"129062","Unicode hex":"1F826"},{"Typeface name":"Wingdings 3","Dingbat dec":"169","Dingbat hex":"A9","Unicode dec":"129064","Unicode hex":"1F828"},{"Typeface name":"Wingdings 3","Dingbat dec":"170","Dingbat hex":"AA","Unicode dec":"129066","Unicode hex":"1F82A"},{"Typeface name":"Wingdings 3","Dingbat dec":"171","Dingbat hex":"AB","Unicode dec":"129068","Unicode hex":"1F82C"},{"Typeface name":"Wingdings 3","Dingbat dec":"172","Dingbat hex":"AC","Unicode dec":"129180","Unicode hex":"1F89C"},{"Typeface name":"Wingdings 3","Dingbat dec":"173","Dingbat hex":"AD","Unicode dec":"129181","Unicode hex":"1F89D"},{"Typeface name":"Wingdings 3","Dingbat dec":"174","Dingbat hex":"AE","Unicode dec":"129182","Unicode hex":"1F89E"},{"Typeface name":"Wingdings 3","Dingbat dec":"175","Dingbat hex":"AF","Unicode dec":"129183","Unicode hex":"1F89F"},{"Typeface name":"Wingdings 3","Dingbat dec":"176","Dingbat hex":"B0","Unicode dec":"129070","Unicode hex":"1F82E"},{"Typeface name":"Wingdings 3","Dingbat dec":"177","Dingbat hex":"B1","Unicode dec":"129072","Unicode hex":"1F830"},{"Typeface name":"Wingdings 3","Dingbat dec":"178","Dingbat hex":"B2","Unicode dec":"129074","Unicode hex":"1F832"},{"Typeface name":"Wingdings 3","Dingbat dec":"179","Dingbat hex":"B3","Unicode dec":"129076","Unicode hex":"1F834"},{"Typeface name":"Wingdings 3","Dingbat dec":"180","Dingbat hex":"B4","Unicode dec":"129078","Unicode hex":"1F836"},{"Typeface name":"Wingdings 3","Dingbat dec":"181","Dingbat hex":"B5","Unicode dec":"129080","Unicode hex":"1F838"},{"Typeface name":"Wingdings 3","Dingbat dec":"182","Dingbat hex":"B6","Unicode dec":"129082","Unicode hex":"1F83A"},{"Typeface name":"Wingdings 3","Dingbat dec":"183","Dingbat hex":"B7","Unicode dec":"129081","Unicode hex":"1F839"},{"Typeface name":"Wingdings 3","Dingbat dec":"184","Dingbat hex":"B8","Unicode dec":"129083","Unicode hex":"1F83B"},{"Typeface name":"Wingdings 3","Dingbat dec":"185","Dingbat hex":"B9","Unicode dec":"129176","Unicode hex":"1F898"},{"Typeface name":"Wingdings 3","Dingbat dec":"186","Dingbat hex":"BA","Unicode dec":"129178","Unicode hex":"1F89A"},{"Typeface name":"Wingdings 3","Dingbat dec":"187","Dingbat hex":"BB","Unicode dec":"129177","Unicode hex":"1F899"},{"Typeface name":"Wingdings 3","Dingbat dec":"188","Dingbat hex":"BC","Unicode dec":"129179","Unicode hex":"1F89B"},{"Typeface name":"Wingdings 3","Dingbat dec":"189","Dingbat hex":"BD","Unicode dec":"129084","Unicode hex":"1F83C"},{"Typeface name":"Wingdings 3","Dingbat dec":"190","Dingbat hex":"BE","Unicode dec":"129086","Unicode hex":"1F83E"},{"Typeface name":"Wingdings 3","Dingbat dec":"191","Dingbat hex":"BF","Unicode dec":"129085","Unicode hex":"1F83D"},{"Typeface name":"Wingdings 3","Dingbat dec":"192","Dingbat hex":"C0","Unicode dec":"129087","Unicode hex":"1F83F"},{"Typeface name":"Wingdings 3","Dingbat dec":"193","Dingbat hex":"C1","Unicode dec":"129088","Unicode hex":"1F840"},{"Typeface name":"Wingdings 3","Dingbat dec":"194","Dingbat hex":"C2","Unicode dec":"129090","Unicode hex":"1F842"},{"Typeface name":"Wingdings 3","Dingbat dec":"195","Dingbat hex":"C3","Unicode dec":"129089","Unicode hex":"1F841"},{"Typeface name":"Wingdings 3","Dingbat dec":"196","Dingbat hex":"C4","Unicode dec":"129091","Unicode hex":"1F843"},{"Typeface name":"Wingdings 3","Dingbat dec":"197","Dingbat hex":"C5","Unicode dec":"129092","Unicode hex":"1F844"},{"Typeface name":"Wingdings 3","Dingbat dec":"198","Dingbat hex":"C6","Unicode dec":"129094","Unicode hex":"1F846"},{"Typeface name":"Wingdings 3","Dingbat dec":"199","Dingbat hex":"C7","Unicode dec":"129093","Unicode hex":"1F845"},{"Typeface name":"Wingdings 3","Dingbat dec":"200","Dingbat hex":"C8","Unicode dec":"129095","Unicode hex":"1F847"},{"Typeface name":"Wingdings 3","Dingbat dec":"201","Dingbat hex":"C9","Unicode dec":"11176","Unicode hex":"2BA8"},{"Typeface name":"Wingdings 3","Dingbat dec":"202","Dingbat hex":"CA","Unicode dec":"11177","Unicode hex":"2BA9"},{"Typeface name":"Wingdings 3","Dingbat dec":"203","Dingbat hex":"CB","Unicode dec":"11178","Unicode hex":"2BAA"},{"Typeface name":"Wingdings 3","Dingbat dec":"204","Dingbat hex":"CC","Unicode dec":"11179","Unicode hex":"2BAB"},{"Typeface name":"Wingdings 3","Dingbat dec":"205","Dingbat hex":"CD","Unicode dec":"11180","Unicode hex":"2BAC"},{"Typeface name":"Wingdings 3","Dingbat dec":"206","Dingbat hex":"CE","Unicode dec":"11181","Unicode hex":"2BAD"},{"Typeface name":"Wingdings 3","Dingbat dec":"207","Dingbat hex":"CF","Unicode dec":"11182","Unicode hex":"2BAE"},{"Typeface name":"Wingdings 3","Dingbat dec":"208","Dingbat hex":"D0","Unicode dec":"11183","Unicode hex":"2BAF"},{"Typeface name":"Wingdings 3","Dingbat dec":"209","Dingbat hex":"D1","Unicode dec":"129120","Unicode hex":"1F860"},{"Typeface name":"Wingdings 3","Dingbat dec":"210","Dingbat hex":"D2","Unicode dec":"129122","Unicode hex":"1F862"},{"Typeface name":"Wingdings 3","Dingbat dec":"211","Dingbat hex":"D3","Unicode dec":"129121","Unicode hex":"1F861"},{"Typeface name":"Wingdings 3","Dingbat dec":"212","Dingbat hex":"D4","Unicode dec":"129123","Unicode hex":"1F863"},{"Typeface name":"Wingdings 3","Dingbat dec":"213","Dingbat hex":"D5","Unicode dec":"129124","Unicode hex":"1F864"},{"Typeface name":"Wingdings 3","Dingbat dec":"214","Dingbat hex":"D6","Unicode dec":"129125","Unicode hex":"1F865"},{"Typeface name":"Wingdings 3","Dingbat dec":"215","Dingbat hex":"D7","Unicode dec":"129127","Unicode hex":"1F867"},{"Typeface name":"Wingdings 3","Dingbat dec":"216","Dingbat hex":"D8","Unicode dec":"129126","Unicode hex":"1F866"},{"Typeface name":"Wingdings 3","Dingbat dec":"217","Dingbat hex":"D9","Unicode dec":"129136","Unicode hex":"1F870"},{"Typeface name":"Wingdings 3","Dingbat dec":"218","Dingbat hex":"DA","Unicode dec":"129138","Unicode hex":"1F872"},{"Typeface name":"Wingdings 3","Dingbat dec":"219","Dingbat hex":"DB","Unicode dec":"129137","Unicode hex":"1F871"},{"Typeface name":"Wingdings 3","Dingbat dec":"220","Dingbat hex":"DC","Unicode dec":"129139","Unicode hex":"1F873"},{"Typeface name":"Wingdings 3","Dingbat dec":"221","Dingbat hex":"DD","Unicode dec":"129140","Unicode hex":"1F874"},{"Typeface name":"Wingdings 3","Dingbat dec":"222","Dingbat hex":"DE","Unicode dec":"129141","Unicode hex":"1F875"},{"Typeface name":"Wingdings 3","Dingbat dec":"223","Dingbat hex":"DF","Unicode dec":"129143","Unicode hex":"1F877"},{"Typeface name":"Wingdings 3","Dingbat dec":"224","Dingbat hex":"E0","Unicode dec":"129142","Unicode hex":"1F876"},{"Typeface name":"Wingdings 3","Dingbat dec":"225","Dingbat hex":"E1","Unicode dec":"129152","Unicode hex":"1F880"},{"Typeface name":"Wingdings 3","Dingbat dec":"226","Dingbat hex":"E2","Unicode dec":"129154","Unicode hex":"1F882"},{"Typeface name":"Wingdings 3","Dingbat dec":"227","Dingbat hex":"E3","Unicode dec":"129153","Unicode hex":"1F881"},{"Typeface name":"Wingdings 3","Dingbat dec":"228","Dingbat hex":"E4","Unicode dec":"129155","Unicode hex":"1F883"},{"Typeface name":"Wingdings 3","Dingbat dec":"229","Dingbat hex":"E5","Unicode dec":"129156","Unicode hex":"1F884"},{"Typeface name":"Wingdings 3","Dingbat dec":"230","Dingbat hex":"E6","Unicode dec":"129157","Unicode hex":"1F885"},{"Typeface name":"Wingdings 3","Dingbat dec":"231","Dingbat hex":"E7","Unicode dec":"129159","Unicode hex":"1F887"},{"Typeface name":"Wingdings 3","Dingbat dec":"232","Dingbat hex":"E8","Unicode dec":"129158","Unicode hex":"1F886"},{"Typeface name":"Wingdings 3","Dingbat dec":"233","Dingbat hex":"E9","Unicode dec":"129168","Unicode hex":"1F890"},{"Typeface name":"Wingdings 3","Dingbat dec":"234","Dingbat hex":"EA","Unicode dec":"129170","Unicode hex":"1F892"},{"Typeface name":"Wingdings 3","Dingbat dec":"235","Dingbat hex":"EB","Unicode dec":"129169","Unicode hex":"1F891"},{"Typeface name":"Wingdings 3","Dingbat dec":"236","Dingbat hex":"EC","Unicode dec":"129171","Unicode hex":"1F893"},{"Typeface name":"Wingdings 3","Dingbat dec":"237","Dingbat hex":"ED","Unicode dec":"129172","Unicode hex":"1F894"},{"Typeface name":"Wingdings 3","Dingbat dec":"238","Dingbat hex":"EE","Unicode dec":"129174","Unicode hex":"1F896"},{"Typeface name":"Wingdings 3","Dingbat dec":"239","Dingbat hex":"EF","Unicode dec":"129173","Unicode hex":"1F895"},{"Typeface name":"Wingdings 3","Dingbat dec":"240","Dingbat hex":"F0","Unicode dec":"129175","Unicode hex":"1F897"}]),nc)),t={},i=String.fromCodePoint?String.fromCodePoint:function(e){if(e<=65535)return String.fromCharCode(e);var n=Math.floor((e-65536)/1024)+55296,t=(e-65536)%1024+56320;return String.fromCharCode(n,t)},r=0,a=n.default;r<a.length;r++){var o=a[r],c=parseInt(o["Unicode dec"],10),s={codePoint:c,string:i(c)};t[o["Typeface name"].toUpperCase()+"_"+o["Dingbat dec"]]=s}function d(e,n){return t[e.toUpperCase()+"_"+n]}return ec.codePoint=d,ec.dec=function(e,n){return d(e,parseInt(n,10))},ec.hex=function(e,n){return d(e,parseInt(n,16))},ec}var ic,rc,ac={};function oc(){if(rc)return Jo;rc=1,Jo.createBodyReader=function(e){return{readXmlElement:function(n){return new c(e).readXmlElement(n)},readXmlElements:function(n){return new c(e).readXmlElements(n)}}},Jo._readNumberingProperties=s;var e=tc(),n=Ht,t=Wr(),i=Sr().Result,r=Sr().warning,a=Qo(),o=(ic||(ic=1,ac.uriToZipEntryName=function(e,n){return"/"===n.charAt(0)?n.substr(1):e+"/"+n},ac.replaceFragment=function(e,n){var t=e.indexOf("#");return-1!==t&&(e=e.substring(0,t)),e+"#"+n}),ac);function c(i){var c=[],m=[],y=[],x=i.relationships,D=i.contentTypes,U=i.docxFile,v=i.files,T=i.numbering,_=i.styles;function w(e){return b(e.map(F))}function F(e){if("element"===e.type){var n=R[e.name];if(n)return n(e);if(!Object.prototype.hasOwnProperty.call(u,e.name))return l([r("An unrecognised element was ignored: "+e.name)])}return h()}function E(e){return function(e){return B(e,"w:pStyle","Paragraph",_.findParagraphStyleById)}(e).map(function(n){return{type:"paragraphProperties",styleId:n.styleId,styleName:n.name,alignment:e.firstOrEmpty("w:jc").attributes["w:val"],numbering:s(n.styleId,e.firstOrEmpty("w:numPr"),T),indent:W(e.firstOrEmpty("w:ind"))}})}function W(e){return{start:e.attributes["w:start"]||e.attributes["w:left"],end:e.attributes["w:end"]||e.attributes["w:right"],firstLine:e.attributes["w:firstLine"],hanging:e.attributes["w:hanging"]}}function C(e){return function(e){return B(e,"w:rStyle","Run",_.findCharacterStyleById)}(e).map(function(n){var t=e.firstOrEmpty("w:sz").attributes["w:val"],i=/^[0-9]+$/.test(t)?parseInt(t,10)/2:null;return{type:"runProperties",styleId:n.styleId,styleName:n.name,verticalAlignment:e.firstOrEmpty("w:vertAlign").attributes["w:val"],font:e.firstOrEmpty("w:rFonts").attributes["w:ascii"],fontSize:i,isBold:S(e.first("w:b")),isUnderline:A(e.first("w:u")),isItalic:S(e.first("w:i")),isStrikethrough:S(e.first("w:strike")),isAllCaps:S(e.first("w:caps")),isSmallCaps:S(e.first("w:smallCaps")),highlight:N(e.firstOrEmpty("w:highlight").attributes["w:val"])}})}function A(e){if(e){var n=e.attributes["w:val"];return void 0!==n&&"false"!==n&&"0"!==n&&"none"!==n}return!1}function S(e){if(e){var n=e.attributes["w:val"];return"false"!==n&&"0"!==n}return!1}function N(e){return e&&"none"!==e?e:null}function B(e,n,t,i){var a=[],o=e.first(n),c=null,s=null;if(o&&(c=o.attributes["w:val"])){var d=i(c);d?s=d.name:a.push(function(e,n){return r(e+" style with ID "+n+" was referenced but not defined in the document")}(t,c))}return f({styleId:c,name:s},a)}function k(e){return function(e,n){var t=/\s*HYPERLINK "(.*)"/.exec(e);if(t)return{type:"hyperlink",options:{href:t[1]}};var i=/\s*HYPERLINK\s+\\l\s+"(.*)"/.exec(e);if(i)return{type:"hyperlink",options:{anchor:i[1]}};if(/\s*FORMCHECKBOX\s*/.exec(e)){var r=n.firstOrEmpty("w:ffData").firstOrEmpty("w:checkBox"),a=r.first("w:checked");return{type:"checkbox",checked:S(null==a?r.first("w:default"):a)}}return{type:"unknown"}}(m.join(""),"begin"===e.type?e.fldChar:a.emptyElement)}function O(e){return function(n){var i=n.attributes["w:id"];return p(new t.NoteReference({noteType:e,noteId:i}))}}function I(e){return w(e.children)}var R={"w:p":function(e){var n=e.firstOrEmpty("w:pPr");if(!!n.firstOrEmpty("w:rPr").first("w:del"))return e.children.forEach(function(e){y.push(e)}),h();var i=e.children;return y.length>0&&(i=y.concat(i),y=[]),g.map(E(n),w(i),function(e,n){return new t.Paragraph(n,e)}).insertExtra()},"w:r":function(e){return g.map(C(e.firstOrEmpty("w:rPr")),w(e.children),function(e,i){var r,a=(r=n.last(c.filter(function(e){return"hyperlink"===e.type})))?r.options:null;return null!==a&&(i=[new t.Hyperlink(i,a)]),new t.Run(i,e)})},"w:fldChar":function(e){var n=e.attributes["w:fldCharType"];if("begin"===n)c.push({type:"begin",fldChar:e}),m=[];else if("end"===n){var i=c.pop();if("begin"===i.type&&(i=k(i)),"checkbox"===i.type)return p(t.checkbox({checked:i.checked}))}else if("separate"===n){var r=k(c.pop());c.push(r)}return h()},"w:instrText":function(e){return m.push(e.text()),h()},"w:t":function(e){return p(new t.Text(e.text()))},"w:tab":function(e){return p(new t.Tab)},"w:noBreakHyphen":function(){return p(new t.Text("‑"))},"w:softHyphen":function(e){return p(new t.Text("­"))},"w:sym":function(n){var i=n.attributes["w:font"],a=n.attributes["w:char"],o=e.hex(i,a);return null==o&&/^F0..$/.test(a)&&(o=e.hex(i,a.substring(2))),null==o?l([r("A w:sym element with an unsupported character was ignored: char "+a+" in font "+i)]):p(new t.Text(o.string))},"w:hyperlink":function(e){var i=e.attributes["r:id"],r=e.attributes["w:anchor"];return w(e.children).map(function(a){function c(i){var r=e.attributes["w:tgtFrame"]||null;return new t.Hyperlink(a,n.extend({targetFrame:r},i))}if(i){var s=x.findTargetByRelationshipId(i);return r&&(s=o.replaceFragment(s,r)),c({href:s})}return r?c({anchor:r}):a})},"w:tbl":function(e){var n=function(e){return function(e){return B(e,"w:tblStyle","Table",_.findTableStyleById)}(e).map(function(e){return{styleId:e.styleId,styleName:e.name}})}(e.firstOrEmpty("w:tblPr"));return w(e.children).flatMap(j).flatMap(function(e){return n.map(function(n){return t.Table(e,n)})})},"w:tr":function(e){var n=!!e.firstOrEmpty("w:trPr").first("w:tblHeader");return w(e.children).map(function(e){return t.TableRow(e,{isHeader:n})})},"w:tc":function(e){return w(e.children).map(function(n){var i=e.firstOrEmpty("w:tcPr"),r=i.firstOrEmpty("w:gridSpan").attributes["w:val"],a=r?parseInt(r,10):1,o=t.TableCell(n,{colSpan:a});return o._vMerge=function(e){var n=e.first("w:vMerge");if(n){var t=n.attributes["w:val"];return"continue"===t||!t}return null}(i),o})},"w:footnoteReference":O("footnote"),"w:endnoteReference":O("endnote"),"w:commentReference":function(e){return p(t.commentReference({commentId:e.attributes["w:id"]}))},"w:br":function(e){var n=e.attributes["w:type"];return null==n||"textWrapping"===n?p(t.lineBreak):"page"===n?p(t.pageBreak):"column"===n?p(t.columnBreak):l([r("Unsupported break type: "+n)])},"w:bookmarkStart":function(e){var n=e.attributes["w:name"];return"_GoBack"===n?h():p(new t.BookmarkStart({name:n}))},"mc:AlternateContent":function(e){return I(e.firstOrEmpty("mc:Fallback"))},"w:sdt":function(e){var n,i=e.firstOrEmpty("w:sdtPr").first("wordml:checkbox");if(i){var r=i.first("wordml:checked"),a=!!r&&("false"!==(n=r.attributes["wordml:val"])&&"0"!==n);return p(t.checkbox({checked:a}))}return w(e.firstOrEmpty("w:sdtContent").children)},"w:ins":I,"w:object":I,"w:smartTag":I,"w:drawing":I,"w:pict":function(e){return I(e).toExtra()},"v:roundrect":I,"v:shape":I,"v:textbox":I,"w:txbxContent":I,"wp:inline":P,"wp:anchor":P,"v:imagedata":function(e){var n=e.attributes["r:id"];return n?M(q(n),e.attributes["o:title"]):l([r("A v:imagedata element without a relationship ID was ignored")])},"v:group":I,"v:rect":I};return{readXmlElement:F,readXmlElements:w};function j(e){if(n.any(e,function(e){return e.type!==t.types.tableRow}))return f(e,[r("unexpected non-row element in table, cell merging may be incorrect")]);if(n.any(e,function(e){return n.any(e.children,function(e){return e.type!==t.types.tableCell})}))return f(e,[r("unexpected non-cell element in table row, cell merging may be incorrect")]);var i={};return e.forEach(function(e){var n=0;e.children.forEach(function(e){e._vMerge&&i[n]?i[n].rowSpan++:(i[n]=e,e._vMerge=!1),n+=e.colSpan})}),e.forEach(function(e){e.children=e.children.filter(function(e){return!e._vMerge}),e.children.forEach(function(e){delete e._vMerge})}),p(e)}function P(e){return b(e.getElementsByTagName("a:graphic").getElementsByTagName("a:graphicData").getElementsByTagName("pic:pic").getElementsByTagName("pic:blipFill").getElementsByTagName("a:blip").map(L.bind(null,e)))}function L(e,n){var t,i=e.first("wp:docPr").attributes,a=null==(t=i.descr)||/^\s*$/.test(t)?i.title:i.descr,o=function(e){var n=e.attributes["r:embed"],t=e.attributes["r:link"];if(n)return q(n);if(t){var i=x.findTargetByRelationshipId(t);return{path:i,read:v.read.bind(v,i)}}return null}(n);return null===o?l([r("Could not find image file for a:blip element")]):M(o,a)}function q(e){var n=o.uriToZipEntryName("word",x.findTargetByRelationshipId(e));return{path:n,read:U.read.bind(U,n)}}function M(e,n){var i=D.findContentType(e.path);return f(t.Image({readImage:e.read,altText:n,contentType:i}),d[i]?[]:r("Image of type "+i+" is unlikely to display in web browsers"))}}function s(e,n,t){var i=n.firstOrEmpty("w:ilvl").attributes["w:val"],r=n.firstOrEmpty("w:numId").attributes["w:val"];if(void 0!==i&&void 0!==r)return t.findLevel(r,i);if(null!=e){var a=t.findLevelByParagraphStyleId(e);if(null!=a)return a}return null}var d={"image/png":!0,"image/gif":!0,"image/jpeg":!0,"image/svg+xml":!0,"image/tiff":!0},u={"office-word:wrap":!0,"v:shadow":!0,"v:shapetype":!0,"w:annotationRef":!0,"w:bookmarkEnd":!0,"w:sectPr":!0,"w:proofErr":!0,"w:lastRenderedPageBreak":!0,"w:commentRangeStart":!0,"w:commentRangeEnd":!0,"w:del":!0,"w:footnoteRef":!0,"w:endnoteRef":!0,"w:pPr":!0,"w:rPr":!0,"w:tblPr":!0,"w:tblGrid":!0,"w:trPr":!0,"w:tcPr":!0};function l(e){return new g(null,null,e)}function h(){return new g(null)}function p(e){return new g(e)}function f(e,n){return new g(e,null,n)}function g(e,n,t){this.value=e||[],this.extra=n||[],this._result=new i({element:this.value,extra:n},t),this.messages=this._result.messages}function b(e){var t=i.combine(n.pluck(e,"_result"));return new g(n.flatten(n.pluck(t.value,"element")),n.filter(n.flatten(n.pluck(t.value,"extra")),y),t.messages)}function m(e,t){return n.flatten([e,t])}function y(e){return e}return g.prototype.toExtra=function(){return new g(null,m(this.extra,this.value),this.messages)},g.prototype.insertExtra=function(){var e=this.extra;return e&&e.length?new g(m(this.value,e),null,this.messages):this},g.prototype.map=function(e){var n=this._result.map(function(n){return e(n.element)});return new g(n.value,this.extra,n.messages)},g.prototype.flatMap=function(e){var n=this._result.flatMap(function(n){return e(n.element)._result});return new g(n.value.element,m(this.extra,n.value.extra),n.messages)},g.map=function(e,n,t){return new g(t(e.value,n.value),m(e.extra,n.extra),e.messages.concat(n.messages))},Jo}var cc,sc={};var dc,uc={};var lc,hc={};var pc,fc={};function gc(){if(pc)return fc;pc=1;var e=Ht;function n(n,t,i){var r=e.flatten(e.values(t).map(function(n){return e.values(n.levels)})),a=e.indexBy(r.filter(function(e){return null!=e.paragraphStyleId}),"paragraphStyleId");return{findLevel:function e(r,a){var o=n[r];if(o){var c=t[o.abstractNumId];return c?null==c.numStyleLink?t[o.abstractNumId].levels[a]:e(i.findNumberingStyleById(c.numStyleLink).numId,a):null}return null},findLevelByParagraphStyleId:function(e){return a[e]||null}}}return fc.readNumberingXml=function(e,t){if(!t||!t.styles)throw new Error("styles is missing");var i=function(e){var n={};return e.getElementsByTagName("w:abstractNum").forEach(function(e){var t=e.attributes["w:abstractNumId"];n[t]=function(e){var n={};e.getElementsByTagName("w:lvl").forEach(function(e){var t=e.attributes["w:ilvl"],i=e.firstOrEmpty("w:numFmt").attributes["w:val"],r=e.firstOrEmpty("w:pStyle").attributes["w:val"];n[t]={isOrdered:"bullet"!==i,level:t,paragraphStyleId:r}});var t=e.firstOrEmpty("w:numStyleLink").attributes["w:val"];return{levels:n,numStyleLink:t}}(e)}),n}(e);return new n(function(e){var n={};return e.getElementsByTagName("w:num").forEach(function(e){var t=e.attributes["w:numId"],i=e.first("w:abstractNumId").attributes["w:val"];n[t]={abstractNumId:i}}),n}(e),i,t.styles)},fc.Numbering=n,fc.defaultNumbering=new n({},{}),fc}var bc,mc={};function yc(){if(bc)return mc;function e(e,n,t,i){return{findParagraphStyleById:function(n){return e[n]},findCharacterStyleById:function(e){return n[e]},findTableStyleById:function(e){return t[e]},findNumberingStyleById:function(e){return i[e]}}}return bc=1,mc.readStylesXml=function(n){var t={},i={},r={},a={},o={paragraph:t,character:i,table:r};return n.getElementsByTagName("w:style").forEach(function(e){var n=function(e){var n=e.attributes["w:type"],t=e.attributes["w:styleId"],i=function(e){var n=e.first("w:name");return n?n.attributes["w:val"]:null}(e);return{type:n,styleId:t,name:i}}(e);if("numbering"===n.type)a[n.styleId]=function(e){var n=e.firstOrEmpty("w:pPr").firstOrEmpty("w:numPr").firstOrEmpty("w:numId").attributes["w:val"];return{numId:n}}(e);else{var t=o[n.type];t&&(t[n.styleId]=n)}}),new e(t,i,r,a)},mc.Styles=e,mc.defaultStyles=new e({},{}),e.EMPTY=new e({},{},{},{}),mc}var xc,Dc={};var Uc,vc={};var Tc,_c,wc={};function Fc(){if(_c)return $t;_c=1,$t.read=function(i,r){return r=r||{},e.props({contentTypes:x(i),partPaths:f(i),docxFile:i,files:r.path?p.relativeToFile(r.path):new p(null)}).also(function(e){return{styles:(n=i,t=e.partPaths.styles,b({filename:t,readElement:u.readStylesXml,defaultValue:u.defaultStyles})(n))};var n,t}).also(function(e){return{numbering:(n=i,t=e.partPaths.numbering,r=e.styles,b({filename:t,readElement:function(e){return d.readNumberingXml(e,{styles:r})},defaultValue:d.defaultNumbering})(n))};var n,t,r}).also(function(e){return{footnotes:m(e.partPaths.footnotes,e,function(e,n){return n?l.createFootnotesReader(e)(n):new t([])}),endnotes:m(e.partPaths.endnotes,e,function(e,n){return n?l.createEndnotesReader(e)(n):new t([])}),comments:m(e.partPaths.comments,e,function(e,n){return n?h.createCommentsReader(e)(n):new t([])})}}).also(function(e){return{notes:e.footnotes.flatMap(function(t){return e.endnotes.map(function(e){return new n.Notes(t.concat(e))})})}}).then(function(e){return m(e.partPaths.mainDocument,e,function(n,t){return e.notes.flatMap(function(i){return e.comments.flatMap(function(e){return new o({bodyReader:n,notes:i,comments:e}).convertXmlToDocument(t)})})})})},$t._findPartPaths=f;var e=wr(),n=Wr(),t=Sr().Result,i=Ir(),r=function(){if(zo)return jr;zo=1;var e=Ht,n=wr(),t=Qo();jr.read=r,jr.readXmlFromZipFile=function(e,t){return e.exists(t)?e.read(t,"utf-8").then(a).then(r):n.resolve(null)};var i={"http://schemas.openxmlformats.org/wordprocessingml/2006/main":"w","http://schemas.openxmlformats.org/officeDocument/2006/relationships":"r","http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing":"wp","http://schemas.openxmlformats.org/drawingml/2006/main":"a","http://schemas.openxmlformats.org/drawingml/2006/picture":"pic","http://purl.oclc.org/ooxml/wordprocessingml/main":"w","http://purl.oclc.org/ooxml/officeDocument/relationships":"r","http://purl.oclc.org/ooxml/drawingml/wordprocessingDrawing":"wp","http://purl.oclc.org/ooxml/drawingml/main":"a","http://purl.oclc.org/ooxml/drawingml/picture":"pic","http://schemas.openxmlformats.org/package/2006/content-types":"content-types","http://schemas.openxmlformats.org/package/2006/relationships":"relationships","http://schemas.openxmlformats.org/markup-compatibility/2006":"mc","urn:schemas-microsoft-com:vml":"v","urn:schemas-microsoft-com:office:word":"office-word","http://schemas.microsoft.com/office/word/2010/wordml":"wordml"};function r(e){return t.readString(e,i).then(function(e){return o(e)[0]})}function a(e){return e.replace(/^\uFEFF/g,"")}function o(n){return"element"===n.type?"mc:AlternateContent"===n.name?n.firstOrEmpty("mc:Fallback").children:(n.children=e.flatten(n.children.map(o,!0)),[n]):[n]}return jr}().readXmlFromZipFile,a=oc().createBodyReader,o=function(){if(cc)return sc;cc=1,sc.DocumentXmlReader=function(t){var i=t.bodyReader;return{convertXmlToDocument:function(r){var a=r.first("w:body");if(null==a)throw new Error("Could not find the body element: are you sure this is a docx file?");var o=i.readXmlElements(a.children).map(function(n){return new e.Document(n,{notes:t.notes,comments:t.comments})});return new n(o.value,o.messages)}}};var e=Wr(),n=Sr().Result;return sc}().DocumentXmlReader,c=function(){if(dc)return uc;function e(e){var n={};e.forEach(function(e){n[e.relationshipId]=e.target});var t={};return e.forEach(function(e){t[e.type]||(t[e.type]=[]),t[e.type].push(e.target)}),{findTargetByRelationshipId:function(e){return n[e]},findTargetsByType:function(e){return t[e]||[]}}}return dc=1,uc.readRelationships=function(n){var t=[];return n.children.forEach(function(e){if("relationships:Relationship"===e.name){var n={relationshipId:e.attributes.Id,target:e.attributes.Target,type:e.attributes.Type};t.push(n)}}),new e(t)},uc.defaultValue=new e([]),uc.Relationships=e,uc}(),s=function(){if(lc)return hc;lc=1,hc.readContentTypesFromXml=function(e){var t={},i={};return e.children.forEach(function(e){if("content-types:Default"===e.name&&(t[e.attributes.Extension]=e.attributes.ContentType),"content-types:Override"===e.name){var n=e.attributes.PartName;"/"===n.charAt(0)&&(n=n.substring(1)),i[n]=e.attributes.ContentType}}),n(i,t)};var e={png:"png",gif:"gif",jpeg:"jpeg",jpg:"jpeg",tif:"tiff",tiff:"tiff",bmp:"bmp"};function n(n,t){return{findContentType:function(i){var r=n[i];if(r)return r;var a=i.split("."),o=a[a.length-1];if(t.hasOwnProperty(o))return t[o];var c=e[o.toLowerCase()];return c?"image/"+c:null}}}return hc.defaultContentTypes=n({},{}),hc}(),d=gc(),u=yc(),l=function(){if(xc)return Dc;xc=1;var e=Wr(),n=Sr().Result;function t(t,i){function r(e){var n=e.attributes["w:type"];return"continuationSeparator"!==n&&"separator"!==n}function a(n){var r=n.attributes["w:id"];return i.readXmlElements(n.children).map(function(n){return e.Note({noteType:t,noteId:r,body:n})})}return function(e){return n.combine(e.getElementsByTagName("w:"+t).filter(r).map(a))}}return Dc.createFootnotesReader=t.bind(Dc,"footnote"),Dc.createEndnotesReader=t.bind(Dc,"endnote"),Dc}(),h=function(){if(Uc)return vc;Uc=1;var e=Wr(),n=Sr().Result;return vc.createCommentsReader=function(t){function i(n){var i=n.attributes["w:id"];function r(e){return(n.attributes[e]||"").trim()||null}return t.readXmlElements(n.children).map(function(n){return e.comment({commentId:i,body:n,authorName:r("w:author"),authorInitials:r("w:initials")})})}return function(e){return n.combine(e.getElementsByTagName("w:comment").map(i))}},vc}(),p=function(){if(Tc)return wc;Tc=1;var e=wr();return wc.Files=function(){return{read:function(n){return e.reject(new Error("could not open external image: '"+n+"'\ncannot open linked files from a web browser"))}}},wc}().Files;function f(e){return D(e).then(function(n){var t=g({docxFile:e,relationships:n,relationshipType:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",basePath:"",fallbackPath:"word/document.xml"});if(!e.exists(t))throw new Error("Could not find main document part. Are you sure this is a valid .docx file?");return b({filename:y(t),readElement:c.readRelationships,defaultValue:c.defaultValue})(e).then(function(n){function r(r){return g({docxFile:e,relationships:n,relationshipType:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/"+r,basePath:i.splitPath(t).dirname,fallbackPath:"word/"+r+".xml"})}return{mainDocument:t,comments:r("comments"),endnotes:r("endnotes"),footnotes:r("footnotes"),numbering:r("numbering"),styles:r("styles")}})})}function g(e){var n=e.docxFile,t=e.relationships,r=e.relationshipType,a=e.basePath,o=e.fallbackPath,c=t.findTargetsByType(r).map(function(e){return n=i.joinPath(a,e),t="/",n.substring(0,t.length)===t?n.substring(t.length):n;var n,t}).filter(function(e){return n.exists(e)});return 0===c.length?o:c[0]}function b(e){return function(n){return r(n,e.filename).then(function(n){return n?e.readElement(n):e.defaultValue})}}function m(e,n,t){return b({filename:y(e),readElement:c.readRelationships,defaultValue:c.defaultValue})(n.docxFile).then(function(i){var o=new a({relationships:i,contentTypes:n.contentTypes,docxFile:n.docxFile,numbering:n.numbering,styles:n.styles,files:n.files});return r(n.docxFile,e).then(function(e){return t(o,e)})})}function y(e){var n=i.splitPath(e);return i.joinPath(n.dirname,"_rels",n.basename+".rels")}var x=b({filename:"[Content_Types].xml",readElement:s.readContentTypesFromXml,defaultValue:s.defaultContentTypes});var D=b({filename:"_rels/.rels",readElement:c.readRelationships,defaultValue:c.defaultValue});return $t}var Ec,Wc={};function Cc(){if(Ec)return Wc;Ec=1;var e=Ht,n=wr(),t=Qo();Wc.writeStyleMap=function(e,n){return e.write(r,n),function(e){var n="word/_rels/document.xml.rels",r="http://schemas.openxmlformats.org/package/2006/relationships",c="{"+r+"}Relationship";return e.read(n,"utf8").then(t.readString).then(function(s){o(s.children,c,"Id",{Id:"rMammothStyleMap",Type:i,Target:a});var d={"":r};return e.write(n,t.writeString(s,d))})}(e).then(function(){return function(e){var n="[Content_Types].xml",i="http://schemas.openxmlformats.org/package/2006/content-types",r="{"+i+"}Override";return e.read(n,"utf8").then(t.readString).then(function(c){o(c.children,r,"PartName",{PartName:a,ContentType:"text/prs.mammoth.style-map"});var s={"":i};return e.write(n,t.writeString(c,s))})}(e)})},Wc.readStyleMap=function(e){return e.exists(r)?e.read(r,"utf8"):n.resolve(null)};var i="http://schemas.zwobble.org/mammoth/style-map",r="mammoth/style-map",a="/"+r;function o(n,i,r,a){var o=e.find(n,function(e){return e.name===i&&e.attributes[r]===a[r]});o?o.attributes=a:n.push(t.element(i,a))}return Wc}var Ac,Sc,Nc,Bc,kc,Oc={},Ic={},Rc={},jc={};function Pc(){if(Ac)return jc;Ac=1;var e=Mc();function n(e,n){return{type:"element",tag:e,children:n||[]}}jc.freshElement=function(t,i,r){return n(e.element(t,i,{fresh:!0}),r)},jc.nonFreshElement=function(t,i,r){return n(e.element(t,i,{fresh:!1}),r)},jc.elementWithTag=n,jc.text=function(e){return{type:"text",value:e}},jc.forceWrite={type:"forceWrite"};var t={br:!0,hr:!0,img:!0,input:!0};return jc.isVoidElement=function(e){return 0===e.children.length&&t[e.tag.tagName]},jc}function Lc(){if(Nc)return Sc;Nc=1;var e=Ht,n=Pc();function t(e){var n=[];return e.map(i).forEach(function(e){o(n,e)}),n}function i(e){return r[e.type](e)}var r={element:function(e){return n.elementWithTag(e.tag,t(e.children))},text:a,forceWrite:a};function a(e){return e}function o(e,t){var i=e[e.length-1];"element"===t.type&&!t.tag.fresh&&i&&"element"===i.type&&t.tag.matchesElement(i.tag)?(t.tag.separator&&o(i.children,n.text(t.tag.separator)),t.children.forEach(function(e){o(i.children,e)})):e.push(t)}function c(n){return t=n,i=function(e){return s[e.type](e)},e.flatten(e.map(t,i),!0);var t,i}var s={element:function(e){var t=c(e.children);return 0!==t.length||n.isVoidElement(e)?[n.elementWithTag(e.tag,t)]:[]},text:function(e){return 0===e.value.length?[]:[e]},forceWrite:function(e){return[e]}};return Sc=function(e){return t(c(e))}}function qc(){if(Bc)return Rc;Bc=1;var e=Pc();function n(e,n){n.forEach(function(n){!function(e,n){t[n.type](e,n)}(e,n)})}Rc.freshElement=e.freshElement,Rc.nonFreshElement=e.nonFreshElement,Rc.elementWithTag=e.elementWithTag,Rc.text=e.text,Rc.forceWrite=e.forceWrite,Rc.simplify=Lc();var t={element:function(t,i){e.isVoidElement(i)?t.selfClosing(i.tag.tagName,i.tag.attributes):(t.open(i.tag.tagName,i.tag.attributes),n(t,i.children),t.close(i.tag.tagName))},text:function(e,n){e.text(n.value)},forceWrite:function(){}};return Rc.write=n,Rc}function Mc(){if(kc)return Ic;kc=1;var e=Ht,n=qc();function t(n){return new i(n.map(function(n){return e.isString(n)?r(n):n}))}function i(e){this._elements=e}function r(e,n,t){return new a(e,n,t=t||{})}function a(n,t,i){var r={};e.isArray(n)?(n.forEach(function(e){r[e]=!0}),n=n[0]):r[n]=!0,this.tagName=n,this.tagNames=r,this.attributes=t||{},this.fresh=i.fresh,this.separator=i.separator}return Ic.topLevelElement=function(e,n){return t([r(e,n,{fresh:!0})])},Ic.elements=t,Ic.element=r,i.prototype.wrap=function(e){for(var n=e(),t=this._elements.length-1;t>=0;t--)n=this._elements[t].wrapNodes(n);return n},a.prototype.matchesElement=function(n){return this.tagNames[n.tagName]&&e.isEqual(this.attributes||{},n.attributes||{})},a.prototype.wrap=function(e){return this.wrapNodes(e())},a.prototype.wrapNodes=function(e){return[n.elementWithTag(this,e)]},Ic.empty=t([]),Ic.ignore={wrap:function(){return[]}},Ic}var Vc,Hc={};function zc(){return Vc||(Vc=1,function(e){var n=Ht,t=wr(),i=qc();function r(e){return function(r,a){return t.when(e(r)).then(function(e){var t={};return r.altText&&(t.alt=r.altText),n.extend(t,e),[i.freshElement("img",t)]})}}e.imgElement=r,e.inline=e.imgElement,e.dataUri=r(function(e){return e.readAsBase64String().then(function(n){return{src:"data:"+e.contentType+";base64,"+n}})})}(Hc)),Hc}var Gc,Xc={},$c={};function Kc(){if(Gc)return $c;Gc=1;var e=Ht;$c.writer=function(i){return(i=i||{}).prettyPrint?function(){var i=0,r="  ",a=[],o=!0,c=!1,s=t();function d(e,t){n[e]&&g(),a.push(e),s.open(e,t),n[e]&&i++,o=!1}function u(e){n[e]&&(i--,g()),a.pop(),s.close(e)}function l(e){f();var n=b()?e:e.replace("\n","\n"+r);s.text(n)}function h(e,n){g(),s.selfClosing(e,n)}function p(){return 0===a.length||n[a[a.length-1]]}function f(){c||(g(),c=!0)}function g(){if(c=!1,!o&&p()&&!b()){s._append("\n");for(var e=0;e<i;e++)s._append(r)}}function b(){return e.some(a,function(e){return"pre"===e})}return{asString:s.asString,open:d,close:u,text:l,selfClosing:h}}():t()};var n={div:!0,p:!0,ul:!0,li:!0};function t(){var n=[];function t(n){return e.map(n,function(e,n){return" "+n+'="'+function(e){return e.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}(e)+'"'}).join("")}return{asString:function(){return n.join("")},open:function(e,i){var r=t(i);n.push("<"+e+r+">")},close:function(e){n.push("</"+e+">")},text:function(e){n.push(function(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}(e))},selfClosing:function(e,i){var r=t(i);n.push("<"+e+r+" />")},_append:function(e){n.push(e)}}}return $c}var Qc,Yc,Zc,Jc={};function es(){if(Yc)return Xc;Yc=1;var e=Kc(),n=function(){if(Qc)return Jc;Qc=1;var e=Ht;function n(e){return t(e,e)}function t(e,n){return function(){return{start:e,end:n}}}function i(e){return function(n,t){return{start:t?"\n":"",end:t?"":"\n",list:{isOrdered:e.isOrdered,indent:t?t.indent+1:0,count:0}}}}var r={p:t("","\n\n"),br:t("","  \n"),ul:i({isOrdered:!1}),ol:i({isOrdered:!0}),li:function(e,n,t){(n=n||{indent:0,isOrdered:!1,count:0}).count++,t.hasClosed=!1;var i=n.isOrdered?n.count+".":"-";return{start:a("\t",n.indent)+i+" ",end:function(){if(!t.hasClosed)return t.hasClosed=!0,"\n"}}},strong:n("__"),em:n("*"),a:function(e){var n=e.href||"";return n?{start:"[",end:"]("+n+")",anchorPosition:"before"}:{}},img:function(e){var n=e.src||"",t=e.alt||"";return n||t?{start:"!["+t+"]("+n+")"}:{}}};function a(e,n){return new Array(n+1).join(e)}return function(){for(var e=1;e<=6;e++)r["h"+e]=t(a("#",e)+" ","\n\n")}(),Jc.writer=function(){var n=[],t=[],i=null,a={};function o(e,o){o=o||{};var s=(r[e]||function(){return{}})(o,i,a);t.push({end:s.end,list:i}),s.list&&(i=s.list);var d="before"===s.anchorPosition;d&&c(o),n.push(s.start||""),d||c(o)}function c(e){e.id&&n.push('<a id="'+e.id+'"></a>')}function s(r){var a=t.pop();i=a.list;var o=e.isFunction(a.end)?a.end():a.end;n.push(o||"")}return{asString:function(){return n.join("")},open:o,close:s,text:function(e){n.push(function(e){return e.replace(/\\/g,"\\\\").replace(/([\`\*_\{\}\[\]\(\)\#\+\-\.\!])/g,"\\$1")}(e))},selfClosing:function(e,n){o(e,n),s()}}},Jc}();return Xc.writer=function(t){return"markdown"===(t=t||{}).outputFormat?n.writer():e.writer(t)},Xc}function ns(){if(Zc)return Oc;Zc=1;var e=Ht,n=wr(),t=Wr(),i=Mc(),r=Sr(),a=zc(),o=qc(),c=es();function s(s,f){var g=1,b=[],m=[],y=void 0===(s=e.extend({ignoreEmptyParagraphs:!0},s)).idPrefix?"":s.idPrefix,x=s.ignoreEmptyParagraphs,D=i.topLevelElement("p"),U=s.styleMap||[];function v(e,n,t){return l(e,function(e){return T(e,n,t)})}function T(e,n,t){if(!t)throw new Error("options not set");var i=I[e.type];return i?i(e,n,t):[]}function _(e,n){var t=w({type:e});return t||(n?i.element(n,{},{fresh:!1}):i.empty)}function w(e,n){var t=F(e);return t?t.to:n}function F(e){for(var n=0;n<U.length;n++)if(U[n].from.matches(e))return U[n]}function E(e){return C(e.noteType,e.noteId)}function W(e){return A(e.noteType,e.noteId)}function C(e,n){return S(e+"-"+n)}function A(e,n){return S(e+"-ref-"+n)}function S(e){return y+e}var N=i.elements([i.element("table",{},{fresh:!0})]);function B(e,n,t){var i=e.label,r=e.comment,a=v(r.body,n,t).concat([o.nonFreshElement("p",{},[o.text(" "),o.freshElement("a",{href:"#"+A("comment",r.commentId)},[o.text("↑")])])]);return[o.freshElement("dt",{id:C("comment",r.commentId)},[o.text("Comment "+i)]),o.freshElement("dd",{},a)]}var k,O,I={document:function(e,n,t){var i=v(e.children,n,t),r=v(b.map(function(n){return e.notes.resolve(n)}),n,t);return i.concat([o.freshElement("ol",{},r),o.freshElement("dl",{},l(m,function(e){return B(e,n,t)}))])},paragraph:function(e,n,t){return function(e,n){var t=F(e);return t?t.to:(e.styleId&&n.push(u("paragraph",e)),D)}(e,n).wrap(function(){var i=v(e.children,n,t);return x?i:[o.forceWrite].concat(i)})},run:function(e,n,r){var a=function(){return v(e.children,n,r)},o=[];if(null!==e.highlight){var c=w({type:"highlight",color:e.highlight});c&&o.push(c)}e.isSmallCaps&&o.push(_("smallCaps")),e.isAllCaps&&o.push(_("allCaps")),e.isStrikethrough&&o.push(_("strikethrough","s")),e.isUnderline&&o.push(_("underline")),e.verticalAlignment===t.verticalAlignment.subscript&&o.push(i.element("sub",{},{fresh:!1})),e.verticalAlignment===t.verticalAlignment.superscript&&o.push(i.element("sup",{},{fresh:!1})),e.isItalic&&o.push(_("italic","em")),e.isBold&&o.push(_("bold","strong"));var s=i.empty,d=F(e);return d?s=d.to:e.styleId&&n.push(u("run",e)),o.push(s),o.forEach(function(e){a=e.wrap.bind(e,a)}),a()},text:function(e,n,t){return[o.text(e.value)]},tab:function(e,n,t){return[o.text("\t")]},hyperlink:function(e,n,t){var i={href:e.anchor?"#"+S(e.anchor):e.href};null!=e.targetFrame&&(i.target=e.targetFrame);var r=v(e.children,n,t);return[o.nonFreshElement("a",i,r)]},checkbox:function(e){var n={type:"checkbox"};return e.checked&&(n.checked="checked"),[o.freshElement("input",n)]},bookmarkStart:function(e,n,t){return[o.freshElement("a",{id:S(e.name)},[o.forceWrite])]},noteReference:function(e,n,t){b.push(e);var i=o.freshElement("a",{href:"#"+E(e),id:W(e)},[o.text("["+g+++"]")]);return[o.freshElement("sup",{},[i])]},note:function(e,n,t){var r=v(e.body,n,t),a=o.elementWithTag(i.element("p",{},{fresh:!1}),[o.text(" "),o.freshElement("a",{href:"#"+W(e)},[o.text("↑")])]),c=r.concat([a]);return o.freshElement("li",{id:E(e)},c)},commentReference:function(e,n,t){return w(e,i.ignore).wrap(function(){var n=f[e.commentId],t=m.length+1,i="["+p(n)+t+"]";return m.push({label:i,comment:n}),[o.freshElement("a",{href:"#"+C("comment",e.commentId),id:A("comment",e.commentId)},[o.text(i)])]})},comment:B,image:(O=s.convertImage||a.dataUri,k=function(e,t){return n.attempt(function(){return O(e,t)}).caught(function(e){return t.push(r.error(e)),[]})},function(e,n,t){return[{type:"deferred",id:d++,value:function(){return k(e,n,t)}}]}),table:function(n,i,r){return w(n,N).wrap(function(){return function(n,i,r){var a,c=e.findIndex(n.children,function(e){return!e.type===t.types.tableRow||!e.isHeader});-1===c&&(c=n.children.length);if(0===c)a=v(n.children,i,e.extend({},r,{isTableHeader:!1}));else{var s=v(n.children.slice(0,c),i,e.extend({},r,{isTableHeader:!0})),d=v(n.children.slice(c),i,e.extend({},r,{isTableHeader:!1}));a=[o.freshElement("thead",{},s),o.freshElement("tbody",{},d)]}return[o.forceWrite].concat(a)}(n,i,r)})},tableRow:function(e,n,t){var i=v(e.children,n,t);return[o.freshElement("tr",{},[o.forceWrite].concat(i))]},tableCell:function(e,n,t){var i=t.isTableHeader?"th":"td",r=v(e.children,n,t),a={};return 1!==e.colSpan&&(a.colspan=e.colSpan.toString()),1!==e.rowSpan&&(a.rowspan=e.rowSpan.toString()),[o.freshElement(i,a,[o.forceWrite].concat(r))]},break:function(e,n,t){return function(e){var n=F(e);return n?n.to:"line"===e.breakType?i.topLevelElement("br"):i.empty}(e).wrap(function(){return[]})}};return{convertToHtml:function(t){var i=[],a=T(t,i,{}),d=[];h(a,function(e){"deferred"===e.type&&d.push(e)});var u={};return n.mapSeries(d,function(e){return e.value().then(function(n){u[e.id]=n})}).then(function(){var n=c.writer({prettyPrint:s.prettyPrint,outputFormat:s.outputFormat});return o.write(n,o.simplify(function n(t){return l(t,function(t){return"deferred"===t.type?u[t.id]:t.children?[e.extend({},t,{children:n(t.children)})]:[t]})}(a))),new r.Result(n.asString(),i)})}}}Oc.DocumentConverter=function(n){return{convertToHtml:function(i){var r=e.indexBy(i.type===t.types.document?i.comments:[],"commentId");return new s(n,r).convertToHtml(i)}}};var d=1;function u(e,n){return r.warning("Unrecognised "+e+" style: '"+n.styleName+"' (Style ID: "+n.styleId+")")}function l(n,t){return e.flatten(n.map(t),!0)}function h(e,n){e.forEach(function(e){n(e),e.children&&h(e.children,n)})}var p=Oc.commentAuthorLabel=function(e){return e.authorInitials||""};return Oc}var ts,is={};var rs,as,os={},cs={},ss={},ds={exports:{}};function us(){if(as)return ss;as=1;var e=function(){if(rs)return ds.exports;rs=1;var e=ds.exports=function(e,n){this._tokens=e,this._startIndex=n||0};return e.prototype.head=function(){return this._tokens[this._startIndex]},e.prototype.tail=function(n){return new e(this._tokens,this._startIndex+1)},e.prototype.toArray=function(){return this._tokens.slice(this._startIndex)},e.prototype.end=function(){return this._tokens[this._tokens.length-1]},e.prototype.to=function(e){var n=this.head().source,t=e.head()||e.end();return n.to(t.source)},ds.exports}();return ss.Parser=function(n){return{parseTokens:function(n,t){return n(new e(t))}}},ss}var ls,hs,ps,fs={},gs={};function bs(){if(ps)return hs;ps=1,hs={failure:function(n,t){if(n.length<1)throw new Error("Failure must have errors");return new e({status:"failure",remaining:t,errors:n})},error:function(n,t){if(n.length<1)throw new Error("Failure must have errors");return new e({status:"error",remaining:t,errors:n})},success:function(n,t,i){return new e({status:"success",value:n,source:i,remaining:t,errors:[]})},cut:function(n){return new e({status:"cut",remaining:n,errors:[]})}};var e=function(e){this._value=e.value,this._status=e.status,this._hasValue=void 0!==e.value,this._remaining=e.remaining,this._source=e.source,this._errors=e.errors};return e.prototype.map=function(n){return this._hasValue?new e({value:n(this._value,this._source),status:this._status,remaining:this._remaining,source:this._source,errors:this._errors}):this},e.prototype.changeRemaining=function(n){return new e({value:this._value,status:this._status,remaining:n,source:this._source,errors:this._errors})},e.prototype.isSuccess=function(){return"success"===this._status||"cut"===this._status},e.prototype.isFailure=function(){return"failure"===this._status},e.prototype.isError=function(){return"error"===this._status},e.prototype.isCut=function(){return"cut"===this._status},e.prototype.value=function(){return this._value},e.prototype.remaining=function(){return this._remaining},e.prototype.source=function(){return this._source},e.prototype.errors=function(){return this._errors},hs}var ms,ys={};function xs(){if(ms)return ys;ms=1,ys.error=function(n){return new e(n)};var e=function(e){this.expected=e.expected,this.actual=e.actual,this._location=e.location};return e.prototype.describe=function(){return(this._location?this._location.describe()+":\n":"")+"Expected "+this.expected+"\nbut got "+this.actual},e.prototype.lineNumber=function(){return this._location.lineNumber()},e.prototype.characterNumber=function(){return this._location.characterNumber()},ys}var Ds,Us,vs={};function Ts(){return Us||(Us=1,function(e){var n=Ht,t=(ls||(ls=1,function(e){function n(e){return"function"==typeof e?e():e}e.none=Object.create({value:function(){throw new Error("Called value on none")},isNone:function(){return!0},isSome:function(){return!1},map:function(){return e.none},flatMap:function(){return e.none},filter:function(){return e.none},toArray:function(){return[]},orElse:n,valueOrElse:n}),e.some=function(e){return new t(e)};var t=function(e){this._value=e};t.prototype.value=function(){return this._value},t.prototype.isNone=function(){return!1},t.prototype.isSome=function(){return!0},t.prototype.map=function(e){return new t(e(this._value))},t.prototype.flatMap=function(e){return e(this._value)},t.prototype.filter=function(n){return n(this._value)?this:e.none},t.prototype.toArray=function(){return[this._value]},t.prototype.orElse=function(e){return this},t.prototype.valueOrElse=function(e){return this._value},e.isOption=function(n){return n===e.none||n instanceof t},e.fromNullable=function(n){return null==n?e.none:new t(n)}}(gs)),gs),i=bs(),r=xs(),a=function(){if(Ds)return vs;Ds=1,vs.fromArray=function(n){var t=0,i=function(){return t<n.length};return new e({hasNext:i,next:function(){if(i())return n[t++];throw new Error("No more elements")}})};var e=function(e){this._iterator=e};return e.prototype.map=function(n){var t=this._iterator;return new e({hasNext:function(){return t.hasNext()},next:function(){return n(t.next())}})},e.prototype.filter=function(n){var t,i=this._iterator,r=!1,a=!1,o=function(){if(!r)for(r=!0,a=!1;i.hasNext()&&!a;)t=i.next(),a=n(t)};return new e({hasNext:function(){return o(),a},next:function(){return o(),r=!1,t}})},e.prototype.first=function(){var e=this._iterator;return this._iterator.hasNext()?e.next():null},e.prototype.toArray=function(){for(var e=[];this._iterator.hasNext();)e.push(this._iterator.next());return e},vs}();e.token=function(e,n){var t=void 0!==n;return function(r){var a=r.head();return!a||a.name!==e||t&&a.value!==n?l(r,u({name:e,value:n})):i.success(a.value,r.tail(),a.source)}},e.tokenOfType=function(n){return e.token(n)},e.firstOf=function(e,t){return n.isArray(t)||(t=Array.prototype.slice.call(arguments,1)),function(n){return a.fromArray(t).map(function(e){return e(n)}).filter(function(e){return e.isSuccess()||e.isError()}).first()||l(n,e)}},e.then=function(e,n){return function(t){var i=e(t);return i.map||console.log(i),i.map(n)}},e.sequence=function(){var t=Array.prototype.slice.call(arguments,0),r=function(r){var a=n.foldl(t,function(e,n){var t=e.result,a=e.hasCut;if(!t.isSuccess())return{result:t,hasCut:a};var o=n(t.remaining());if(o.isCut())return{result:t,hasCut:!0};if(o.isSuccess()){var c;c=n.isCaptured?t.value().withValue(n,o.value()):t.value();var s=o.remaining(),d=r.to(s);return{result:i.success(c,s,d),hasCut:a}}return a?{result:i.error(o.errors(),o.remaining()),hasCut:a}:{result:o,hasCut:a}},{result:i.success(new o,r),hasCut:!1}).result,c=r.to(a.remaining());return a.map(function(n){return n.withValue(e.sequence.source,c)})};function a(e){return e.isCaptured}return r.head=function(){var i=n.find(t,a);return e.then(r,e.sequence.extract(i))},r.map=function(n){return e.then(r,function(e){return n.apply(this,e.toArray())})},r};var o=function(e,n){this._values=e||{},this._valuesArray=n||[]};o.prototype.withValue=function(e,t){if(e.captureName&&e.captureName in this._values)throw new Error('Cannot add second value for capture "'+e.captureName+'"');var i=n.clone(this._values);i[e.captureName]=t;var r=this._valuesArray.concat([t]);return new o(i,r)},o.prototype.get=function(e){if(e.captureName in this._values)return this._values[e.captureName];throw new Error('No value for capture "'+e.captureName+'"')},o.prototype.toArray=function(){return this._valuesArray},e.sequence.capture=function(e,n){var t=function(){return e.apply(this,arguments)};return t.captureName=n,t.isCaptured=!0,t},e.sequence.extract=function(e){return function(n){return n.get(e)}},e.sequence.applyValues=function(e){var n=Array.prototype.slice.call(arguments,1);return function(t){var i=n.map(function(e){return t.get(e)});return e.apply(this,i)}},e.sequence.source={captureName:"☃source☃"},e.sequence.cut=function(){return function(e){return i.cut(e)}},e.optional=function(e){return function(n){var r=e(n);return r.isSuccess()?r.map(t.some):r.isFailure()?i.success(t.none,n):r}},e.zeroOrMoreWithSeparator=function(e,n){return d(e,n,!1)},e.oneOrMoreWithSeparator=function(e,n){return d(e,n,!0)};var c=e.zeroOrMore=function(e){return function(n){for(var t,r=[];(t=e(n))&&t.isSuccess();)n=t.remaining(),r.push(t.value());return t.isError()?t:i.success(r,n)}};function s(e){return i.success(null,e)}e.oneOrMore=function(n){return e.oneOrMoreWithSeparator(n,s)};var d=function(n,t,r){return function(a){var o=n(a);if(o.isSuccess()){var s=e.sequence.capture(n,"main"),d=c(e.then(e.sequence(t,s),e.sequence.extract(s)))(o.remaining());return i.success([o.value()].concat(d.value()),d.remaining())}return r||o.isError()?o:i.success([],a)}};e.leftAssociative=function(n,t,r){var a;a=(a=r?[{func:r,rule:t}]:t).map(function(n){return e.then(n.rule,function(e){return function(t,i){return n.func(t,e,i)}})});var o=e.firstOf.apply(null,["rules"].concat(a));return function(e){var t=e,r=n(e);if(!r.isSuccess())return r;for(var a=o(r.remaining());a.isSuccess();){var c=a.remaining(),s=t.to(a.remaining()),d=a.value();r=i.success(d(r.value(),s),c,s),a=o(r.remaining())}return a.isError()?a:r}},e.leftAssociative.firstOf=function(){return Array.prototype.slice.call(arguments,0)},e.nonConsuming=function(e){return function(n){return e(n).changeRemaining(n)}};var u=function(e){return e.value?e.name+' "'+e.value+'"':e.name};function l(e,n){var t,a=e.head();return t=a?r.error({expected:n,actual:u(a),location:a.source}):r.error({expected:n,actual:"end of tokens"}),i.failure([t],e)}}(fs)),fs}var _s,ws,Fs,Es={exports:{}};function Ws(){if(_s)return Es.exports;_s=1,Es.exports=function(n,t){return{asString:function(){return n},range:function(i,r){return new e(n,t,i,r)}}};var e=function(e,n,t,i){this._string=e,this._description=n,this._startIndex=t,this._endIndex=i};return e.prototype.to=function(n){return new e(this._string,this._description,this._startIndex,n._endIndex)},e.prototype.describe=function(){var e=this._position();return(this._description?this._description+"\n":"")+"Line number: "+e.lineNumber+"\nCharacter number: "+e.characterNumber},e.prototype.lineNumber=function(){return this._position().lineNumber},e.prototype.characterNumber=function(){return this._position().characterNumber},e.prototype._position=function(){for(var e=this,n=0,t=function(){return e._string.indexOf("\n",n)},i=1;-1!==t()&&t()<this._startIndex;)n=t()+1,i+=1;return{lineNumber:i,characterNumber:this._startIndex-n+1}},Es.exports}function Cs(){return Fs?ws:(Fs=1,ws=function(e,n,t){this.name=e,this.value=n,t&&(this.source=t)})}var As,Ss={};function Ns(){return As||(As=1,function(e){var n=Ts(),t=bs();function i(e){function r(){return e.map(function(e){return e.name})}function a(t){return n.firstOf("infix",e.map(function(e){return e.rule}))(t)}return{apply:function(e){for(var n,i;;){if(!(n=a(e.remaining())).isSuccess())return n.isFailure()?e:n;i=e.source().to(n.source()),e=t.success(n.value()(e.value(),i),n.remaining(),i)}},untilExclusive:function(n){return new i(e.slice(0,r().indexOf(n)))},untilInclusive:function(n){return new i(e.slice(0,r().indexOf(n)+1))}}}e.parser=function(e,t,a){var o={rule:function(){return d(c)},leftAssociative:function(e){return d(c.untilExclusive(e))},rightAssociative:function(e){return d(c.untilInclusive(e))}},c=new i(a.map(function(e){return{name:e.name,rule:r(e.ruleBuilder.bind(null,o))}})),s=n.firstOf(e,t);function d(e){return u.bind(null,e)}function u(e,n){var t=s(n);return t.isSuccess()?e.apply(t):t}return o},e.infix=function(n,t){return{name:n,ruleBuilder:t,map:function(i){return e.infix(n,function(e){var n=t(e);return function(e){return n(e).map(function(e){return function(n,t){return i(n,e,t)}})}})}}};var r=function(e){var n;return function(t){return n||(n=e()),n(t)}}}(Ss)),Ss}var Bs,ks,Os={};function Is(){return ks||(ks=1,cs.Parser=us().Parser,cs.rules=Ts(),cs.errors=xs(),cs.results=bs(),cs.StringSource=Ws(),cs.Token=Cs(),cs.bottomUp=Ns(),cs.RegexTokeniser=function(){if(Bs)return Os;Bs=1;var e=Cs(),n=Ws();return Os.RegexTokeniser=function(t){function i(n,i,r){for(var a=0;a<t.length;a++){var o=t[a].regex;o.lastIndex=i;var c=o.exec(n);if(c){var s=i+c[0].length;if(c.index===i&&s>i){var d=c[1];return{token:new e(t[a].name,d,r.range(i,s)),endIndex:s}}}}return s=i+1,{token:new e("unrecognisedCharacter",n.substring(i,s),r.range(i,s)),endIndex:s}}function r(n,t){return new e("end",null,t.range(n.length,n.length))}return t=t.map(function(e){return{name:e.name,regex:new RegExp(e.regex.source,"g")}}),{tokenise:function(e,t){for(var a=new n(e,t),o=0,c=[];o<e.length;){var s=i(e,o,a);o=s.endIndex,c.push(s.token)}return c.push(r(e,a)),c}}},Os}().RegexTokeniser,cs.rule=function(e){var n;return function(t){return n||(n=e()),n(t)}}),cs}var Rs,js={};var Ps,Ls,qs={};function Ms(){if(Ls)return os;Ls=1;var e=Ht,n=Is(),t=function(){if(Rs)return js;function e(e,n){n=n||{},this._elementType=e,this._styleId=n.styleId,this._styleName=n.styleName,n.list&&(this._listIndex=n.list.levelIndex,this._listIsOrdered=n.list.isOrdered)}function n(e){e=e||{},this._color=e.color}function t(e){e=e||{},this._breakType=e.breakType}function i(e,n){return e.toUpperCase()===n.toUpperCase()}function r(e,n){return 0===n.toUpperCase().indexOf(e.toUpperCase())}return Rs=1,js.paragraph=function(n){return new e("paragraph",n)},js.run=function(n){return new e("run",n)},js.table=function(n){return new e("table",n)},js.bold=new e("bold"),js.italic=new e("italic"),js.underline=new e("underline"),js.strikethrough=new e("strikethrough"),js.allCaps=new e("allCaps"),js.smallCaps=new e("smallCaps"),js.highlight=function(e){return new n(e)},js.commentReference=new e("commentReference"),js.lineBreak=new t({breakType:"line"}),js.pageBreak=new t({breakType:"page"}),js.columnBreak=new t({breakType:"column"}),js.equalTo=function(e){return{operator:i,operand:e}},js.startsWith=function(e){return{operator:r,operand:e}},e.prototype.matches=function(e){return e.type===this._elementType&&(void 0===this._styleId||e.styleId===this._styleId)&&(void 0===this._styleName||e.styleName&&this._styleName.operator(this._styleName.operand,e.styleName))&&(void 0===this._listIndex||function(e,n,t){return e.numbering&&e.numbering.level==n&&e.numbering.isOrdered==t}(e,this._listIndex,this._listIsOrdered))&&(void 0===this._breakType||this._breakType===e.breakType)},n.prototype.matches=function(e){return"highlight"===e.type&&(void 0===this._color||e.color===this._color)},t.prototype.matches=function(e){return"break"===e.type&&(void 0===this._breakType||e.breakType===this._breakType)},js}(),i=Mc(),r=function(){if(Ps)return qs;Ps=1;var e=Is().RegexTokeniser;qs.tokenise=function(t){var i="(?:[a-zA-Z\\-_]|\\\\.)";return new e([{name:"identifier",regex:new RegExp("("+i+"(?:"+i+"|[0-9])*)")},{name:"dot",regex:/\./},{name:"colon",regex:/:/},{name:"gt",regex:/>/},{name:"whitespace",regex:/\s+/},{name:"arrow",regex:/=>/},{name:"equals",regex:/=/},{name:"startsWith",regex:/\^=/},{name:"open-paren",regex:/\(/},{name:"close-paren",regex:/\)/},{name:"open-square-bracket",regex:/\[/},{name:"close-square-bracket",regex:/\]/},{name:"string",regex:new RegExp(n+"'")},{name:"unterminated-string",regex:new RegExp(n)},{name:"integer",regex:/([0-9]+)/},{name:"choice",regex:/\|/},{name:"bang",regex:/(!)/}]).tokenise(t)};var n="'((?:\\\\.|[^'])*)";return qs}().tokenise,a=Sr();function o(){var i=n.rules.sequence,r=function(e,t){return n.rules.then(n.rules.token("identifier",e),function(){return t})},a=r("p",t.paragraph),o=r("r",t.run),c=n.rules.firstOf("p or r or table",a,o),l=n.rules.sequence(n.rules.tokenOfType("dot"),n.rules.sequence.cut(),n.rules.sequence.capture(s)).map(function(e){return{styleId:e}}),h=n.rules.firstOf("style name matcher",n.rules.then(n.rules.sequence(n.rules.tokenOfType("equals"),n.rules.sequence.cut(),n.rules.sequence.capture(u)).head(),function(e){return{styleName:t.equalTo(e)}}),n.rules.then(n.rules.sequence(n.rules.tokenOfType("startsWith"),n.rules.sequence.cut(),n.rules.sequence.capture(u)).head(),function(e){return{styleName:t.startsWith(e)}})),p=n.rules.sequence(n.rules.tokenOfType("open-square-bracket"),n.rules.sequence.cut(),n.rules.token("identifier","style-name"),n.rules.sequence.capture(h),n.rules.tokenOfType("close-square-bracket")).head(),f=n.rules.firstOf("list type",r("ordered-list",{isOrdered:!0}),r("unordered-list",{isOrdered:!1})),g=i(n.rules.tokenOfType("colon"),i.capture(f),i.cut(),n.rules.tokenOfType("open-paren"),i.capture(d),n.rules.tokenOfType("close-paren")).map(function(e,n){return{list:{isOrdered:e.isOrdered,levelIndex:n-1}}});function b(t){var i=n.rules.firstOf.apply(n.rules.firstOf,["matcher suffix"].concat(t)),r=n.rules.zeroOrMore(i);return n.rules.then(r,function(n){var t={};return n.forEach(function(n){e.extend(t,n)}),t})}var m=i(i.capture(c),i.capture(b([l,p,g]))).map(function(e,n){return e(n)}),y=i(n.rules.token("identifier","table"),i.capture(b([l,p]))).map(function(e){return t.table(e)}),x=r("b",t.bold),D=r("i",t.italic),U=r("u",t.underline),v=r("strike",t.strikethrough),T=r("all-caps",t.allCaps),_=r("small-caps",t.smallCaps),w=i(n.rules.token("identifier","highlight"),n.rules.sequence.capture(n.rules.optional(n.rules.sequence(n.rules.tokenOfType("open-square-bracket"),n.rules.sequence.cut(),n.rules.token("identifier","color"),n.rules.tokenOfType("equals"),n.rules.sequence.capture(u),n.rules.tokenOfType("close-square-bracket")).head()))).map(function(e){return t.highlight({color:e.valueOrElse(void 0)})}),F=r("comment-reference",t.commentReference),E=i(n.rules.token("identifier","br"),i.cut(),n.rules.tokenOfType("open-square-bracket"),n.rules.token("identifier","type"),n.rules.tokenOfType("equals"),i.capture(u),n.rules.tokenOfType("close-square-bracket")).map(function(e){switch(e){case"line":return t.lineBreak;case"page":return t.pageBreak;case"column":return t.columnBreak}});return n.rules.firstOf("element type",m,y,x,D,U,v,T,_,w,F,E)}function c(){var e=n.rules.sequence.capture,t=n.rules.tokenOfType("whitespace"),r=n.rules.then(n.rules.optional(n.rules.sequence(n.rules.tokenOfType("colon"),n.rules.token("identifier","fresh"))),function(e){return e.map(function(){return!0}).valueOrElse(!1)}),a=n.rules.then(n.rules.optional(n.rules.sequence(n.rules.tokenOfType("colon"),n.rules.token("identifier","separator"),n.rules.tokenOfType("open-paren"),e(u),n.rules.tokenOfType("close-paren")).head()),function(e){return e.valueOrElse("")}),o=n.rules.oneOrMoreWithSeparator(s,n.rules.tokenOfType("choice")),c=n.rules.sequence(e(o),e(n.rules.zeroOrMore(g)),e(r),e(a)).map(function(e,n,t,r){var a={},o={};return n.forEach(function(e){e.append&&a[e.name]?a[e.name]+=" "+e.value:a[e.name]=e.value}),t&&(o.fresh=!0),r&&(o.separator=r),i.element(e,a,o)});return n.rules.firstOf("html path",n.rules.then(n.rules.tokenOfType("bang"),function(){return i.ignore}),n.rules.then(n.rules.zeroOrMoreWithSeparator(c,n.rules.sequence(t,n.rules.tokenOfType("gt"),t)),i.elements))}os.readHtmlPath=function(e){return b(c(),e)},os.readDocumentMatcher=function(e){return b(o(),e)},os.readStyle=function(e){return b(x,e)};var s=n.rules.then(n.rules.tokenOfType("identifier"),h),d=n.rules.tokenOfType("integer"),u=n.rules.then(n.rules.tokenOfType("string"),h),l={n:"\n",r:"\r",t:"\t"};function h(e){return e.replace(/\\(.)/g,function(e,n){return l[n]||n})}var p=n.rules.sequence(n.rules.tokenOfType("open-square-bracket"),n.rules.sequence.cut(),n.rules.sequence.capture(s),n.rules.tokenOfType("equals"),n.rules.sequence.capture(u),n.rules.tokenOfType("close-square-bracket")).map(function(e,n){return{name:e,value:n,append:!1}}),f=n.rules.sequence(n.rules.tokenOfType("dot"),n.rules.sequence.cut(),n.rules.sequence.capture(s)).map(function(e){return{name:"class",value:e,append:!0}}),g=n.rules.firstOf("attribute or class",p,f);function b(e,t){var i=r(t),o=n.Parser().parseTokens(e,i);return o.isSuccess()?a.success(o.value()):new a.Result(null,[a.warning(m(t,o))])}function m(e,n){return"Did not understand this style mapping, so ignored it: "+e+"\n"+n.errors().map(y).join("\n")}function y(e){return"Error was at character number "+e.characterNumber()+": Expected "+e.expected+" but got "+e.actual}var x=n.rules.sequence(n.rules.sequence.capture(o()),n.rules.tokenOfType("whitespace"),n.rules.tokenOfType("arrow"),n.rules.sequence.capture(n.rules.optional(n.rules.sequence(n.rules.tokenOfType("whitespace"),n.rules.sequence.capture(c())).head())),n.rules.tokenOfType("end")).map(function(e,n){return{from:e,to:n.valueOrElse(i.empty)}});return os}var Vs,Hs={};var zs,Gs={};var Xs,$s={};function Ks(){if(Xs)return $s;Xs=1;var e=Ht;function n(e,n){return t(function(t){return t.type===e?n(t):t})}function t(n){return function t(i){if(i.children){var r=e.map(i.children,t);i=e.extend(i,{children:r})}return n(i)}}function i(e){var n=[];return r(e,function(e){n.push(e)}),n}function r(e,n){e.children&&e.children.forEach(function(e){r(e,n),n(e)})}return $s.paragraph=function(e){return n("paragraph",e)},$s.run=function(e){return n("run",e)},$s._elements=t,$s.getDescendantsOfType=function(e,n){return i(e).filter(function(e){return e.type===n})},$s.getDescendants=i,$s}var Qs,Ys,Zs={};var Js=function(){if(Ys)return o;Ys=1;var e=Ht,n=Fc(),t=Cc(),i=ns().DocumentConverter,r=function(){if(ts)return is;ts=1;var e=Wr();return is.convertElementToRawText=function n(t){if("text"===t.type)return t.value;if(t.type===e.types.tab)return"\t";var i="paragraph"===t.type?"\n\n":"";return(t.children||[]).map(n).join("")+i},is}().convertElementToRawText,a=Ms().readStyle,c=function(){if(Vs)return Hs;Vs=1,Hs.readOptions=function(r){return r=r||{},e.extend({},t,r,{customStyleMap:i(r.styleMap),readStyleMap:function(){var e=this.customStyleMap;return this.includeEmbeddedStyleMap&&(e=e.concat(i(this.embeddedStyleMap))),this.includeDefaultStyleMap&&(e=e.concat(n)),e}})};var e=Ht,n=Hs._defaultStyleMap=["p.Heading1 => h1:fresh","p.Heading2 => h2:fresh","p.Heading3 => h3:fresh","p.Heading4 => h4:fresh","p.Heading5 => h5:fresh","p.Heading6 => h6:fresh","p[style-name='Heading 1'] => h1:fresh","p[style-name='Heading 2'] => h2:fresh","p[style-name='Heading 3'] => h3:fresh","p[style-name='Heading 4'] => h4:fresh","p[style-name='Heading 5'] => h5:fresh","p[style-name='Heading 6'] => h6:fresh","p[style-name='heading 1'] => h1:fresh","p[style-name='heading 2'] => h2:fresh","p[style-name='heading 3'] => h3:fresh","p[style-name='heading 4'] => h4:fresh","p[style-name='heading 5'] => h5:fresh","p[style-name='heading 6'] => h6:fresh","r[style-name='Strong'] => strong","p[style-name='footnote text'] => p:fresh","r[style-name='footnote reference'] =>","p[style-name='endnote text'] => p:fresh","r[style-name='endnote reference'] =>","p[style-name='annotation text'] => p:fresh","r[style-name='annotation reference'] =>","p[style-name='Footnote'] => p:fresh","r[style-name='Footnote anchor'] =>","p[style-name='Endnote'] => p:fresh","r[style-name='Endnote anchor'] =>","p:unordered-list(1) => ul > li:fresh","p:unordered-list(2) => ul|ol > li > ul > li:fresh","p:unordered-list(3) => ul|ol > li > ul|ol > li > ul > li:fresh","p:unordered-list(4) => ul|ol > li > ul|ol > li > ul|ol > li > ul > li:fresh","p:unordered-list(5) => ul|ol > li > ul|ol > li > ul|ol > li > ul|ol > li > ul > li:fresh","p:ordered-list(1) => ol > li:fresh","p:ordered-list(2) => ul|ol > li > ol > li:fresh","p:ordered-list(3) => ul|ol > li > ul|ol > li > ol > li:fresh","p:ordered-list(4) => ul|ol > li > ul|ol > li > ul|ol > li > ol > li:fresh","p:ordered-list(5) => ul|ol > li > ul|ol > li > ul|ol > li > ul|ol > li > ol > li:fresh","r[style-name='Hyperlink'] =>","p[style-name='Normal'] => p:fresh"],t=Hs._standardOptions={transformDocument:function(e){return e},includeDefaultStyleMap:!0,includeEmbeddedStyleMap:!0};function i(n){return n?e.isString(n)?n.split("\n").map(function(e){return e.trim()}).filter(function(e){return""!==e&&"#"!==e.charAt(0)}):n:[]}return Hs}().readOptions,s=function(){if(zs)return Gs;zs=1;var e=wr(),n=Ir();return Gs.openZip=function(t){return t.arrayBuffer?e.resolve(n.openArrayBuffer(t.arrayBuffer)):e.reject(new Error("Could not find file in options"))},Gs}(),d=Sr().Result;function u(r,o){return o=c(o),s.openZip(r).tap(function(e){return t.readStyleMap(e).then(function(e){o.embeddedStyleMap=e})}).then(function(t){return n.read(t,r).then(function(e){return e.map(o.transformDocument)}).then(function(n){return function(n,t){var r=(s=t.readStyleMap(),d.combine((s||[]).map(a)).map(function(e){return e.filter(function(e){return!!e})})),o=e.extend({},t,{styleMap:r.value}),c=new i(o);var s;return n.flatMapThen(function(e){return r.flatMapThen(function(n){return c.convertToHtml(e)})})}(n,o)})})}return o.convertToHtml=function(e,n){return u(e,n)},o.convertToMarkdown=function(e,n){var t=Object.create(n||{});return t.outputFormat="markdown",u(e,t)},o.convert=u,o.extractRawText=function(e){return s.openZip(e).then(n.read).then(function(e){return e.map(r)})},o.images=zc(),o.transforms=Ks(),o.underline=function(){if(Qs)return Zs;Qs=1;var e=Mc(),n=qc();return Zs.element=function(t){return function(i){return n.elementWithTag(e.element(t),[i])}},Zs}(),o.embedStyleMap=function(e,n){return s.openZip(e).tap(function(e){return t.writeStyleMap(e,n)}).then(function(e){return e.toArrayBuffer()}).then(function(e){return{toArrayBuffer:function(){return e},toBuffer:function(){return Buffer.from(e)}}})},o.readEmbeddedStyleMap=function(e){return s.openZip(e).then(t.readStyleMap)},o.styleMapping=function(){throw new Error("Use a raw string instead of mammoth.styleMapping e.g. \"p[style-name='Title'] => h1\" instead of mammoth.styleMapping(\"p[style-name='Title'] => h1\")")},o}();const ed=i(Js),nd=a({__proto__:null,default:ed},[Js]),td=class e{static getInstance(){return e.instance||(e.instance=new e),e.instance}async analyzeDocument(e){try{console.log("开始高级文档分析:",e.name);const n=await this.fileToArrayBuffer(e),t=await ed.convertToHtml({arrayBuffer:n}),i=await ed.extractRawText({arrayBuffer:n}),r={content:i.value,htmlContent:t.value,styles:this.extractStyles(t.value),paragraphs:this.analyzeParagraphs(t.value),tables:this.analyzeTables(t.value),variables:this.detectVariables(i.value),metadata:await this.extractMetadata(n),formatInfo:{hasHeaders:this.hasHeaders(t.value),hasFooters:this.hasFooters(t.value),pageLayout:{orientation:"portrait"}}};return console.log("文档分析完成:",{contentLength:r.content.length,variablesFound:r.variables.length,tablesFound:r.tables.length,stylesFound:r.styles.length}),r}catch(n){throw console.error("高级文档分析失败:",n),new Error(`文档分析失败: ${n.message}`)}}fileToArrayBuffer(e){return new Promise((n,t)=>{const i=new FileReader;i.onload=e=>{var i;(null==(i=e.target)?void 0:i.result)instanceof ArrayBuffer?n(e.target.result):t(new Error("文件读取失败"))},i.onerror=()=>t(new Error("文件读取错误")),i.readAsArrayBuffer(e)})}extractStyles(e){const n=[],t=/style="([^"]+)"/g;let i;for(;null!==(i=t.exec(e));){const e=i[1],t={},r=e.match(/font-family:\s*([^;]+)/);r&&(t.fontFamily=r[1].trim());const a=e.match(/font-size:\s*(\d+)px/);a&&(t.fontSize=parseInt(a[1])),(e.includes("font-weight: bold")||e.includes("font-weight: 700"))&&(t.bold=!0),e.includes("font-style: italic")&&(t.italic=!0),e.includes("text-decoration: underline")&&(t.underline=!0);const o=e.match(/color:\s*([^;]+)/);o&&(t.color=o[1].trim()),n.push(t)}return n}analyzeParagraphs(e){const n=[],t=/<p[^>]*style="([^"]*)"[^>]*>/g;let i;for(;null!==(i=t.exec(e));){const e=i[1],t={},r=e.match(/text-align:\s*([^;]+)/);if(r){const e=r[1].trim();["left","center","right","justify"].includes(e)&&(t.alignment=e)}const a=e.match(/line-height:\s*([^;]+)/);if(a){const e=parseFloat(a[1]);isNaN(e)||(t.lineSpacing=e)}n.push(t)}return n}analyzeTables(e){const n=[],t=/<table[^>]*>([\s\S]*?)<\/table>/g;let i;for(;null!==(i=t.exec(e));){const e=i[1],t=(e.match(/<tr[^>]*>/g)||[]).length,r=e.match(/<tr[^>]*>([\s\S]*?)<\/tr>/);let a=0;r&&(a=(r[1].match(/<td[^>]*>|<th[^>]*>/g)||[]).length);const o=i[0].includes("border")||e.includes("border");n.push({rows:t,columns:a,hasBorder:o,borderStyle:o?"solid":"none"})}return n}detectVariables(e){const n=[];return[/\[([^\]]+)\]/g,/\{\{([^}]+)\}\}/g,/\{([^}]+)\}/g,/___+/g,/\s{3,}/g].forEach((t,i)=>{let r;for(;null!==(r=t.exec(e));){const t=r[0],i=r[1]||t,a=Math.max(0,r.index-20),o=Math.min(e.length,r.index+t.length+20),c=e.substring(a,o),s=this.suggestVariableMapping(i);n.push({text:t,position:r.index,context:c,suggestedMapping:s,confidence:this.calculateMappingConfidence(i,s)})}}),n}suggestVariableMapping(e){const n={"学生姓名":"studentName","姓名":"studentName","学号":"studentId","班级":"class","专业":"major","学院":"college","性别":"gender","入学时间":"enrollmentDate","入学日期":"enrollmentDate","学制":"duration","学历层次":"educationLevel","当前日期":"currentDate","开具日期":"issueDate","学校名称":"schoolName"};if(n[e])return n[e];for(const[t,i]of Object.entries(n))if(e.includes(t)||t.includes(e))return i;return e.toLowerCase().replace(/\s+/g,"")}calculateMappingConfidence(e,n){if(!e||!n)return 0;if(["学生姓名","学号","班级","专业","学院"].includes(e))return.95;const t=["姓名","学号","班级","专业","日期","时间"];for(const i of t)if(e.includes(i))return.8;return.5}async extractMetadata(e){return{title:"在读证明模板",author:"智慧学工系统",createdDate:(new Date).toISOString()}}hasHeaders(e){return e.includes("header")||e.includes("页眉")}hasFooters(e){return e.includes("footer")||e.includes("页脚")}};var id,rd;((n,t,i)=>{t in n?e(n,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):n[t]=i})(td,"symbol"!=typeof(id="instance")?id+"":id,rd);let ad=td;export{ad as A,nd as i};
