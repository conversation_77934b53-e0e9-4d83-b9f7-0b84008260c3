import{k as e,j as t,l as r,m as n,n as i,o as a,p as o,q as l,s as c,P as s}from"./index-DXaqwR6F-1753160720439.js";import{r as u,F as f,N as d,M as h,O as p,Y as y,Q as v,aa as g,aR as m,G as b,H as x,J as w,g as O,i as j,l as A,j as P,ag as S,aE as E,a0 as k,ae as M,a as T,X as C,as as D,aH as N}from"./antd-lXsGnH6e-1753160720439.js";import{g as I,r as _}from"./vendor-D2RBMdQ0-1753160720439.js";var R=e=>Array.isArray(e)?e:[e];function L(e){const t=Array.isArray(e[0])?e[0]:e;return function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){const r=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw new TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}var z="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function B(){return{s:0,v:void 0,o:null,p:null}}function K(e,t={}){let r={s:0,v:void 0,o:null,p:null};const{resultEqualityCheck:n}=t;let i,a=0;function o(){var t;let o=r;const{length:l}=arguments;for(let e=0,r=l;e<r;e++){const t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=o.o;null===e&&(o.o=e=new WeakMap);const r=e.get(t);void 0===r?(o=B(),e.set(t,o)):o=r}else{let e=o.p;null===e&&(o.p=e=new Map);const r=e.get(t);void 0===r?(o=B(),e.set(t,o)):o=r}}const c=o;let s;if(1===o.s)s=o.v;else if(s=e.apply(null,arguments),a++,n){const e=(null==(t=null==i?void 0:i.deref)?void 0:t.call(i))??i;null!=e&&n(e,s)&&(s=e,0!==a&&a--);i="object"==typeof s&&null!==s||"function"==typeof s?new z(s):s}return c.s=1,c.v=s,s}return o.clearCache=()=>{r={s:0,v:void 0,o:null,p:null},o.resetResultsCount()},o.resultsCount=()=>a,o.resetResultsCount=()=>{a=0},o}function $(e,...t){const r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,n=(...e)=>{let t,n=0,i=0,a={},o=e.pop();"object"==typeof o&&(a=o,o=e.pop()),function(e,t="expected a function, instead received "+typeof e){if("function"!=typeof e)throw new TypeError(t)}(o,`createSelector expects an output function after the inputs, but received: [${typeof o}]`);const l={...r,...a},{memoize:c,memoizeOptions:s=[],argsMemoize:u=K,argsMemoizeOptions:f=[]}=l,d=R(s),h=R(f),p=L(e),y=c(function(){return n++,o.apply(null,arguments)},...d),v=u(function(){i++;const e=function(e,t){const r=[],{length:n}=e;for(let i=0;i<n;i++)r.push(e[i].apply(null,t));return r}(p,arguments);return t=y.apply(null,e),t},...h);return Object.assign(v,{resultFunc:o,memoizedResultFunc:y,dependencies:p,dependencyRecomputations:()=>i,resetDependencyRecomputations:()=>{i=0},lastResult:()=>t,recomputations:()=>n,resetRecomputations:()=>{n=0},memoize:c,argsMemoize:u})};return Object.assign(n,{withTypes:()=>n}),n}var U=$(K),F=Object.assign((e,t=U)=>{!function(e,t="expected an object, instead received "+typeof e){if("object"!=typeof e)throw new TypeError(t)}(e,"createStructuredSelector expects first argument to be an object where each property is a selector, instead received a "+typeof e);const r=Object.keys(e);return t(r.map(t=>e[t]),(...e)=>e.reduce((e,t,n)=>(e[r[n]]=t,e),{}))},{withTypes:()=>F});class W{static async getAnalyticsData(){try{console.log("📊 开始从数据库获取统计数据...");const t=await e.get("/analytics/dashboard");if(t.data.success)return console.log("✅ 数据库统计数据获取成功:",t.data.data),t.data.data;throw new Error(t.data.message||"获取统计数据失败")}catch(t){console.error("❌ 获取数据库统计数据失败:",t);const e={totalStudents:0,totalClasses:0,totalCourses:0,totalDormitories:0,genderDistribution:{male:0,female:0},gradeDistribution:{},majorDistribution:[],collegeDistribution:[],dormitoryOccupancy:0,averageGPA:0,passRate:0,rewardCount:0,punishmentCount:0,financialAidCount:0,partyMemberCount:0,mentalHealthRecords:0,certificateApplications:0,academicWarnings:0,pendingApplications:0,gradesTrend:[],dormitoryTrend:[],totalTeachers:0,totalTeachingCourses:0,totalTeachingMaterials:0,totalTeachingAchievements:0,totalTrainingRecords:0,totalMentorshipProjects:0,totalAcademicRecords:0};return console.log("返回默认空数据结构"),e}}static async getStudentStats(){try{return(await e.get("/analytics/students")).data}catch(t){throw console.error("获取学生统计数据失败:",t),t}}static async getClassStats(){try{return(await e.get("/analytics/classes")).data}catch(t){throw console.error("获取班级统计数据失败:",t),t}}static async getDormitoryStats(){try{return(await e.get("/analytics/dormitories")).data}catch(t){throw console.error("获取宿舍统计数据失败:",t),t}}static async getStudentAffairsStats(){try{return(await e.get("/analytics/student-affairs")).data}catch(t){throw console.error("获取学工管理统计数据失败:",t),t}}static async getTeacherStats(){try{return(await e.get("/analytics/teachers")).data}catch(t){throw console.error("获取教师统计数据失败:",t),t}}static async getTrendData(t,r="6months"){try{return(await e.get(`/analytics/trends/${t}`,{params:{period:r}})).data}catch(n){throw console.error(`获取${t}趋势数据失败:`,n),n}}static async refreshCache(){try{return(await e.post("/analytics/refresh-cache")).data}catch(t){throw console.error("刷新统计数据缓存失败:",t),t}}}const H=({warningCount:e,warningDetails:r})=>{const[n,i]=u.useState(!1);return t.jsxs(t.Fragment,{children:[t.jsxs(f,{className:"text-center cursor-pointer hover:shadow-lg transition-shadow",onClick:()=>{i(!0)},children:[t.jsxs("div",{className:"flex items-center justify-center mb-2",children:[t.jsx(d,{style:{fontSize:24,color:"#ff4d4f",marginRight:8}}),t.jsx("span",{className:"text-lg font-semibold",children:"学业预警"})]}),t.jsx("div",{className:"text-3xl font-bold text-red-500 mb-2",children:e}),t.jsx("div",{className:"text-sm text-gray-500 mb-3",children:"需要关注的学生"}),t.jsxs("div",{className:"flex justify-center space-x-4 text-xs",children:[t.jsx("div",{children:t.jsxs("span",{className:"text-orange-500",children:["预警: ",r.warning]})}),t.jsx("div",{children:t.jsxs("span",{className:"text-red-500",children:["严重: ",r.serious]})})]})]}),t.jsxs(h,{title:t.jsxs("div",{className:"flex items-center",children:[t.jsx(g,{style:{color:"#ff4d4f",marginRight:8}}),"学业预警统计详情"]}),open:n,onCancel:()=>i(!1),width:600,footer:null,children:[t.jsx(p,{message:"学业预警说明",description:"根据学业情况导入的数据统计，包含挂科课程信息和预警状态。请及时关注预警学生的学习情况。",type:"info",showIcon:!0,style:{marginBottom:24}}),t.jsxs("div",{className:"space-y-6",children:[t.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[t.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[t.jsx(y,{className:"mr-2"}),"总体统计"]}),t.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[t.jsxs("div",{className:"text-center",children:[t.jsx("div",{className:"text-2xl font-bold text-green-500",children:r.normal}),t.jsx("div",{className:"text-sm text-gray-600",children:"正常"})]}),t.jsxs("div",{className:"text-center",children:[t.jsx("div",{className:"text-2xl font-bold text-orange-500",children:r.warning}),t.jsx("div",{className:"text-sm text-gray-600",children:"预警"})]}),t.jsxs("div",{className:"text-center",children:[t.jsx("div",{className:"text-2xl font-bold text-red-500",children:r.serious}),t.jsx("div",{className:"text-sm text-gray-600",children:"严重"})]})]})]}),t.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[t.jsx("h4",{className:"font-semibold mb-2 text-blue-800",children:"预警等级说明"}),t.jsxs("div",{className:"space-y-2 text-sm",children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(v,{color:"green",className:"mr-2",children:"正常"}),t.jsx("span",{children:"学业情况良好，无挂科记录"})]}),t.jsxs("div",{className:"flex items-center",children:[t.jsx(v,{color:"orange",className:"mr-2",children:"预警"}),t.jsx("span",{children:"存在挂科课程，需要关注学习情况"})]}),t.jsxs("div",{className:"flex items-center",children:[t.jsx(v,{color:"red",className:"mr-2",children:"严重"}),t.jsx("span",{children:"挂科课程较多，需要重点关注和帮助"})]})]})]}),t.jsxs("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[t.jsx("h4",{className:"font-semibold mb-2 text-yellow-800",children:"处理建议"}),t.jsxs("ul",{className:"text-sm space-y-1 text-yellow-700",children:[t.jsx("li",{children:"• 及时联系预警学生，了解学习困难"}),t.jsx("li",{children:"• 协助学生制定学习计划和重修安排"}),t.jsx("li",{children:"• 关注学生心理状态，提供必要支持"}),t.jsx("li",{children:"• 定期跟踪学生学习进展情况"})]})]})]})]})]})};function V(e){var t,r,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(r=V(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function q(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=V(e))&&(n&&(n+=" "),n+=t);return n}var Y,G={},X={};function Z(){return Y||(Y=1,e=X,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isUnsafeProperty=function(e){return"__proto__"===e}),X;var e}var J,Q={};function ee(){return J||(J=1,e=Q,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isDeepKey=function(e){switch(typeof e){case"number":case"symbol":return!1;case"string":return e.includes(".")||e.includes("[")||e.includes("]")}}),Q;var e}var te,re={};function ne(){return te||(te=1,e=re,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.toKey=function(e){var t;return"string"==typeof e||"symbol"==typeof e?e:Object.is(null==(t=null==e?void 0:e.valueOf)?void 0:t.call(e),-0)?"-0":String(e)}),re;var e}var ie,ae,oe,le,ce={};function se(){return ie||(ie=1,e=ce,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.toPath=function(e){const t=[],r=e.length;if(0===r)return t;let n=0,i="",a="",o=!1;for(46===e.charCodeAt(0)&&(t.push(""),n++);n<r;){const l=e[n];a?"\\"===l&&n+1<r?(n++,i+=e[n]):l===a?a="":i+=l:o?'"'===l||"'"===l?a=l:"]"===l?(o=!1,t.push(i),i=""):i+=l:"["===l?(o=!0,i&&(t.push(i),i="")):"."===l?i&&(t.push(i),i=""):i+=l,n++}return i&&t.push(i),t}),ce;var e}function ue(){return ae||(ae=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Z(),r=ee(),n=ne(),i=se();e.get=function e(a,o,l){if(null==a)return l;switch(typeof o){case"string":{if(t.isUnsafeProperty(o))return l;const n=a[o];return void 0===n?r.isDeepKey(o)?e(a,i.toPath(o),l):l:n}case"number":case"symbol":{"number"==typeof o&&(o=n.toKey(o));const e=a[o];return void 0===e?l:e}default:{if(Array.isArray(o))return function(e,r,n){if(0===r.length)return n;let i=e;for(let a=0;a<r.length;a++){if(null==i)return n;if(t.isUnsafeProperty(r[a]))return n;i=i[r[a]]}if(void 0===i)return n;return i}(a,o,l);if(o=Object.is(null==o?void 0:o.valueOf(),-0)?"-0":String(o),t.isUnsafeProperty(o))return l;const e=a[o];return void 0===e?l:e}}}}(G)),G}function fe(){return le?oe:(le=1,oe=ue().get)}const de=I(fe());var he,pe,ye={exports:{}},ve={};var ge=(pe||(pe=1,ye.exports=function(){if(he)return ve;he=1;var e="function"==typeof Symbol&&Symbol.for,t=e?Symbol.for("react.element"):60103,r=e?Symbol.for("react.portal"):60106,n=e?Symbol.for("react.fragment"):60107,i=e?Symbol.for("react.strict_mode"):60108,a=e?Symbol.for("react.profiler"):60114,o=e?Symbol.for("react.provider"):60109,l=e?Symbol.for("react.context"):60110,c=e?Symbol.for("react.async_mode"):60111,s=e?Symbol.for("react.concurrent_mode"):60111,u=e?Symbol.for("react.forward_ref"):60112,f=e?Symbol.for("react.suspense"):60113,d=e?Symbol.for("react.suspense_list"):60120,h=e?Symbol.for("react.memo"):60115,p=e?Symbol.for("react.lazy"):60116,y=e?Symbol.for("react.block"):60121,v=e?Symbol.for("react.fundamental"):60117,g=e?Symbol.for("react.responder"):60118,m=e?Symbol.for("react.scope"):60119;function b(e){if("object"==typeof e&&null!==e){var d=e.$$typeof;switch(d){case t:switch(e=e.type){case c:case s:case n:case a:case i:case f:return e;default:switch(e=e&&e.$$typeof){case l:case u:case p:case h:case o:return e;default:return d}}case r:return d}}}function x(e){return b(e)===s}return ve.AsyncMode=c,ve.ConcurrentMode=s,ve.ContextConsumer=l,ve.ContextProvider=o,ve.Element=t,ve.ForwardRef=u,ve.Fragment=n,ve.Lazy=p,ve.Memo=h,ve.Portal=r,ve.Profiler=a,ve.StrictMode=i,ve.Suspense=f,ve.isAsyncMode=function(e){return x(e)||b(e)===c},ve.isConcurrentMode=x,ve.isContextConsumer=function(e){return b(e)===l},ve.isContextProvider=function(e){return b(e)===o},ve.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===t},ve.isForwardRef=function(e){return b(e)===u},ve.isFragment=function(e){return b(e)===n},ve.isLazy=function(e){return b(e)===p},ve.isMemo=function(e){return b(e)===h},ve.isPortal=function(e){return b(e)===r},ve.isProfiler=function(e){return b(e)===a},ve.isStrictMode=function(e){return b(e)===i},ve.isSuspense=function(e){return b(e)===f},ve.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===n||e===s||e===a||e===i||e===f||e===d||"object"==typeof e&&null!==e&&(e.$$typeof===p||e.$$typeof===h||e.$$typeof===o||e.$$typeof===l||e.$$typeof===u||e.$$typeof===v||e.$$typeof===g||e.$$typeof===m||e.$$typeof===y)},ve.typeOf=b,ve}()),ye.exports),me=e=>0===e?0:e>0?1:-1,be=e=>"number"==typeof e&&e!=+e,xe=e=>"string"==typeof e&&e.indexOf("%")===e.length-1,we=e=>("number"==typeof e||e instanceof Number)&&!be(e),Oe=e=>we(e)||"string"==typeof e,je=0,Ae=e=>{var t=++je;return"".concat(e||"").concat(t)},Pe=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!we(e)&&"string"!=typeof e)return n;if(xe(e)){if(null==t)return n;var a=e.indexOf("%");r=t*parseFloat(e.slice(0,a))/100}else r=+e;return be(r)&&(r=n),i&&null!=t&&r>t&&(r=t),r},Se=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++){if(r[e[n]])return!0;r[e[n]]=!0}return!1},Ee=(e,t)=>we(e)&&we(t)?r=>e+r*(t-e):()=>t;function ke(e,t,r){return we(e)&&we(t)?e+r*(t-e):t}function Me(e,t,r){if(e&&e.length)return e.find(e=>e&&("function"==typeof t?t(e):de(e,t))===r)}var Te=e=>null==e,Ce=e=>Te(e)?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1)),De=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],Ne=["points","pathLength"],Ie={svg:["viewBox","children"],polygon:Ne,polyline:Ne},_e=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],Re=(e,t)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if(u.isValidElement(e)&&(r=e.props),"object"!=typeof r&&"function"!=typeof r)return null;var n={};return Object.keys(r).forEach(e=>{_e.includes(e)&&(n[e]=t=>r[e](r,t))}),n},Le=(e,t,r)=>{if(null===e||"object"!=typeof e&&"function"!=typeof e)return null;var n=null;return Object.keys(e).forEach(i=>{var a=e[i];_e.includes(i)&&"function"==typeof a&&(n||(n={}),n[i]=((e,t,r)=>n=>(e(t,r,n),null))(a,t,r))}),n},ze=e=>"string"==typeof e?e:e?e.displayName||e.name||"Component":"",Be=null,Ke=null,$e=e=>{if(e===Be&&Array.isArray(Ke))return Ke;var t=[];return u.Children.forEach(e,e=>{Te(e)||(ge.isFragment(e)?t=t.concat($e(e.props.children)):t.push(e))}),Ke=t,Be=e,t};function Ue(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(e=>ze(e)):[ze(t)],$e(e).forEach(e=>{var t=de(e,"type.displayName")||de(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}var Fe=e=>!e||"object"!=typeof e||!("clipDot"in e)||Boolean(e.clipDot),We=(e,t,r)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if(u.isValidElement(e)&&(n=e.props),"object"!=typeof n&&"function"!=typeof n)return null;var i={};return Object.keys(n).forEach(e=>{var a;((e,t,r,n)=>{var i,a=null!==(i=n&&(null==Ie?void 0:Ie[n]))&&void 0!==i?i:[];return t.startsWith("data-")||"function"!=typeof e&&(n&&a.includes(t)||De.includes(t))||r&&_e.includes(t)})(null===(a=n)||void 0===a?void 0:a[e],e,t,r)&&(i[e]=n[e])}),i},He=["children","width","height","viewBox","className","style","title","desc"];function Ve(){return Ve=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ve.apply(null,arguments)}var qe=u.forwardRef((e,t)=>{var{children:r,width:n,height:i,viewBox:a,className:o,style:l,title:c,desc:s}=e,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,He),d=a||{width:n,height:i,x:0,y:0},h=q("recharts-surface",o);return u.createElement("svg",Ve({},We(f,!0,"svg"),{className:h,width:n,height:i,style:l,viewBox:"".concat(d.x," ").concat(d.y," ").concat(d.width," ").concat(d.height),ref:t}),u.createElement("title",null,c),u.createElement("desc",null,s),r)}),Ye=["children","className"];function Ge(){return Ge=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ge.apply(null,arguments)}var Xe=u.forwardRef((e,t)=>{var{children:r,className:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Ye),a=q("recharts-layer",n);return u.createElement("g",Ge({className:a},We(i,!0),{ref:t}),r)}),Ze=u.createContext(null);function Je(e){return function(){return e}}const Qe=Math.cos,et=Math.sin,tt=Math.sqrt,rt=Math.PI,nt=2*rt,it=Math.PI,at=2*it,ot=1e-6,lt=at-ot;function ct(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class st{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?ct:function(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return ct;const r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,i,a){this._append`C${+e},${+t},${+r},${+n},${this._x1=+i},${this._y1=+a}`}arcTo(e,t,r,n,i){if(e=+e,t=+t,r=+r,n=+n,(i=+i)<0)throw new Error(`negative radius: ${i}`);let a=this._x1,o=this._y1,l=r-e,c=n-t,s=a-e,u=o-t,f=s*s+u*u;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(f>ot)if(Math.abs(u*l-c*s)>ot&&i){let d=r-a,h=n-o,p=l*l+c*c,y=d*d+h*h,v=Math.sqrt(p),g=Math.sqrt(f),m=i*Math.tan((it-Math.acos((p+f-y)/(2*v*g)))/2),b=m/g,x=m/v;Math.abs(b-1)>ot&&this._append`L${e+b*s},${t+b*u}`,this._append`A${i},${i},0,0,${+(u*d>s*h)},${this._x1=e+x*l},${this._y1=t+x*c}`}else this._append`L${this._x1=e},${this._y1=t}`;else;}arc(e,t,r,n,i,a){if(e=+e,t=+t,a=!!a,(r=+r)<0)throw new Error(`negative radius: ${r}`);let o=r*Math.cos(n),l=r*Math.sin(n),c=e+o,s=t+l,u=1^a,f=a?n-i:i-n;null===this._x1?this._append`M${c},${s}`:(Math.abs(this._x1-c)>ot||Math.abs(this._y1-s)>ot)&&this._append`L${c},${s}`,r&&(f<0&&(f=f%at+at),f>lt?this._append`A${r},${r},0,1,${u},${e-o},${t-l}A${r},${r},0,1,${u},${this._x1=c},${this._y1=s}`:f>ot&&this._append`A${r},${r},0,${+(f>=it)},${u},${this._x1=e+r*Math.cos(i)},${this._y1=t+r*Math.sin(i)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r=+r}v${+n}h${-r}Z`}toString(){return this._}}function ut(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{const e=Math.floor(r);if(!(e>=0))throw new RangeError(`invalid digits: ${r}`);t=e}return e},()=>new st(t)}function ft(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function dt(e){this._context=e}function ht(e){return new dt(e)}function pt(e){return e[0]}function yt(e){return e[1]}function vt(e,t){var r=Je(!0),n=null,i=ht,a=null,o=ut(l);function l(l){var c,s,u,f=(l=ft(l)).length,d=!1;for(null==n&&(a=i(u=o())),c=0;c<=f;++c)!(c<f&&r(s=l[c],c,l))===d&&((d=!d)?a.lineStart():a.lineEnd()),d&&a.point(+e(s,c,l),+t(s,c,l));if(u)return a=null,u+""||null}return e="function"==typeof e?e:void 0===e?pt:Je(e),t="function"==typeof t?t:void 0===t?yt:Je(t),l.x=function(t){return arguments.length?(e="function"==typeof t?t:Je(+t),l):e},l.y=function(e){return arguments.length?(t="function"==typeof e?e:Je(+e),l):t},l.defined=function(e){return arguments.length?(r="function"==typeof e?e:Je(!!e),l):r},l.curve=function(e){return arguments.length?(i=e,null!=n&&(a=i(n)),l):i},l.context=function(e){return arguments.length?(null==e?n=a=null:a=i(n=e),l):n},l}function gt(e,t,r){var n=null,i=Je(!0),a=null,o=ht,l=null,c=ut(s);function s(s){var u,f,d,h,p,y=(s=ft(s)).length,v=!1,g=new Array(y),m=new Array(y);for(null==a&&(l=o(p=c())),u=0;u<=y;++u){if(!(u<y&&i(h=s[u],u,s))===v)if(v=!v)f=u,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),d=u-1;d>=f;--d)l.point(g[d],m[d]);l.lineEnd(),l.areaEnd()}v&&(g[u]=+e(h,u,s),m[u]=+t(h,u,s),l.point(n?+n(h,u,s):g[u],r?+r(h,u,s):m[u]))}if(p)return l=null,p+""||null}function u(){return vt().defined(i).curve(o).context(a)}return e="function"==typeof e?e:void 0===e?pt:Je(+e),t="function"==typeof t?t:Je(void 0===t?0:+t),r="function"==typeof r?r:void 0===r?yt:Je(+r),s.x=function(t){return arguments.length?(e="function"==typeof t?t:Je(+t),n=null,s):e},s.x0=function(t){return arguments.length?(e="function"==typeof t?t:Je(+t),s):e},s.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:Je(+e),s):n},s.y=function(e){return arguments.length?(t="function"==typeof e?e:Je(+e),r=null,s):t},s.y0=function(e){return arguments.length?(t="function"==typeof e?e:Je(+e),s):t},s.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:Je(+e),s):r},s.lineX0=s.lineY0=function(){return u().x(e).y(t)},s.lineY1=function(){return u().x(e).y(r)},s.lineX1=function(){return u().x(n).y(t)},s.defined=function(e){return arguments.length?(i="function"==typeof e?e:Je(!!e),s):i},s.curve=function(e){return arguments.length?(o=e,null!=a&&(l=o(a)),s):o},s.context=function(e){return arguments.length?(null==e?a=l=null:l=o(a=e),s):a},s}dt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}};class mt{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}const bt={draw(e,t){const r=tt(t/rt);e.moveTo(r,0),e.arc(0,0,r,0,nt)}},xt={draw(e,t){const r=tt(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},wt=tt(1/3),Ot=2*wt,jt={draw(e,t){const r=tt(t/Ot),n=r*wt;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},At={draw(e,t){const r=tt(t),n=-r/2;e.rect(n,n,r,r)}},Pt=et(rt/10)/et(7*rt/10),St=et(nt/10)*Pt,Et=-Qe(nt/10)*Pt,kt={draw(e,t){const r=tt(.8908130915292852*t),n=St*r,i=Et*r;e.moveTo(0,-r),e.lineTo(n,i);for(let a=1;a<5;++a){const t=nt*a/5,o=Qe(t),l=et(t);e.lineTo(l*r,-o*r),e.lineTo(o*n-l*i,l*n+o*i)}e.closePath()}},Mt=tt(3),Tt={draw(e,t){const r=-tt(t/(3*Mt));e.moveTo(0,2*r),e.lineTo(-Mt*r,-r),e.lineTo(Mt*r,-r),e.closePath()}},Ct=-.5,Dt=tt(3)/2,Nt=1/tt(12),It=3*(Nt/2+1),_t={draw(e,t){const r=tt(t/It),n=r/2,i=r*Nt,a=n,o=r*Nt+r,l=-a,c=o;e.moveTo(n,i),e.lineTo(a,o),e.lineTo(l,c),e.lineTo(Ct*n-Dt*i,Dt*n+Ct*i),e.lineTo(Ct*a-Dt*o,Dt*a+Ct*o),e.lineTo(Ct*l-Dt*c,Dt*l+Ct*c),e.lineTo(Ct*n+Dt*i,Ct*i-Dt*n),e.lineTo(Ct*a+Dt*o,Ct*o-Dt*a),e.lineTo(Ct*l+Dt*c,Ct*c-Dt*l),e.closePath()}};function Rt(){}function Lt(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function zt(e){this._context=e}function Bt(e){this._context=e}function Kt(e){this._context=e}function $t(e){this._context=e}function Ut(e){return e<0?-1:1}function Ft(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0),l=(a*i+o*n)/(n+i);return(Ut(a)+Ut(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(l))||0}function Wt(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function Ht(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,l=(a-n)/3;e._context.bezierCurveTo(n+l,i+l*t,a-l,o-l*r,a,o)}function Vt(e){this._context=e}function qt(e){this._context=new Yt(e)}function Yt(e){this._context=e}function Gt(e){this._context=e}function Xt(e){var t,r,n=e.length-1,i=new Array(n),a=new Array(n),o=new Array(n);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<n-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[n-1]=2,a[n-1]=7,o[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=i[t]/a[t-1],a[t]-=r,o[t]-=r*o[t-1];for(i[n-1]=o[n-1]/a[n-1],t=n-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[n-1]=(e[n]+i[n-1])/2,t=0;t<n-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function Zt(e,t){this._context=e,this._t=t}function Jt(e,t){if((i=e.length)>1)for(var r,n,i,a=1,o=e[t[0]],l=o.length;a<i;++a)for(n=o,o=e[t[a]],r=0;r<l;++r)o[r][1]+=o[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function Qt(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function er(e,t){return e[t]}function tr(e){const t=[];return t.key=e,t}zt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:Lt(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:Lt(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},Bt.prototype={areaStart:Rt,areaEnd:Rt,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:Lt(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},Kt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:Lt(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},$t.prototype={areaStart:Rt,areaEnd:Rt,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}},Vt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:Ht(this,this._t0,Wt(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t=+t,(e=+e)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,Ht(this,Wt(this,r=Ft(this,e,t)),r);break;default:Ht(this,this._t0,r=Ft(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(qt.prototype=Object.create(Vt.prototype)).point=function(e,t){Vt.prototype.point.call(this,t,e)},Yt.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}},Gt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=Xt(e),i=Xt(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},Zt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var rr=["type","size","sizeType"];function nr(){return nr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},nr.apply(null,arguments)}function ir(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ar(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ir(Object(r),!0).forEach(function(t){or(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ir(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function or(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var lr={symbolCircle:bt,symbolCross:xt,symbolDiamond:jt,symbolSquare:At,symbolStar:kt,symbolTriangle:Tt,symbolWye:_t},cr=Math.PI/180,sr=e=>{var t,r,{type:n="circle",size:i=64,sizeType:a="area"}=e,o=ar(ar({},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,rr)),{},{type:n,size:i,sizeType:a}),{className:l,cx:c,cy:s}=o,f=We(o,!0);return c===+c&&s===+s&&i===+i?u.createElement("path",nr({},f,{className:q("recharts-symbols",l),transform:"translate(".concat(c,", ").concat(s,")"),d:(t=(e=>{var t="symbol".concat(Ce(e));return lr[t]||bt})(n),r=function(e,t){let r=null,n=ut(i);function i(){let i;if(r||(r=i=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),i)return r=null,i+""||null}return e="function"==typeof e?e:Je(e||bt),t="function"==typeof t?t:Je(void 0===t?64:+t),i.type=function(t){return arguments.length?(e="function"==typeof t?t:Je(t),i):e},i.size=function(e){return arguments.length?(t="function"==typeof e?e:Je(+e),i):t},i.context=function(e){return arguments.length?(r=null==e?null:e,i):r},i}().type(t).size(((e,t,r)=>{if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return.5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*cr;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.tan(n)**2);case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}})(i,a,n)),r())})):null};function ur(){return ur=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ur.apply(null,arguments)}function fr(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dr(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}sr.registerSymbol=(e,t)=>{lr["symbol".concat(Ce(e))]=t};var hr=32;class pr extends u.PureComponent{renderIcon(e,t){var{inactiveColor:r}=this.props,n=16,i=hr/6,a=hr/3,o=e.inactive?r:e.color,l=null!=t?t:e.type;if("none"===l)return null;if("plainline"===l)return u.createElement("line",{strokeWidth:4,fill:"none",stroke:o,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:n,x2:hr,y2:n,className:"recharts-legend-icon"});if("line"===l)return u.createElement("path",{strokeWidth:4,fill:"none",stroke:o,d:"M0,".concat(n,"h").concat(a,"\n            A").concat(i,",").concat(i,",0,1,1,").concat(2*a,",").concat(n,"\n            H").concat(hr,"M").concat(2*a,",").concat(n,"\n            A").concat(i,",").concat(i,",0,1,1,").concat(a,",").concat(n),className:"recharts-legend-icon"});if("rect"===l)return u.createElement("path",{stroke:"none",fill:o,d:"M0,".concat(4,"h").concat(hr,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(u.isValidElement(e.legendIcon)){var c=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fr(Object(r),!0).forEach(function(t){dr(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fr(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return delete c.legendIcon,u.cloneElement(e.legendIcon,c)}return u.createElement(sr,{fill:o,cx:n,cy:n,size:hr,sizeType:"diameter",type:l})}renderItems(){var{payload:e,iconSize:t,layout:r,formatter:n,inactiveColor:i,iconType:a}=this.props,o={x:0,y:0,width:hr,height:hr},l={display:"horizontal"===r?"inline-block":"block",marginRight:10},c={display:"inline-block",verticalAlign:"middle",marginRight:4};return e.map((e,r)=>{var s=e.formatter||n,f=q({"recharts-legend-item":!0,["legend-item-".concat(r)]:!0,inactive:e.inactive});if("none"===e.type)return null;var d=e.inactive?i:e.color,h=s?s(e.value,e,r):e.value;return u.createElement("li",ur({className:f,style:l,key:"legend-item-".concat(r)},Le(this.props,e,r)),u.createElement(qe,{width:t,height:t,viewBox:o,style:c,"aria-label":"".concat(h," legend icon")},this.renderIcon(e,a)),u.createElement("span",{className:"recharts-legend-item-text",style:{color:d}},h))})}render(){var{payload:e,layout:t,align:r}=this.props;if(!e||!e.length)return null;var n={padding:0,margin:0,textAlign:"horizontal"===t?r:"left"};return u.createElement("ul",{className:"recharts-default-legend",style:n},this.renderItems())}}dr(pr,"displayName","Legend"),dr(pr,"defaultProps",{align:"center",iconSize:14,inactiveColor:"#ccc",layout:"horizontal",verticalAlign:"middle"});var yr,vr={},gr={};function mr(){return yr||(yr=1,e=gr,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.uniqBy=function(e,t){const r=new Map;for(let n=0;n<e.length;n++){const i=e[n],a=t(i);r.has(a)||r.set(a,i)}return Array.from(r.values())}),gr;var e}var br,xr={};function wr(){return br||(br=1,e=xr,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.identity=function(e){return e}),xr;var e}var Or,jr,Ar={},Pr={},Sr={};function Er(){return Or||(Or=1,e=Sr,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isLength=function(e){return Number.isSafeInteger(e)&&e>=0}),Sr;var e}function kr(){return jr||(jr=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Er();e.isArrayLike=function(e){return null!=e&&"function"!=typeof e&&t.isLength(e.length)}}(Pr)),Pr}var Mr,Tr,Cr={};function Dr(){return Mr||(Mr=1,e=Cr,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isObjectLike=function(e){return"object"==typeof e&&null!==e}),Cr;var e}function Nr(){return Tr||(Tr=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=kr(),r=Dr();e.isArrayLikeObject=function(e){return r.isObjectLike(e)&&t.isArrayLike(e)}}(Ar)),Ar}var Ir,_r={},Rr={};function Lr(){return Ir||(Ir=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=ue();e.property=function(e){return function(r){return t.get(r,e)}}}(Rr)),Rr}var zr,Br={},Kr={},$r={},Ur={};function Fr(){return zr||(zr=1,e=Ur,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isObject=function(e){return null!==e&&("object"==typeof e||"function"==typeof e)}),Ur;var e}var Wr,Hr={};function Vr(){return Wr||(Wr=1,e=Hr,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isPrimitive=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e}),Hr;var e}var qr,Yr,Gr,Xr={};function Zr(){return qr||(qr=1,e=Xr,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.eq=function(e,t){return e===t||Number.isNaN(e)&&Number.isNaN(t)}),Xr;var e}function Jr(){return Yr||(Yr=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Qr(),r=Fr(),n=Vr(),i=Zr();function a(e,t,c,s){if(t===e)return!0;switch(typeof t){case"object":return function(e,t,r,i){if(null==t)return!0;if(Array.isArray(t))return o(e,t,r,i);if(t instanceof Map)return function(e,t,r,n){if(0===t.size)return!0;if(!(e instanceof Map))return!1;for(const[i,a]of t.entries()){if(!1===r(e.get(i),a,i,e,t,n))return!1}return!0}(e,t,r,i);if(t instanceof Set)return l(e,t,r,i);const a=Object.keys(t);if(null==e)return 0===a.length;if(0===a.length)return!0;if(i&&i.has(t))return i.get(t)===e;i&&i.set(t,e);try{for(let o=0;o<a.length;o++){const l=a[o];if(!n.isPrimitive(e)&&!(l in e))return!1;if(void 0===t[l]&&void 0!==e[l])return!1;if(null===t[l]&&null!==e[l])return!1;if(!r(e[l],t[l],l,e,t,i))return!1}return!0}finally{i&&i.delete(t)}}(e,t,c,s);case"function":return Object.keys(t).length>0?a(e,{...t},c,s):i.eq(e,t);default:return r.isObject(e)?"string"!=typeof t||""===t:i.eq(e,t)}}function o(e,t,r,n){if(0===t.length)return!0;if(!Array.isArray(e))return!1;const i=new Set;for(let a=0;a<t.length;a++){const o=t[a];let l=!1;for(let c=0;c<e.length;c++){if(i.has(c))continue;let s=!1;if(r(e[c],o,a,e,t,n)&&(s=!0),s){i.add(c),l=!0;break}}if(!l)return!1}return!0}function l(e,t,r,n){return 0===t.size||e instanceof Set&&o([...e],[...t],r,n)}e.isMatchWith=function(e,r,n){return"function"!=typeof n?t.isMatch(e,r):a(e,r,function e(t,r,i,o,l,c){const s=n(t,r,i,o,l,c);return void 0!==s?Boolean(s):a(t,r,e,c)},new Map)},e.isSetMatch=l}($r)),$r}function Qr(){return Gr||(Gr=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Jr();e.isMatch=function(e,r){return t.isMatchWith(e,r,()=>{})}}(Kr)),Kr}var en,tn={},rn={},nn={};function an(){return en||(en=1,e=nn,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.getSymbols=function(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}),nn;var e}var on,ln={};function cn(){return on||(on=1,e=ln,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.getTag=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}),ln;var e}var sn,un={};function fn(){return sn||(sn=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});e.argumentsTag="[object Arguments]",e.arrayBufferTag="[object ArrayBuffer]",e.arrayTag="[object Array]",e.bigInt64ArrayTag="[object BigInt64Array]",e.bigUint64ArrayTag="[object BigUint64Array]",e.booleanTag="[object Boolean]",e.dataViewTag="[object DataView]",e.dateTag="[object Date]",e.errorTag="[object Error]",e.float32ArrayTag="[object Float32Array]",e.float64ArrayTag="[object Float64Array]",e.functionTag="[object Function]",e.int16ArrayTag="[object Int16Array]",e.int32ArrayTag="[object Int32Array]",e.int8ArrayTag="[object Int8Array]",e.mapTag="[object Map]",e.numberTag="[object Number]",e.objectTag="[object Object]",e.regexpTag="[object RegExp]",e.setTag="[object Set]",e.stringTag="[object String]",e.symbolTag="[object Symbol]",e.uint16ArrayTag="[object Uint16Array]",e.uint32ArrayTag="[object Uint32Array]",e.uint8ArrayTag="[object Uint8Array]",e.uint8ClampedArrayTag="[object Uint8ClampedArray]"}(un)),un}var dn,hn,pn,yn,vn={};function gn(){return dn||(dn=1,e=vn,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isTypedArray=function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}),vn;var e}function mn(){return hn||(hn=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=an(),r=cn(),n=fn(),i=Vr(),a=gn();function o(e,t,c,s=new Map,u=void 0){const f=null==u?void 0:u(e,t,c,s);if(null!=f)return f;if(i.isPrimitive(e))return e;if(s.has(e))return s.get(e);if(Array.isArray(e)){const t=new Array(e.length);s.set(e,t);for(let r=0;r<e.length;r++)t[r]=o(e[r],r,c,s,u);return Object.hasOwn(e,"index")&&(t.index=e.index),Object.hasOwn(e,"input")&&(t.input=e.input),t}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){const t=new RegExp(e.source,e.flags);return t.lastIndex=e.lastIndex,t}if(e instanceof Map){const t=new Map;s.set(e,t);for(const[r,n]of e)t.set(r,o(n,r,c,s,u));return t}if(e instanceof Set){const t=new Set;s.set(e,t);for(const r of e)t.add(o(r,void 0,c,s,u));return t}if("undefined"!=typeof Buffer&&Buffer.isBuffer(e))return e.subarray();if(a.isTypedArray(e)){const t=new(Object.getPrototypeOf(e).constructor)(e.length);s.set(e,t);for(let r=0;r<e.length;r++)t[r]=o(e[r],r,c,s,u);return t}if(e instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){const t=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return s.set(e,t),l(t,e,c,s,u),t}if("undefined"!=typeof File&&e instanceof File){const t=new File([e],e.name,{type:e.type});return s.set(e,t),l(t,e,c,s,u),t}if(e instanceof Blob){const t=new Blob([e],{type:e.type});return s.set(e,t),l(t,e,c,s,u),t}if(e instanceof Error){const t=new e.constructor;return s.set(e,t),t.message=e.message,t.name=e.name,t.stack=e.stack,t.cause=e.cause,l(t,e,c,s,u),t}if("object"==typeof e&&function(e){switch(r.getTag(e)){case n.argumentsTag:case n.arrayTag:case n.arrayBufferTag:case n.dataViewTag:case n.booleanTag:case n.dateTag:case n.float32ArrayTag:case n.float64ArrayTag:case n.int8ArrayTag:case n.int16ArrayTag:case n.int32ArrayTag:case n.mapTag:case n.numberTag:case n.objectTag:case n.regexpTag:case n.setTag:case n.stringTag:case n.symbolTag:case n.uint8ArrayTag:case n.uint8ClampedArrayTag:case n.uint16ArrayTag:case n.uint32ArrayTag:return!0;default:return!1}}(e)){const t=Object.create(Object.getPrototypeOf(e));return s.set(e,t),l(t,e,c,s,u),t}return e}function l(e,r,n=e,i,a){const l=[...Object.keys(r),...t.getSymbols(r)];for(let t=0;t<l.length;t++){const c=l[t],s=Object.getOwnPropertyDescriptor(e,c);(null==s||s.writable)&&(e[c]=o(r[c],c,n,i,a))}}e.cloneDeepWith=function(e,t){return o(e,void 0,e,new Map,t)},e.cloneDeepWithImpl=o,e.copyProperties=l}(rn)),rn}function bn(){return pn||(pn=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=mn();e.cloneDeep=function(e){return t.cloneDeepWithImpl(e,void 0,e,new Map,void 0)}}(tn)),tn}function xn(){return yn||(yn=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Qr(),r=bn();e.matches=function(e){return e=r.cloneDeep(e),r=>t.isMatch(r,e)}}(Br)),Br}var wn,On,jn={},An={},Pn={};function Sn(){return wn||(wn=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=mn(),r=fn();e.cloneDeepWith=function(e,n){return t.cloneDeepWith(e,(i,a,o,l)=>{const c=null==n?void 0:n(i,a,o,l);if(null!=c)return c;if("object"==typeof e)switch(Object.prototype.toString.call(e)){case r.numberTag:case r.stringTag:case r.booleanTag:{const r=new e.constructor(null==e?void 0:e.valueOf());return t.copyProperties(r,e),r}case r.argumentsTag:{const r={};return t.copyProperties(r,e),r.length=e.length,r[Symbol.iterator]=e[Symbol.iterator],r}default:return}})}}(Pn)),Pn}function En(){return On||(On=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Sn();e.cloneDeep=function(e){return t.cloneDeepWith(e)}}(An)),An}var kn,Mn={},Tn={};function Cn(){return kn||(kn=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=/^(?:0|[1-9]\d*)$/;e.isIndex=function(e,r=Number.MAX_SAFE_INTEGER){switch(typeof e){case"number":return Number.isInteger(e)&&e>=0&&e<r;case"symbol":return!1;case"string":return t.test(e)}}}(Tn)),Tn}var Dn,Nn,In,_n,Rn,Ln,zn,Bn={};function Kn(){return Dn||(Dn=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=cn();e.isArguments=function(e){return null!==e&&"object"==typeof e&&"[object Arguments]"===t.getTag(e)}}(Bn)),Bn}function $n(){return Nn||(Nn=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=ee(),r=Cn(),n=Kn(),i=se();e.has=function(e,a){let o;if(o=Array.isArray(a)?a:"string"==typeof a&&t.isDeepKey(a)&&null==(null==e?void 0:e[a])?i.toPath(a):[a],0===o.length)return!1;let l=e;for(let t=0;t<o.length;t++){const e=o[t];if(null==l||!Object.hasOwn(l,e)){if(!((Array.isArray(l)||n.isArguments(l))&&r.isIndex(e)&&e<l.length))return!1}l=l[e]}return!0}}(Mn)),Mn}function Un(){return In||(In=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Qr(),r=ne(),n=En(),i=ue(),a=$n();e.matchesProperty=function(e,o){switch(typeof e){case"object":Object.is(null==e?void 0:e.valueOf(),-0)&&(e="-0");break;case"number":e=r.toKey(e)}return o=n.cloneDeep(o),function(r){const n=i.get(r,e);return void 0===n?a.has(r,e):void 0===o?void 0===n:t.isMatch(n,o)}}}(jn)),jn}function Fn(){return _n||(_n=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=wr(),r=Lr(),n=xn(),i=Un();e.iteratee=function(e){if(null==e)return t.identity;switch(typeof e){case"function":return e;case"object":return Array.isArray(e)&&2===e.length?i.matchesProperty(e[0],e[1]):n.matches(e);case"string":case"symbol":case"number":return r.property(e)}}}(_r)),_r}function Wn(){return zn?Ln:(zn=1,Ln=(Rn||(Rn=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=mr(),r=wr(),n=Nr(),i=Fn();e.uniqBy=function(e,a=r.identity){return n.isArrayLikeObject(e)?t.uniqBy(Array.from(e),i.iteratee(a)):[]}}(vr)),vr).uniqBy)}const Hn=I(Wn());function Vn(e,t,r){return!0===t?Hn(e,r):"function"==typeof t?Hn(e,t):e}var qn,Yn,Gn,Xn,Zn={exports:{}},Jn={},Qn={exports:{}},ei={};function ti(){return Yn||(Yn=1,Qn.exports=function(){if(qn)return ei;qn=1;var e=_(),t="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},r=e.useState,n=e.useEffect,i=e.useLayoutEffect,a=e.useDebugValue;function o(e){var r=e.getSnapshot;e=e.value;try{var n=r();return!t(e,n)}catch(i){return!0}}var l="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var l=t(),c=r({inst:{value:l,getSnapshot:t}}),s=c[0].inst,u=c[1];return i(function(){s.value=l,s.getSnapshot=t,o(s)&&u({inst:s})},[e,l,t]),n(function(){return o(s)&&u({inst:s}),e(function(){o(s)&&u({inst:s})})},[e]),a(l),l};return ei.useSyncExternalStore=void 0!==e.useSyncExternalStore?e.useSyncExternalStore:l,ei}()),Qn.exports}
/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ri=(Xn||(Xn=1,Zn.exports=function(){if(Gn)return Jn;Gn=1;var e=_(),t=ti(),r="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},n=t.useSyncExternalStore,i=e.useRef,a=e.useEffect,o=e.useMemo,l=e.useDebugValue;return Jn.useSyncExternalStoreWithSelector=function(e,t,c,s,u){var f=i(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;f=o(function(){function e(e){if(!a){if(a=!0,n=e,e=s(e),void 0!==u&&d.hasValue){var t=d.value;if(u(t,e))return i=t}return i=e}if(t=i,r(n,e))return t;var o=s(e);return void 0!==u&&u(t,o)?(n=e,t):(n=e,i=o)}var n,i,a=!1,o=void 0===c?null:c;return[function(){return e(t())},null===o?void 0:function(){return e(o())}]},[t,c,s,u]);var h=n(e,f[0],f[1]);return a(function(){d.hasValue=!0,d.value=h},[h]),l(h),h},Jn}()),Zn.exports),ni=u.createContext(null),ii=e=>e,ai=()=>{var e=u.useContext(ni);return e?e.store.dispatch:ii},oi=()=>{},li=()=>oi,ci=(e,t)=>e===t;function si(e){var t=u.useContext(ni);return ri.useSyncExternalStoreWithSelector(t?t.subscription.addNestedSub:li,t?t.store.getState:oi,t?t.store.getState:oi,t?e:oi,ci)}var ui,fi={},di={},hi={};function pi(){return ui||(ui=1,function(e){function t(e){return"symbol"==typeof e?1:null===e?2:void 0===e?3:e!=e?4:0}Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});e.compareValues=(e,r,n)=>{if(e!==r){const i=t(e),a=t(r);if(i===a&&0===i){if(e<r)return"desc"===n?1:-1;if(e>r)return"desc"===n?-1:1}return"desc"===n?a-i:i-a}return 0}}(hi)),hi}var yi,vi,gi,mi={},bi={};function xi(){return yi||(yi=1,e=bi,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isSymbol=function(e){return"symbol"==typeof e||e instanceof Symbol}),bi;var e}function wi(){return vi||(vi=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=xi(),r=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,n=/^\w*$/;e.isKey=function(e,i){return!Array.isArray(e)&&(!("number"!=typeof e&&"boolean"!=typeof e&&null!=e&&!t.isSymbol(e))||("string"==typeof e&&(n.test(e)||!r.test(e))||null!=i&&Object.hasOwn(i,e)))}}(mi)),mi}function Oi(){return gi||(gi=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=pi(),r=wi(),n=se();e.orderBy=function(e,i,a,o){if(null==e)return[];a=o?void 0:a,Array.isArray(e)||(e=Object.values(e)),Array.isArray(i)||(i=null==i?[null]:[i]),0===i.length&&(i=[null]),Array.isArray(a)||(a=null==a?[]:[a]),a=a.map(e=>String(e));const l=(e,t)=>{let r=e;for(let n=0;n<t.length&&null!=r;++n)r=r[t[n]];return r},c=i.map(e=>(Array.isArray(e)&&1===e.length&&(e=e[0]),null==e||"function"==typeof e||Array.isArray(e)||r.isKey(e)?e:{key:e,path:n.toPath(e)}));return e.map(e=>({original:e,criteria:c.map(t=>((e,t)=>null==t||null==e?t:"object"==typeof e&&"key"in e?Object.hasOwn(t,e.key)?t[e.key]:l(t,e.path):"function"==typeof e?e(t):Array.isArray(e)?l(t,e):"object"==typeof t?t[e]:t)(t,e))})).slice().sort((e,r)=>{for(let n=0;n<c.length;n++){const i=t.compareValues(e.criteria[n],r.criteria[n],a[n]);if(0!==i)return i}return 0}).map(e=>e.original)}}(di)),di}var ji,Ai={};function Pi(){return ji||(ji=1,e=Ai,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.flatten=function(e,t=1){const r=[],n=Math.floor(t),i=(e,t)=>{for(let a=0;a<e.length;a++){const o=e[a];Array.isArray(o)&&t<n?i(o,t+1):r.push(o)}};return i(e,0),r}),Ai;var e}var Si,Ei,ki,Mi,Ti={};function Ci(){return Si||(Si=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Cn(),r=kr(),n=Fr(),i=Zr();e.isIterateeCall=function(e,a,o){return!!n.isObject(o)&&(!!("number"==typeof a&&r.isArrayLike(o)&&t.isIndex(a)&&a<o.length||"string"==typeof a&&a in o)&&i.eq(o[a],e))}}(Ti)),Ti}function Di(){return Mi?ki:(Mi=1,ki=(Ei||(Ei=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Oi(),r=Pi(),n=Ci();e.sortBy=function(e,...i){const a=i.length;return a>1&&n.isIterateeCall(e,i[0],i[1])?i=[]:a>2&&n.isIterateeCall(i[0],i[1],i[2])&&(i=[i[0]]),t.orderBy(e,r.flatten(i),["asc"])}}(fi)),fi).sortBy)}const Ni=I(Di());var Ii=e=>e.legend.settings,_i=U([e=>e.legend.payload,Ii],(e,t)=>{var{itemSorter:r}=t,n=e.flat(1);return r?Ni(n,r):n});function Ri(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[t,r]=u.useState({height:0,left:0,top:0,width:0}),n=u.useCallback(e=>{if(null!=e){var n=e.getBoundingClientRect(),i={height:n.height,left:n.left,top:n.top,width:n.width};(Math.abs(i.height-t.height)>1||Math.abs(i.left-t.left)>1||Math.abs(i.top-t.top)>1||Math.abs(i.width-t.width)>1)&&r({height:i.height,left:i.left,top:i.top,width:i.width})}},[t.width,t.height,t.top,t.left,...e]);return[t,n]}var Li=r({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:zi,setLayout:Bi,setChartSize:Ki,setScale:$i}=Li.actions,Ui=Li.reducer;function Fi(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Wi(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Fi(Object(r),!0).forEach(function(t){Hi(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Fi(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Hi(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Vi=Math.PI/180,qi=e=>180*e/Math.PI,Yi=(e,t,r,n)=>({x:e+Math.cos(-Vi*n)*r,y:t+Math.sin(-Vi*n)*r}),Gi=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},Xi=(e,t)=>{var{x:r,y:n}=e,{cx:i,cy:a}=t,o=((e,t)=>{var{x:r,y:n}=e,{x:i,y:a}=t;return Math.sqrt((r-i)**2+(n-a)**2)})({x:r,y:n},{x:i,y:a});if(o<=0)return{radius:o,angle:0};var l=(r-i)/o,c=Math.acos(l);return n>a&&(c=2*Math.PI-c),{radius:o,angle:qi(c),angleInRadian:c}},Zi=(e,t)=>{var{startAngle:r,endAngle:n}=t,i=Math.floor(r/360),a=Math.floor(n/360);return e+360*Math.min(i,a)},Ji=(e,t)=>{var{x:r,y:n}=e,{radius:i,angle:a}=Xi({x:r,y:n},t),{innerRadius:o,outerRadius:l}=t;if(i<o||i>l)return null;if(0===i)return null;var c,{startAngle:s,endAngle:u}=(e=>{var{startAngle:t,endAngle:r}=e,n=Math.floor(t/360),i=Math.floor(r/360),a=Math.min(n,i);return{startAngle:t-360*a,endAngle:r-360*a}})(t),f=a;if(s<=u){for(;f>u;)f-=360;for(;f<s;)f+=360;c=f>=s&&f<=u}else{for(;f>s;)f-=360;for(;f<u;)f+=360;c=f>=u&&f<=s}return c?Wi(Wi({},t),{},{radius:i,angle:Zi(f,t)}):null};function Qi(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ea(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Qi(Object(r),!0).forEach(function(t){ta(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Qi(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ta(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ra(e,t,r){return Te(e)||Te(t)?r:Oe(t)?de(e,t,r):"function"==typeof t?t(e):r}var na=(e,t)=>"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t,ia=(e,t,r,n)=>{if(n)return e.map(e=>e.coordinate);var i,a,o=e.map(e=>(e.coordinate===t&&(i=!0),e.coordinate===r&&(a=!0),e.coordinate));return i||o.push(t),a||o.push(r),o},aa=(e,t,r)=>{if(!e)return null;var{duplicateDomain:n,type:i,range:a,scale:o,realScaleType:l,isCategorical:c,categoricalDomain:s,tickCount:u,ticks:f,niceTicks:d,axisType:h}=e;if(!o)return null;var p="scaleBand"===l&&o.bandwidth?o.bandwidth()/2:2,y="category"===i&&o.bandwidth?o.bandwidth()/p:0;return y="angleAxis"===h&&a&&a.length>=2?2*me(a[0]-a[1])*y:y,f||d?(f||d||[]).map((e,t)=>{var r=n?n.indexOf(e):e;return{coordinate:o(r)+y,value:e,offset:y,index:t}}).filter(e=>!be(e.coordinate)):c&&s?s.map((e,t)=>({coordinate:o(e)+y,value:e,index:t,offset:y})):o.ticks&&null!=u?o.ticks(u).map((e,t)=>({coordinate:o(e)+y,value:e,offset:y,index:t})):o.domain().map((e,t)=>({coordinate:o(e)+y,value:n?n[e]:e,index:t,offset:y}))},oa=1e-4,la={sign:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0,o=0;o<t;++o){var l=be(e[o][r][1])?e[o][r][0]:e[o][r][1];l>=0?(e[o][r][0]=i,e[o][r][1]=i+l,i=e[o][r][1]):(e[o][r][0]=a,e[o][r][1]=a+l,a=e[o][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,i,a=0,o=e[0].length;a<o;++a){for(i=r=0;r<n;++r)i+=e[r][a][1]||0;if(i)for(r=0;r<n;++r)e[r][a][1]/=i}Jt(e,t)}},none:Jt,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,i=e[t[0]],a=i.length;n<a;++n){for(var o=0,l=0;o<r;++o)l+=e[o][n][1]||0;i[n][1]+=i[n][0]=-l/2}Jt(e,t)}},wiggle:function(e,t){if((i=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,i,a=0,o=1;o<n;++o){for(var l=0,c=0,s=0;l<i;++l){for(var u=e[t[l]],f=u[o][1]||0,d=(f-(u[o-1][1]||0))/2,h=0;h<l;++h){var p=e[t[h]];d+=(p[o][1]||0)-(p[o-1][1]||0)}c+=f,s+=d*f}r[o-1][1]+=r[o-1][0]=a,c&&(a-=s/c)}r[o-1][1]+=r[o-1][0]=a,Jt(e,t)}},positive:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0;a<t;++a){var o=be(e[a][r][1])?e[a][r][0]:e[a][r][1];o>=0?(e[a][r][0]=i,e[a][r][1]=i+o,i=e[a][r][1]):(e[a][r][0]=0,e[a][r][1]=0)}}},ca=(e,t,r)=>{var n=la[r],i=function(){var e=Je([]),t=Qt,r=Jt,n=er;function i(i){var a,o,l=Array.from(e.apply(this,arguments),tr),c=l.length,s=-1;for(const e of i)for(a=0,++s;a<c;++a)(l[a][s]=[0,+n(e,l[a].key,s,i)]).data=e;for(a=0,o=ft(t(l));a<c;++a)l[o[a]].index=a;return r(l,o),l}return i.keys=function(t){return arguments.length?(e="function"==typeof t?t:Je(Array.from(t)),i):e},i.value=function(e){return arguments.length?(n="function"==typeof e?e:Je(+e),i):n},i.order=function(e){return arguments.length?(t=null==e?Qt:"function"==typeof e?e:Je(Array.from(e)),i):t},i.offset=function(e){return arguments.length?(r=null==e?Jt:e,i):r},i}().keys(t).value((e,t)=>+ra(e,t,0)).order(Qt).offset(n);return i(e)};function sa(e){return null==e?void 0:String(e)}function ua(e){var{axis:t,ticks:r,bandSize:n,entry:i,index:a,dataKey:o}=e;if("category"===t.type){if(!t.allowDuplicatedCategory&&t.dataKey&&!Te(i[t.dataKey])){var l=Me(r,"value",i[t.dataKey]);if(l)return l.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var c=ra(i,Te(o)?t.dataKey:o);return Te(c)?null:t.scale(c)}var fa=e=>{var{axis:t,ticks:r,offset:n,bandSize:i,entry:a,index:o}=e;if("category"===t.type)return r[o]?r[o].coordinate+n:null;var l=ra(a,t.dataKey,t.scale.domain()[o]);return Te(l)?null:t.scale(l)-i/2+n},da=(e,t,r)=>{var n;if(null!=e)return[(n=Object.keys(e).reduce((n,i)=>{var a=e[i],{stackedData:o}=a,l=o.reduce((e,n)=>{var i,a,o=(i=n.slice(t,r+1),a=i.flat(2).filter(we),[Math.min(...a),Math.max(...a)]);return[Math.min(e[0],o[0]),Math.max(e[1],o[1])]},[1/0,-1/0]);return[Math.min(l[0],n[0]),Math.max(l[1],n[1])]},[1/0,-1/0]))[0]===1/0?0:n[0],n[1]===-1/0?0:n[1]]},ha=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,pa=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,ya=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var i=Ni(t,e=>e.coordinate),a=1/0,o=1,l=i.length;o<l;o++){var c=i[o],s=i[o-1];a=Math.min((c.coordinate||0)-(s.coordinate||0),a)}return a===1/0?0:a}return r?void 0:0};function va(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:i,name:a}=e;return ea(ea({},t),{},{dataKey:r,payload:n,value:i,name:a})}function ga(e,t){return e?String(e):"string"==typeof t?t:void 0}var ma=e=>e.layout.width,ba=e=>e.layout.height,xa=e=>e.layout.scale,wa=e=>e.layout.margin,Oa=U(e=>e.cartesianAxis.xAxis,e=>Object.values(e)),ja=U(e=>e.cartesianAxis.yAxis,e=>Object.values(e)),Aa="data-recharts-item-index",Pa="data-recharts-item-data-key";function Sa(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Ea(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Sa(Object(r),!0).forEach(function(t){ka(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Sa(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ka(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Ma=U([ma,ba,wa,e=>e.brush.height,Oa,ja,Ii,e=>e.legend.size],(e,t,r,n,i,a,o,l)=>{var c=a.reduce((e,t)=>{var{orientation:r}=t;if(!t.mirror&&!t.hide){var n="number"==typeof t.width?t.width:60;return Ea(Ea({},e),{},{[r]:e[r]+n})}return e},{left:r.left||0,right:r.right||0}),s=i.reduce((e,t)=>{var{orientation:r}=t;return t.mirror||t.hide?e:Ea(Ea({},e),{},{[r]:de(e,"".concat(r))+t.height})},{top:r.top||0,bottom:r.bottom||0}),u=Ea(Ea({},s),c),f=u.bottom;u.bottom+=n,u=((e,t,r)=>{if(t&&r){var{width:n,height:i}=r,{align:a,verticalAlign:o,layout:l}=t;if(("vertical"===l||"horizontal"===l&&"middle"===o)&&"center"!==a&&we(e[a]))return ea(ea({},e),{},{[a]:e[a]+(n||0)});if(("horizontal"===l||"vertical"===l&&"center"===a)&&"middle"!==o&&we(e[o]))return ea(ea({},e),{},{[o]:e[o]+(i||0)})}return e})(u,o,l);var d=e-u.left-u.right,h=t-u.top-u.bottom;return Ea(Ea({brushBottom:f},u),{},{width:Math.max(d,0),height:Math.max(h,0)})}),Ta=U(Ma,e=>({x:e.left,y:e.top,width:e.width,height:e.height})),Ca=U(ma,ba,(e,t)=>({x:0,y:0,width:e,height:t})),Da=u.createContext(null),Na=()=>null!=u.useContext(Da),Ia=e=>e.brush,_a=U([Ia,Ma,wa],(e,t,r)=>({height:e.height,x:we(e.x)?e.x:t.left,y:we(e.y)?e.y:t.top+t.height+t.brushBottom-((null==r?void 0:r.bottom)||0),width:we(e.width)?e.width:t.width})),Ra=()=>{var e,t=Na(),r=si(Ta),n=si(_a),i=null===(e=si(Ia))||void 0===e?void 0:e.padding;return t&&n&&i?{width:n.width-i.left-i.right,height:n.height-i.top-i.bottom,x:i.left,y:i.top}:r},La={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},za=()=>{var e;return null!==(e=si(Ma))&&void 0!==e?e:La},Ba=()=>si(ma),Ka=()=>si(ba),$a={top:0,right:0,bottom:0,left:0},Ua=e=>e.layout.layoutType,Fa=()=>si(Ua),Wa=r({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push(i(t.payload))},removeLegendPayload(e,t){var r=n(e).payload.indexOf(i(t.payload));r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:Ha,setLegendSettings:Va,addLegendPayload:qa,removeLegendPayload:Ya}=Wa.actions,Ga=Wa.reducer,Xa=["contextPayload"];function Za(){return Za=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Za.apply(null,arguments)}function Ja(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Qa(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ja(Object(r),!0).forEach(function(t){eo(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ja(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function eo(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function to(e){return e.value}function ro(e){var{contextPayload:t}=e,r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Xa),n=Vn(t,e.payloadUniqBy,to),i=Qa(Qa({},r),{},{payload:n});return u.isValidElement(e.content)?u.cloneElement(e.content,i):"function"==typeof e.content?u.createElement(e.content,i):u.createElement(pr,i)}function no(e){var t=ai();return u.useEffect(()=>{t(Va(e))},[t,e]),null}function io(e){var t=ai();return u.useEffect(()=>(t(Ha(e)),()=>{t(Ha({width:0,height:0}))}),[t,e]),null}function ao(e){var t,r=si(_i),n=u.useContext(Ze),i=null!==(t=si(e=>e.layout.margin))&&void 0!==t?t:$a,{width:a,height:o,wrapperStyle:l,portal:c}=e,[s,f]=Ri([r]),d=Ba(),h=Ka(),p=d-(i.left||0)-(i.right||0),y=oo.getWidthOrHeight(e.layout,o,a,p),v=c?l:Qa(Qa({position:"absolute",width:(null==y?void 0:y.width)||a||"auto",height:(null==y?void 0:y.height)||o||"auto"},function(e,t,r,n,i,a){var o,l,{layout:c,align:s,verticalAlign:u}=t;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(o="center"===s&&"vertical"===c?{left:((n||0)-a.width)/2}:"right"===s?{right:r&&r.right||0}:{left:r&&r.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(l="middle"===u?{top:((i||0)-a.height)/2}:"bottom"===u?{bottom:r&&r.bottom||0}:{top:r&&r.top||0}),Qa(Qa({},o),l)}(l,e,i,d,h,s)),l),g=null!=c?c:n;if(null==g)return null;var b=u.createElement("div",{className:"recharts-legend-wrapper",style:v,ref:f},u.createElement(no,{layout:e.layout,align:e.align,verticalAlign:e.verticalAlign,itemSorter:e.itemSorter}),u.createElement(io,{width:s.width,height:s.height}),u.createElement(ro,Za({},e,y,{margin:i,chartWidth:d,chartHeight:h,contextPayload:r})));return m.createPortal(b,g)}class oo extends u.PureComponent{static getWidthOrHeight(e,t,r,n){return"vertical"===e&&we(t)?{height:t}:"horizontal"===e?{width:r||n}:null}render(){return u.createElement(ao,this.props)}}function lo(){return lo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},lo.apply(null,arguments)}function co(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function so(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?co(Object(r),!0).forEach(function(t){uo(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):co(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function uo(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fo(e){return Array.isArray(e)&&Oe(e[0])&&Oe(e[1])?e.join(" ~ "):e}eo(oo,"displayName","Legend"),eo(oo,"defaultProps",{align:"center",iconSize:14,itemSorter:"value",layout:"horizontal",verticalAlign:"bottom"});var ho=e=>{var{separator:t=" : ",contentStyle:r={},itemStyle:n={},labelStyle:i={},payload:a,formatter:o,itemSorter:l,wrapperClassName:c,labelClassName:s,label:f,labelFormatter:d,accessibilityLayer:h=!1}=e,p=so({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),y=so({margin:0},i),v=!Te(f),g=v?f:"",m=q("recharts-default-tooltip",c),b=q("recharts-tooltip-label",s);v&&d&&null!=a&&(g=d(f,a));var x=h?{role:"status","aria-live":"assertive"}:{};return u.createElement("div",lo({className:m,style:p},x),u.createElement("p",{className:b,style:y},u.isValidElement(g)?g:"".concat(g)),(()=>{if(a&&a.length){var e=(l?Ni(a,l):a).map((e,r)=>{if("none"===e.type)return null;var i=e.formatter||o||fo,{value:l,name:c}=e,s=l,f=c;if(i){var d=i(l,c,e,r,a);if(Array.isArray(d))[s,f]=d;else{if(null==d)return null;s=d}}var h=so({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},n);return u.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(r),style:h},Oe(f)?u.createElement("span",{className:"recharts-tooltip-item-name"},f):null,Oe(f)?u.createElement("span",{className:"recharts-tooltip-item-separator"},t):null,u.createElement("span",{className:"recharts-tooltip-item-value"},s),u.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return u.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null})())},po="recharts-tooltip-wrapper",yo={visibility:"hidden"};function vo(e){var{coordinate:t,translateX:r,translateY:n}=e;return q(po,{["".concat(po,"-right")]:we(r)&&t&&we(t.x)&&r>=t.x,["".concat(po,"-left")]:we(r)&&t&&we(t.x)&&r<t.x,["".concat(po,"-bottom")]:we(n)&&t&&we(t.y)&&n>=t.y,["".concat(po,"-top")]:we(n)&&t&&we(t.y)&&n<t.y})}function go(e){var{allowEscapeViewBox:t,coordinate:r,key:n,offsetTopLeft:i,position:a,reverseDirection:o,tooltipDimension:l,viewBox:c,viewBoxDimension:s}=e;if(a&&we(a[n]))return a[n];var u=r[n]-l-(i>0?i:0),f=r[n]+i;if(t[n])return o[n]?u:f;var d=c[n];return null==d?0:o[n]?u<d?Math.max(f,d):Math.max(u,d):null==s?0:f+l>d+s?Math.max(u,d):Math.max(f,d)}function mo(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function bo(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mo(Object(r),!0).forEach(function(t){xo(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mo(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function xo(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class wo extends u.PureComponent{constructor(){super(...arguments),xo(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),xo(this,"handleKeyDown",e=>{var t,r,n,i;"Escape"===e.key&&this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(t=null===(r=this.props.coordinate)||void 0===r?void 0:r.x)&&void 0!==t?t:0,y:null!==(n=null===(i=this.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==n?n:0}})})}componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var e,t;this.state.dismissed&&((null===(e=this.props.coordinate)||void 0===e?void 0:e.x)===this.state.dismissedAtCoordinate.x&&(null===(t=this.props.coordinate)||void 0===t?void 0:t.y)===this.state.dismissedAtCoordinate.y||(this.state.dismissed=!1))}render(){var{active:e,allowEscapeViewBox:t,animationDuration:r,animationEasing:n,children:i,coordinate:a,hasPayload:o,isAnimationActive:l,offset:c,position:s,reverseDirection:f,useTranslate3d:d,viewBox:h,wrapperStyle:p,lastBoundingBox:y,innerRef:v,hasPortalFromProps:g}=this.props,{cssClasses:m,cssProperties:b}=function(e){var t,r,n,{allowEscapeViewBox:i,coordinate:a,offsetTopLeft:o,position:l,reverseDirection:c,tooltipBox:s,useTranslate3d:u,viewBox:f}=e;return t=s.height>0&&s.width>0&&a?function(e){var{translateX:t,translateY:r,useTranslate3d:n}=e;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}({translateX:r=go({allowEscapeViewBox:i,coordinate:a,key:"x",offsetTopLeft:o,position:l,reverseDirection:c,tooltipDimension:s.width,viewBox:f,viewBoxDimension:f.width}),translateY:n=go({allowEscapeViewBox:i,coordinate:a,key:"y",offsetTopLeft:o,position:l,reverseDirection:c,tooltipDimension:s.height,viewBox:f,viewBoxDimension:f.height}),useTranslate3d:u}):yo,{cssProperties:t,cssClasses:vo({translateX:r,translateY:n,coordinate:a})}}({allowEscapeViewBox:t,coordinate:a,offsetTopLeft:c,position:s,reverseDirection:f,tooltipBox:{height:y.height,width:y.width},useTranslate3d:d,viewBox:h}),x=g?{}:bo(bo({transition:l&&e?"transform ".concat(r,"ms ").concat(n):void 0},b),{},{pointerEvents:"none",visibility:!this.state.dismissed&&e&&o?"visible":"hidden",position:"absolute",top:0,left:0}),w=bo(bo({},x),{},{visibility:!this.state.dismissed&&e&&o?"visible":"hidden"},p);return u.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:m,style:w,ref:v},i)}}var Oo=!("undefined"!=typeof window&&window.document&&Boolean(window.document.createElement)&&window.setTimeout),jo=()=>si(e=>e.rootProps.accessibilityLayer);function Ao(e){return Number.isFinite(e)}function Po(e){return"number"==typeof e&&e>0&&Number.isFinite(e)}function So(){return So=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},So.apply(null,arguments)}function Eo(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ko(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Eo(Object(r),!0).forEach(function(t){Mo(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Eo(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Mo(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var To={curveBasisClosed:function(e){return new Bt(e)},curveBasisOpen:function(e){return new Kt(e)},curveBasis:function(e){return new zt(e)},curveBumpX:function(e){return new mt(e,!0)},curveBumpY:function(e){return new mt(e,!1)},curveLinearClosed:function(e){return new $t(e)},curveLinear:ht,curveMonotoneX:function(e){return new Vt(e)},curveMonotoneY:function(e){return new qt(e)},curveNatural:function(e){return new Gt(e)},curveStep:function(e){return new Zt(e,.5)},curveStepAfter:function(e){return new Zt(e,1)},curveStepBefore:function(e){return new Zt(e,0)}},Co=e=>Ao(e.x)&&Ao(e.y),Do=e=>e.x,No=e=>e.y,Io=e=>{var t,{type:r="linear",points:n=[],baseLine:i,layout:a,connectNulls:o=!1}=e,l=((e,t)=>{if("function"==typeof e)return e;var r="curve".concat(Ce(e));return"curveMonotone"!==r&&"curveBump"!==r||!t?To[r]||ht:To["".concat(r).concat("vertical"===t?"Y":"X")]})(r,a),c=o?n.filter(Co):n;if(Array.isArray(i)){var s=o?i.filter(e=>Co(e)):i,u=c.map((e,t)=>ko(ko({},e),{},{base:s[t]}));return(t="vertical"===a?gt().y(No).x1(Do).x0(e=>e.base.x):gt().x(Do).y1(No).y0(e=>e.base.y)).defined(Co).curve(l),t(u)}return(t="vertical"===a&&we(i)?gt().y(No).x1(Do).x0(i):we(i)?gt().x(Do).y1(No).y0(i):vt().x(Do).y(No)).defined(Co).curve(l),t(c)},_o=e=>{var{className:t,points:r,path:n,pathRef:i}=e;if(!(r&&r.length||n))return null;var a=r&&r.length?Io(e):n;return u.createElement("path",So({},We(e,!1),Re(e),{className:q("recharts-curve",t),d:null===a?void 0:a,ref:i}))},Ro=["x","y","top","left","width","height","className"];function Lo(){return Lo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Lo.apply(null,arguments)}function zo(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Bo(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Ko=(e,t,r,n,i,a)=>"M".concat(e,",").concat(i,"v").concat(n,"M").concat(a,",").concat(t,"h").concat(r),$o=e=>{var{x:t=0,y:r=0,top:n=0,left:i=0,width:a=0,height:o=0,className:l}=e,c=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?zo(Object(r),!0).forEach(function(t){Bo(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):zo(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:t,y:r,top:n,left:i,width:a,height:o},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Ro));return we(t)&&we(r)&&we(a)&&we(o)&&we(n)&&we(i)?u.createElement("path",Lo({},We(c,!0),{className:q("recharts-cross",l),d:Ko(t,r,a,o,n,i)})):null};function Uo(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Fo(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Wo(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Uo(Object(r),!0).forEach(function(t){Fo(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Uo(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e),n=t;return Object.keys(t).reduce((e,t)=>(void 0===e[t]&&void 0!==n[t]&&(e[t]=n[t]),e),r)}var Ho,Vo,qo={},Yo={},Go={};function Xo(){return Ho||(Ho=1,e=Go,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isPlainObject=function(e){if(!e||"object"!=typeof e)return!1;const t=Object.getPrototypeOf(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}),Go;var e}function Zo(){return Vo||(Vo=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Xo(),r=an(),n=cn(),i=fn(),a=Zr();function o(e,t,r,n,i,a,o){const c=o(e,t,r,n,i,a);if(void 0!==c)return c;if(typeof e==typeof t)switch(typeof e){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return e===t;case"number":return e===t||Object.is(e,t);case"object":return l(e,t,a,o)}return l(e,t,a,o)}function l(e,c,s,u){if(Object.is(e,c))return!0;let f=n.getTag(e),d=n.getTag(c);if(f===i.argumentsTag&&(f=i.objectTag),d===i.argumentsTag&&(d=i.objectTag),f!==d)return!1;switch(f){case i.stringTag:return e.toString()===c.toString();case i.numberTag:{const t=e.valueOf(),r=c.valueOf();return a.eq(t,r)}case i.booleanTag:case i.dateTag:case i.symbolTag:return Object.is(e.valueOf(),c.valueOf());case i.regexpTag:return e.source===c.source&&e.flags===c.flags;case i.functionTag:return e===c}const h=(s=s??new Map).get(e),p=s.get(c);if(null!=h&&null!=p)return h===c;s.set(e,c),s.set(c,e);try{switch(f){case i.mapTag:if(e.size!==c.size)return!1;for(const[t,r]of e.entries())if(!c.has(t)||!o(r,c.get(t),t,e,c,s,u))return!1;return!0;case i.setTag:{if(e.size!==c.size)return!1;const t=Array.from(e.values()),r=Array.from(c.values());for(let n=0;n<t.length;n++){const i=t[n],a=r.findIndex(t=>o(i,t,void 0,e,c,s,u));if(-1===a)return!1;r.splice(a,1)}return!0}case i.arrayTag:case i.uint8ArrayTag:case i.uint8ClampedArrayTag:case i.uint16ArrayTag:case i.uint32ArrayTag:case i.bigUint64ArrayTag:case i.int8ArrayTag:case i.int16ArrayTag:case i.int32ArrayTag:case i.bigInt64ArrayTag:case i.float32ArrayTag:case i.float64ArrayTag:if("undefined"!=typeof Buffer&&Buffer.isBuffer(e)!==Buffer.isBuffer(c))return!1;if(e.length!==c.length)return!1;for(let t=0;t<e.length;t++)if(!o(e[t],c[t],t,e,c,s,u))return!1;return!0;case i.arrayBufferTag:return e.byteLength===c.byteLength&&l(new Uint8Array(e),new Uint8Array(c),s,u);case i.dataViewTag:return e.byteLength===c.byteLength&&e.byteOffset===c.byteOffset&&l(new Uint8Array(e),new Uint8Array(c),s,u);case i.errorTag:return e.name===c.name&&e.message===c.message;case i.objectTag:{if(!(l(e.constructor,c.constructor,s,u)||t.isPlainObject(e)&&t.isPlainObject(c)))return!1;const n=[...Object.keys(e),...r.getSymbols(e)],i=[...Object.keys(c),...r.getSymbols(c)];if(n.length!==i.length)return!1;for(let t=0;t<n.length;t++){const r=n[t],i=e[r];if(!Object.hasOwn(c,r))return!1;if(!o(i,c[r],r,e,c,s,u))return!1}return!0}default:return!1}}finally{s.delete(e),s.delete(c)}}e.isEqualWith=function(e,t,r){return o(e,t,void 0,void 0,void 0,void 0,r)}}(Yo)),Yo}var Jo,Qo,el,tl,rl={};function nl(){return Jo||(Jo=1,e=rl,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.noop=function(){}),rl;var e}function il(){return tl?el:(tl=1,el=(Qo||(Qo=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Zo(),r=nl();e.isEqual=function(e,n){return t.isEqualWith(e,n,r.noop)}}(qo)),qo).isEqual)}const al=I(il());var ol=1e-4,ll=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],cl=(e,t)=>e.map((e,r)=>e*t**r).reduce((e,t)=>e+t),sl=(e,t)=>r=>{var n=ll(e,t);return cl(n,r)},ul=function(){for(var e,t,r,n,i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];if(1===a.length)switch(a[0]){case"linear":[e,r,t,n]=[0,0,1,1];break;case"ease":[e,r,t,n]=[.25,.1,.25,1];break;case"ease-in":[e,r,t,n]=[.42,0,1,1];break;case"ease-out":[e,r,t,n]=[.42,0,.58,1];break;case"ease-in-out":[e,r,t,n]=[0,0,.58,1];break;default:var l=a[0].split("(");"cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length&&([e,r,t,n]=l[1].split(")")[0].split(",").map(e=>parseFloat(e)))}else 4===a.length&&([e,r,t,n]=a);var c,s,u=sl(e,t),f=sl(r,n),d=(c=e,s=t,e=>{var t=[...ll(c,s).map((e,t)=>e*t).slice(1),0];return cl(t,e)}),h=e=>e>1?1:e<0?0:e,p=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var i=u(r)-t,a=d(r);if(Math.abs(i-t)<ol||a<ol)return f(r);r=h(r-i/a)}return f(r)};return p.isStepper=!1,p},fl=e=>{if("string"==typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return ul(e);case"spring":return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,i=(e,i,a)=>{var o=a+(-(e-i)*t-a*r)*n/1e3,l=a*n/1e3+e;return Math.abs(l-i)<ol&&Math.abs(o)<ol?[i,0]:[l,o]};return i.isStepper=!0,i.dt=n,i}();default:if("cubic-bezier"===e.split("(")[0])return ul(e)}return"function"==typeof e?e:null};function dl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hl(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dl(Object(r),!0).forEach(function(t){pl(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dl(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pl(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var yl=(e,t)=>Object.keys(t).reduce((r,n)=>hl(hl({},r),{},{[n]:e(n,t[n])}),{});function vl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gl(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vl(Object(r),!0).forEach(function(t){ml(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vl(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ml(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var bl=(e,t,r)=>e+(t-e)*r,xl=e=>{var{from:t,to:r}=e;return t!==r},wl=(e,t,r)=>{var n=yl((t,r)=>{if(xl(r)){var[n,i]=e(r.from,r.to,r.velocity);return gl(gl({},r),{},{from:n,velocity:i})}return r},t);return r<1?yl((e,t)=>xl(t)?gl(gl({},t),{},{velocity:bl(t.velocity,n[e].velocity,r),from:bl(t.from,n[e].from,r)}):t,t):wl(e,n,r-1)};function Ol(e,t,r,n,i,a){var o,l=n.reduce((r,n)=>gl(gl({},r),{},{[n]:{from:e[n],velocity:0,to:t[n]}}),{}),c=null,s=n=>{o||(o=n);var u=(n-o)/r.dt;l=wl(r,l,u),i(gl(gl(gl({},e),t),yl((e,t)=>t.from,l))),o=n,Object.values(l).filter(xl).length&&(c=a.setTimeout(s))};return()=>(c=a.setTimeout(s),()=>{c()})}const jl=(e,t,r,n,i,a)=>{var o,l,c=(o=e,l=t,[Object.keys(o),Object.keys(l)].reduce((e,t)=>e.filter(e=>t.includes(e))));return!0===r.isStepper?Ol(e,t,r,c,i,a):function(e,t,r,n,i,a,o){var l,c=null,s=i.reduce((r,n)=>gl(gl({},r),{},{[n]:[e[n],t[n]]}),{}),u=i=>{l||(l=i);var f=(i-l)/n,d=yl((e,t)=>bl(...t,r(f)),s);if(a(gl(gl(gl({},e),t),d)),f<1)c=o.setTimeout(u);else{var h=yl((e,t)=>bl(...t,r(1)),s);a(gl(gl(gl({},e),t),h))}};return()=>(c=o.setTimeout(u),()=>{c()})}(e,t,r,n,c,i,a)};class Al{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=a=>{a-r>=t?e(a):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}var Pl=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function Sl(){return Sl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Sl.apply(null,arguments)}function El(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function kl(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?El(Object(r),!0).forEach(function(t){Ml(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):El(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Ml(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Tl(){return e=new Al,t=()=>null,r=!1,n=null,i=a=>{if(!r){if(Array.isArray(a)){if(!a.length)return;var o=a,[l,...c]=o;return"number"==typeof l?void(n=e.setTimeout(i.bind(null,c),l)):(i(l),void(n=e.setTimeout(i.bind(null,c))))}"object"==typeof a&&t(a),"function"==typeof a&&a()}},{stop:()=>{r=!0},start:e=>{r=!1,n&&(n(),n=null),i(e)},subscribe:e=>(t=e,()=>{t=()=>null}),getTimeoutController:()=>e};var e,t,r,n,i}class Cl extends u.PureComponent{constructor(e,t){super(e,t),Ml(this,"mounted",!1),Ml(this,"manager",null),Ml(this,"stopJSAnimation",null),Ml(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:a,children:o,duration:l,animationManager:c}=this.props;if(this.manager=c,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0)return this.state={style:{}},void("function"==typeof o&&(this.state={style:a}));if(i){if("function"==typeof o)return void(this.state={style:i});this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:i,to:a,from:o}=this.props,{style:l}=this.state;if(r)if(t){if(!(al(e.to,a)&&e.canBegin&&e.isActive)){var c=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=c||i?o:e.to;if(this.state&&l){var u={style:n?{[n]:s}:s};(n&&l[n]!==s||!n&&l!==s)&&this.setState(u)}this.runAnimation(kl(kl({},this.props),{},{from:s,begin:0}))}}else{var f={style:n?{[n]:a}:a};this.state&&l&&(n&&l[n]!==a||!n&&l!==a)&&this.setState(f)}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var{from:t,to:r,duration:n,easing:i,begin:a,onAnimationEnd:o,onAnimationStart:l}=e,c=jl(t,r,fl(i),n,this.changeStyle,this.manager.getTimeoutController());this.manager.start([l,a,()=>{this.stopJSAnimation=c()},n,o])}runAnimation(e){var{begin:t,duration:r,attributeName:n,to:i,easing:a,onAnimationStart:o,onAnimationEnd:l,children:c}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"!=typeof a&&"function"!=typeof c&&"spring"!==a){var s=n?{[n]:i}:i,u=((e,t,r)=>e.map(e=>{return"".concat((n=e,n.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase())))," ").concat(t,"ms ").concat(r);var n}).join(","))(Object.keys(s),r,a);this.manager.start([o,t,kl(kl({},s),{},{transition:u}),r,l])}else this.runJSAnimation(e)}render(){var e=this.props,{children:t,begin:r,duration:n,attributeName:i,easing:a,isActive:o,from:l,to:c,canBegin:s,onAnimationEnd:f,shouldReAnimate:d,onAnimationReStart:h,animationManager:p}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Pl),v=u.Children.count(t),g=this.state.style;if("function"==typeof t)return t(g);if(!o||0===v||n<=0)return t;var m=e=>{var{style:t={},className:r}=e.props;return u.cloneElement(e,kl(kl({},y),{},{style:kl(kl({},t),g),className:r}))};return 1===v?m(u.Children.only(t)):u.createElement("div",null,u.Children.map(t,e=>m(e)))}}Ml(Cl,"displayName","Animate"),Ml(Cl,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var Dl=u.createContext(null);function Nl(e){var t,r,n=u.useContext(Dl);return u.createElement(Cl,Sl({},e,{animationManager:null!==(t=null!==(r=e.animationManager)&&void 0!==r?r:n)&&void 0!==t?t:Tl()}))}function Il(){return Il=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Il.apply(null,arguments)}var _l=(e,t,r,n,i)=>{var a,o=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,c=r>=0?1:-1,s=n>=0&&r>=0||n<0&&r<0?1:0;if(o>0&&i instanceof Array){for(var u=[0,0,0,0],f=0;f<4;f++)u[f]=i[f]>o?o:i[f];a="M".concat(e,",").concat(t+l*u[0]),u[0]>0&&(a+="A ".concat(u[0],",").concat(u[0],",0,0,").concat(s,",").concat(e+c*u[0],",").concat(t)),a+="L ".concat(e+r-c*u[1],",").concat(t),u[1]>0&&(a+="A ".concat(u[1],",").concat(u[1],",0,0,").concat(s,",\n        ").concat(e+r,",").concat(t+l*u[1])),a+="L ".concat(e+r,",").concat(t+n-l*u[2]),u[2]>0&&(a+="A ".concat(u[2],",").concat(u[2],",0,0,").concat(s,",\n        ").concat(e+r-c*u[2],",").concat(t+n)),a+="L ".concat(e+c*u[3],",").concat(t+n),u[3]>0&&(a+="A ".concat(u[3],",").concat(u[3],",0,0,").concat(s,",\n        ").concat(e,",").concat(t+n-l*u[3])),a+="Z"}else if(o>0&&i===+i&&i>0){var d=Math.min(o,i);a="M ".concat(e,",").concat(t+l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e+c*d,",").concat(t,"\n            L ").concat(e+r-c*d,",").concat(t,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e+r,",").concat(t+l*d,"\n            L ").concat(e+r,",").concat(t+n-l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e+r-c*d,",").concat(t+n,"\n            L ").concat(e+c*d,",").concat(t+n,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e,",").concat(t+n-l*d," Z")}else a="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return a},Rl={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},Ll=e=>{var t=Wo(e,Rl),r=u.useRef(null),[n,i]=u.useState(-1);u.useEffect(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&i(e)}catch(t){}},[]);var{x:a,y:o,width:l,height:c,radius:s,className:f}=t,{animationEasing:d,animationDuration:h,animationBegin:p,isAnimationActive:y,isUpdateAnimationActive:v}=t;if(a!==+a||o!==+o||l!==+l||c!==+c||0===l||0===c)return null;var g=q("recharts-rectangle",f);return v?u.createElement(Nl,{canBegin:n>0,from:{width:l,height:c,x:a,y:o},to:{width:l,height:c,x:a,y:o},duration:h,animationEasing:d,isActive:v},e=>{var{width:i,height:a,x:o,y:l}=e;return u.createElement(Nl,{canBegin:n>0,from:"0px ".concat(-1===n?1:n,"px"),to:"".concat(n,"px 0px"),attributeName:"strokeDasharray",begin:p,duration:h,isActive:y,easing:d},u.createElement("path",Il({},We(t,!0),{className:g,d:_l(o,l,i,a,s),ref:r})))}):u.createElement("path",Il({},We(t,!0),{className:g,d:_l(a,o,l,c,s)}))};function zl(e){var{cx:t,cy:r,radius:n,startAngle:i,endAngle:a}=e;return{points:[Yi(t,r,n,i),Yi(t,r,n,a)],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function Bl(){return Bl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Bl.apply(null,arguments)}var Kl=e=>{var{cx:t,cy:r,radius:n,angle:i,sign:a,isExternal:o,cornerRadius:l,cornerIsExternal:c}=e,s=l*(o?1:-1)+n,u=Math.asin(l/s)/Vi,f=c?i:i+a*u,d=c?i-a*u:i;return{center:Yi(t,r,s,f),circleTangency:Yi(t,r,n,f),lineTangency:Yi(t,r,s*Math.cos(u*Vi),d),theta:u}},$l=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:a,endAngle:o}=e,l=((e,t)=>me(t-e)*Math.min(Math.abs(t-e),359.999))(a,o),c=a+l,s=Yi(t,r,i,a),u=Yi(t,r,i,c),f="M ".concat(s.x,",").concat(s.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(a>c),",\n    ").concat(u.x,",").concat(u.y,"\n  ");if(n>0){var d=Yi(t,r,n,a),h=Yi(t,r,n,c);f+="L ".concat(h.x,",").concat(h.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(a<=c),",\n            ").concat(d.x,",").concat(d.y," Z")}else f+="L ".concat(t,",").concat(r," Z");return f},Ul={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},Fl=e=>{var t=Wo(e,Ul),{cx:r,cy:n,innerRadius:i,outerRadius:a,cornerRadius:o,forceCornerRadius:l,cornerIsExternal:c,startAngle:s,endAngle:f,className:d}=t;if(a<i||s===f)return null;var h,p=q("recharts-sector",d),y=a-i,v=Pe(o,y,0,!0);return h=v>0&&Math.abs(s-f)<360?(e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,cornerRadius:a,forceCornerRadius:o,cornerIsExternal:l,startAngle:c,endAngle:s}=e,u=me(s-c),{circleTangency:f,lineTangency:d,theta:h}=Kl({cx:t,cy:r,radius:i,angle:c,sign:u,cornerRadius:a,cornerIsExternal:l}),{circleTangency:p,lineTangency:y,theta:v}=Kl({cx:t,cy:r,radius:i,angle:s,sign:-u,cornerRadius:a,cornerIsExternal:l}),g=l?Math.abs(c-s):Math.abs(c-s)-h-v;if(g<0)return o?"M ".concat(d.x,",").concat(d.y,"\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*a,",0\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*-a,",0\n      "):$l({cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:c,endAngle:s});var m="M ".concat(d.x,",").concat(d.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(u<0),",").concat(f.x,",").concat(f.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(g>180),",").concat(+(u<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(u<0),",").concat(y.x,",").concat(y.y,"\n  ");if(n>0){var{circleTangency:b,lineTangency:x,theta:w}=Kl({cx:t,cy:r,radius:n,angle:c,sign:u,isExternal:!0,cornerRadius:a,cornerIsExternal:l}),{circleTangency:O,lineTangency:j,theta:A}=Kl({cx:t,cy:r,radius:n,angle:s,sign:-u,isExternal:!0,cornerRadius:a,cornerIsExternal:l}),P=l?Math.abs(c-s):Math.abs(c-s)-w-A;if(P<0&&0===a)return"".concat(m,"L").concat(t,",").concat(r,"Z");m+="L".concat(j.x,",").concat(j.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(u<0),",").concat(O.x,",").concat(O.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(P>180),",").concat(+(u>0),",").concat(b.x,",").concat(b.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(u<0),",").concat(x.x,",").concat(x.y,"Z")}else m+="L".concat(t,",").concat(r,"Z");return m})({cx:r,cy:n,innerRadius:i,outerRadius:a,cornerRadius:Math.min(v,y/2),forceCornerRadius:l,cornerIsExternal:c,startAngle:s,endAngle:f}):$l({cx:r,cy:n,innerRadius:i,outerRadius:a,startAngle:s,endAngle:f}),u.createElement("path",Bl({},We(t,!0),{className:p,d:h}))};function Wl(e,t,r){var n,i,a,o;if("horizontal"===e)a=n=t.x,i=r.top,o=r.top+r.height;else if("vertical"===e)o=i=t.y,n=r.left,a=r.left+r.width;else if(null!=t.cx&&null!=t.cy){if("centric"!==e)return zl(t);var{cx:l,cy:c,innerRadius:s,outerRadius:u,angle:f}=t,d=Yi(l,c,s,f),h=Yi(l,c,u,f);n=d.x,i=d.y,a=h.x,o=h.y}return[{x:n,y:i},{x:a,y:o}]}var Hl,Vl,ql,Yl,Gl,Xl={},Zl={},Jl={};function Ql(){return Hl||(Hl=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=xi();e.toNumber=function(e){return t.isSymbol(e)?NaN:Number(e)}}(Jl)),Jl}function ec(){return Vl||(Vl=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Ql();e.toFinite=function(e){if(!e)return 0===e?e:0;if((e=t.toNumber(e))===1/0||e===-1/0){return(e<0?-1:1)*Number.MAX_VALUE}return e==e?e:0}}(Zl)),Zl}function tc(){return Gl?Yl:(Gl=1,Yl=(ql||(ql=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Ci(),r=ec();e.range=function(e,n,i){i&&"number"!=typeof i&&t.isIterateeCall(e,n,i)&&(n=i=void 0),e=r.toFinite(e),void 0===n?(n=e,e=0):n=r.toFinite(n),i=void 0===i?e<n?1:-1:r.toFinite(i);const a=Math.max(Math.ceil((n-e)/(i||1)),0),o=new Array(a);for(let t=0;t<a;t++)o[t]=e,e+=i;return o}}(Xl)),Xl).range)}const rc=I(tc());function nc(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function ic(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function ac(e){let t,r,n;function i(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{const t=i+a>>>1;r(e[t],n)<0?i=t+1:a=t}while(i<a)}return i}return 2!==e.length?(t=nc,r=(t,r)=>nc(e(t),r),n=(t,r)=>e(t)-r):(t=e===nc||e===ic?e:oc,r=e,n=e),{left:i,center:function(e,t,r=0,a=e.length){const o=i(e,t,r,a-1);return o>r&&n(e[o-1],t)>-n(e[o],t)?o-1:o},right:function(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{const t=i+a>>>1;r(e[t],n)<=0?i=t+1:a=t}while(i<a)}return i}}}function oc(){return 0}function lc(e){return null===e?NaN:+e}const cc=ac(nc).right;ac(lc).center;class sc extends Map{constructor(e,t=fc){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(const[r,n]of e)this.set(r,n)}get(e){return super.get(uc(this,e))}has(e){return super.has(uc(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){const n=t(r);e.has(n)&&(r=e.get(n),e.delete(n));return r}(this,e))}}function uc({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):r}function fc(e){return null!==e&&"object"==typeof e?e.valueOf():e}function dc(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:e>t?1:0)}const hc=Math.sqrt(50),pc=Math.sqrt(10),yc=Math.sqrt(2);function vc(e,t,r){const n=(t-e)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=hc?10:a>=pc?5:a>=yc?2:1;let l,c,s;return i<0?(s=Math.pow(10,-i)/o,l=Math.round(e*s),c=Math.round(t*s),l/s<e&&++l,c/s>t&&--c,s=-s):(s=Math.pow(10,i)*o,l=Math.round(e/s),c=Math.round(t/s),l*s<e&&++l,c*s>t&&--c),c<l&&.5<=r&&r<2?vc(e,t,2*r):[l,c,s]}function gc(e,t,r){if(!((r=+r)>0))return[];if((e=+e)===(t=+t))return[e];const n=t<e,[i,a,o]=n?vc(t,e,r):vc(e,t,r);if(!(a>=i))return[];const l=a-i+1,c=new Array(l);if(n)if(o<0)for(let s=0;s<l;++s)c[s]=(a-s)/-o;else for(let s=0;s<l;++s)c[s]=(a-s)*o;else if(o<0)for(let s=0;s<l;++s)c[s]=(i+s)/-o;else for(let s=0;s<l;++s)c[s]=(i+s)*o;return c}function mc(e,t,r){return vc(e=+e,t=+t,r=+r)[2]}function bc(e,t,r){r=+r;const n=(t=+t)<(e=+e),i=n?mc(t,e,r):mc(e,t,r);return(n?-1:1)*(i<0?1/-i:i)}function xc(e,t){let r;for(const n of e)null!=n&&(r<n||void 0===r&&n>=n)&&(r=n);return r}function wc(e,t){let r;for(const n of e)null!=n&&(r>n||void 0===r&&n>=n)&&(r=n);return r}function Oc(e,t,r=0,n=1/0,i){if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(i=void 0===i?dc:function(e=nc){if(e===nc)return dc;if("function"!=typeof e)throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(i);n>r;){if(n-r>600){const a=n-r+1,o=t-r+1,l=Math.log(a),c=.5*Math.exp(2*l/3),s=.5*Math.sqrt(l*c*(a-c)/a)*(o-a/2<0?-1:1);Oc(e,t,Math.max(r,Math.floor(t-o*c/a+s)),Math.min(n,Math.floor(t+(a-o)*c/a+s)),i)}const a=e[t];let o=r,l=n;for(jc(e,r,t),i(e[n],a)>0&&jc(e,r,n);o<l;){for(jc(e,o,l),++o,--l;i(e[o],a)<0;)++o;for(;i(e[l],a)>0;)--l}0===i(e[r],a)?jc(e,r,l):(++l,jc(e,l,n)),l<=t&&(r=l+1),t<=l&&(n=l-1)}return e}function jc(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function Ac(e,t,r=lc){if((n=e.length)&&!isNaN(t=+t)){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e);return o+(+r(e[a+1],a+1,e)-o)*(i-a)}}function Pc(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function Sc(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}const Ec=Symbol("implicit");function kc(){var e=new sc,t=[],r=[],n=Ec;function i(i){let a=e.get(i);if(void 0===a){if(n!==Ec)return n;e.set(i,a=t.push(i)-1)}return r[a%r.length]}return i.domain=function(r){if(!arguments.length)return t.slice();t=[],e=new sc;for(const n of r)e.has(n)||e.set(n,t.push(n)-1);return i},i.range=function(e){return arguments.length?(r=Array.from(e),i):r.slice()},i.unknown=function(e){return arguments.length?(n=e,i):n},i.copy=function(){return kc(t,r).unknown(n)},Pc.apply(i,arguments),i}function Mc(){var e,t,r=kc().unknown(void 0),n=r.domain,i=r.range,a=0,o=1,l=!1,c=0,s=0,u=.5;function f(){var r=n().length,f=o<a,d=f?o:a,h=f?a:o;e=(h-d)/Math.max(1,r-c+2*s),l&&(e=Math.floor(e)),d+=(h-d-e*(r-c))*u,t=e*(1-c),l&&(d=Math.round(d),t=Math.round(t));var p=function(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((t-e)/r)),a=new Array(i);++n<i;)a[n]=e+n*r;return a}(r).map(function(t){return d+e*t});return i(f?p.reverse():p)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([a,o]=e,a=+a,o=+o,f()):[a,o]},r.rangeRound=function(e){return[a,o]=e,a=+a,o=+o,l=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(l=!!e,f()):l},r.padding=function(e){return arguments.length?(c=Math.min(1,s=+e),f()):c},r.paddingInner=function(e){return arguments.length?(c=Math.min(1,e),f()):c},r.paddingOuter=function(e){return arguments.length?(s=+e,f()):s},r.align=function(e){return arguments.length?(u=Math.max(0,Math.min(1,e)),f()):u},r.copy=function(){return Mc(n(),[a,o]).round(l).paddingInner(c).paddingOuter(s).align(u)},Pc.apply(f(),arguments)}function Tc(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return Tc(t())},e}function Cc(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function Dc(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function Nc(){}var Ic=.7,_c=1/Ic,Rc="\\s*([+-]?\\d+)\\s*",Lc="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",zc="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Bc=/^#([0-9a-f]{3,8})$/,Kc=new RegExp(`^rgb\\(${Rc},${Rc},${Rc}\\)$`),$c=new RegExp(`^rgb\\(${zc},${zc},${zc}\\)$`),Uc=new RegExp(`^rgba\\(${Rc},${Rc},${Rc},${Lc}\\)$`),Fc=new RegExp(`^rgba\\(${zc},${zc},${zc},${Lc}\\)$`),Wc=new RegExp(`^hsl\\(${Lc},${zc},${zc}\\)$`),Hc=new RegExp(`^hsla\\(${Lc},${zc},${zc},${Lc}\\)$`),Vc={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function qc(){return this.rgb().formatHex()}function Yc(){return this.rgb().formatRgb()}function Gc(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=Bc.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?Xc(t):3===r?new Qc(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?Zc(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?Zc(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=Kc.exec(e))?new Qc(t[1],t[2],t[3],1):(t=$c.exec(e))?new Qc(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=Uc.exec(e))?Zc(t[1],t[2],t[3],t[4]):(t=Fc.exec(e))?Zc(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=Wc.exec(e))?as(t[1],t[2]/100,t[3]/100,1):(t=Hc.exec(e))?as(t[1],t[2]/100,t[3]/100,t[4]):Vc.hasOwnProperty(e)?Xc(Vc[e]):"transparent"===e?new Qc(NaN,NaN,NaN,0):null}function Xc(e){return new Qc(e>>16&255,e>>8&255,255&e,1)}function Zc(e,t,r,n){return n<=0&&(e=t=r=NaN),new Qc(e,t,r,n)}function Jc(e,t,r,n){return 1===arguments.length?((i=e)instanceof Nc||(i=Gc(i)),i?new Qc((i=i.rgb()).r,i.g,i.b,i.opacity):new Qc):new Qc(e,t,r,null==n?1:n);var i}function Qc(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function es(){return`#${is(this.r)}${is(this.g)}${is(this.b)}`}function ts(){const e=rs(this.opacity);return`${1===e?"rgb(":"rgba("}${ns(this.r)}, ${ns(this.g)}, ${ns(this.b)}${1===e?")":`, ${e})`}`}function rs(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function ns(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function is(e){return((e=ns(e))<16?"0":"")+e.toString(16)}function as(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new ls(e,t,r,n)}function os(e){if(e instanceof ls)return new ls(e.h,e.s,e.l,e.opacity);if(e instanceof Nc||(e=Gc(e)),!e)return new ls;if(e instanceof ls)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,l=a-i,c=(a+i)/2;return l?(o=t===a?(r-n)/l+6*(r<n):r===a?(n-t)/l+2:(t-r)/l+4,l/=c<.5?a+i:2-a-i,o*=60):l=c>0&&c<1?0:o,new ls(o,l,c,e.opacity)}function ls(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function cs(e){return(e=(e||0)%360)<0?e+360:e}function ss(e){return Math.max(0,Math.min(1,e||0))}function us(e,t,r){return 255*(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)}Cc(Nc,Gc,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:qc,formatHex:qc,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return os(this).formatHsl()},formatRgb:Yc,toString:Yc}),Cc(Qc,Jc,Dc(Nc,{brighter(e){return e=null==e?_c:Math.pow(_c,e),new Qc(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?Ic:Math.pow(Ic,e),new Qc(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Qc(ns(this.r),ns(this.g),ns(this.b),rs(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:es,formatHex:es,formatHex8:function(){return`#${is(this.r)}${is(this.g)}${is(this.b)}${is(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:ts,toString:ts})),Cc(ls,function(e,t,r,n){return 1===arguments.length?os(e):new ls(e,t,r,null==n?1:n)},Dc(Nc,{brighter(e){return e=null==e?_c:Math.pow(_c,e),new ls(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?Ic:Math.pow(Ic,e),new ls(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+360*(this.h<0),t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new Qc(us(e>=240?e-240:e+120,i,n),us(e,i,n),us(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new ls(cs(this.h),ss(this.s),ss(this.l),rs(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=rs(this.opacity);return`${1===e?"hsl(":"hsla("}${cs(this.h)}, ${100*ss(this.s)}%, ${100*ss(this.l)}%${1===e?")":`, ${e})`}`}}));const fs=e=>()=>e;function ds(e){return 1===(e=+e)?hs:function(t,r){return r-t?function(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}(t,r,e):fs(isNaN(t)?r:t)}}function hs(e,t){var r=t-e;return r?function(e,t){return function(r){return e+r*t}}(e,r):fs(isNaN(e)?t:e)}const ps=function e(t){var r=ds(t);function n(e,t){var n=r((e=Jc(e)).r,(t=Jc(t)).r),i=r(e.g,t.g),a=r(e.b,t.b),o=hs(e.opacity,t.opacity);return function(t){return e.r=n(t),e.g=i(t),e.b=a(t),e.opacity=o(t),e+""}}return n.gamma=e,n}(1);function ys(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,i=t.slice();return function(a){for(r=0;r<n;++r)i[r]=e[r]*(1-a)+t[r]*a;return i}}function vs(e,t){var r,n=t?t.length:0,i=e?Math.min(n,e.length):0,a=new Array(i),o=new Array(n);for(r=0;r<i;++r)a[r]=js(e[r],t[r]);for(;r<n;++r)o[r]=t[r];return function(e){for(r=0;r<i;++r)o[r]=a[r](e);return o}}function gs(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function ms(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function bs(e,t){var r,n={},i={};for(r in null!==e&&"object"==typeof e||(e={}),null!==t&&"object"==typeof t||(t={}),t)r in e?n[r]=js(e[r],t[r]):i[r]=t[r];return function(e){for(r in n)i[r]=n[r](e);return i}}var xs=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ws=new RegExp(xs.source,"g");function Os(e,t){var r,n,i,a=xs.lastIndex=ws.lastIndex=0,o=-1,l=[],c=[];for(e+="",t+="";(r=xs.exec(e))&&(n=ws.exec(t));)(i=n.index)>a&&(i=t.slice(a,i),l[o]?l[o]+=i:l[++o]=i),(r=r[0])===(n=n[0])?l[o]?l[o]+=n:l[++o]=n:(l[++o]=null,c.push({i:o,x:ms(r,n)})),a=ws.lastIndex;return a<t.length&&(i=t.slice(a),l[o]?l[o]+=i:l[++o]=i),l.length<2?c[0]?function(e){return function(t){return e(t)+""}}(c[0].x):function(e){return function(){return e}}(t):(t=c.length,function(e){for(var r,n=0;n<t;++n)l[(r=c[n]).i]=r.x(e);return l.join("")})}function js(e,t){var r,n,i=typeof t;return null==t||"boolean"===i?fs(t):("number"===i?ms:"string"===i?(r=Gc(t))?(t=r,ps):Os:t instanceof Gc?ps:t instanceof Date?gs:(n=t,!ArrayBuffer.isView(n)||n instanceof DataView?Array.isArray(t)?vs:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?bs:ms:ys))(e,t)}function As(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function Ps(e){return+e}var Ss=[0,1];function Es(e){return e}function ks(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r});var r}function Ms(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=ks(i,n),a=r(o,a)):(n=ks(n,i),a=r(a,o)),function(e){return a(n(e))}}function Ts(e,t,r){var n=Math.min(e.length,t.length)-1,i=new Array(n),a=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=ks(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(t){var r=cc(e,t,1,n)-1;return a[r](i[r](t))}}function Cs(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function Ds(){var e,t,r,n,i,a,o=Ss,l=Ss,c=js,s=Es;function u(){var e,t,r,c=Math.min(o.length,l.length);return s!==Es&&(e=o[0],t=o[c-1],e>t&&(r=e,e=t,t=r),s=function(r){return Math.max(e,Math.min(t,r))}),n=c>2?Ts:Ms,i=a=null,f}function f(t){return null==t||isNaN(t=+t)?r:(i||(i=n(o.map(e),l,c)))(e(s(t)))}return f.invert=function(r){return s(t((a||(a=n(l,o.map(e),ms)))(r)))},f.domain=function(e){return arguments.length?(o=Array.from(e,Ps),u()):o.slice()},f.range=function(e){return arguments.length?(l=Array.from(e),u()):l.slice()},f.rangeRound=function(e){return l=Array.from(e),c=As,u()},f.clamp=function(e){return arguments.length?(s=!!e||Es,u()):s!==Es},f.interpolate=function(e){return arguments.length?(c=e,u()):c},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,u()}}function Ns(){return Ds()(Es,Es)}function Is(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function _s(e){return(e=Is(Math.abs(e)))?e[1]:NaN}var Rs,Ls=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function zs(e){if(!(t=Ls.exec(e)))throw new Error("invalid format: "+e);var t;return new Bs({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function Bs(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function Ks(e,t){var r=Is(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}zs.prototype=Bs.prototype,Bs.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};const $s={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>Ks(100*e,t),r:Ks,s:function(e,t){var r=Is(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(Rs=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+Is(e,Math.max(0,t+a-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function Us(e){return e}var Fs,Ws,Hs,Vs=Array.prototype.map,qs=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function Ys(e){var t,r,n=void 0===e.grouping||void 0===e.thousands?Us:(t=Vs.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var i=e.length,a=[],o=0,l=t[0],c=0;i>0&&l>0&&(c+l+1>n&&(l=Math.max(1,n-c)),a.push(e.substring(i-=l,i+l)),!((c+=l+1)>n));)l=t[o=(o+1)%t.length];return a.reverse().join(r)}),i=void 0===e.currency?"":e.currency[0]+"",a=void 0===e.currency?"":e.currency[1]+"",o=void 0===e.decimal?".":e.decimal+"",l=void 0===e.numerals?Us:function(e){return function(t){return t.replace(/[0-9]/g,function(t){return e[+t]})}}(Vs.call(e.numerals,String)),c=void 0===e.percent?"%":e.percent+"",s=void 0===e.minus?"−":e.minus+"",u=void 0===e.nan?"NaN":e.nan+"";function f(e){var t=(e=zs(e)).fill,r=e.align,f=e.sign,d=e.symbol,h=e.zero,p=e.width,y=e.comma,v=e.precision,g=e.trim,m=e.type;"n"===m?(y=!0,m="g"):$s[m]||(void 0===v&&(v=12),g=!0,m="g"),(h||"0"===t&&"="===r)&&(h=!0,t="0",r="=");var b="$"===d?i:"#"===d&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",x="$"===d?a:/[%p]/.test(m)?c:"",w=$s[m],O=/[defgprs%]/.test(m);function j(e){var i,a,c,d=b,j=x;if("c"===m)j=w(e)+j,e="";else{var A=(e=+e)<0||1/e<0;if(e=isNaN(e)?u:w(Math.abs(e),v),g&&(e=function(e){e:for(var t,r=e.length,n=1,i=-1;n<r;++n)switch(e[n]){case".":i=t=n;break;case"0":0===i&&(i=n),t=n;break;default:if(!+e[n])break e;i>0&&(i=0)}return i>0?e.slice(0,i)+e.slice(t+1):e}(e)),A&&0===+e&&"+"!==f&&(A=!1),d=(A?"("===f?f:s:"-"===f||"("===f?"":f)+d,j=("s"===m?qs[8+Rs/3]:"")+j+(A&&"("===f?")":""),O)for(i=-1,a=e.length;++i<a;)if(48>(c=e.charCodeAt(i))||c>57){j=(46===c?o+e.slice(i+1):e.slice(i))+j,e=e.slice(0,i);break}}y&&!h&&(e=n(e,1/0));var P=d.length+e.length+j.length,S=P<p?new Array(p-P+1).join(t):"";switch(y&&h&&(e=n(S+e,S.length?p-j.length:1/0),S=""),r){case"<":e=d+e+j+S;break;case"=":e=d+S+e+j;break;case"^":e=S.slice(0,P=S.length>>1)+d+e+j+S.slice(P);break;default:e=S+d+e+j}return l(e)}return v=void 0===v?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return e+""},j}return{format:f,formatPrefix:function(e,t){var r=f(((e=zs(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(_s(t)/3))),i=Math.pow(10,-n),a=qs[8+n/3];return function(e){return r(i*e)+a}}}}function Gs(e,t,r,n){var i,a=bc(e,t,r);switch((n=zs(null==n?",f":n)).type){case"s":var o=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(i=function(e,t){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(_s(t)/3)))-_s(Math.abs(e)))}(a,o))||(n.precision=i),Hs(n,o);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(i=function(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,_s(t)-_s(e))+1}(a,Math.max(Math.abs(e),Math.abs(t))))||(n.precision=i-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(i=function(e){return Math.max(0,-_s(Math.abs(e)))}(a))||(n.precision=i-2*("%"===n.type))}return Ws(n)}function Xs(e){var t=e.domain;return e.ticks=function(e){var r=t();return gc(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return Gs(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,i,a=t(),o=0,l=a.length-1,c=a[o],s=a[l],u=10;for(s<c&&(i=c,c=s,s=i,i=o,o=l,l=i);u-- >0;){if((i=mc(c,s,r))===n)return a[o]=c,a[l]=s,t(a);if(i>0)c=Math.floor(c/i)*i,s=Math.ceil(s/i)*i;else{if(!(i<0))break;c=Math.ceil(c*i)/i,s=Math.floor(s*i)/i}n=i}return e},e}function Zs(e,t){var r,n=0,i=(e=e.slice()).length-1,a=e[n],o=e[i];return o<a&&(r=n,n=i,i=r,r=a,a=o,o=r),e[n]=t.floor(a),e[i]=t.ceil(o),e}function Js(e){return Math.log(e)}function Qs(e){return Math.exp(e)}function eu(e){return-Math.log(-e)}function tu(e){return-Math.exp(-e)}function ru(e){return isFinite(e)?+("1e"+e):e<0?0:e}function nu(e){return(t,r)=>-e(-t,r)}function iu(e){const t=e(Js,Qs),r=t.domain;let n,i,a=10;function o(){return n=function(e){return e===Math.E?Math.log:10===e&&Math.log10||2===e&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}(a),i=function(e){return 10===e?ru:e===Math.E?Math.exp:t=>Math.pow(e,t)}(a),r()[0]<0?(n=nu(n),i=nu(i),e(eu,tu)):e(Js,Qs),t}return t.base=function(e){return arguments.length?(a=+e,o()):a},t.domain=function(e){return arguments.length?(r(e),o()):r()},t.ticks=e=>{const t=r();let o=t[0],l=t[t.length-1];const c=l<o;c&&([o,l]=[l,o]);let s,u,f=n(o),d=n(l);const h=null==e?10:+e;let p=[];if(!(a%1)&&d-f<h){if(f=Math.floor(f),d=Math.ceil(d),o>0){for(;f<=d;++f)for(s=1;s<a;++s)if(u=f<0?s/i(-f):s*i(f),!(u<o)){if(u>l)break;p.push(u)}}else for(;f<=d;++f)for(s=a-1;s>=1;--s)if(u=f>0?s/i(-f):s*i(f),!(u<o)){if(u>l)break;p.push(u)}2*p.length<h&&(p=gc(o,l,h))}else p=gc(f,d,Math.min(d-f,h)).map(i);return c?p.reverse():p},t.tickFormat=(e,r)=>{if(null==e&&(e=10),null==r&&(r=10===a?"s":","),"function"!=typeof r&&(a%1||null!=(r=zs(r)).precision||(r.trim=!0),r=Ws(r)),e===1/0)return r;const o=Math.max(1,a*e/t.ticks().length);return e=>{let t=e/i(Math.round(n(e)));return t*a<a-.5&&(t*=a),t<=o?r(e):""}},t.nice=()=>r(Zs(r(),{floor:e=>i(Math.floor(n(e))),ceil:e=>i(Math.ceil(n(e)))})),t}function au(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function ou(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function lu(e){var t=1,r=e(au(t),ou(t));return r.constant=function(r){return arguments.length?e(au(t=+r),ou(t)):t},Xs(r)}function cu(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function su(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function uu(e){return e<0?-e*e:e*e}function fu(e){var t=e(Es,Es),r=1;return t.exponent=function(t){return arguments.length?1===(r=+t)?e(Es,Es):.5===r?e(su,uu):e(cu(r),cu(1/r)):r},Xs(t)}function du(){var e=fu(Ds());return e.copy=function(){return Cs(e,du()).exponent(e.exponent())},Pc.apply(e,arguments),e}function hu(e){return Math.sign(e)*e*e}Fs=Ys({thousands:",",grouping:[3],currency:["$",""]}),Ws=Fs.format,Hs=Fs.formatPrefix;const pu=new Date,yu=new Date;function vu(e,t,r,n){function i(t){return e(t=0===arguments.length?new Date:new Date(+t)),t}return i.floor=t=>(e(t=new Date(+t)),t),i.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),i.round=e=>{const t=i(e),r=i.ceil(e);return e-t<r-e?t:r},i.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),i.range=(r,n,a)=>{const o=[];if(r=i.ceil(r),a=null==a?1:Math.floor(a),!(r<n&&a>0))return o;let l;do{o.push(l=new Date(+r)),t(r,a),e(r)}while(l<r&&r<n);return o},i.filter=r=>vu(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e)if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}),r&&(i.count=(t,n)=>(pu.setTime(+t),yu.setTime(+n),e(pu),e(yu),Math.floor(r(pu,yu))),i.every=e=>(e=Math.floor(e),isFinite(e)&&e>0?e>1?i.filter(n?t=>n(t)%e===0:t=>i.count(0,t)%e===0):i:null)),i}const gu=vu(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);gu.every=e=>(e=Math.floor(e),isFinite(e)&&e>0?e>1?vu(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):gu:null),gu.range;const mu=1e3,bu=6e4,xu=36e5,wu=864e5,Ou=6048e5,ju=2592e6,Au=31536e6,Pu=vu(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*mu)},(e,t)=>(t-e)/mu,e=>e.getUTCSeconds());Pu.range;const Su=vu(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*mu)},(e,t)=>{e.setTime(+e+t*bu)},(e,t)=>(t-e)/bu,e=>e.getMinutes());Su.range;const Eu=vu(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*bu)},(e,t)=>(t-e)/bu,e=>e.getUTCMinutes());Eu.range;const ku=vu(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*mu-e.getMinutes()*bu)},(e,t)=>{e.setTime(+e+t*xu)},(e,t)=>(t-e)/xu,e=>e.getHours());ku.range;const Mu=vu(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*xu)},(e,t)=>(t-e)/xu,e=>e.getUTCHours());Mu.range;const Tu=vu(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*bu)/wu,e=>e.getDate()-1);Tu.range;const Cu=vu(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/wu,e=>e.getUTCDate()-1);Cu.range;const Du=vu(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/wu,e=>Math.floor(e/wu));function Nu(e){return vu(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*bu)/Ou)}Du.range;const Iu=Nu(0),_u=Nu(1),Ru=Nu(2),Lu=Nu(3),zu=Nu(4),Bu=Nu(5),Ku=Nu(6);function $u(e){return vu(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/Ou)}Iu.range,_u.range,Ru.range,Lu.range,zu.range,Bu.range,Ku.range;const Uu=$u(0),Fu=$u(1),Wu=$u(2),Hu=$u(3),Vu=$u(4),qu=$u(5),Yu=$u(6);Uu.range,Fu.range,Wu.range,Hu.range,Vu.range,qu.range,Yu.range;const Gu=vu(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+12*(t.getFullYear()-e.getFullYear()),e=>e.getMonth());Gu.range;const Xu=vu(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+12*(t.getUTCFullYear()-e.getUTCFullYear()),e=>e.getUTCMonth());Xu.range;const Zu=vu(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());Zu.every=e=>isFinite(e=Math.floor(e))&&e>0?vu(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,Zu.range;const Ju=vu(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function Qu(e,t,r,n,i,a){const o=[[Pu,1,mu],[Pu,5,5e3],[Pu,15,15e3],[Pu,30,3e4],[a,1,bu],[a,5,3e5],[a,15,9e5],[a,30,18e5],[i,1,xu],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,wu],[n,2,1728e5],[r,1,Ou],[t,1,ju],[t,3,7776e6],[e,1,Au]];function l(t,r,n){const i=Math.abs(r-t)/n,a=ac(([,,e])=>e).right(o,i);if(a===o.length)return e.every(bc(t/Au,r/Au,n));if(0===a)return gu.every(Math.max(bc(t,r,n),1));const[l,c]=o[i/o[a-1][2]<o[a][2]/i?a-1:a];return l.every(c)}return[function(e,t,r){const n=t<e;n&&([e,t]=[t,e]);const i=r&&"function"==typeof r.range?r:l(e,t,r),a=i?i.range(e,+t+1):[];return n?a.reverse():a},l]}Ju.every=e=>isFinite(e=Math.floor(e))&&e>0?vu(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,Ju.range;const[ef,tf]=Qu(Ju,Xu,Uu,Du,Mu,Eu),[rf,nf]=Qu(Zu,Gu,Iu,Tu,ku,Su);function af(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function of(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function lf(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var cf,sf,uf,ff={"-":"",_:" ",0:"0"},df=/^\s*\d+/,hf=/^%/,pf=/[\\^$*+?|[\]().{}]/g;function yf(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(t)+i:i)}function vf(e){return e.replace(pf,"\\$&")}function gf(e){return new RegExp("^(?:"+e.map(vf).join("|")+")","i")}function mf(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function bf(e,t,r){var n=df.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function xf(e,t,r){var n=df.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function wf(e,t,r){var n=df.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function Of(e,t,r){var n=df.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function jf(e,t,r){var n=df.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function Af(e,t,r){var n=df.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function Pf(e,t,r){var n=df.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function Sf(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function Ef(e,t,r){var n=df.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function kf(e,t,r){var n=df.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function Mf(e,t,r){var n=df.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function Tf(e,t,r){var n=df.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function Cf(e,t,r){var n=df.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function Df(e,t,r){var n=df.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function Nf(e,t,r){var n=df.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function If(e,t,r){var n=df.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function _f(e,t,r){var n=df.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function Rf(e,t,r){var n=hf.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function Lf(e,t,r){var n=df.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function zf(e,t,r){var n=df.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function Bf(e,t){return yf(e.getDate(),t,2)}function Kf(e,t){return yf(e.getHours(),t,2)}function $f(e,t){return yf(e.getHours()%12||12,t,2)}function Uf(e,t){return yf(1+Tu.count(Zu(e),e),t,3)}function Ff(e,t){return yf(e.getMilliseconds(),t,3)}function Wf(e,t){return Ff(e,t)+"000"}function Hf(e,t){return yf(e.getMonth()+1,t,2)}function Vf(e,t){return yf(e.getMinutes(),t,2)}function qf(e,t){return yf(e.getSeconds(),t,2)}function Yf(e){var t=e.getDay();return 0===t?7:t}function Gf(e,t){return yf(Iu.count(Zu(e)-1,e),t,2)}function Xf(e){var t=e.getDay();return t>=4||0===t?zu(e):zu.ceil(e)}function Zf(e,t){return e=Xf(e),yf(zu.count(Zu(e),e)+(4===Zu(e).getDay()),t,2)}function Jf(e){return e.getDay()}function Qf(e,t){return yf(_u.count(Zu(e)-1,e),t,2)}function ed(e,t){return yf(e.getFullYear()%100,t,2)}function td(e,t){return yf((e=Xf(e)).getFullYear()%100,t,2)}function rd(e,t){return yf(e.getFullYear()%1e4,t,4)}function nd(e,t){var r=e.getDay();return yf((e=r>=4||0===r?zu(e):zu.ceil(e)).getFullYear()%1e4,t,4)}function id(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+yf(t/60|0,"0",2)+yf(t%60,"0",2)}function ad(e,t){return yf(e.getUTCDate(),t,2)}function od(e,t){return yf(e.getUTCHours(),t,2)}function ld(e,t){return yf(e.getUTCHours()%12||12,t,2)}function cd(e,t){return yf(1+Cu.count(Ju(e),e),t,3)}function sd(e,t){return yf(e.getUTCMilliseconds(),t,3)}function ud(e,t){return sd(e,t)+"000"}function fd(e,t){return yf(e.getUTCMonth()+1,t,2)}function dd(e,t){return yf(e.getUTCMinutes(),t,2)}function hd(e,t){return yf(e.getUTCSeconds(),t,2)}function pd(e){var t=e.getUTCDay();return 0===t?7:t}function yd(e,t){return yf(Uu.count(Ju(e)-1,e),t,2)}function vd(e){var t=e.getUTCDay();return t>=4||0===t?Vu(e):Vu.ceil(e)}function gd(e,t){return e=vd(e),yf(Vu.count(Ju(e),e)+(4===Ju(e).getUTCDay()),t,2)}function md(e){return e.getUTCDay()}function bd(e,t){return yf(Fu.count(Ju(e)-1,e),t,2)}function xd(e,t){return yf(e.getUTCFullYear()%100,t,2)}function wd(e,t){return yf((e=vd(e)).getUTCFullYear()%100,t,2)}function Od(e,t){return yf(e.getUTCFullYear()%1e4,t,4)}function jd(e,t){var r=e.getUTCDay();return yf((e=r>=4||0===r?Vu(e):Vu.ceil(e)).getUTCFullYear()%1e4,t,4)}function Ad(){return"+0000"}function Pd(){return"%"}function Sd(e){return+e}function Ed(e){return Math.floor(+e/1e3)}function kd(e){return new Date(e)}function Md(e){return e instanceof Date?+e:+new Date(+e)}function Td(e,t,r,n,i,a,o,l,c,s){var u=Ns(),f=u.invert,d=u.domain,h=s(".%L"),p=s(":%S"),y=s("%I:%M"),v=s("%I %p"),g=s("%a %d"),m=s("%b %d"),b=s("%B"),x=s("%Y");function w(e){return(c(e)<e?h:l(e)<e?p:o(e)<e?y:a(e)<e?v:n(e)<e?i(e)<e?g:m:r(e)<e?b:x)(e)}return u.invert=function(e){return new Date(f(e))},u.domain=function(e){return arguments.length?d(Array.from(e,Md)):d().map(kd)},u.ticks=function(t){var r=d();return e(r[0],r[r.length-1],null==t?10:t)},u.tickFormat=function(e,t){return null==t?w:s(t)},u.nice=function(e){var r=d();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?d(Zs(r,e)):u},u.copy=function(){return Cs(u,Td(e,t,r,n,i,a,o,l,c,s))},u}function Cd(){var e,t,r,n,i,a=0,o=1,l=Es,c=!1;function s(t){return null==t||isNaN(t=+t)?i:l(0===r?.5:(t=(n(t)-e)*r,c?Math.max(0,Math.min(1,t)):t))}function u(e){return function(t){var r,n;return arguments.length?([r,n]=t,l=e(r,n),s):[l(0),l(1)]}}return s.domain=function(i){return arguments.length?([a,o]=i,e=n(a=+a),t=n(o=+o),r=e===t?0:1/(t-e),s):[a,o]},s.clamp=function(e){return arguments.length?(c=!!e,s):c},s.interpolator=function(e){return arguments.length?(l=e,s):l},s.range=u(js),s.rangeRound=u(As),s.unknown=function(e){return arguments.length?(i=e,s):i},function(i){return n=i,e=i(a),t=i(o),r=e===t?0:1/(t-e),s}}function Dd(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function Nd(){var e=fu(Cd());return e.copy=function(){return Dd(e,Nd()).exponent(e.exponent())},Sc.apply(e,arguments)}function Id(){var e,t,r,n,i,a,o,l=0,c=.5,s=1,u=1,f=Es,d=!1;function h(e){return isNaN(e=+e)?o:(e=.5+((e=+a(e))-t)*(u*e<u*t?n:i),f(d?Math.max(0,Math.min(1,e)):e))}function p(e){return function(t){var r,n,i;return arguments.length?([r,n,i]=t,f=function(e,t){void 0===t&&(t=e,e=js);for(var r=0,n=t.length-1,i=t[0],a=new Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return a[t](e-t)}}(e,[r,n,i]),h):[f(0),f(.5),f(1)]}}return h.domain=function(o){return arguments.length?([l,c,s]=o,e=a(l=+l),t=a(c=+c),r=a(s=+s),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),u=t<e?-1:1,h):[l,c,s]},h.clamp=function(e){return arguments.length?(d=!!e,h):d},h.interpolator=function(e){return arguments.length?(f=e,h):f},h.range=p(js),h.rangeRound=p(As),h.unknown=function(e){return arguments.length?(o=e,h):o},function(o){return a=o,e=o(l),t=o(c),r=o(s),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),u=t<e?-1:1,h}}function _d(){var e=fu(Id());return e.copy=function(){return Dd(e,_d()).exponent(e.exponent())},Sc.apply(e,arguments)}!function(e){cf=function(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,l=e.months,c=e.shortMonths,s=gf(i),u=mf(i),f=gf(a),d=mf(a),h=gf(o),p=mf(o),y=gf(l),v=mf(l),g=gf(c),m=mf(c),b={a:function(e){return o[e.getDay()]},A:function(e){return a[e.getDay()]},b:function(e){return c[e.getMonth()]},B:function(e){return l[e.getMonth()]},c:null,d:Bf,e:Bf,f:Wf,g:td,G:nd,H:Kf,I:$f,j:Uf,L:Ff,m:Hf,M:Vf,p:function(e){return i[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:Sd,s:Ed,S:qf,u:Yf,U:Gf,V:Zf,w:Jf,W:Qf,x:null,X:null,y:ed,Y:rd,Z:id,"%":Pd},x={a:function(e){return o[e.getUTCDay()]},A:function(e){return a[e.getUTCDay()]},b:function(e){return c[e.getUTCMonth()]},B:function(e){return l[e.getUTCMonth()]},c:null,d:ad,e:ad,f:ud,g:wd,G:jd,H:od,I:ld,j:cd,L:sd,m:fd,M:dd,p:function(e){return i[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:Sd,s:Ed,S:hd,u:pd,U:yd,V:gd,w:md,W:bd,x:null,X:null,y:xd,Y:Od,Z:Ad,"%":Pd},w={a:function(e,t,r){var n=h.exec(t.slice(r));return n?(e.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=g.exec(t.slice(r));return n?(e.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return A(e,t,r,n)},d:Mf,e:Mf,f:_f,g:Pf,G:Af,H:Cf,I:Cf,j:Tf,L:If,m:kf,M:Df,p:function(e,t,r){var n=s.exec(t.slice(r));return n?(e.p=u.get(n[0].toLowerCase()),r+n[0].length):-1},q:Ef,Q:Lf,s:zf,S:Nf,u:xf,U:wf,V:Of,w:bf,W:jf,x:function(e,t,n){return A(e,r,t,n)},X:function(e,t,r){return A(e,n,t,r)},y:Pf,Y:Af,Z:Sf,"%":Rf};function O(e,t){return function(r){var n,i,a,o=[],l=-1,c=0,s=e.length;for(r instanceof Date||(r=new Date(+r));++l<s;)37===e.charCodeAt(l)&&(o.push(e.slice(c,l)),null!=(i=ff[n=e.charAt(++l)])?n=e.charAt(++l):i="e"===n?" ":"0",(a=t[n])&&(n=a(r,i)),o.push(n),c=l+1);return o.push(e.slice(c,l)),o.join("")}}function j(e,t){return function(r){var n,i,a=lf(1900,void 0,1);if(A(a,e,r+="",0)!=r.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(1e3*a.s+("L"in a?a.L:0));if(t&&!("Z"in a)&&(a.Z=0),"p"in a&&(a.H=a.H%12+12*a.p),void 0===a.m&&(a.m="q"in a?a.q:0),"V"in a){if(a.V<1||a.V>53)return null;"w"in a||(a.w=1),"Z"in a?(i=(n=of(lf(a.y,0,1))).getUTCDay(),n=i>4||0===i?Fu.ceil(n):Fu(n),n=Cu.offset(n,7*(a.V-1)),a.y=n.getUTCFullYear(),a.m=n.getUTCMonth(),a.d=n.getUTCDate()+(a.w+6)%7):(i=(n=af(lf(a.y,0,1))).getDay(),n=i>4||0===i?_u.ceil(n):_u(n),n=Tu.offset(n,7*(a.V-1)),a.y=n.getFullYear(),a.m=n.getMonth(),a.d=n.getDate()+(a.w+6)%7)}else("W"in a||"U"in a)&&("w"in a||(a.w="u"in a?a.u%7:"W"in a?1:0),i="Z"in a?of(lf(a.y,0,1)).getUTCDay():af(lf(a.y,0,1)).getDay(),a.m=0,a.d="W"in a?(a.w+6)%7+7*a.W-(i+5)%7:a.w+7*a.U-(i+6)%7);return"Z"in a?(a.H+=a.Z/100|0,a.M+=a.Z%100,of(a)):af(a)}}function A(e,t,r,n){for(var i,a,o=0,l=t.length,c=r.length;o<l;){if(n>=c)return-1;if(37===(i=t.charCodeAt(o++))){if(i=t.charAt(o++),!(a=w[i in ff?t.charAt(o++):i])||(n=a(e,r,n))<0)return-1}else if(i!=r.charCodeAt(n++))return-1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(t,b),x.x=O(r,x),x.X=O(n,x),x.c=O(t,x),{format:function(e){var t=O(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=j(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=j(e+="",!0);return t.toString=function(){return e},t}}}(e),sf=cf.format,cf.parse,uf=cf.utcFormat,cf.utcParse}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});const Rd=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:Mc,scaleDiverging:function e(){var t=Xs(Id()(Es));return t.copy=function(){return Dd(t,e())},Sc.apply(t,arguments)},scaleDivergingLog:function e(){var t=iu(Id()).domain([.1,1,10]);return t.copy=function(){return Dd(t,e()).base(t.base())},Sc.apply(t,arguments)},scaleDivergingPow:_d,scaleDivergingSqrt:function(){return _d.apply(null,arguments).exponent(.5)},scaleDivergingSymlog:function e(){var t=lu(Id());return t.copy=function(){return Dd(t,e()).constant(t.constant())},Sc.apply(t,arguments)},scaleIdentity:function e(t){var r;function n(e){return null==e||isNaN(e=+e)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,Ps),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,Ps):[0,1],Xs(n)},scaleImplicit:Ec,scaleLinear:function e(){var t=Ns();return t.copy=function(){return Cs(t,e())},Pc.apply(t,arguments),Xs(t)},scaleLog:function e(){const t=iu(Ds()).domain([1,10]);return t.copy=()=>Cs(t,e()).base(t.base()),Pc.apply(t,arguments),t},scaleOrdinal:kc,scalePoint:function(){return Tc(Mc.apply(null,arguments).paddingInner(1))},scalePow:du,scaleQuantile:function e(){var t,r=[],n=[],i=[];function a(){var e=0,t=Math.max(1,n.length);for(i=new Array(t-1);++e<t;)i[e-1]=Ac(r,e/t);return o}function o(e){return null==e||isNaN(e=+e)?t:n[cc(i,e)]}return o.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?i[t-1]:r[0],t<i.length?i[t]:r[r.length-1]]},o.domain=function(e){if(!arguments.length)return r.slice();r=[];for(let t of e)null==t||isNaN(t=+t)||r.push(t);return r.sort(nc),a()},o.range=function(e){return arguments.length?(n=Array.from(e),a()):n.slice()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.quantiles=function(){return i.slice()},o.copy=function(){return e().domain(r).range(n).unknown(t)},Pc.apply(o,arguments)},scaleQuantize:function e(){var t,r=0,n=1,i=1,a=[.5],o=[0,1];function l(e){return null!=e&&e<=e?o[cc(a,e,0,i)]:t}function c(){var e=-1;for(a=new Array(i);++e<i;)a[e]=((e+1)*n-(e-i)*r)/(i+1);return l}return l.domain=function(e){return arguments.length?([r,n]=e,r=+r,n=+n,c()):[r,n]},l.range=function(e){return arguments.length?(i=(o=Array.from(e)).length-1,c()):o.slice()},l.invertExtent=function(e){var t=o.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,a[0]]:t>=i?[a[i-1],n]:[a[t-1],a[t]]},l.unknown=function(e){return arguments.length?(t=e,l):l},l.thresholds=function(){return a.slice()},l.copy=function(){return e().domain([r,n]).range(o).unknown(t)},Pc.apply(Xs(l),arguments)},scaleRadial:function e(){var t,r=Ns(),n=[0,1],i=!1;function a(e){var n=function(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}(r(e));return isNaN(n)?t:i?Math.round(n):n}return a.invert=function(e){return r.invert(hu(e))},a.domain=function(e){return arguments.length?(r.domain(e),a):r.domain()},a.range=function(e){return arguments.length?(r.range((n=Array.from(e,Ps)).map(hu)),a):n.slice()},a.rangeRound=function(e){return a.range(e).round(!0)},a.round=function(e){return arguments.length?(i=!!e,a):i},a.clamp=function(e){return arguments.length?(r.clamp(e),a):r.clamp()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e(r.domain(),n).round(i).clamp(r.clamp()).unknown(t)},Pc.apply(a,arguments),Xs(a)},scaleSequential:function e(){var t=Xs(Cd()(Es));return t.copy=function(){return Dd(t,e())},Sc.apply(t,arguments)},scaleSequentialLog:function e(){var t=iu(Cd()).domain([1,10]);return t.copy=function(){return Dd(t,e()).base(t.base())},Sc.apply(t,arguments)},scaleSequentialPow:Nd,scaleSequentialQuantile:function e(){var t=[],r=Es;function n(e){if(null!=e&&!isNaN(e=+e))return r((cc(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();t=[];for(let r of e)null==r||isNaN(r=+r)||t.push(r);return t.sort(nc),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>function(e,t){if((r=(e=Float64Array.from(function*(e){for(let t of e)null!=t&&(t=+t)>=t&&(yield t)}(e))).length)&&!isNaN(t=+t)){if(t<=0||r<2)return wc(e);if(t>=1)return xc(e);var r,n=(r-1)*t,i=Math.floor(n),a=xc(Oc(e,i).subarray(0,i+1));return a+(wc(e.subarray(i+1))-a)*(n-i)}}(t,n/e))},n.copy=function(){return e(r).domain(t)},Sc.apply(n,arguments)},scaleSequentialSqrt:function(){return Nd.apply(null,arguments).exponent(.5)},scaleSequentialSymlog:function e(){var t=lu(Cd());return t.copy=function(){return Dd(t,e()).constant(t.constant())},Sc.apply(t,arguments)},scaleSqrt:function(){return du.apply(null,arguments).exponent(.5)},scaleSymlog:function e(){var t=lu(Ds());return t.copy=function(){return Cs(t,e()).constant(t.constant())},Pc.apply(t,arguments)},scaleThreshold:function e(){var t,r=[.5],n=[0,1],i=1;function a(e){return null!=e&&e<=e?n[cc(r,e,0,i)]:t}return a.domain=function(e){return arguments.length?(r=Array.from(e),i=Math.min(r.length,n.length-1),a):r.slice()},a.range=function(e){return arguments.length?(n=Array.from(e),i=Math.min(r.length,n.length-1),a):n.slice()},a.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e().domain(r).range(n).unknown(t)},Pc.apply(a,arguments)},scaleTime:function(){return Pc.apply(Td(rf,nf,Zu,Gu,Iu,Tu,ku,Su,Pu,sf).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)},scaleUtc:function(){return Pc.apply(Td(ef,tf,Ju,Xu,Uu,Cu,Mu,Eu,Pu,uf).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)},tickFormat:Gs},Symbol.toStringTag,{value:"Module"}));var Ld=e=>e.chartData,zd=U([Ld],e=>{var t=null!=e.chartData?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}}),Bd=(e,t,r,n)=>n?zd(e):Ld(e);function Kd(e){if(Array.isArray(e)&&2===e.length){var[t,r]=e;if(Ao(t)&&Ao(r))return!0}return!1}function $d(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}var Ud,Fd=1e9,Wd=!0,Hd="[DecimalError] ",Vd=Hd+"Invalid argument: ",qd=Hd+"Exponent out of range: ",Yd=Math.floor,Gd=Math.pow,Xd=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,Zd=1e7,Jd=9007199254740991,Qd=Yd(1286742750677284.5),eh={};function th(e,t){var r,n,i,a,o,l,c,s,u=e.constructor,f=u.precision;if(!e.s||!t.s)return t.s||(t=new u(e)),Wd?fh(t,f):t;if(c=e.d,s=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i){for(a<0?(n=c,a=-a,l=s.length):(n=s,i=o,l=c.length),a>(l=(o=Math.ceil(f/7))>l?o+1:l+1)&&(a=l,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for((l=c.length)-(a=s.length)<0&&(a=l,n=s,s=c,c=n),r=0;a;)r=(c[--a]=c[a]+s[a]+r)/Zd|0,c[a]%=Zd;for(r&&(c.unshift(r),++i),l=c.length;0==c[--l];)c.pop();return t.d=c,t.e=i,Wd?fh(t,f):t}function rh(e,t,r){if(e!==~~e||e<t||e>r)throw Error(Vd+e)}function nh(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)(r=7-(n=e[t]+"").length)&&(a+=ch(r)),a+=n;(r=7-(n=(o=e[t])+"").length)&&(a+=ch(r))}else if(0===o)return"0";for(;o%10==0;)o/=10;return a+o}eh.absoluteValue=eh.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},eh.comparedTo=eh.cmp=function(e){var t,r,n,i,a=this;if(e=new a.constructor(e),a.s!==e.s)return a.s||-e.s;if(a.e!==e.e)return a.e>e.e^a.s<0?1:-1;for(t=0,r=(n=a.d.length)<(i=e.d.length)?n:i;t<r;++t)if(a.d[t]!==e.d[t])return a.d[t]>e.d[t]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1},eh.decimalPlaces=eh.dp=function(){var e=this,t=e.d.length-1,r=7*(t-e.e);if(t=e.d[t])for(;t%10==0;t/=10)r--;return r<0?0:r},eh.dividedBy=eh.div=function(e){return ih(this,new this.constructor(e))},eh.dividedToIntegerBy=eh.idiv=function(e){var t=this.constructor;return fh(ih(this,new t(e),0,1),t.precision)},eh.equals=eh.eq=function(e){return!this.cmp(e)},eh.exponent=function(){return oh(this)},eh.greaterThan=eh.gt=function(e){return this.cmp(e)>0},eh.greaterThanOrEqualTo=eh.gte=function(e){return this.cmp(e)>=0},eh.isInteger=eh.isint=function(){return this.e>this.d.length-2},eh.isNegative=eh.isneg=function(){return this.s<0},eh.isPositive=eh.ispos=function(){return this.s>0},eh.isZero=function(){return 0===this.s},eh.lessThan=eh.lt=function(e){return this.cmp(e)<0},eh.lessThanOrEqualTo=eh.lte=function(e){return this.cmp(e)<1},eh.logarithm=eh.log=function(e){var t,r=this,n=r.constructor,i=n.precision,a=i+5;if(void 0===e)e=new n(10);else if((e=new n(e)).s<1||e.eq(Ud))throw Error(Hd+"NaN");if(r.s<1)throw Error(Hd+(r.s?"NaN":"-Infinity"));return r.eq(Ud)?new n(0):(Wd=!1,t=ih(sh(r,a),sh(e,a),a),Wd=!0,fh(t,i))},eh.minus=eh.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?dh(t,e):th(t,(e.s=-e.s,e))},eh.modulo=eh.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(!(e=new n(e)).s)throw Error(Hd+"NaN");return r.s?(Wd=!1,t=ih(r,e,0,1).times(e),Wd=!0,r.minus(t)):fh(new n(r),i)},eh.naturalExponential=eh.exp=function(){return ah(this)},eh.naturalLogarithm=eh.ln=function(){return sh(this)},eh.negated=eh.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},eh.plus=eh.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?th(t,e):dh(t,(e.s=-e.s,e))},eh.precision=eh.sd=function(e){var t,r,n,i=this;if(void 0!==e&&e!==!!e&&1!==e&&0!==e)throw Error(Vd+e);if(t=oh(i)+1,r=7*(n=i.d.length-1)+1,n=i.d[n]){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},eh.squareRoot=eh.sqrt=function(){var e,t,r,n,i,a,o,l=this,c=l.constructor;if(l.s<1){if(!l.s)return new c(0);throw Error(Hd+"NaN")}for(e=oh(l),Wd=!1,0==(i=Math.sqrt(+l))||i==1/0?(((t=nh(l.d)).length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=Yd((e+1)/2)-(e<0||e%2),n=new c(t=i==1/0?"5e"+e:(t=i.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new c(i.toString()),i=o=(r=c.precision)+3;;)if(n=(a=n).plus(ih(l,a,o+2)).times(.5),nh(a.d).slice(0,o)===(t=nh(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&"4999"==t){if(fh(a,r+1,0),a.times(a).eq(l)){n=a;break}}else if("9999"!=t)break;o+=4}return Wd=!0,fh(n,r)},eh.times=eh.mul=function(e){var t,r,n,i,a,o,l,c,s,u=this,f=u.constructor,d=u.d,h=(e=new f(e)).d;if(!u.s||!e.s)return new f(0);for(e.s*=u.s,r=u.e+e.e,(c=d.length)<(s=h.length)&&(a=d,d=h,h=a,o=c,c=s,s=o),a=[],n=o=c+s;n--;)a.push(0);for(n=s;--n>=0;){for(t=0,i=c+n;i>n;)l=a[i]+h[n]*d[i-n-1]+t,a[i--]=l%Zd|0,t=l/Zd|0;a[i]=(a[i]+t)%Zd|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,Wd?fh(e,f.precision):e},eh.toDecimalPlaces=eh.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),void 0===e?r:(rh(e,0,Fd),void 0===t?t=n.rounding:rh(t,0,8),fh(r,e+oh(r)+1,t))},eh.toExponential=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=hh(n,!0):(rh(e,0,Fd),void 0===t?t=i.rounding:rh(t,0,8),r=hh(n=fh(new i(n),e+1,t),!0,e+1)),r},eh.toFixed=function(e,t){var r,n,i=this,a=i.constructor;return void 0===e?hh(i):(rh(e,0,Fd),void 0===t?t=a.rounding:rh(t,0,8),r=hh((n=fh(new a(i),e+oh(i)+1,t)).abs(),!1,e+oh(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)},eh.toInteger=eh.toint=function(){var e=this,t=e.constructor;return fh(new t(e),oh(e)+1,t.rounding)},eh.toNumber=function(){return+this},eh.toPower=eh.pow=function(e){var t,r,n,i,a,o,l=this,c=l.constructor,s=+(e=new c(e));if(!e.s)return new c(Ud);if(!(l=new c(l)).s){if(e.s<1)throw Error(Hd+"Infinity");return l}if(l.eq(Ud))return l;if(n=c.precision,e.eq(Ud))return fh(l,n);if(o=(t=e.e)>=(r=e.d.length-1),a=l.s,o){if((r=s<0?-s:s)<=Jd){for(i=new c(Ud),t=Math.ceil(n/7+4),Wd=!1;r%2&&ph((i=i.times(l)).d,t),0!==(r=Yd(r/2));)ph((l=l.times(l)).d,t);return Wd=!0,e.s<0?new c(Ud).div(i):fh(i,n)}}else if(a<0)throw Error(Hd+"NaN");return a=a<0&&1&e.d[Math.max(t,r)]?-1:1,l.s=1,Wd=!1,i=e.times(sh(l,n+12)),Wd=!0,(i=ah(i)).s=a,i},eh.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return void 0===e?n=hh(i,(r=oh(i))<=a.toExpNeg||r>=a.toExpPos):(rh(e,1,Fd),void 0===t?t=a.rounding:rh(t,0,8),n=hh(i=fh(new a(i),e,t),e<=(r=oh(i))||r<=a.toExpNeg,e)),n},eh.toSignificantDigits=eh.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(rh(e,1,Fd),void 0===t?t=r.rounding:rh(t,0,8)),fh(new r(this),e,t)},eh.toString=eh.valueOf=eh.val=eh.toJSON=eh[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=oh(e),r=e.constructor;return hh(e,t<=r.toExpNeg||t>=r.toExpPos)};var ih=function(){function e(e,t){var r,n=0,i=e.length;for(e=e.slice();i--;)r=e[i]*t+n,e[i]=r%Zd|0,n=r/Zd|0;return n&&e.unshift(n),e}function t(e,t,r,n){var i,a;if(r!=n)a=r>n?1:-1;else for(i=a=0;i<r;i++)if(e[i]!=t[i]){a=e[i]>t[i]?1:-1;break}return a}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=e[r]<t[r]?1:0,e[r]=n*Zd+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,i,a,o){var l,c,s,u,f,d,h,p,y,v,g,m,b,x,w,O,j,A,P=n.constructor,S=n.s==i.s?1:-1,E=n.d,k=i.d;if(!n.s)return new P(n);if(!i.s)throw Error(Hd+"Division by zero");for(c=n.e-i.e,j=k.length,w=E.length,p=(h=new P(S)).d=[],s=0;k[s]==(E[s]||0);)++s;if(k[s]>(E[s]||0)&&--c,(m=null==a?a=P.precision:o?a+(oh(n)-oh(i))+1:a)<0)return new P(0);if(m=m/7+2|0,s=0,1==j)for(u=0,k=k[0],m++;(s<w||u)&&m--;s++)b=u*Zd+(E[s]||0),p[s]=b/k|0,u=b%k|0;else{for((u=Zd/(k[0]+1)|0)>1&&(k=e(k,u),E=e(E,u),j=k.length,w=E.length),x=j,v=(y=E.slice(0,j)).length;v<j;)y[v++]=0;(A=k.slice()).unshift(0),O=k[0],k[1]>=Zd/2&&++O;do{u=0,(l=t(k,y,j,v))<0?(g=y[0],j!=v&&(g=g*Zd+(y[1]||0)),(u=g/O|0)>1?(u>=Zd&&(u=Zd-1),1==(l=t(f=e(k,u),y,d=f.length,v=y.length))&&(u--,r(f,j<d?A:k,d))):(0==u&&(l=u=1),f=k.slice()),(d=f.length)<v&&f.unshift(0),r(y,f,v),-1==l&&(l=t(k,y,j,v=y.length))<1&&(u++,r(y,j<v?A:k,v)),v=y.length):0===l&&(u++,y=[0]),p[s++]=u,l&&y[0]?y[v++]=E[x]||0:(y=[E[x]],v=1)}while((x++<w||void 0!==y[0])&&m--)}return p[0]||p.shift(),h.e=c,fh(h,o?a+oh(h)+1:a)}}();function ah(e,t){var r,n,i,a,o,l=0,c=0,s=e.constructor,u=s.precision;if(oh(e)>16)throw Error(qd+oh(e));if(!e.s)return new s(Ud);for(Wd=!1,o=u,a=new s(.03125);e.abs().gte(.1);)e=e.times(a),c+=5;for(o+=Math.log(Gd(2,c))/Math.LN10*2+5|0,r=n=i=new s(Ud),s.precision=o;;){if(n=fh(n.times(e),o),r=r.times(++l),nh((a=i.plus(ih(n,r,o))).d).slice(0,o)===nh(i.d).slice(0,o)){for(;c--;)i=fh(i.times(i),o);return s.precision=u,null==t?(Wd=!0,fh(i,u)):i}i=a}}function oh(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function lh(e,t,r){if(t>e.LN10.sd())throw Wd=!0,r&&(e.precision=r),Error(Hd+"LN10 precision limit exceeded");return fh(new e(e.LN10),t)}function ch(e){for(var t="";e--;)t+="0";return t}function sh(e,t){var r,n,i,a,o,l,c,s,u,f=1,d=e,h=d.d,p=d.constructor,y=p.precision;if(d.s<1)throw Error(Hd+(d.s?"NaN":"-Infinity"));if(d.eq(Ud))return new p(0);if(null==t?(Wd=!1,s=y):s=t,d.eq(10))return null==t&&(Wd=!0),lh(p,s);if(s+=10,p.precision=s,n=(r=nh(h)).charAt(0),a=oh(d),!(Math.abs(a)<15e14))return c=lh(p,s+2,y).times(a+""),d=sh(new p(n+"."+r.slice(1)),s-10).plus(c),p.precision=y,null==t?(Wd=!0,fh(d,y)):d;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=nh((d=d.times(e)).d)).charAt(0),f++;for(a=oh(d),n>1?(d=new p("0."+r),a++):d=new p(n+"."+r.slice(1)),l=o=d=ih(d.minus(Ud),d.plus(Ud),s),u=fh(d.times(d),s),i=3;;){if(o=fh(o.times(u),s),nh((c=l.plus(ih(o,new p(i),s))).d).slice(0,s)===nh(l.d).slice(0,s))return l=l.times(2),0!==a&&(l=l.plus(lh(p,s+2,y).times(a+""))),l=ih(l,new p(f),s),p.precision=y,null==t?(Wd=!0,fh(l,y)):l;l=c,i+=2}}function uh(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(i=t.length;48===t.charCodeAt(i-1);)--i;if(t=t.slice(n,i)){if(i-=n,r=r-n-1,e.e=Yd(r/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=7;n<i;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),Wd&&(e.e>Qd||e.e<-Qd))throw Error(qd+r)}else e.s=0,e.e=0,e.d=[0];return e}function fh(e,t,r){var n,i,a,o,l,c,s,u,f=e.d;for(o=1,a=f[0];a>=10;a/=10)o++;if((n=t-o)<0)n+=7,i=t,s=f[u=0];else{if((u=Math.ceil((n+1)/7))>=(a=f.length))return e;for(s=a=f[u],o=1;a>=10;a/=10)o++;i=(n%=7)-7+o}if(void 0!==r&&(l=s/(a=Gd(10,o-i-1))%10|0,c=t<0||void 0!==f[u+1]||s%a,c=r<4?(l||c)&&(0==r||r==(e.s<0?3:2)):l>5||5==l&&(4==r||c||6==r&&(n>0?i>0?s/Gd(10,o-i):0:f[u-1])%10&1||r==(e.s<0?8:7))),t<1||!f[0])return c?(a=oh(e),f.length=1,t=t-a-1,f[0]=Gd(10,(7-t%7)%7),e.e=Yd(-t/7)||0):(f.length=1,f[0]=e.e=e.s=0),e;if(0==n?(f.length=u,a=1,u--):(f.length=u+1,a=Gd(10,7-n),f[u]=i>0?(s/Gd(10,o-i)%Gd(10,i)|0)*a:0),c)for(;;){if(0==u){(f[0]+=a)==Zd&&(f[0]=1,++e.e);break}if(f[u]+=a,f[u]!=Zd)break;f[u--]=0,a=1}for(n=f.length;0===f[--n];)f.pop();if(Wd&&(e.e>Qd||e.e<-Qd))throw Error(qd+oh(e));return e}function dh(e,t){var r,n,i,a,o,l,c,s,u,f,d=e.constructor,h=d.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new d(e),Wd?fh(t,h):t;if(c=e.d,f=t.d,n=t.e,s=e.e,c=c.slice(),o=s-n){for((u=o<0)?(r=c,o=-o,l=f.length):(r=f,n=s,l=c.length),o>(i=Math.max(Math.ceil(h/7),l)+2)&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for((u=(i=c.length)<(l=f.length))&&(l=i),i=0;i<l;i++)if(c[i]!=f[i]){u=c[i]<f[i];break}o=0}for(u&&(r=c,c=f,f=r,t.s=-t.s),l=c.length,i=f.length-l;i>0;--i)c[l++]=0;for(i=f.length;i>o;){if(c[--i]<f[i]){for(a=i;a&&0===c[--a];)c[a]=Zd-1;--c[a],c[i]+=Zd}c[i]-=f[i]}for(;0===c[--l];)c.pop();for(;0===c[0];c.shift())--n;return c[0]?(t.d=c,t.e=n,Wd?fh(t,h):t):new d(0)}function hh(e,t,r){var n,i=oh(e),a=nh(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+ch(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+ch(-i-1)+a,r&&(n=r-o)>0&&(a+=ch(n))):i>=o?(a+=ch(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+ch(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=ch(n))),e.s<0?"-"+a:a}function ph(e,t){if(e.length>t)return e.length=t,!0}function yh(e){if(!e||"object"!=typeof e)throw Error(Hd+"Object expected");var t,r,n,i=["precision",1,Fd,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if(void 0!==(n=e[r=i[t]])){if(!(Yd(n)===n&&n>=i[t+1]&&n<=i[t+2]))throw Error(Vd+r+": "+n);this[r]=n}if(void 0!==(n=e[r="LN10"])){if(n!=Math.LN10)throw Error(Vd+r+": "+n);this[r]=new this(n)}return this}var vh=function e(t){var r,n,i;function a(e){var t=this;if(!(t instanceof a))return new a(e);if(t.constructor=a,e instanceof a)return t.s=e.s,t.e=e.e,void(t.d=(e=e.d)?e.slice():e);if("number"==typeof e){if(0*e!=0)throw Error(Vd+e);if(e>0)t.s=1;else{if(!(e<0))return t.s=0,t.e=0,void(t.d=[0]);e=-e,t.s=-1}return e===~~e&&e<1e7?(t.e=0,void(t.d=[e])):uh(t,e.toString())}if("string"!=typeof e)throw Error(Vd+e);if(45===e.charCodeAt(0)?(e=e.slice(1),t.s=-1):t.s=1,!Xd.test(e))throw Error(Vd+e);uh(t,e)}if(a.prototype=eh,a.ROUND_UP=0,a.ROUND_DOWN=1,a.ROUND_CEIL=2,a.ROUND_FLOOR=3,a.ROUND_HALF_UP=4,a.ROUND_HALF_DOWN=5,a.ROUND_HALF_EVEN=6,a.ROUND_HALF_CEIL=7,a.ROUND_HALF_FLOOR=8,a.clone=e,a.config=a.set=yh,void 0===t&&(t={}),t)for(i=["precision","rounding","toExpNeg","toExpPos","LN10"],r=0;r<i.length;)t.hasOwnProperty(n=i[r++])||(t[n]=this[n]);return a.config(t),a}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});Ud=new vh(1);const gh=vh;var mh=e=>e,bh={},xh=e=>e===bh,wh=e=>function t(){return 0===arguments.length||1===arguments.length&&xh(arguments.length<=0?void 0:arguments[0])?t:e(...arguments)},Oh=(e,t)=>1===e?t:wh(function(){for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];var a=n.filter(e=>e!==bh).length;return a>=e?t(...n):Oh(e-a,wh(function(){for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];var a=n.map(e=>xh(e)?r.shift():e);return t(...a,...r)}))}),jh=e=>Oh(e.length,e),Ah=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},Ph=jh((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map(e=>t[e]).map(e)),Sh=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),Eh=e=>{var t=null,r=null;return function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return t&&i.every((e,r)=>{var n;return e===(null===(n=t)||void 0===n?void 0:n[r])})?r:(t=i,r=e(...i))}};function kh(e){return 0===e?1:Math.floor(new gh(e).abs().log(10).toNumber())+1}function Mh(e,t,r){for(var n=new gh(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}jh((e,t,r)=>{var n=+e;return n+r*(+t-n)}),jh((e,t,r)=>{var n=t-+e;return(r-e)/(n=n||1/0)}),jh((e,t,r)=>{var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))});var Th=e=>{var[t,r]=e,[n,i]=[t,r];return t>r&&([n,i]=[r,t]),[n,i]},Ch=(e,t,r)=>{if(e.lte(0))return new gh(0);var n=kh(e.toNumber()),i=new gh(10).pow(n),a=e.div(i),o=1!==n?.05:.1,l=new gh(Math.ceil(a.div(o).toNumber())).add(r).mul(o).mul(i);return new gh(t?l.toNumber():Math.ceil(l.toNumber()))},Dh=(e,t,r)=>{var n=new gh(1),i=new gh(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new gh(10).pow(kh(e)-1),i=new gh(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new gh(Math.floor(e)))}else 0===e?i=new gh(Math.floor((t-1)/2)):r||(i=new gh(Math.floor(e)));var o=Math.floor((t-1)/2);return function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return mh;var n=t.reverse(),i=n[0],a=n.slice(1);return function(){return a.reduce((e,t)=>t(e),i(...arguments))}}(Ph(e=>i.add(new gh(e-o).mul(n)).toNumber()),Ah)(0,t)},Nh=function(e,t,r,n){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new gh(0),tickMin:new gh(0),tickMax:new gh(0)};var a,o=Ch(new gh(t).sub(e).div(r-1),n,i);a=e<=0&&t>=0?new gh(0):(a=new gh(e).add(t).div(2)).sub(new gh(a).mod(o));var l=Math.ceil(a.sub(e).div(o).toNumber()),c=Math.ceil(new gh(t).sub(a).div(o).toNumber()),s=l+c+1;return s>r?Nh(e,t,r,n,i+1):(s<r&&(c=t>0?c+(r-s):c,l=t>0?l:l+(r-s)),{step:o,tickMin:a.sub(new gh(l).mul(o)),tickMax:a.add(new gh(c).mul(o))})};var Ih=Eh(function(e){var[t,r]=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Math.max(n,2),[o,l]=Th([t,r]);if(o===-1/0||l===1/0){var c=l===1/0?[o,...Ah(0,n-1).map(()=>1/0)]:[...Ah(0,n-1).map(()=>-1/0),l];return t>r?Sh(c):c}if(o===l)return Dh(o,n,i);var{step:s,tickMin:u,tickMax:f}=Nh(o,l,a,i,0),d=Mh(u,f.add(new gh(.1).mul(s)),s);return t>r?Sh(d):d}),_h=Eh(function(e,t){var[r,n]=e,i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],[a,o]=Th([r,n]);if(a===-1/0||o===1/0)return[r,n];if(a===o)return[a];var l=Math.max(t,2),c=Ch(new gh(o).sub(a).div(l-1),i,0),s=[...Mh(new gh(a),new gh(o),c),o];return!1===i&&(s=s.map(e=>Math.round(e))),r>n?Sh(s):s}),Rh=e=>e.rootProps.maxBarSize,Lh=e=>e.rootProps.barCategoryGap,zh=e=>e.rootProps.stackOffset,Bh=e=>e.options.chartName,Kh=e=>e.rootProps.syncId,$h=e=>e.rootProps.syncMethod,Uh=e=>e.options.eventEmitter,Fh=0,Wh="auto",Hh=!0,Vh=!1,qh=!0,Yh=0,Gh="auto",Xh=!0,Zh=5,Jh=(e,t)=>{if(e&&t)return null!=e&&e.reversed?[t[1],t[0]]:t},Qh={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:Fh,includeHidden:!1,name:void 0,reversed:!1,scale:Wh,tick:Hh,tickCount:void 0,ticks:void 0,type:"category",unit:void 0},ep={allowDataOverflow:Vh,allowDecimals:!1,allowDuplicatedCategory:qh,dataKey:void 0,domain:void 0,id:Yh,includeHidden:!1,name:void 0,reversed:!1,scale:Gh,tick:Xh,tickCount:Zh,ticks:void 0,type:"number",unit:void 0},tp={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!0,dataKey:void 0,domain:void 0,id:Fh,includeHidden:!1,name:void 0,reversed:!1,scale:Wh,tick:Hh,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},rp={allowDataOverflow:Vh,allowDecimals:!1,allowDuplicatedCategory:qh,dataKey:void 0,domain:void 0,id:Yh,includeHidden:!1,name:void 0,reversed:!1,scale:Gh,tick:Xh,tickCount:Zh,ticks:void 0,type:"category",unit:void 0},np=(e,t)=>null!=e.polarAxis.angleAxis[t]?e.polarAxis.angleAxis[t]:"radial"===e.layout.layoutType?tp:Qh,ip=(e,t)=>null!=e.polarAxis.radiusAxis[t]?e.polarAxis.radiusAxis[t]:"radial"===e.layout.layoutType?rp:ep,ap=e=>e.polarOptions,op=U([ma,ba,Ma],Gi),lp=U([ap,op],(e,t)=>{if(null!=e)return Pe(e.innerRadius,t,0)}),cp=U([ap,op],(e,t)=>{if(null!=e)return Pe(e.outerRadius,t,.8*t)}),sp=U([ap],e=>{if(null==e)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]});U([np,sp],Jh);var up=U([op,lp,cp],(e,t,r)=>{if(null!=e&&null!=t&&null!=r)return[t,r]});U([ip,up],Jh);var fp=U([Ua,ap,lp,cp,ma,ba],(e,t,r,n,i,a)=>{if(("centric"===e||"radial"===e)&&null!=t&&null!=r&&null!=n){var{cx:o,cy:l,startAngle:c,endAngle:s}=t;return{cx:Pe(o,i,i/2),cy:Pe(l,a,a/2),innerRadius:r,outerRadius:n,startAngle:c,endAngle:s,clockWise:!1}}}),dp=(e,t)=>t,hp=(e,t,r)=>r;function pp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yp(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pp(Object(r),!0).forEach(function(t){vp(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pp(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function vp(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var gp=[0,"auto"],mp={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},bp=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return null==r?mp:r},xp={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:gp,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:60},wp=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return null==r?xp:r},Op={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},jp=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return null==r?Op:r},Ap=(e,t,r)=>{switch(t){case"xAxis":return bp(e,r);case"yAxis":return wp(e,r);case"zAxis":return jp(e,r);case"angleAxis":return np(e,r);case"radiusAxis":return ip(e,r);default:throw new Error("Unexpected axis type: ".concat(t))}},Pp=(e,t,r)=>{switch(t){case"xAxis":return bp(e,r);case"yAxis":return wp(e,r);case"angleAxis":return np(e,r);case"radiusAxis":return ip(e,r);default:throw new Error("Unexpected axis type: ".concat(t))}},Sp=e=>e.graphicalItems.countOfBars>0;function Ep(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var kp=e=>e.graphicalItems.cartesianItems,Mp=U([dp,hp],Ep),Tp=(e,t,r)=>e.filter(r).filter(e=>!0===(null==t?void 0:t.includeHidden)||!e.hide),Cp=U([kp,Ap,Mp],Tp),Dp=e=>e.filter(e=>void 0===e.stackId),Np=U([Cp],Dp),Ip=e=>e.map(e=>e.data).filter(Boolean).flat(1),_p=U([Cp],Ip),Rp=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=t;return e.length>0?e:r.slice(n,i+1)},Lp=U([_p,Bd],Rp),zp=(e,t,r)=>null!=(null==t?void 0:t.dataKey)?e.map(e=>({value:ra(e,t.dataKey)})):r.length>0?r.map(e=>e.dataKey).flatMap(t=>e.map(e=>({value:ra(e,t)}))):e.map(e=>({value:e})),Bp=U([Lp,Ap,Cp],zp);function Kp(e,t){switch(e){case"xAxis":return"x"===t.direction;case"yAxis":return"y"===t.direction;default:return!1}}function $p(e){return e.filter(e=>Oe(e)||e instanceof Date).map(Number).filter(e=>!1===be(e))}function Up(e,t,r){return!r||"number"!=typeof t||be(t)?[]:r.length?$p(r.flatMap(r=>{var n,i,a=ra(e,r.dataKey);if(Array.isArray(a)?[n,i]=a:n=i=a,Ao(n)&&Ao(i))return[t-n,t+i]})):[]}var Fp=(e,t,r)=>{var n=t.reduce((e,t)=>(null==t.stackId||(null==e[t.stackId]&&(e[t.stackId]=[]),e[t.stackId].push(t)),e),{});return Object.fromEntries(Object.entries(n).map(t=>{var[n,i]=t,a=i.map(e=>e.dataKey);return[n,{stackedData:ca(e,a,r),graphicalItems:i}]}))},Wp=U([Lp,Cp,zh],Fp),Hp=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:i}=t;if("zAxis"!==r){var a=da(e,n,i);if(null==a||0!==a[0]||0!==a[1])return a}},Vp=U([Wp,Ld,dp],Hp),qp=(e,t,r,n)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var i,a,o=null===(i=r.errorBars)||void 0===i?void 0:i.filter(e=>Kp(n,e)),l=ra(e,null!==(a=t.dataKey)&&void 0!==a?a:r.dataKey);return{value:l,errorDomain:Up(e,l,o)}})).filter(Boolean):null!=(null==t?void 0:t.dataKey)?e.map(e=>({value:ra(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]})),Yp=U(Lp,Ap,Np,dp,qp);function Gp(e){var{value:t}=e;if(Oe(t)||t instanceof Date)return t}var Xp=e=>{var t;if(null==e||!("domain"in e))return gp;if(null!=e.domain)return e.domain;if(null!=e.ticks){if("number"===e.type){var r=$p(e.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===e.type)return e.ticks.map(String)}return null!==(t=null==e?void 0:e.domain)&&void 0!==t?t:gp},Zp=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.filter(Boolean);if(0!==n.length){var i=n.flat();return[Math.min(...i),Math.max(...i)]}},Jp=e=>e.referenceElements.dots,Qp=(e,t,r)=>e.filter(e=>"extendDomain"===e.ifOverflow).filter(e=>"xAxis"===t?e.xAxisId===r:e.yAxisId===r),ey=U([Jp,dp,hp],Qp),ty=e=>e.referenceElements.areas,ry=U([ty,dp,hp],Qp),ny=e=>e.referenceElements.lines,iy=U([ny,dp,hp],Qp),ay=(e,t)=>{var r=$p(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},oy=U(ey,dp,ay),ly=(e,t)=>{var r=$p(e.flatMap(e=>["xAxis"===t?e.x1:e.y1,"xAxis"===t?e.x2:e.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},cy=U([ry,dp],ly),sy=(e,t)=>{var r=$p(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},uy=U(iy,dp,sy),fy=U(oy,uy,cy,(e,t,r)=>Zp(e,r,t)),dy=U([Ap],Xp),hy=(e,t,r,n,i)=>{var a=function(e,t){if(t&&"function"!=typeof e&&Array.isArray(e)&&2===e.length){var r,n,[i,a]=e;if(Ao(i))r=i;else if("function"==typeof i)return;if(Ao(a))n=a;else if("function"==typeof a)return;var o=[r,n];if(Kd(o))return o}}(t,e.allowDataOverflow);return null!=a?a:function(e,t,r){if(r||null!=t){if("function"==typeof e&&null!=t)try{var n=e(t,r);if(Kd(n))return $d(n,t,r)}catch(h){}if(Array.isArray(e)&&2===e.length){var i,a,[o,l]=e;if("auto"===o)null!=t&&(i=Math.min(...t));else if(we(o))i=o;else if("function"==typeof o)try{null!=t&&(i=o(null==t?void 0:t[0]))}catch(p){}else if("string"==typeof o&&ha.test(o)){var c=ha.exec(o);if(null==c||null==t)i=void 0;else{var s=+c[1];i=t[0]-s}}else i=null==t?void 0:t[0];if("auto"===l)null!=t&&(a=Math.max(...t));else if(we(l))a=l;else if("function"==typeof l)try{null!=t&&(a=l(null==t?void 0:t[1]))}catch(y){}else if("string"==typeof l&&pa.test(l)){var u=pa.exec(l);if(null==u||null==t)a=void 0;else{var f=+u[1];a=t[1]+f}}else a=null==t?void 0:t[1];var d=[i,a];if(Kd(d))return null==t?d:$d(d,t,r)}}}(t,Zp(r,i,(e=>{var t=$p(e.flatMap(e=>[e.value,e.errorDomain]).flat(1));if(0!==t.length)return[Math.min(...t),Math.max(...t)]})(n)),e.allowDataOverflow)},py=U([Ap,dy,Vp,Yp,fy],hy),yy=[0,1],vy=(e,t,r,n,i,a,o)=>{if(null!=e&&null!=r&&0!==r.length){var{dataKey:l,type:c}=e,s=na(t,a);return s&&null==l?rc(0,r.length):"category"===c?((e,t,r)=>{var n=e.map(Gp).filter(e=>null!=e);return r&&(null==t.dataKey||t.allowDuplicatedCategory&&Se(n))?rc(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))})(n,e,s):"expand"===i?yy:o}},gy=U([Ap,Ua,Lp,Bp,zh,dp,py],vy),my=(e,t,r,n,i)=>{if(null!=e){var{scale:a,type:o}=e;if("auto"===a)return"radial"===t&&"radiusAxis"===i?"band":"radial"===t&&"angleAxis"===i?"linear":"category"===o&&n&&(n.indexOf("LineChart")>=0||n.indexOf("AreaChart")>=0||n.indexOf("ComposedChart")>=0&&!r)?"point":"category"===o?"band":"linear";if("string"==typeof a){var l="scale".concat(Ce(a));return l in Rd?l:"point"}}},by=U([Ap,Ua,Sp,Bh,dp],my);function xy(e,t,r,n){if(null!=r&&null!=n){if("function"==typeof e.scale)return e.scale.copy().domain(r).range(n);var i=function(e){if(null!=e){if(e in Rd)return Rd[e]();var t="scale".concat(Ce(e));return t in Rd?Rd[t]():void 0}}(t);if(null!=i){var a=i.domain(r).range(n);return(e=>{var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),i=Math.min(n[0],n[1])-oa,a=Math.max(n[0],n[1])+oa,o=e(t[0]),l=e(t[r-1]);(o<i||o>a||l<i||l>a)&&e.domain([t[0],t[r-1]])}})(a),a}}}var wy=(e,t,r)=>{var n=Xp(t);if("auto"===r||"linear"===r)return null!=t&&t.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&Kd(e)?Ih(e,t.tickCount,t.allowDecimals):null!=t&&t.tickCount&&"number"===t.type&&Kd(e)?_h(e,t.tickCount,t.allowDecimals):void 0},Oy=U([gy,Pp,by],wy),jy=(e,t,r,n)=>{if("angleAxis"!==n&&"number"===(null==e?void 0:e.type)&&Kd(t)&&Array.isArray(r)&&r.length>0){var i=t[0],a=r[0],o=t[1],l=r[r.length-1];return[Math.min(i,a),Math.max(o,l)]}return t},Ay=U([Ap,gy,Oy,dp],jy),Py=U(Bp,Ap,(e,t)=>{if(t&&"number"===t.type){var r=1/0,n=Array.from($p(e.map(e=>e.value))).sort((e,t)=>e-t);if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(0===i)return 1/0;for(var a=0;a<n.length-1;a++){var o=n[a+1]-n[a];r=Math.min(r,o)}return r/i}}),Sy=U(Py,Ua,Lh,Ma,(e,t,r,n)=>n,(e,t,r,n,i)=>{if(!Ao(e))return 0;var a="vertical"===t?n.height:n.width;if("gap"===i)return e*a/2;if("no-gap"===i){var o=Pe(r,e*a),l=e*a/2;return l-o-(l-o)/a*o}return 0}),Ey=U(bp,(e,t)=>{var r=bp(e,t);return null==r||"string"!=typeof r.padding?0:Sy(e,"xAxis",t,r.padding)},(e,t)=>{var r,n;if(null==e)return{left:0,right:0};var{padding:i}=e;return"string"==typeof i?{left:t,right:t}:{left:(null!==(r=i.left)&&void 0!==r?r:0)+t,right:(null!==(n=i.right)&&void 0!==n?n:0)+t}}),ky=U(wp,(e,t)=>{var r=wp(e,t);return null==r||"string"!=typeof r.padding?0:Sy(e,"yAxis",t,r.padding)},(e,t)=>{var r,n;if(null==e)return{top:0,bottom:0};var{padding:i}=e;return"string"==typeof i?{top:t,bottom:t}:{top:(null!==(r=i.top)&&void 0!==r?r:0)+t,bottom:(null!==(n=i.bottom)&&void 0!==n?n:0)+t}}),My=U([Ma,Ey,_a,Ia,(e,t,r)=>r],(e,t,r,n,i)=>{var{padding:a}=n;return i?[a.left,r.width-a.right]:[e.left+t.left,e.left+e.width-t.right]}),Ty=U([Ma,Ua,ky,_a,Ia,(e,t,r)=>r],(e,t,r,n,i,a)=>{var{padding:o}=i;return a?[n.height-o.bottom,o.top]:"horizontal"===t?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]}),Cy=(e,t,r,n)=>{var i;switch(t){case"xAxis":return My(e,r,n);case"yAxis":return Ty(e,r,n);case"zAxis":return null===(i=jp(e,r))||void 0===i?void 0:i.range;case"angleAxis":return sp(e);case"radiusAxis":return up(e,r);default:return}},Dy=U([Ap,Cy],Jh),Ny=U([Ap,by,Ay,Dy],xy);function Iy(e,t){return e.id<t.id?-1:e.id>t.id?1:0}U(Cp,dp,(e,t)=>e.flatMap(e=>{var t;return null!==(t=e.errorBars)&&void 0!==t?t:[]}).filter(e=>Kp(t,e)));var _y=(e,t)=>t,Ry=(e,t,r)=>r,Ly=U(Oa,_y,Ry,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(Iy)),zy=U(ja,_y,Ry,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(Iy)),By=(e,t)=>({width:e.width,height:t.height}),Ky=U(Ma,bp,By),$y=U(ba,Ma,Ly,_y,Ry,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=By(t,r);null==a&&(a=((e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}})(t,n,e));var c="top"===n&&!i||"bottom"===n&&i;o[r.id]=a-Number(c)*l.height,a+=(c?-1:1)*l.height}),o}),Uy=U(ma,Ma,zy,_y,Ry,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=((e,t)=>({width:"number"==typeof t.width?t.width:60,height:e.height}))(t,r);null==a&&(a=((e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}})(t,n,e));var c="left"===n&&!i||"right"===n&&i;o[r.id]=a-Number(c)*l.width,a+=(c?-1:1)*l.width}),o}),Fy=U(Ma,wp,(e,t)=>({width:"number"==typeof t.width?t.width:60,height:e.height})),Wy=(e,t,r)=>{switch(t){case"xAxis":return Ky(e,r).width;case"yAxis":return Fy(e,r).height;default:return}},Hy=(e,t,r,n)=>{if(null!=r){var{allowDuplicatedCategory:i,type:a,dataKey:o}=r,l=na(e,n),c=t.map(e=>e.value);return o&&l&&"category"===a&&i&&Se(c)?c:void 0}},Vy=U([Ua,Bp,Ap,dp],Hy),qy=(e,t,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:a}=r;return!na(e,n)||"number"!==i&&"auto"===a?void 0:t.map(e=>e.value)}},Yy=U([Ua,Bp,Pp,dp],qy),Gy=U([Ua,(e,t,r)=>{switch(t){case"xAxis":return bp(e,r);case"yAxis":return wp(e,r);default:throw new Error("Unexpected axis type: ".concat(t))}},by,Ny,Vy,Yy,Cy,Oy,dp],(e,t,r,n,i,a,o,l,c)=>{if(null==t)return null;var s=na(e,c);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:c,categoricalDomain:a,duplicateDomain:i,isCategorical:s,niceTicks:l,range:o,realScaleType:r,scale:n}}),Xy=U([Ua,Pp,by,Ny,Oy,Cy,Vy,Yy,dp],(e,t,r,n,i,a,o,l,c)=>{if(null!=t&&null!=n){var s=na(e,c),{type:u,ticks:f,tickCount:d}=t,h="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,p="category"===u&&n.bandwidth?n.bandwidth()/h:0;p="angleAxis"===c&&null!=a&&a.length>=2?2*me(a[0]-a[1])*p:p;var y=f||i;return y?y.map((e,t)=>{var r=o?o.indexOf(e):e;return{index:t,coordinate:n(r)+p,value:e,offset:p}}).filter(e=>!be(e.coordinate)):s&&l?l.map((e,t)=>({coordinate:n(e)+p,value:e,index:t,offset:p})):n.ticks?n.ticks(d).map(e=>({coordinate:n(e)+p,value:e,offset:p})):n.domain().map((e,t)=>({coordinate:n(e)+p,value:o?o[e]:e,index:t,offset:p}))}}),Zy=U([Ua,Pp,Ny,Cy,Vy,Yy,dp],(e,t,r,n,i,a,o)=>{if(null!=t&&null!=r&&null!=n&&n[0]!==n[1]){var l=na(e,o),{tickCount:c}=t,s=0;return s="angleAxis"===o&&(null==n?void 0:n.length)>=2?2*me(n[0]-n[1])*s:s,l&&a?a.map((e,t)=>({coordinate:r(e)+s,value:e,index:t,offset:s})):r.ticks?r.ticks(c).map(e=>({coordinate:r(e)+s,value:e,offset:s})):r.domain().map((e,t)=>({coordinate:r(e)+s,value:i?i[e]:e,index:t,offset:s}))}}),Jy=U(Ap,Ny,(e,t)=>{if(null!=e&&null!=t)return yp(yp({},e),{},{scale:t})}),Qy=U([Ap,by,gy,Dy],xy);U((e,t,r)=>jp(e,r),Qy,(e,t)=>{if(null!=e&&null!=t)return yp(yp({},e),{},{scale:t})});var ev=U([Ua,Oa,ja],(e,t,r)=>{switch(e){case"horizontal":return t.some(e=>e.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(e=>e.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}}),tv=e=>e.options.defaultTooltipEventType,rv=e=>e.options.validateTooltipEventTypes;function nv(e,t,r){if(null==e)return t;var n=e?"axis":"item";return null==r?t:r.includes(n)?n:t}function iv(e,t){return nv(t,tv(e),rv(e))}var av=(e,t)=>{var r,n=Number(t);if(!be(n)&&null!=t)return n>=0?null==e||null===(r=e[n])||void 0===r?void 0:r.value:void 0},ov={active:!1,index:null,dataKey:void 0,coordinate:void 0},lv=r({name:"tooltip",initialState:{itemInteraction:{click:ov,hover:ov},axisInteraction:{click:ov,hover:ov},keyboardInteraction:ov,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push(i(t.payload))},removeTooltipEntrySettings(e,t){var r=n(e).tooltipItemPayloads.indexOf(i(t.payload));r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:cv,removeTooltipEntrySettings:sv,setTooltipSettingsState:uv,setActiveMouseOverItemIndex:fv,mouseLeaveItem:dv,mouseLeaveChart:hv,setActiveClickItemIndex:pv,setMouseOverAxisIndex:yv,setMouseClickAxisIndex:vv,setSyncInteraction:gv,setKeyboardInteraction:mv}=lv.actions,bv=lv.reducer;function xv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function wv(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?xv(Object(r),!0).forEach(function(t){Ov(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xv(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Ov(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var jv=(e,t,r,n)=>{if(null==t)return ov;var i=function(e,t,r){return"axis"===t?"click"===r?e.axisInteraction.click:e.axisInteraction.hover:"click"===r?e.itemInteraction.click:e.itemInteraction.hover}(e,t,r);if(null==i)return ov;if(i.active)return i;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&null!=e.syncInteraction.index)return e.syncInteraction;var a=!0===e.settings.active;if(null!=i.index){if(a)return wv(wv({},i),{},{active:!0})}else if(null!=n)return{active:!0,coordinate:void 0,dataKey:void 0,index:n};return wv(wv({},ov),{},{coordinate:i.coordinate})},Av=(e,t)=>{var r=null==e?void 0:e.index;if(null==r)return null;var n=Number(r);if(!Ao(n))return r;var i=1/0;return t.length>0&&(i=t.length-1),String(Math.max(0,Math.min(n,i)))},Pv=(e,t,r,n,i,a,o,l)=>{if(null!=a&&null!=l){var c=o[0],s=null==c?void 0:l(c.positions,a);if(null!=s)return s;var u=null==i?void 0:i[Number(a)];if(u)return"horizontal"===r?{x:u.coordinate,y:(n.top+t)/2}:{x:(n.left+e)/2,y:u.coordinate}}},Sv=(e,t,r,n)=>{return"axis"===t?e.tooltipItemPayloads:0===e.tooltipItemPayloads.length?[]:null==(i="hover"===r?e.itemInteraction.hover.dataKey:e.itemInteraction.click.dataKey)&&null!=n?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter(e=>{var t;return(null===(t=e.settings)||void 0===t?void 0:t.dataKey)===i});var i},Ev=e=>e.options.tooltipPayloadSearcher,kv=e=>e.tooltip;function Mv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Tv(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Mv(Object(r),!0).forEach(function(t){Cv(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Mv(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Cv(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Dv=(e,t,r,n,i,a,o)=>{if(null!=t&&null!=a){var{chartData:l,computedData:c,dataStartIndex:s,dataEndIndex:u}=r;return e.reduce((e,r)=>{var f,d,h,p,y,v,{dataDefinedOnItem:g,settings:m}=r,b=function(e,t){return null!=e?e:t}(g,l),x=(d=b,h=s,p=u,Array.isArray(d)&&d&&h+p!==0?d.slice(h,p+1):d),w=null!==(f=null==m?void 0:m.dataKey)&&void 0!==f?f:null==n?void 0:n.dataKey,O=null==m?void 0:m.nameKey;(y=null!=n&&n.dataKey&&Array.isArray(x)&&!Array.isArray(x[0])&&"axis"===o?Me(x,n.dataKey,i):a(x,t,c,O),Array.isArray(y))?y.forEach(t=>{var r=Tv(Tv({},m),{},{name:t.name,unit:t.unit,color:void 0,fill:void 0});e.push(va({tooltipEntrySettings:r,dataKey:t.dataKey,payload:t.payload,value:ra(t.payload,t.dataKey),name:t.name}))}):e.push(va({tooltipEntrySettings:m,dataKey:w,payload:y,value:ra(y,w),name:null!==(v=ra(y,O))&&void 0!==v?v:null==m?void 0:m.name}));return e},[])}},Nv=e=>{var t=Ua(e);return"horizontal"===t?"xAxis":"vertical"===t?"yAxis":"centric"===t?"angleAxis":"radiusAxis"},Iv=e=>e.tooltip.settings.axisId,_v=e=>{var t=Nv(e),r=Iv(e);return Pp(e,t,r)},Rv=U([_v,Ua,Sp,Bh,Nv],my),Lv=U([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],(e,t)=>[...e,...t]),zv=U([Nv,Iv],Ep),Bv=U([Lv,_v,zv],Tp),Kv=U([Bv],Ip),$v=U([Kv,Ld],Rp),Uv=U([$v,_v,Bv],zp),Fv=U([_v],Xp),Wv=U([$v,Bv,zh],Fp),Hv=U([Wv,Ld,Nv],Hp),Vv=U([Bv],Dp),qv=U([$v,_v,Vv,Nv],qp),Yv=U([Jp,Nv,Iv],Qp),Gv=U([Yv,Nv],ay),Xv=U([ty,Nv,Iv],Qp),Zv=U([Xv,Nv],ly),Jv=U([ny,Nv,Iv],Qp),Qv=U([Jv,Nv],sy),eg=U([Gv,Qv,Zv],Zp),tg=U([_v,Fv,Hv,qv,eg],hy),rg=U([_v,Ua,$v,Uv,zh,Nv,tg],vy),ng=U([rg,_v,Rv],wy),ig=U([_v,rg,ng,Nv],jy),ag=e=>{var t=Nv(e),r=Iv(e);return Cy(e,t,r,!1)},og=U([_v,ag],Jh),lg=U([_v,Rv,ig,og],xy),cg=U([Ua,Uv,_v,Nv],Hy),sg=U([Ua,Uv,_v,Nv],qy),ug=U([Ua,_v,Rv,lg,ag,cg,sg,Nv],(e,t,r,n,i,a,o,l)=>{if(t){var{type:c}=t,s=na(e,l);if(n){var u="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,f="category"===c&&n.bandwidth?n.bandwidth()/u:0;return f="angleAxis"===l&&null!=i&&(null==i?void 0:i.length)>=2?2*me(i[0]-i[1])*f:f,s&&o?o.map((e,t)=>({coordinate:n(e)+f,value:e,index:t,offset:f})):n.domain().map((e,t)=>({coordinate:n(e)+f,value:a?a[e]:e,index:t,offset:f}))}}}),fg=U([tv,rv,e=>e.tooltip.settings],(e,t,r)=>nv(r.shared,e,t)),dg=e=>e.tooltip.settings.trigger,hg=e=>e.tooltip.settings.defaultIndex,pg=U([kv,fg,dg,hg],jv),yg=U([pg,$v],Av),vg=U([ug,yg],av),gg=U([pg],e=>{if(e)return e.dataKey}),mg=U([kv,fg,dg,hg],Sv),bg=U([ma,ba,Ua,Ma,ug,hg,mg,Ev],Pv),xg=U([pg,bg],(e,t)=>null!=e&&e.coordinate?e.coordinate:t),wg=U([pg],e=>e.active),Og=U([mg,yg,Ld,_v,vg,Ev,fg],Dv),jg=U([Og],e=>{if(null!=e){var t=e.map(e=>e.payload).filter(e=>null!=e);return Array.from(new Set(t))}});function Ag(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Pg(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ag(Object(r),!0).forEach(function(t){Sg(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ag(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Sg(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Eg=()=>{var e=si(_v),t=si(ug),r=si(lg);return ya(Pg(Pg({},e),{},{scale:r}),t)},kg=()=>si(Bh),Mg=(e,t)=>t,Tg=(e,t,r)=>r,Cg=(e,t,r,n)=>n,Dg=U(ug,e=>Ni(e,e=>e.coordinate)),Ng=U([kv,Mg,Tg,Cg],jv),Ig=U([Ng,$v],Av),_g=U([kv,Mg,Tg,Cg],Sv),Rg=U([ma,ba,Ua,Ma,ug,Cg,_g,Ev],Pv),Lg=U([Ng,Rg],(e,t)=>{var r;return null!==(r=e.coordinate)&&void 0!==r?r:t}),zg=U(ug,Ig,av),Bg=U([_g,Ig,Ld,_v,zg,Ev,Mg],Dv),Kg=U([Ng],e=>({isActive:e.active,activeIndex:e.index}));function $g(){return $g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$g.apply(null,arguments)}function Ug(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Fg(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ug(Object(r),!0).forEach(function(t){Wg(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ug(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Wg(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Hg(e){var t,r,{coordinate:n,payload:i,index:a,offset:o,tooltipAxisBandSize:l,layout:c,cursor:s,tooltipEventType:f,chartName:d}=e,h=n,p=i,y=a;if(!s||!h||"ScatterChart"!==d&&"axis"!==f)return null;if("ScatterChart"===d)t=h,r=$o;else if("BarChart"===d)t=function(e,t,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:"horizontal"===e?t.x-i:r.left+.5,y:"horizontal"===e?r.top+.5:t.y-i,width:"horizontal"===e?n:r.width-1,height:"horizontal"===e?r.height-1:n}}(c,h,o,l),r=Ll;else if("radial"===c){var{cx:v,cy:g,radius:m,startAngle:b,endAngle:x}=zl(h);t={cx:v,cy:g,startAngle:b,endAngle:x,innerRadius:m,outerRadius:m},r=Fl}else t={points:Wl(c,h,o)},r=_o;var w="object"==typeof s&&"className"in s?s.className:void 0,O=Fg(Fg(Fg(Fg({stroke:"#ccc",pointerEvents:"none"},o),t),We(s,!1)),{},{payload:p,payloadIndex:y,className:q("recharts-tooltip-cursor",w)});return u.isValidElement(s)?u.cloneElement(s,O):u.createElement(r,O)}function Vg(e){var t=Eg(),r=za(),n=Fa(),i=kg();return u.createElement(Hg,$g({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:r,layout:n,tooltipAxisBandSize:t,chartName:i}))}var qg,Yg=u.createContext(null),Gg={exports:{}};var Xg=new(I((qg||(qg=1,function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,n,a,o){if("function"!=typeof n)throw new TypeError("The listener must be a function");var l=new i(n,a||e,o),c=r?r+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],l]:e._events[c].push(l):(e._events[c]=l,e._eventsCount++),e}function o(e,t){0===--e._eventsCount?e._events=new n:delete e._events[t]}function l(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),l.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},l.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,o=new Array(a);i<a;i++)o[i]=n[i].fn;return o},l.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},l.prototype.emit=function(e,t,n,i,a,o){var l=r?r+e:e;if(!this._events[l])return!1;var c,s,u=this._events[l],f=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),f){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,i),!0;case 5:return u.fn.call(u.context,t,n,i,a),!0;case 6:return u.fn.call(u.context,t,n,i,a,o),!0}for(s=1,c=new Array(f-1);s<f;s++)c[s-1]=arguments[s];u.fn.apply(u.context,c)}else{var d,h=u.length;for(s=0;s<h;s++)switch(u[s].once&&this.removeListener(e,u[s].fn,void 0,!0),f){case 1:u[s].fn.call(u[s].context);break;case 2:u[s].fn.call(u[s].context,t);break;case 3:u[s].fn.call(u[s].context,t,n);break;case 4:u[s].fn.call(u[s].context,t,n,i);break;default:if(!c)for(d=1,c=new Array(f-1);d<f;d++)c[d-1]=arguments[d];u[s].fn.apply(u[s].context,c)}}return!0},l.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},l.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},l.prototype.removeListener=function(e,t,n,i){var a=r?r+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var l=this._events[a];if(l.fn)l.fn!==t||i&&!l.once||n&&l.context!==n||o(this,a);else{for(var c=0,s=[],u=l.length;c<u;c++)(l[c].fn!==t||i&&!l[c].once||n&&l[c].context!==n)&&s.push(l[c]);s.length?this._events[a]=1===s.length?s[0]:s:o(this,a)}return this},l.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=r,l.EventEmitter=l,e.exports=l}(Gg)),Gg.exports))),Zg="recharts.syncEvent.tooltip",Jg="recharts.syncEvent.brush";function Qg(e,t){if(t){var r=Number.parseInt(t,10);if(!be(r))return null==e?void 0:e[r]}}var em=r({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:e=>{null==e.eventEmitter&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),tm=em.reducer,{createEventEmitter:rm}=em.actions;function nm(e){return e.tooltip.syncInteraction}var im=r({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(e,t){if(e.chartData=t.payload,null==t.payload)return e.dataStartIndex=0,void(e.dataEndIndex=0);t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;null!=r&&(e.dataStartIndex=r),null!=n&&(e.dataEndIndex=n)}}}),{setChartData:am,setDataStartEndIndexes:om,setComputedData:lm}=im.actions,cm=im.reducer,sm=()=>{};function um(){var e=ai();u.useEffect(()=>{e(rm())},[e]),function(){var e=si(Kh),t=si(Uh),r=ai(),n=si($h),i=si(ug),a=Fa(),o=Ra(),l=si(e=>e.rootProps.className);u.useEffect(()=>{if(null==e)return sm;var l=(l,c,s)=>{if(t!==s&&e===l)if("index"!==n){if(null!=i){var u;if("function"==typeof n){var f={activeTooltipIndex:null==c.payload.index?void 0:Number(c.payload.index),isTooltipActive:c.payload.active,activeIndex:null==c.payload.index?void 0:Number(c.payload.index),activeLabel:c.payload.label,activeDataKey:c.payload.dataKey,activeCoordinate:c.payload.coordinate},d=n(i,f);u=i[d]}else"value"===n&&(u=i.find(e=>String(e.value)===c.payload.label));var{coordinate:h}=c.payload;if(null!=u&&!1!==c.payload.active&&null!=h&&null!=o){var{x:p,y:y}=h,v=Math.min(p,o.x+o.width),g=Math.min(y,o.y+o.height),m={x:"horizontal"===a?u.coordinate:v,y:"horizontal"===a?g:u.coordinate},b=gv({active:c.payload.active,coordinate:m,dataKey:c.payload.dataKey,index:String(u.index),label:c.payload.label});r(b)}else r(gv({active:!1,coordinate:void 0,dataKey:void 0,index:null,label:void 0}))}}else r(c)};return Xg.on(Zg,l),()=>{Xg.off(Zg,l)}},[l,r,t,e,n,i,a,o])}(),function(){var e=si(Kh),t=si(Uh),r=ai();u.useEffect(()=>{if(null==e)return sm;var n=(n,i,a)=>{t!==a&&e===n&&r(om(i))};return Xg.on(Jg,n),()=>{Xg.off(Jg,n)}},[r,t,e])}()}function fm(e,t,r,n,i,a){var o=si(r=>((e,t,r)=>{if(null!=t){var n=kv(e);return"axis"===t?"hover"===r?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:"hover"===r?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}})(r,e,t)),l=si(Uh),c=si(Kh),s=si($h),f=si(nm),d=null==f?void 0:f.active;u.useEffect(()=>{if(!d&&null!=c&&null!=l){var e=gv({active:a,coordinate:r,dataKey:o,index:i,label:"number"==typeof n?String(n):n});Xg.emit(Zg,c,e,l)}},[d,r,o,i,n,l,c,s,a])}function dm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hm(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dm(Object(r),!0).forEach(function(t){pm(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dm(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pm(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ym(e){return e.dataKey}var vm=[],gm={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!Oo,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function mm(e){var t=Wo(e,gm),{active:r,allowEscapeViewBox:n,animationDuration:i,animationEasing:a,content:o,filterNull:l,isAnimationActive:c,offset:s,payloadUniqBy:f,position:d,reverseDirection:h,useTranslate3d:p,wrapperStyle:y,cursor:v,shared:g,trigger:b,defaultIndex:x,portal:w,axisId:O}=t,j=ai(),A="number"==typeof x?String(x):x;u.useEffect(()=>{j(uv({shared:g,trigger:b,axisId:O,active:r,defaultIndex:A}))},[j,g,b,O,r,A]);var P=Ra(),S=jo(),E=function(e){return si(t=>iv(t,e))}(g),{activeIndex:k,isActive:M}=si(e=>Kg(e,E,b,A)),T=si(e=>Bg(e,E,b,A)),C=si(e=>zg(e,E,b,A)),D=si(e=>Lg(e,E,b,A)),N=T,I=u.useContext(Yg),_=null!=r?r:M,[R,L]=Ri([N,_]),z="axis"===E?C:void 0;fm(E,b,D,z,k,_);var B=null!=w?w:I;if(null==B)return null;var K=null!=N?N:vm;_||(K=vm),l&&K.length&&(K=Vn(N.filter(e=>null!=e.value&&(!0!==e.hide||t.includeHidden)),f,ym));var $=K.length>0,U=u.createElement(wo,{allowEscapeViewBox:n,animationDuration:i,animationEasing:a,isAnimationActive:c,active:_,coordinate:D,hasPayload:$,offset:s,position:d,reverseDirection:h,useTranslate3d:p,viewBox:P,wrapperStyle:y,lastBoundingBox:R,innerRef:L,hasPortalFromProps:Boolean(w)},function(e,t){return u.isValidElement(e)?u.cloneElement(e,t):"function"==typeof e?u.createElement(e,t):u.createElement(ho,t)}(o,hm(hm({},t),{},{payload:K,label:z,active:_,coordinate:D,accessibilityLayer:S})));return u.createElement(u.Fragment,null,m.createPortal(U,B),_&&u.createElement(Vg,{cursor:v,tooltipEventType:E,coordinate:D,payload:N,index:k}))}var bm,xm,wm,Om,jm={},Am={};function Pm(){return bm||(bm=1,e=Am,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.debounce=function(e,t=0,r={}){"object"!=typeof r&&(r={});let n,i=null,a=null,o=null,l=0,c=null;const{leading:s=!1,trailing:u=!0,maxWait:f}=r,d="maxWait"in r,h=d?Math.max(Number(f)||0,t):0,p=t=>(null!==i&&(n=e.apply(a,i)),i=a=null,l=t,n),y=e=>(c=null,u&&null!==i?p(e):n),v=e=>{if(null===o)return!0;const r=e-o;return r>=t||r<0||d&&e-l>=h},g=()=>{const e=Date.now();if(v(e))return y(e);c=setTimeout(g,(e=>{const r=t-(null===o?0:e-o),n=h-(e-l);return d?Math.min(r,n):r})(e))},m=function(...e){const r=Date.now(),u=v(r);if(i=e,a=this,o=r,u){if(null===c)return l=f=r,c=setTimeout(g,t),s&&null!==i?p(f):n;if(d)return clearTimeout(c),c=setTimeout(g,t),p(r)}var f;return null===c&&(c=setTimeout(g,t)),n};return m.cancel=()=>{null!==c&&clearTimeout(c),l=0,o=i=a=c=null},m.flush=()=>null===c?n:y(Date.now()),m}),Am;var e}function Sm(){return Om?wm:(Om=1,wm=(xm||(xm=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=Pm();e.throttle=function(e,r=0,n={}){const{leading:i=!0,trailing:a=!0}=n;return t.debounce(e,r,{leading:i,maxWait:r,trailing:a})}}(jm)),jm).throttle)}const Em=I(Sm());var km=function(e,t){for(var r=arguments.length,n=new Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]};function Mm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Tm(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Mm(Object(r),!0).forEach(function(t){Cm(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Mm(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Cm(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Dm=u.forwardRef((e,t)=>{var{aspect:r,initialDimension:n={width:-1,height:-1},width:i="100%",height:a="100%",minWidth:o=0,minHeight:l,maxHeight:c,children:s,debounce:f=0,id:d,className:h,onResize:p,style:y={}}=e,v=u.useRef(null),g=u.useRef();g.current=p,u.useImperativeHandle(t,()=>v.current);var[m,b]=u.useState({containerWidth:n.width,containerHeight:n.height}),x=u.useCallback((e,t)=>{b(r=>{var n=Math.round(e),i=Math.round(t);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);u.useEffect(()=>{var e=e=>{var t,{width:r,height:n}=e[0].contentRect;x(r,n),null===(t=g.current)||void 0===t||t.call(g,r,n)};f>0&&(e=Em(e,f,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),{width:r,height:n}=v.current.getBoundingClientRect();return x(r,n),t.observe(v.current),()=>{t.disconnect()}},[x,f]);var w=u.useMemo(()=>{var{containerWidth:e,containerHeight:t}=m;if(e<0||t<0)return null;km(xe(i)||xe(a),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",i,a),km(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=xe(i)?e:i,f=xe(a)?t:a;return r&&r>0&&(n?f=n/r:f&&(n=f*r),c&&f>c&&(f=c)),km(n>0||f>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,f,i,a,o,l,r),u.Children.map(s,e=>u.cloneElement(e,{width:n,height:f,style:Tm({width:n,height:f},e.props.style)}))},[r,s,a,c,l,o,m,i]);return u.createElement("div",{id:d?"".concat(d):void 0,className:q("recharts-responsive-container",h),style:Tm(Tm({},y),{},{width:i,height:a,minWidth:o,minHeight:l,maxHeight:c}),ref:v},u.createElement("div",{style:{width:0,height:0,overflow:"visible"}},w))}),Nm=e=>null;function Im(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function _m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Im(Object(r),!0).forEach(function(t){Rm(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Im(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Rm(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}Nm.displayName="Cell";var Lm={widthCache:{},cacheCount:0},zm={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},Bm="recharts_measurement_span";var Km=function(e){if(null==e||Oo)return{width:0,height:0};var t,r=(t=_m({},arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}),Object.keys(t).forEach(e=>{t[e]||delete t[e]}),t),n=JSON.stringify({text:e,copyStyle:r});if(Lm.widthCache[n])return Lm.widthCache[n];try{var i=document.getElementById(Bm);i||((i=document.createElement("span")).setAttribute("id",Bm),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var a=_m(_m({},zm),r);Object.assign(i.style,a),i.textContent="".concat(e);var o=i.getBoundingClientRect(),l={width:o.width,height:o.height};return Lm.widthCache[n]=l,++Lm.cacheCount>2e3&&(Lm.cacheCount=0,Lm.widthCache={}),l}catch(c){return{width:0,height:0}}},$m=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Um=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Fm=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,Wm=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,Hm={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},Vm=Object.keys(Hm),qm="NaN";class Ym{static parse(e){var t,[,r,n]=null!==(t=Wm.exec(e))&&void 0!==t?t:[];return new Ym(parseFloat(r),null!=n?n:"")}constructor(e,t){this.num=e,this.unit=t,this.num=e,this.unit=t,be(e)&&(this.unit=""),""===t||Fm.test(t)||(this.num=NaN,this.unit=""),Vm.includes(t)&&(this.num=function(e,t){return e*Hm[t]}(e,t),this.unit="px")}add(e){return this.unit!==e.unit?new Ym(NaN,""):new Ym(this.num+e.num,this.unit)}subtract(e){return this.unit!==e.unit?new Ym(NaN,""):new Ym(this.num-e.num,this.unit)}multiply(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new Ym(NaN,""):new Ym(this.num*e.num,this.unit||e.unit)}divide(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new Ym(NaN,""):new Ym(this.num/e.num,this.unit||e.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return be(this.num)}}function Gm(e){if(e.includes(qm))return qm;for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,i,a]=null!==(r=$m.exec(t))&&void 0!==r?r:[],o=Ym.parse(null!=n?n:""),l=Ym.parse(null!=a?a:""),c="*"===i?o.multiply(l):o.divide(l);if(c.isNaN())return qm;t=t.replace($m,c.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var s,[,u,f,d]=null!==(s=Um.exec(t))&&void 0!==s?s:[],h=Ym.parse(null!=u?u:""),p=Ym.parse(null!=d?d:""),y="+"===f?h.add(p):h.subtract(p);if(y.isNaN())return qm;t=t.replace(Um,y.toString())}return t}var Xm=/\(([^()]*)\)/;function Zm(e){var t=e.replace(/\s+/g,"");return t=function(e){for(var t,r=e;null!=(t=Xm.exec(r));){var[,n]=t;r=r.replace(Xm,Gm(n))}return r}(t),t=Gm(t)}function Jm(e){var t=function(e){try{return Zm(e)}catch(t){return qm}}(e.slice(5,-1));return t===qm?"":t}var Qm=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],eb=["dx","dy","angle","className","breakAll"];function tb(){return tb=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},tb.apply(null,arguments)}function rb(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var nb=/[ \f\n\r\t\v\u2028\u2029]+/,ib=e=>{var{children:t,breakAll:r,style:n}=e;try{var i=[];return Te(t)||(i=r?t.toString().split(""):t.toString().split(nb)),{wordsWithComputedWidth:i.map(e=>({word:e,width:Km(e,n).width})),spaceWidth:r?0:Km(" ",n).width}}catch(a){return null}},ab=e=>[{words:Te(e)?[]:e.toString().split(nb)}],ob=e=>{var{width:t,scaleToFit:r,children:n,style:i,breakAll:a,maxLines:o}=e;if((t||r)&&!Oo){var l=ib({breakAll:a,children:n,style:i});if(!l)return ab(n);var{wordsWithComputedWidth:c,spaceWidth:s}=l;return((e,t,r,n,i)=>{var{maxLines:a,children:o,style:l,breakAll:c}=e,s=we(a),u=o,f=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).reduce((e,t)=>{var{word:a,width:o}=t,l=e[e.length-1];if(l&&(null==n||i||l.width+o+r<Number(n)))l.words.push(a),l.width+=o+r;else{var c={words:[a],width:o};e.push(c)}return e},[])},d=f(t),h=e=>e.reduce((e,t)=>e.width>t.width?e:t);if(!s||i)return d;if(!(d.length>a||h(d).width>Number(n)))return d;for(var p,y=e=>{var t=u.slice(0,e),r=ib({breakAll:c,style:l,children:t+"…"}).wordsWithComputedWidth,i=f(r);return[i.length>a||h(i).width>Number(n),i]},v=0,g=u.length-1,m=0;v<=g&&m<=u.length-1;){var b=Math.floor((v+g)/2),x=b-1,[w,O]=y(x),[j]=y(b);if(w||j||(v=b+1),w&&j&&(g=b-1),!w&&j){p=O;break}m++}return p||d})({breakAll:a,children:n,maxLines:o,style:i},c,s,t,r)}return ab(n)},lb="#808080",cb=u.forwardRef((e,t)=>{var{x:r=0,y:n=0,lineHeight:i="1em",capHeight:a="0.71em",scaleToFit:o=!1,textAnchor:l="start",verticalAnchor:c="end",fill:s=lb}=e,f=rb(e,Qm),d=u.useMemo(()=>ob({breakAll:f.breakAll,children:f.children,maxLines:f.maxLines,scaleToFit:o,style:f.style,width:f.width}),[f.breakAll,f.children,f.maxLines,o,f.style,f.width]),{dx:h,dy:p,angle:y,className:v,breakAll:g}=f,m=rb(f,eb);if(!Oe(r)||!Oe(n))return null;var b,x=r+(we(h)?h:0),w=n+(we(p)?p:0);switch(c){case"start":b=Jm("calc(".concat(a,")"));break;case"middle":b=Jm("calc(".concat((d.length-1)/2," * -").concat(i," + (").concat(a," / 2))"));break;default:b=Jm("calc(".concat(d.length-1," * -").concat(i,")"))}var O=[];if(o){var j=d[0].width,{width:A}=f;O.push("scale(".concat(we(A)?A/j:1,")"))}return y&&O.push("rotate(".concat(y,", ").concat(x,", ").concat(w,")")),O.length&&(m.transform=O.join(" ")),u.createElement("text",tb({},We(m,!0),{ref:t,x:x,y:w,className:q("recharts-text",v),textAnchor:l,fill:s.includes("url")?lb:s}),d.map((e,t)=>{var r=e.words.join(g?"":" ");return u.createElement("tspan",{x:x,dy:0===t?b:i,key:"".concat(r,"-").concat(t)},r)}))});cb.displayName="Text";var sb=["offset"],ub=["labelRef"];function fb(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function db(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hb(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?db(Object(r),!0).forEach(function(t){pb(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):db(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pb(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yb(){return yb=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},yb.apply(null,arguments)}var vb=e=>null!=e&&"function"==typeof e,gb=(e,t,r)=>{var n,i,{position:a,viewBox:o,offset:l,className:c}=e,{cx:s,cy:f,innerRadius:d,outerRadius:h,startAngle:p,endAngle:y,clockWise:v}=o,g=(d+h)/2,m=((e,t)=>me(t-e)*Math.min(Math.abs(t-e),360))(p,y),b=m>=0?1:-1;"insideStart"===a?(n=p+b*l,i=v):"insideEnd"===a?(n=y-b*l,i=!v):"end"===a&&(n=y+b*l,i=v),i=m<=0?i:!i;var x=Yi(s,f,g,n),w=Yi(s,f,g,n+359*(i?1:-1)),O="M".concat(x.x,",").concat(x.y,"\n    A").concat(g,",").concat(g,",0,1,").concat(i?0:1,",\n    ").concat(w.x,",").concat(w.y),j=Te(e.id)?Ae("recharts-radial-line-"):e.id;return u.createElement("text",yb({},r,{dominantBaseline:"central",className:q("recharts-radial-bar-label",c)}),u.createElement("defs",null,u.createElement("path",{id:j,d:O})),u.createElement("textPath",{xlinkHref:"#".concat(j)},t))};function mb(e){var t,{offset:r=5}=e,n=hb({offset:r},fb(e,sb)),{viewBox:i,position:a,value:o,children:l,content:c,className:s="",textBreakAll:f,labelRef:d}=n,h=Ra(),p=i||h;if(!p||Te(o)&&Te(l)&&!u.isValidElement(c)&&"function"!=typeof c)return null;if(u.isValidElement(c)){var{labelRef:y}=n,v=fb(n,ub);return u.cloneElement(c,v)}if("function"==typeof c){if(t=u.createElement(c,n),u.isValidElement(t))return t}else t=(e=>{var{value:t,formatter:r}=e,n=Te(e.children)?t:e.children;return"function"==typeof r?r(n):n})(n);var g=(e=>"cx"in e&&we(e.cx))(p),m=We(n,!0);if(g&&("insideStart"===a||"insideEnd"===a||"end"===a))return gb(n,t,m);var b=g?(e=>{var{viewBox:t,offset:r,position:n}=e,{cx:i,cy:a,innerRadius:o,outerRadius:l,startAngle:c,endAngle:s}=t,u=(c+s)/2;if("outside"===n){var{x:f,y:d}=Yi(i,a,l+r,u);return{x:f,y:d,textAnchor:f>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var h=(o+l)/2,{x:p,y:y}=Yi(i,a,h,u);return{x:p,y:y,textAnchor:"middle",verticalAnchor:"middle"}})(n):((e,t)=>{var{parentViewBox:r,offset:n,position:i}=e,{x:a,y:o,width:l,height:c}=t,s=c>=0?1:-1,u=s*n,f=s>0?"end":"start",d=s>0?"start":"end",h=l>=0?1:-1,p=h*n,y=h>0?"end":"start",v=h>0?"start":"end";if("top"===i)return hb(hb({},{x:a+l/2,y:o-s*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(o-r.y,0),width:l}:{});if("bottom"===i)return hb(hb({},{x:a+l/2,y:o+c+u,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(r.y+r.height-(o+c),0),width:l}:{});if("left"===i){var g={x:a-p,y:o+c/2,textAnchor:y,verticalAnchor:"middle"};return hb(hb({},g),r?{width:Math.max(g.x-r.x,0),height:c}:{})}if("right"===i){var m={x:a+l+p,y:o+c/2,textAnchor:v,verticalAnchor:"middle"};return hb(hb({},m),r?{width:Math.max(r.x+r.width-m.x,0),height:c}:{})}var b=r?{width:l,height:c}:{};return"insideLeft"===i?hb({x:a+p,y:o+c/2,textAnchor:v,verticalAnchor:"middle"},b):"insideRight"===i?hb({x:a+l-p,y:o+c/2,textAnchor:y,verticalAnchor:"middle"},b):"insideTop"===i?hb({x:a+l/2,y:o+u,textAnchor:"middle",verticalAnchor:d},b):"insideBottom"===i?hb({x:a+l/2,y:o+c-u,textAnchor:"middle",verticalAnchor:f},b):"insideTopLeft"===i?hb({x:a+p,y:o+u,textAnchor:v,verticalAnchor:d},b):"insideTopRight"===i?hb({x:a+l-p,y:o+u,textAnchor:y,verticalAnchor:d},b):"insideBottomLeft"===i?hb({x:a+p,y:o+c-u,textAnchor:v,verticalAnchor:f},b):"insideBottomRight"===i?hb({x:a+l-p,y:o+c-u,textAnchor:y,verticalAnchor:f},b):i&&"object"==typeof i&&(we(i.x)||xe(i.x))&&(we(i.y)||xe(i.y))?hb({x:a+Pe(i.x,l),y:o+Pe(i.y,c),textAnchor:"end",verticalAnchor:"end"},b):hb({x:a+l/2,y:o+c/2,textAnchor:"middle",verticalAnchor:"middle"},b)})(n,p);return u.createElement(cb,yb({ref:d,className:q("recharts-label",s)},m,b,{breakAll:f}),t)}mb.displayName="Label";var bb=e=>{var{cx:t,cy:r,angle:n,startAngle:i,endAngle:a,r:o,radius:l,innerRadius:c,outerRadius:s,x:u,y:f,top:d,left:h,width:p,height:y,clockWise:v,labelViewBox:g}=e;if(g)return g;if(we(p)&&we(y)){if(we(u)&&we(f))return{x:u,y:f,width:p,height:y};if(we(d)&&we(h))return{x:d,y:h,width:p,height:y}}return we(u)&&we(f)?{x:u,y:f,width:0,height:0}:we(t)&&we(r)?{cx:t,cy:r,startAngle:i||n||0,endAngle:a||n||0,innerRadius:c||0,outerRadius:s||l||o||0,clockWise:v}:e.viewBox?e.viewBox:void 0};mb.parseViewBox=bb,mb.renderCallByParent=function(e,t){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!e||!e.children&&r&&!e.label)return null;var{children:n,labelRef:i}=e,a=bb(e),o=Ue(n,mb).map((e,r)=>u.cloneElement(e,{viewBox:t||a,key:"label-".concat(r)}));if(!r)return o;var l=((e,t,r)=>{if(!e)return null;var n={viewBox:t,labelRef:r};return!0===e?u.createElement(mb,yb({key:"label-implicit"},n)):Oe(e)?u.createElement(mb,yb({key:"label-implicit",value:e},n)):u.isValidElement(e)?e.type===mb?u.cloneElement(e,hb({key:"label-implicit"},n)):u.createElement(mb,yb({key:"label-implicit",content:e},n)):vb(e)?u.createElement(mb,yb({key:"label-implicit",content:e},n)):e&&"object"==typeof e?u.createElement(mb,yb({},e,{key:"label-implicit"},n)):null})(e.label,t||a,i);return[l,...o]};var xb,wb={},Ob={};function jb(){return xb||(xb=1,e=Ob,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.last=function(e){return e[e.length-1]}),Ob;var e}var Ab,Pb,Sb,Eb,kb={};function Mb(){return Ab||(Ab=1,e=kb,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.toArray=function(e){return Array.isArray(e)?e:Array.from(e)}),kb;var e}function Tb(){return Eb?Sb:(Eb=1,Sb=(Pb||(Pb=1,function(e){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});const t=jb(),r=Mb(),n=kr();e.last=function(e){if(n.isArrayLike(e))return t.last(r.toArray(e))}}(wb)),wb).last)}const Cb=I(Tb());var Db=["valueAccessor"],Nb=["data","dataKey","clockWise","id","textBreakAll"];function Ib(){return Ib=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ib.apply(null,arguments)}function _b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Rb(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_b(Object(r),!0).forEach(function(t){Lb(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Lb(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zb(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var Bb=e=>Array.isArray(e.value)?Cb(e.value):e.value;function Kb(e){var{valueAccessor:t=Bb}=e,r=zb(e,Db),{data:n,dataKey:i,clockWise:a,id:o,textBreakAll:l}=r,c=zb(r,Nb);return n&&n.length?u.createElement(Xe,{className:"recharts-label-list"},n.map((e,r)=>{var n=Te(i)?t(e,r):ra(e&&e.payload,i),s=Te(o)?{}:{id:"".concat(o,"-").concat(r)};return u.createElement(mb,Ib({},We(e,!0),c,s,{parentViewBox:e.parentViewBox,value:n,textBreakAll:l,viewBox:mb.parseViewBox(Te(a)?e:Rb(Rb({},e),{},{clockWise:a})),key:"label-".concat(r),index:r}))})):null}function $b(){return $b=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$b.apply(null,arguments)}Kb.displayName="LabelList",Kb.renderCallByParent=function(e,t){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!e||!e.children&&r&&!e.label)return null;var{children:n}=e,i=Ue(n,Kb).map((e,r)=>u.cloneElement(e,{data:t,key:"labelList-".concat(r)}));return r?[function(e,t){return e?!0===e?u.createElement(Kb,{key:"labelList-implicit",data:t}):u.isValidElement(e)||vb(e)?u.createElement(Kb,{key:"labelList-implicit",data:t,content:e}):"object"==typeof e?u.createElement(Kb,Ib({data:t},e,{key:"labelList-implicit"})):null:null}(e.label,t),...i]:i};var Ub=e=>{var{cx:t,cy:r,r:n,className:i}=e,a=q("recharts-dot",i);return t===+t&&r===+r&&n===+n?u.createElement("circle",$b({},We(e,!1),Re(e),{className:a,cx:t,cy:r,r:n})):null},Fb=e=>e.graphicalItems.polarItems,Wb=U([dp,hp],Ep),Hb=U([Fb,Ap,Wb],Tp),Vb=U([Hb],Ip),qb=U([Vb,zd],Rp),Yb=U([qb,Ap,Hb],zp),Gb=U([qb,Ap,Hb],(e,t,r)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var n;return{value:ra(e,null!==(n=t.dataKey)&&void 0!==n?n:r.dataKey),errorDomain:[]}})).filter(Boolean):null!=(null==t?void 0:t.dataKey)?e.map(e=>({value:ra(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]}))),Xb=()=>{},Zb=U([Ap,dy,Xb,Gb,Xb],hy),Jb=U([Ap,Ua,qb,Yb,zh,dp,Zb],vy),Qb=U([Jb,Ap,by],wy);U([Ap,Jb,Qb,dp],jy);var ex=r({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=i(t.payload)},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=i(t.payload)},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:tx,removeRadiusAxis:rx,addAngleAxis:nx,removeAngleAxis:ix}=ex.actions,ax=ex.reducer;function ox(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function lx(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ox(Object(r),!0).forEach(function(t){cx(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ox(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function cx(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var sx=(e,t)=>t,ux=[],fx=(e,t,r)=>0===(null==r?void 0:r.length)?ux:r,dx=U([zd,sx,fx],(e,t,r)=>{var n,{chartData:i}=e;if((n=null!=(null==t?void 0:t.data)&&t.data.length>0?t.data:i)&&n.length||null==r||(n=r.map(e=>lx(lx({},t.presentationProps),e.props))),null!=n)return n}),hx=U([dx,sx,fx],(e,t,r)=>{if(null!=e)return e.map((e,n)=>{var i,a,o=ra(e,t.nameKey,t.name);return a=null!=r&&null!==(i=r[n])&&void 0!==i&&null!==(i=i.props)&&void 0!==i&&i.fill?r[n].props.fill:"object"==typeof e&&null!=e&&"fill"in e?e.fill:t.fill,{value:ga(o,t.dataKey),color:a,payload:e,type:t.legendType}})}),px=U([Fb,sx],(e,t)=>{if(e.some(e=>"pie"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),yx=U([dx,px,fx,Ma],(e,t,r,n)=>{if(null!=t&&null!=e)return function(e){var t,r,n,{pieSettings:i,displayedData:a,cells:o,offset:l}=e,{cornerRadius:c,startAngle:s,endAngle:u,dataKey:f,nameKey:d,tooltipType:h}=i,p=Math.abs(i.minAngle),y=sw(s,u),v=Math.abs(y),g=a.length<=1?0:null!==(t=i.paddingAngle)&&void 0!==t?t:0,m=a.filter(e=>0!==ra(e,f,0)).length,b=v-m*p-(v>=360?m:m-1)*g,x=a.reduce((e,t)=>{var r=ra(t,f,0);return e+(we(r)?r:0)},0);x>0&&(r=a.map((e,t)=>{var r,a=ra(e,f,0),u=ra(e,d,t),v=cw(i,l,e),m=(we(a)?a:0)/x,w=nw(nw({},e),o&&o[t]&&o[t].props),O=(r=t?n.endAngle+me(y)*g*(0!==a?1:0):s)+me(y)*((0!==a?p:0)+m*b),j=(r+O)/2,A=(v.innerRadius+v.outerRadius)/2,P=[{name:u,value:a,payload:w,dataKey:f,type:h}],S=Yi(v.cx,v.cy,A,j);return n=nw(nw(nw(nw({},i.presentationProps),{},{percent:m,cornerRadius:c,name:u,tooltipPayload:P,midAngle:j,middleRadius:A,tooltipPosition:S},w),v),{},{value:ra(e,f),startAngle:r,endAngle:O,payload:w,paddingAngle:me(y)*g})}));return r}({offset:n,pieSettings:t,displayedData:e,cells:r})}),vx=r({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(e){e.countOfBars+=1},removeBar(e){e.countOfBars-=1},addCartesianGraphicalItem(e,t){e.cartesianItems.push(i(t.payload))},replaceCartesianGraphicalItem(e,t){var{prev:r,next:a}=t.payload,o=n(e).cartesianItems.indexOf(i(r));o>-1&&(e.cartesianItems[o]=i(a))},removeCartesianGraphicalItem(e,t){var r=n(e).cartesianItems.indexOf(i(t.payload));r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push(i(t.payload))},removePolarGraphicalItem(e,t){var r=n(e).polarItems.indexOf(i(t.payload));r>-1&&e.polarItems.splice(r,1)}}}),{addBar:gx,removeBar:mx,addCartesianGraphicalItem:bx,replaceCartesianGraphicalItem:xx,removeCartesianGraphicalItem:wx,addPolarGraphicalItem:Ox,removePolarGraphicalItem:jx}=vx.actions,Ax=vx.reducer;function Px(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Sx(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Px(Object(r),!0).forEach(function(t){Ex(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Px(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Ex(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function kx(e){var t=ai(),r=u.useRef(null);return u.useEffect(()=>{var n=Sx(Sx({},e),{},{stackId:sa(e.stackId)});null===r.current?t(bx(n)):r.current!==n&&t(xx({prev:r.current,next:n})),r.current=n},[t,e]),u.useEffect(()=>()=>{r.current&&(t(wx(r.current)),r.current=null)},[t]),null}function Mx(e){var t=ai();return u.useEffect(()=>(t(Ox(e)),()=>{t(jx(e))}),[t,e]),null}var Tx,Cx,Dx,Nx={};function Ix(){return Dx?Cx:(Dx=1,Cx=(Tx||(Tx=1,e=Nx,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isPlainObject=function(e){var t;if("object"!=typeof e)return!1;if(null==e)return!1;if(null===Object.getPrototypeOf(e))return!0;if("[object Object]"!==Object.prototype.toString.call(e)){const r=e[Symbol.toStringTag];return null!=r&&!!(null==(t=Object.getOwnPropertyDescriptor(e,Symbol.toStringTag))?void 0:t.writable)&&e.toString()===`[object ${r}]`}let r=e;for(;null!==Object.getPrototypeOf(r);)r=Object.getPrototypeOf(r);return Object.getPrototypeOf(e)===r}),Nx).isPlainObject);var e}const _x=I(Ix());function Rx(){return Rx=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Rx.apply(null,arguments)}var Lx=(e,t,r,n,i)=>{var a,o=r-n;return a="M ".concat(e,",").concat(t),a+="L ".concat(e+r,",").concat(t),a+="L ".concat(e+r-o/2,",").concat(t+i),a+="L ".concat(e+r-o/2-n,",").concat(t+i),a+="L ".concat(e,",").concat(t," Z")},zx={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},Bx=e=>{var t=Wo(e,zx),r=u.useRef(),[n,i]=u.useState(-1);u.useEffect(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&i(e)}catch(t){}},[]);var{x:a,y:o,upperWidth:l,lowerWidth:c,height:s,className:f}=t,{animationEasing:d,animationDuration:h,animationBegin:p,isUpdateAnimationActive:y}=t;if(a!==+a||o!==+o||l!==+l||c!==+c||s!==+s||0===l&&0===c||0===s)return null;var v=q("recharts-trapezoid",f);return y?u.createElement(Nl,{canBegin:n>0,from:{upperWidth:0,lowerWidth:0,height:s,x:a,y:o},to:{upperWidth:l,lowerWidth:c,height:s,x:a,y:o},duration:h,animationEasing:d,isActive:y},e=>{var{upperWidth:i,lowerWidth:a,height:o,x:l,y:c}=e;return u.createElement(Nl,{canBegin:n>0,from:"0px ".concat(-1===n?1:n,"px"),to:"".concat(n,"px 0px"),attributeName:"strokeDasharray",begin:p,duration:h,easing:d},u.createElement("path",Rx({},We(t,!0),{className:v,d:Lx(l,c,i,a,o),ref:r})))}):u.createElement("g",null,u.createElement("path",Rx({},We(t,!0),{className:v,d:Lx(a,o,l,c,s)})))},Kx=["option","shapeType","propTransformer","activeClassName","isActive"];function $x(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Ux(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?$x(Object(r),!0).forEach(function(t){Fx(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$x(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Fx(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Wx(e,t){return Ux(Ux({},t),e)}function Hx(e){var{shapeType:t,elementProps:r}=e;switch(t){case"rectangle":return u.createElement(Ll,r);case"trapezoid":return u.createElement(Bx,r);case"sector":return u.createElement(Fl,r);case"symbols":if(function(e){return"symbols"===e}(t))return u.createElement(sr,r);break;default:return null}}function Vx(e){var t,{option:r,shapeType:n,propTransformer:i=Wx,activeClassName:a="recharts-active-shape",isActive:o}=e,l=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Kx);if(u.isValidElement(r))t=u.cloneElement(r,Ux(Ux({},l),function(e){return u.isValidElement(e)?e.props:e}(r)));else if("function"==typeof r)t=r(l);else if(_x(r)&&"boolean"!=typeof r){var c=i(r,l);t=u.createElement(Hx,{shapeType:n,elementProps:c})}else{var s=l;t=u.createElement(Hx,{shapeType:n,elementProps:s})}return o?u.createElement(Xe,{className:a},t):t}var qx=(e,t)=>{var r=ai();return(n,i)=>a=>{null==e||e(n,i,a),r(fv({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},Yx=e=>{var t=ai();return(r,n)=>i=>{null==e||e(r,n,i),t(dv())}},Gx=(e,t)=>{var r=ai();return(n,i)=>a=>{null==e||e(n,i,a),r(pv({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}};function Xx(e){var{fn:t,args:r}=e,n=ai(),i=Na();return u.useEffect(()=>{if(!i){var e=t(r);return n(cv(e)),()=>{n(sv(e))}}},[t,r,n,i]),null}var Zx=()=>{};function Jx(e){var{legendPayload:t}=e,r=ai(),n=Na();return u.useEffect(()=>n?Zx:(r(qa(t)),()=>{r(Ya(t))}),[r,n,t]),null}function Qx(e){var{legendPayload:t}=e,r=ai(),n=si(Ua);return u.useEffect(()=>"centric"!==n&&"radial"!==n?Zx:(r(qa(t)),()=>{r(Ya(t))}),[r,n,t]),null}function ew(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",r=u.useRef(Ae(t)),n=u.useRef(e);return n.current!==e&&(r.current=Ae(t),n.current=e),r.current}var tw=["onMouseEnter","onClick","onMouseLeave"];function rw(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nw(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rw(Object(r),!0).forEach(function(t){iw(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rw(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function iw(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function aw(){return aw=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},aw.apply(null,arguments)}function ow(e){var t=u.useMemo(()=>We(e,!1),[e]),r=u.useMemo(()=>Ue(e.children,Nm),[e.children]),n=u.useMemo(()=>({name:e.name,nameKey:e.nameKey,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey,cx:e.cx,cy:e.cy,startAngle:e.startAngle,endAngle:e.endAngle,minAngle:e.minAngle,paddingAngle:e.paddingAngle,innerRadius:e.innerRadius,outerRadius:e.outerRadius,cornerRadius:e.cornerRadius,legendType:e.legendType,fill:e.fill,presentationProps:t}),[e.cornerRadius,e.cx,e.cy,e.data,e.dataKey,e.endAngle,e.innerRadius,e.minAngle,e.name,e.nameKey,e.outerRadius,e.paddingAngle,e.startAngle,e.tooltipType,e.legendType,e.fill,t]),i=si(e=>hx(e,n,r));return u.createElement(Qx,{legendPayload:i})}function lw(e){var{dataKey:t,nameKey:r,sectors:n,stroke:i,strokeWidth:a,fill:o,name:l,hide:c,tooltipType:s}=e;return{dataDefinedOnItem:null==n?void 0:n.map(e=>e.tooltipPayload),positions:null==n?void 0:n.map(e=>e.tooltipPosition),settings:{stroke:i,strokeWidth:a,fill:o,dataKey:t,nameKey:r,name:ga(l,t),hide:c,type:s,color:o,unit:""}}}var cw=(e,t,r)=>{var{top:n,left:i,width:a,height:o}=t,l=Gi(a,o),c=i+Pe(e.cx,a,a/2),s=n+Pe(e.cy,o,o/2),u=Pe(e.innerRadius,l,0),f=((e,t,r)=>"function"==typeof t?t(e):Pe(t,r,.8*r))(r,e.outerRadius,l);return{cx:c,cy:s,innerRadius:u,outerRadius:f,maxRadius:e.maxRadius||Math.sqrt(a*a+o*o)/2}},sw=(e,t)=>me(t-e)*Math.min(Math.abs(t-e),360);function uw(e){var{sectors:t,props:r,showLabels:n}=e,{label:i,labelLine:a,dataKey:o}=r;if(!n||!i||!t)return null;var l=We(r,!1),c=We(i,!1),s=We(a,!1),f="object"==typeof i&&"offsetRadius"in i&&i.offsetRadius||20,d=t.map((e,t)=>{var r,n,d=(e.startAngle+e.endAngle)/2,h=Yi(e.cx,e.cy,e.outerRadius+f,d),p=nw(nw(nw(nw({},l),e),{},{stroke:"none"},c),{},{index:t,textAnchor:(r=h.x,n=e.cx,r>n?"start":r<n?"end":"middle")},h),y=nw(nw(nw(nw({},l),e),{},{fill:"none",stroke:e.fill},s),{},{index:t,points:[Yi(e.cx,e.cy,e.outerRadius,d),h],key:"line"});return u.createElement(Xe,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},a&&((e,t)=>{if(u.isValidElement(e))return u.cloneElement(e,t);if("function"==typeof e)return e(t);var r=q("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return u.createElement(_o,aw({},t,{type:"linear",className:r}))})(a,y),((e,t,r)=>{if(u.isValidElement(e))return u.cloneElement(e,t);var n=r;if("function"==typeof e&&(n=e(t),u.isValidElement(n)))return n;var i=q("recharts-pie-label-text","boolean"!=typeof e&&"function"!=typeof e?e.className:"");return u.createElement(cb,aw({},t,{alignmentBaseline:"middle",className:i}),n)})(i,p,ra(e,o)))});return u.createElement(Xe,{className:"recharts-pie-labels"},d)}function fw(e){var{sectors:t,activeShape:r,inactiveShape:n,allOtherPieProps:i,showLabels:a}=e,o=si(yg),{onMouseEnter:l,onClick:c,onMouseLeave:s}=i,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,tw),d=qx(l,i.dataKey),h=Yx(s),p=Gx(c,i.dataKey);return null==t?null:u.createElement(u.Fragment,null,t.map((e,a)=>{if(0===(null==e?void 0:e.startAngle)&&0===(null==e?void 0:e.endAngle)&&1!==t.length)return null;var l=r&&String(a)===o,c=l?r:o?n:null,s=nw(nw({},e),{},{stroke:e.stroke,tabIndex:-1,[Aa]:a,[Pa]:i.dataKey});return u.createElement(Xe,aw({tabIndex:-1,className:"recharts-pie-sector"},Le(f,e,a),{onMouseEnter:d(e,a),onMouseLeave:h(e,a),onClick:p(e,a),key:"sector-".concat(null==e?void 0:e.startAngle,"-").concat(null==e?void 0:e.endAngle,"-").concat(e.midAngle,"-").concat(a)}),u.createElement(Vx,aw({option:c,isActive:l,shapeType:"sector"},s)))}),u.createElement(uw,{sectors:t,props:i,showLabels:a}))}function dw(e){var{props:t,previousSectorsRef:r}=e,{sectors:n,isAnimationActive:i,animationBegin:a,animationDuration:o,animationEasing:l,activeShape:c,inactiveShape:s,onAnimationStart:f,onAnimationEnd:d}=t,h=ew(t,"recharts-pie-"),p=r.current,[y,v]=u.useState(!0),g=u.useCallback(()=>{"function"==typeof d&&d(),v(!1)},[d]),m=u.useCallback(()=>{"function"==typeof f&&f(),v(!0)},[f]);return u.createElement(Nl,{begin:a,duration:o,isActive:i,easing:l,from:{t:0},to:{t:1},onAnimationStart:m,onAnimationEnd:g,key:h},e=>{var{t:i}=e,a=[],o=(n&&n[0]).startAngle;return n.forEach((e,t)=>{var r=p&&p[t],n=t>0?de(e,"paddingAngle",0):0;if(r){var l=Ee(r.endAngle-r.startAngle,e.endAngle-e.startAngle),c=nw(nw({},e),{},{startAngle:o+n,endAngle:o+l(i)+n});a.push(c),o=c.endAngle}else{var{endAngle:s,startAngle:u}=e,f=Ee(0,s-u)(i),d=nw(nw({},e),{},{startAngle:o+n,endAngle:o+f+n});a.push(d),o=d.endAngle}}),r.current=a,u.createElement(Xe,null,u.createElement(fw,{sectors:a,activeShape:c,inactiveShape:s,allOtherPieProps:t,showLabels:!y}))})}function hw(e){var{sectors:t,isAnimationActive:r,activeShape:n,inactiveShape:i}=e,a=u.useRef(null),o=a.current;return r&&t&&t.length&&(!o||o!==t)?u.createElement(dw,{props:e,previousSectorsRef:a}):u.createElement(fw,{sectors:t,activeShape:n,inactiveShape:i,allOtherPieProps:e,showLabels:!0})}function pw(e){var{hide:t,className:r,rootTabIndex:n}=e,i=q("recharts-pie",r);return t?null:u.createElement(Xe,{tabIndex:n,className:i},u.createElement(hw,e))}var yw={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!Oo,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function vw(e){var t=Wo(e,yw),r=u.useMemo(()=>Ue(e.children,Nm),[e.children]),n=We(t,!1),i=u.useMemo(()=>({name:t.name,nameKey:t.nameKey,tooltipType:t.tooltipType,data:t.data,dataKey:t.dataKey,cx:t.cx,cy:t.cy,startAngle:t.startAngle,endAngle:t.endAngle,minAngle:t.minAngle,paddingAngle:t.paddingAngle,innerRadius:t.innerRadius,outerRadius:t.outerRadius,cornerRadius:t.cornerRadius,legendType:t.legendType,fill:t.fill,presentationProps:n}),[t.cornerRadius,t.cx,t.cy,t.data,t.dataKey,t.endAngle,t.innerRadius,t.minAngle,t.name,t.nameKey,t.outerRadius,t.paddingAngle,t.startAngle,t.tooltipType,t.legendType,t.fill,n]),a=si(e=>yx(e,i,r));return u.createElement(u.Fragment,null,u.createElement(Xx,{fn:lw,args:nw(nw({},t),{},{sectors:a})}),u.createElement(pw,aw({},t,{sectors:a})))}class gw extends u.PureComponent{constructor(){super(...arguments),iw(this,"id",Ae("recharts-pie-"))}render(){return u.createElement(u.Fragment,null,u.createElement(Mx,{data:this.props.data,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:0,radiusAxisId:0,stackId:void 0,barSize:void 0,type:"pie"}),u.createElement(ow,this.props),u.createElement(vw,this.props),this.props.children)}}iw(gw,"displayName","Pie"),iw(gw,"defaultProps",yw);var mw=U([Ma],e=>{if(e)return{top:e.top,bottom:e.bottom,left:e.left,right:e.right}}),bw=U([mw,ma,ba],(e,t,r)=>{if(e&&null!=t&&null!=r)return{x:e.left,y:e.top,width:Math.max(0,t-e.left-e.right),height:Math.max(0,r-e.top-e.bottom)}}),xw=()=>si(bw);function ww(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Ow(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ww(Object(r),!0).forEach(function(t){jw(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ww(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function jw(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Aw(e){var{points:t,mainColor:r,activeDot:n,itemDataKey:i}=e,a=si(yg),o=si(jg);if(null==t||null==o)return null;var l=t.find(e=>o.includes(e.payload));return Te(l)?null:(e=>{var{point:t,childIndex:r,mainColor:n,activeDot:i,dataKey:a}=e;if(!1===i||null==t.x||null==t.y)return null;var o,l=Ow(Ow({index:r,dataKey:a,cx:t.x,cy:t.y,r:4,fill:null!=n?n:"none",strokeWidth:2,stroke:"#fff",payload:t.payload,value:t.value},We(i,!1)),Re(i));return o=u.isValidElement(i)?u.cloneElement(i,l):"function"==typeof i?i(l):u.createElement(Ub,l),u.createElement(Xe,{className:"recharts-active-dot"},o)})({point:l,childIndex:Number(a),mainColor:r,dataKey:i,activeDot:n})}var Pw=()=>{var e=ai();return u.useEffect(()=>(e(gx()),()=>{e(mx())})),null},Sw=["children"];var Ew=()=>{},kw=u.createContext({addErrorBar:Ew,removeErrorBar:Ew}),Mw={data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0},Tw=u.createContext(Mw);function Cw(e){var{children:t}=e,r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Sw);return u.createElement(Tw.Provider,{value:r},t)}var Dw=e=>{var{children:t,xAxisId:r,yAxisId:n,zAxisId:i,dataKey:a,data:o,stackId:l,hide:c,type:s,barSize:f}=e,[d,h]=u.useState([]),p=u.useCallback(e=>{h(t=>[...t,e])},[h]),y=u.useCallback(e=>{h(t=>t.filter(t=>t!==e))},[h]),v=Na();return u.createElement(kw.Provider,{value:{addErrorBar:p,removeErrorBar:y}},u.createElement(kx,{type:s,data:o,xAxisId:r,yAxisId:n,zAxisId:i,dataKey:a,errorBars:d,stackId:l,hide:c,barSize:f,isPanorama:v}),t)};function Nw(e){var{addErrorBar:t,removeErrorBar:r}=u.useContext(kw);return u.useEffect(()=>(t(e),()=>{r(e)}),[t,r,e]),null}var Iw=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function _w(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Rw(){return Rw=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Rw.apply(null,arguments)}function Lw(e){var{direction:t,width:r,dataKey:n,isAnimationActive:i,animationBegin:a,animationDuration:o,animationEasing:l}=e,c=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Iw),s=We(c,!1),{data:f,dataPointFormatter:d,xAxisId:h,yAxisId:p,errorBarOffset:y}=u.useContext(Tw),v=(e=>{var t=Na();return si(r=>Jy(r,"xAxis",e,t))})(h),g=(e=>{var t=Na();return si(r=>Jy(r,"yAxis",e,t))})(p);if(null==(null==v?void 0:v.scale)||null==(null==g?void 0:g.scale)||null==f)return null;if("x"===t&&"number"!==v.type)return null;var m=f.map(e=>{var{x:c,y:f,value:h,errorVal:p}=d(e,n,t);if(!p)return null;var m,b,x=[];if(Array.isArray(p)?[m,b]=p:m=b=p,"x"===t){var{scale:w}=v,O=f+y,j=O+r,A=O-r,P=w(h-m),S=w(h+b);x.push({x1:S,y1:j,x2:S,y2:A}),x.push({x1:P,y1:O,x2:S,y2:O}),x.push({x1:P,y1:j,x2:P,y2:A})}else if("y"===t){var{scale:E}=g,k=c+y,M=k-r,T=k+r,C=E(h-m),D=E(h+b);x.push({x1:M,y1:D,x2:T,y2:D}),x.push({x1:k,y1:C,x2:k,y2:D}),x.push({x1:M,y1:C,x2:T,y2:C})}var N="".concat(c+y,"px ").concat(f+y,"px");return u.createElement(Xe,Rw({className:"recharts-errorBar",key:"bar-".concat(x.map(e=>"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)))},s),x.map(e=>{var t=i?{transformOrigin:"".concat(e.x1-5,"px")}:void 0;return u.createElement(Nl,{from:{transform:"scaleY(0)",transformOrigin:N},to:{transform:"scaleY(1)",transformOrigin:N},begin:a,easing:l,isActive:i,duration:o,key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2),style:{transformOrigin:N}},u.createElement("line",Rw({},e,{style:t})))}))});return u.createElement(Xe,{className:"recharts-errorBars"},m)}var zw=u.createContext(void 0);function Bw(e){var{direction:t,children:r}=e;return u.createElement(zw.Provider,{value:t},r)}var Kw={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function $w(e){var t,r,n=(t=e.direction,r=u.useContext(zw),null!=t?t:null!=r?r:"x"),{width:i,isAnimationActive:a,animationBegin:o,animationDuration:l,animationEasing:c}=Wo(e,Kw);return u.createElement(u.Fragment,null,u.createElement(Nw,{dataKey:e.dataKey,direction:n}),u.createElement(Lw,Rw({},e,{direction:n,width:i,isAnimationActive:a,animationBegin:o,animationDuration:l,animationEasing:c})))}class Uw extends u.Component{render(){return u.createElement($w,this.props)}}_w(Uw,"defaultProps",Kw),_w(Uw,"displayName","ErrorBar");var Fw=["x","y"];function Ww(){return Ww=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ww.apply(null,arguments)}function Hw(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Vw(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Hw(Object(r),!0).forEach(function(t){qw(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Hw(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function qw(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Yw(e,t){var{x:r,y:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Fw),a="".concat(r),o=parseInt(a,10),l="".concat(n),c=parseInt(l,10),s="".concat(t.height||i.height),u=parseInt(s,10),f="".concat(t.width||i.width),d=parseInt(f,10);return Vw(Vw(Vw(Vw(Vw({},t),i),o?{x:o}:{}),c?{y:c}:{}),{},{height:u,width:d,name:t.name,radius:t.radius})}function Gw(e){return u.createElement(Vx,Ww({shapeType:"rectangle",propTransformer:Yw,activeClassName:"recharts-active-bar"},e))}var Xw=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(r,n)=>{if(we(e))return e;var i=we(r)||Te(r);return i?e(r,n):(i||function(){throw new Error("Invariant failed")}(),t)}};function Zw(e,t){var r,n,i=si(t=>bp(t,e)),a=si(e=>wp(e,t)),o=null!==(r=null==i?void 0:i.allowDataOverflow)&&void 0!==r?r:mp.allowDataOverflow,l=null!==(n=null==a?void 0:a.allowDataOverflow)&&void 0!==n?n:xp.allowDataOverflow;return{needClip:o||l,needClipX:o,needClipY:l}}function Jw(e){var{xAxisId:t,yAxisId:r,clipPathId:n}=e,i=xw(),{needClipX:a,needClipY:o,needClip:l}=Zw(t,r);if(!l)return null;var{x:c,y:s,width:f,height:d}=i;return u.createElement("clipPath",{id:"clipPath-".concat(n)},u.createElement("rect",{x:a?c:c-f/2,y:o?s:s-d/2,width:a?f:2*f,height:o?d:2*d}))}var Qw=["onMouseEnter","onMouseLeave","onClick"],eO=["value","background","tooltipPosition"],tO=["onMouseEnter","onClick","onMouseLeave"];function rO(){return rO=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},rO.apply(null,arguments)}function nO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function iO(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nO(Object(r),!0).forEach(function(t){aO(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nO(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function aO(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function oO(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var lO=e=>{var{dataKey:t,name:r,fill:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:ga(r,t),payload:e}]};function cO(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:i,name:a,hide:o,unit:l}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:i,dataKey:t,nameKey:void 0,name:ga(a,t),hide:o,type:e.tooltipType,color:e.fill,unit:l}}}function sO(e){var t=si(yg),{data:r,dataKey:n,background:i,allOtherBarProps:a}=e,{onMouseEnter:o,onMouseLeave:l,onClick:c}=a,s=oO(a,Qw),f=qx(o,n),d=Yx(l),h=Gx(c,n);if(!i||null==r)return null;var p=We(i,!1);return u.createElement(u.Fragment,null,r.map((e,r)=>{var{value:a,background:o,tooltipPosition:l}=e,c=oO(e,eO);if(!o)return null;var y=f(e,r),v=d(e,r),g=h(e,r),m=iO(iO(iO(iO(iO({option:i,isActive:String(r)===t},c),{},{fill:"#eee"},o),p),Le(s,e,r)),{},{onMouseEnter:y,onMouseLeave:v,onClick:g,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return u.createElement(Gw,rO({key:"background-bar-".concat(r)},m))}))}function uO(e){var{data:t,props:r,showLabels:n}=e,i=We(r,!1),{shape:a,dataKey:o,activeBar:l}=r,c=si(yg),s=si(gg),{onMouseEnter:f,onClick:d,onMouseLeave:h}=r,p=oO(r,tO),y=qx(f,o),v=Yx(h),g=Gx(d,o);return t?u.createElement(u.Fragment,null,t.map((e,t)=>{var r=l&&String(t)===c&&(null==s||o===s),n=r?l:a,f=iO(iO(iO({},i),e),{},{isActive:r,option:n,index:t,dataKey:o});return u.createElement(Xe,rO({className:"recharts-bar-rectangle"},Le(p,e,t),{onMouseEnter:y(e,t),onMouseLeave:v(e,t),onClick:g(e,t),key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(t)}),u.createElement(Gw,f))}),n&&Kb.renderCallByParent(r,t)):null}function fO(e){var{props:t,previousRectanglesRef:r}=e,{data:n,layout:i,isAnimationActive:a,animationBegin:o,animationDuration:l,animationEasing:c,onAnimationEnd:s,onAnimationStart:f}=t,d=r.current,h=ew(t,"recharts-bar-"),[p,y]=u.useState(!1),v=u.useCallback(()=>{"function"==typeof s&&s(),y(!1)},[s]),g=u.useCallback(()=>{"function"==typeof f&&f(),y(!0)},[f]);return u.createElement(Nl,{begin:o,duration:l,isActive:a,easing:c,from:{t:0},to:{t:1},onAnimationEnd:v,onAnimationStart:g,key:h},e=>{var{t:a}=e,o=1===a?n:n.map((e,t)=>{var r=d&&d[t];if(r){var n=Ee(r.x,e.x),o=Ee(r.y,e.y),l=Ee(r.width,e.width),c=Ee(r.height,e.height);return iO(iO({},e),{},{x:n(a),y:o(a),width:l(a),height:c(a)})}if("horizontal"===i){var s=Ee(0,e.height)(a);return iO(iO({},e),{},{y:e.y+e.height-s,height:s})}var u=Ee(0,e.width)(a);return iO(iO({},e),{},{width:u})});return a>0&&(r.current=o),u.createElement(Xe,null,u.createElement(uO,{props:t,data:o,showLabels:!p}))})}function dO(e){var{data:t,isAnimationActive:r}=e,n=u.useRef(null);return r&&t&&t.length&&(null==n.current||n.current!==t)?u.createElement(fO,{previousRectanglesRef:n,props:e}):u.createElement(uO,{props:e,data:t,showLabels:!0})}var hO=(e,t)=>{var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:ra(e,t)}};class pO extends u.PureComponent{constructor(){super(...arguments),aO(this,"id",Ae("recharts-bar-"))}render(){var{hide:e,data:t,dataKey:r,className:n,xAxisId:i,yAxisId:a,needClip:o,background:l,id:c,layout:s}=this.props;if(e)return null;var f=q("recharts-bar",n),d=Te(c)?this.id:c;return u.createElement(Xe,{className:f},o&&u.createElement("defs",null,u.createElement(Jw,{clipPathId:d,xAxisId:i,yAxisId:a})),u.createElement(Xe,{className:"recharts-bar-rectangles",clipPath:o?"url(#clipPath-".concat(d,")"):null},u.createElement(sO,{data:t,dataKey:r,background:l,allOtherBarProps:this.props}),u.createElement(dO,this.props)),u.createElement(Bw,{direction:"horizontal"===s?"y":"x"},this.props.children))}}var yO={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!Oo,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function vO(e){var t,{xAxisId:r,yAxisId:n,hide:i,legendType:a,minPointSize:o,activeBar:l,animationBegin:c,animationDuration:s,animationEasing:f,isAnimationActive:d}=Wo(e,yO),{needClip:h}=Zw(r,n),p=Fa(),y=Na(),v=u.useMemo(()=>({barSize:e.barSize,data:void 0,dataKey:e.dataKey,maxBarSize:e.maxBarSize,minPointSize:o,stackId:sa(e.stackId)}),[e.barSize,e.dataKey,e.maxBarSize,o,e.stackId]),g=Ue(e.children,Nm),m=si(e=>_O(e,r,n,y,v,g));if("vertical"!==p&&"horizontal"!==p)return null;var b=null==m?void 0:m[0];return t=null==b||null==b.height||null==b.width?0:"vertical"===p?b.height/2:b.width/2,u.createElement(Cw,{xAxisId:r,yAxisId:n,data:m,dataPointFormatter:hO,errorBarOffset:t},u.createElement(pO,rO({},e,{layout:p,needClip:h,data:m,xAxisId:r,yAxisId:n,hide:i,legendType:a,minPointSize:o,activeBar:l,animationBegin:c,animationDuration:s,animationEasing:f,isAnimationActive:d})))}function gO(e){var{layout:t,barSettings:{dataKey:r,minPointSize:n},pos:i,bandSize:a,xAxis:o,yAxis:l,xAxisTicks:c,yAxisTicks:s,stackedData:u,displayedData:f,offset:d,cells:h}=e,p="horizontal"===t?l:o,y=u?p.scale.domain():null,v=(e=>{var{numericAxis:t}=e,r=t.scale.domain();if("number"===t.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]})({numericAxis:p});return f.map((e,f)=>{var p,g,m,b,x,w;u?p=((e,t)=>{if(!t||2!==t.length||!we(t[0])||!we(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),i=[e[0],e[1]];return(!we(e[0])||e[0]<r)&&(i[0]=r),(!we(e[1])||e[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i})(u[f],y):(p=ra(e,r),Array.isArray(p)||(p=[v,p]));var O=Xw(n,0)(p[1],f);if("horizontal"===t){var j,[A,P]=[l.scale(p[0]),l.scale(p[1])];g=fa({axis:o,ticks:c,bandSize:a,offset:i.offset,entry:e,index:f}),m=null!==(j=null!=P?P:A)&&void 0!==j?j:void 0,b=i.size;var S=A-P;if(x=be(S)?0:S,w={x:g,y:d.top,width:b,height:d.height},Math.abs(O)>0&&Math.abs(x)<Math.abs(O)){var E=me(x||O)*(Math.abs(O)-Math.abs(x));m-=E,x+=E}}else{var[k,M]=[o.scale(p[0]),o.scale(p[1])];if(g=k,m=fa({axis:l,ticks:s,bandSize:a,offset:i.offset,entry:e,index:f}),b=M-k,x=i.size,w={x:d.left,y:m,width:d.width,height:x},Math.abs(O)>0&&Math.abs(b)<Math.abs(O))b+=me(b||O)*(Math.abs(O)-Math.abs(b))}return iO(iO({},e),{},{x:g,y:m,width:b,height:x,value:u?p:p[1],payload:e,background:w,tooltipPosition:{x:g+b/2,y:m+x/2}},h&&h[f]&&h[f].props)})}class mO extends u.PureComponent{render(){return u.createElement(Dw,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},u.createElement(Pw,null),u.createElement(Jx,{legendPayload:lO(this.props)}),u.createElement(Xx,{fn:cO,args:this.props}),u.createElement(vO,this.props))}}function bO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function xO(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?bO(Object(r),!0).forEach(function(t){wO(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bO(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function wO(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}aO(mO,"displayName","Bar"),aO(mO,"defaultProps",yO);var OO=(e,t,r,n,i)=>i,jO=(e,t,r)=>{var n=null!=r?r:e;if(!Te(n))return Pe(n,t,0)},AO=U([Ua,kp,(e,t)=>t,(e,t,r)=>r,(e,t,r,n)=>n],(e,t,r,n,i)=>t.filter(t=>"horizontal"===e?t.xAxisId===r:t.yAxisId===n).filter(e=>e.isPanorama===i).filter(e=>!1===e.hide).filter(e=>"bar"===e.type));function PO(e){return null!=e.stackId&&null!=e.dataKey}var SO=U([AO,e=>e.rootProps.barSize,(e,t,r)=>"horizontal"===Ua(e)?Wy(e,"xAxis",t):Wy(e,"yAxis",r)],(e,t,r)=>{var n=e.filter(PO),i=e.filter(e=>null==e.stackId),a=n.reduce((e,t)=>(e[t.stackId]||(e[t.stackId]=[]),e[t.stackId].push(t),e),{});return[...Object.entries(a).map(e=>{var[n,i]=e;return{stackId:n,dataKeys:i.map(e=>e.dataKey),barSize:jO(t,r,i[0].barSize)}}),...i.map(e=>({stackId:void 0,dataKeys:[e.dataKey].filter(e=>null!=e),barSize:jO(t,r,e.barSize)}))]}),EO=(e,t,r,n)=>{var i,a;return"horizontal"===Ua(e)?(i=Jy(e,"xAxis",t,n),a=Zy(e,"xAxis",t,n)):(i=Jy(e,"yAxis",r,n),a=Zy(e,"yAxis",r,n)),ya(i,a)};var kO,MO,TO,CO=U([SO,Rh,e=>e.rootProps.barGap,Lh,(e,t,r,n,i)=>{var a,o,l,c,s=Ua(e),u=Rh(e),{maxBarSize:f}=i,d=Te(f)?u:f;return"horizontal"===s?(l=Jy(e,"xAxis",t,n),c=Zy(e,"xAxis",t,n)):(l=Jy(e,"yAxis",r,n),c=Zy(e,"yAxis",r,n)),null!==(a=null!==(o=ya(l,c,!0))&&void 0!==o?o:d)&&void 0!==a?a:0},EO,(e,t,r,n,i)=>i.maxBarSize],(e,t,r,n,i,a,o)=>{var l=Te(o)?t:o,c=function(e,t,r,n,i){var a=n.length;if(!(a<1)){var o,l=Pe(e,r,0,!0),c=[];if(Ao(n[0].barSize)){var s=!1,u=r/a,f=n.reduce((e,t)=>e+(t.barSize||0),0);(f+=(a-1)*l)>=r&&(f-=(a-1)*l,l=0),f>=r&&u>0&&(s=!0,f=a*(u*=.9));var d={offset:((r-f)/2|0)-l,size:0};o=n.reduce((e,t)=>{var r,n=[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:d.offset+d.size+l,size:s?u:null!==(r=t.barSize)&&void 0!==r?r:0}}];return d=n[n.length-1].position,n},c)}else{var h=Pe(t,r,0,!0);r-2*h-(a-1)*l<=0&&(l=0);var p=(r-2*h-(a-1)*l)/a;p>1&&(p>>=0);var y=Ao(i)?Math.min(p,i):p;o=n.reduce((e,t,r)=>[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:h+(p+l)*r+(p-y)/2,size:y}}],c)}return o}}(r,n,i!==a?i:a,e,l);return i!==a&&null!=c&&(c=c.map(e=>xO(xO({},e),{},{position:xO(xO({},e.position),{},{offset:e.position.offset-i/2})}))),c}),DO=U([CO,OO],(e,t)=>{if(null!=e){var r=e.find(e=>e.stackId===t.stackId&&e.dataKeys.includes(t.dataKey));if(null!=r)return r.position}}),NO=U([kp,OO],(e,t)=>{if(e.some(e=>"bar"===e.type&&t.dataKey===e.dataKey&&t.stackId===e.stackId&&t.stackId===e.stackId))return t}),IO=U([(e,t,r,n)=>"horizontal"===Ua(e)?Wp(e,"yAxis",r,n):Wp(e,"xAxis",t,n),OO],(e,t)=>{if(e&&null!=(null==t?void 0:t.dataKey)){var{stackId:r}=t;if(null!=r){var n=e[r];if(n){var{stackedData:i}=n;if(i)return i.find(e=>e.key===t.dataKey)}}}}),_O=U([Ma,(e,t,r,n)=>Jy(e,"xAxis",t,n),(e,t,r,n)=>Jy(e,"yAxis",r,n),(e,t,r,n)=>Zy(e,"xAxis",t,n),(e,t,r,n)=>Zy(e,"yAxis",r,n),DO,Ua,Bd,EO,IO,NO,(e,t,r,n,i,a)=>a],(e,t,r,n,i,a,o,l,c,s,u,f)=>{var{chartData:d,dataStartIndex:h,dataEndIndex:p}=l;if(null!=u&&null!=a&&("horizontal"===o||"vertical"===o)&&null!=t&&null!=r&&null!=n&&null!=i&&null!=c){var y,{data:v}=u;if(null!=(y=null!=v&&v.length>0?v:null==d?void 0:d.slice(h,p+1)))return gO({layout:o,barSettings:u,pos:a,bandSize:c,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,stackedData:s,displayedData:y,offset:e,cells:f})}}),RO=e=>{var{chartData:t}=e,r=ai(),n=Na();return u.useEffect(()=>n?()=>{}:(r(am(t)),()=>{r(am(void 0))}),[t,r,n]),null},LO={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},zO=r({name:"brush",initialState:LO,reducers:{setBrushSettings:(e,t)=>null==t.payload?LO:t.payload}}),{setBrushSettings:BO}=zO.actions,KO=zO.reducer;class $O{static create(e){return new $O(e)}constructor(e){this.scale=e}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(e){var{bandAware:t,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==e){if(r)switch(r){case"start":default:return this.scale(e);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+n;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(t){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}isInRange(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}}kO=$O,TO=1e-4,(MO=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(MO="EPS"))in kO?Object.defineProperty(kO,MO,{value:TO,enumerable:!0,configurable:!0,writable:!0}):kO[MO]=TO;var UO=r({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=n(e).dots.findIndex(e=>e===t.payload);-1!==r&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=n(e).areas.findIndex(e=>e===t.payload);-1!==r&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=n(e).lines.findIndex(e=>e===t.payload);-1!==r&&e.lines.splice(r,1)}}}),{addDot:FO,removeDot:WO,addArea:HO,removeArea:VO,addLine:qO,removeLine:YO}=UO.actions,GO=UO.reducer,XO=u.createContext(void 0),ZO=e=>{var{children:t}=e,[r]=u.useState("".concat(Ae("recharts"),"-clip")),n=xw();if(null==n)return null;var{x:i,y:a,width:o,height:l}=n;return u.createElement(XO.Provider,{value:r},u.createElement("defs",null,u.createElement("clipPath",{id:r},u.createElement("rect",{x:i,y:a,height:l,width:o}))),t)};function JO(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}function QO(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],i=0;i<e.length;i+=t)n.push(e[i]);return n}function ej(e,t,r){return function(e){var{width:t,height:r}=e,n=function(e){return(e%180+180)%180}(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0),i=n*Math.PI/180,a=Math.atan(r/t),o=i>a&&i<Math.PI-a?r/Math.sin(i):t/Math.cos(i);return Math.abs(o)}({width:e.width+t.width,height:e.height+t.height},r)}function tj(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function rj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rj(Object(r),!0).forEach(function(t){ij(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rj(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ij(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function aj(e,t,r){var n,{tick:i,ticks:a,viewBox:o,minTickGap:l,orientation:c,interval:s,tickFormatter:u,unit:f,angle:d}=e;if(!a||!a.length||!i)return[];if(we(s)||Oo)return null!==(n=function(e,t){return QO(e,t+1)}(a,we(s)?s:0))&&void 0!==n?n:[];var h=[],p="top"===c||"bottom"===c?"width":"height",y=f&&"width"===p?Km(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},v=(e,n)=>{var i="function"==typeof u?u(e.value,n):e.value;return"width"===p?ej(Km(i,{fontSize:t,letterSpacing:r}),y,d):Km(i,{fontSize:t,letterSpacing:r})[p]},g=a.length>=2?me(a[1].coordinate-a[0].coordinate):1,m=function(e,t,r){var n="width"===r,{x:i,y:a,width:o,height:l}=e;return 1===t?{start:n?i:a,end:n?i+o:a+l}:{start:n?i+o:a+l,end:n?i:a}}(o,g,p);return"equidistantPreserveStart"===s?function(e,t,r,n,i){for(var a,o=(n||[]).slice(),{start:l,end:c}=t,s=0,u=1,f=l,d=function(){var t=null==n?void 0:n[s];if(void 0===t)return{v:QO(n,u)};var a,o=s,d=()=>(void 0===a&&(a=r(t,o)),a),h=t.coordinate,p=0===s||tj(e,h,d,f,c);p||(s=0,f=l,u+=1),p&&(f=h+e*(d()/2+i),s+=u)};u<=o.length;)if(a=d())return a.v;return[]}(g,m,v,a,l):(h="preserveStart"===s||"preserveStartEnd"===s?function(e,t,r,n,i,a){var o=(n||[]).slice(),l=o.length,{start:c,end:s}=t;if(a){var u=n[l-1],f=r(u,l-1),d=e*(u.coordinate+e*f/2-s);o[l-1]=u=nj(nj({},u),{},{tickCoord:d>0?u.coordinate-d*e:u.coordinate}),tj(e,u.tickCoord,()=>f,c,s)&&(s=u.tickCoord-e*(f/2+i),o[l-1]=nj(nj({},u),{},{isShow:!0}))}for(var h=a?l-1:l,p=function(t){var n,a=o[t],l=()=>(void 0===n&&(n=r(a,t)),n);if(0===t){var u=e*(a.coordinate-e*l()/2-c);o[t]=a=nj(nj({},a),{},{tickCoord:u<0?a.coordinate-u*e:a.coordinate})}else o[t]=a=nj(nj({},a),{},{tickCoord:a.coordinate});tj(e,a.tickCoord,l,c,s)&&(c=a.tickCoord+e*(l()/2+i),o[t]=nj(nj({},a),{},{isShow:!0}))},y=0;y<h;y++)p(y);return o}(g,m,v,a,l,"preserveStartEnd"===s):function(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,{start:l}=t,{end:c}=t,s=function(t){var n,s=a[t],u=()=>(void 0===n&&(n=r(s,t)),n);if(t===o-1){var f=e*(s.coordinate+e*u()/2-c);a[t]=s=nj(nj({},s),{},{tickCoord:f>0?s.coordinate-f*e:s.coordinate})}else a[t]=s=nj(nj({},s),{},{tickCoord:s.coordinate});tj(e,s.tickCoord,u,l,c)&&(c=s.tickCoord-e*(u()/2+i),a[t]=nj(nj({},s),{},{isShow:!0}))},u=o-1;u>=0;u--)s(u);return a}(g,m,v,a,l),h.filter(e=>e.isShow))}var oj=["viewBox"],lj=["viewBox"];function cj(){return cj=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},cj.apply(null,arguments)}function sj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function uj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?sj(Object(r),!0).forEach(function(t){dj(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sj(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function fj(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function dj(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class hj extends u.Component{constructor(e){super(e),this.tickRefs=u.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}shouldComponentUpdate(e,t){var{viewBox:r}=e,n=fj(e,oj),i=this.props,{viewBox:a}=i,o=fj(i,lj);return!JO(r,a)||!JO(n,o)||!JO(t,this.state)}getTickLineCoord(e){var t,r,n,i,a,o,{x:l,y:c,width:s,height:u,orientation:f,tickSize:d,mirror:h,tickMargin:p}=this.props,y=h?-1:1,v=e.tickSize||d,g=we(e.tickCoord)?e.tickCoord:e.coordinate;switch(f){case"top":t=r=e.coordinate,o=(n=(i=c+ +!h*u)-y*v)-y*p,a=g;break;case"left":n=i=e.coordinate,a=(t=(r=l+ +!h*s)-y*v)-y*p,o=g;break;case"right":n=i=e.coordinate,a=(t=(r=l+ +h*s)+y*v)+y*p,o=g;break;default:t=r=e.coordinate,o=(n=(i=c+ +h*u)+y*v)+y*p,a=g}return{line:{x1:t,y1:n,x2:r,y2:i},tick:{x:a,y:o}}}getTickTextAnchor(){var e,{orientation:t,mirror:r}=this.props;switch(t){case"left":e=r?"start":"end";break;case"right":e=r?"end":"start";break;default:e="middle"}return e}getTickVerticalAnchor(){var{orientation:e,mirror:t}=this.props;switch(e){case"left":case"right":return"middle";case"top":return t?"start":"end";default:return t?"end":"start"}}renderAxisLine(){var{x:e,y:t,width:r,height:n,orientation:i,mirror:a,axisLine:o}=this.props,l=uj(uj(uj({},We(this.props,!1)),We(o,!1)),{},{fill:"none"});if("top"===i||"bottom"===i){var c=+("top"===i&&!a||"bottom"===i&&a);l=uj(uj({},l),{},{x1:e,y1:t+c*n,x2:e+r,y2:t+c*n})}else{var s=+("left"===i&&!a||"right"===i&&a);l=uj(uj({},l),{},{x1:e+s*r,y1:t,x2:e+s*r,y2:t+n})}return u.createElement("line",cj({},l,{className:q("recharts-cartesian-axis-line",de(o,"className"))}))}static renderTickItem(e,t,r){var n,i=q(t.className,"recharts-cartesian-axis-tick-value");if(u.isValidElement(e))n=u.cloneElement(e,uj(uj({},t),{},{className:i}));else if("function"==typeof e)n=e(uj(uj({},t),{},{className:i}));else{var a="recharts-cartesian-axis-tick-value";"boolean"!=typeof e&&(a=q(a,e.className)),n=u.createElement(cb,cj({},t,{className:a}),r)}return n}renderTicks(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:n,stroke:i,tick:a,tickFormatter:o,unit:l}=this.props,c=aj(uj(uj({},this.props),{},{ticks:r}),e,t),s=this.getTickTextAnchor(),f=this.getTickVerticalAnchor(),d=We(this.props,!1),h=We(a,!1),p=uj(uj({},d),{},{fill:"none"},We(n,!1)),y=c.map((e,t)=>{var{line:r,tick:y}=this.getTickLineCoord(e),v=uj(uj(uj(uj({textAnchor:s,verticalAnchor:f},d),{},{stroke:"none",fill:i},h),y),{},{index:t,payload:e,visibleTicksCount:c.length,tickFormatter:o});return u.createElement(Xe,cj({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},Le(this.props,e,t)),n&&u.createElement("line",cj({},p,r,{className:q("recharts-cartesian-axis-tick-line",de(n,"className"))})),a&&hj.renderTickItem(a,v,"".concat("function"==typeof o?o(e.value,t):e.value).concat(l||"")))});return y.length>0?u.createElement("g",{className:"recharts-cartesian-axis-ticks"},y):null}render(){var{axisLine:e,width:t,height:r,className:n,hide:i}=this.props;if(i)return null;var{ticks:a}=this.props;return null!=t&&t<=0||null!=r&&r<=0?null:u.createElement(Xe,{className:q("recharts-cartesian-axis",n),ref:e=>{if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(t);var r=t[0];if(r){var n=window.getComputedStyle(r).fontSize,i=window.getComputedStyle(r).letterSpacing;n===this.state.fontSize&&i===this.state.letterSpacing||this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},e&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,a),mb.renderCallByParent(this.props))}}dj(hj,"displayName","CartesianAxis"),dj(hj,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var pj=["x1","y1","x2","y2","key"],yj=["offset"],vj=["xAxisId","yAxisId"],gj=["xAxisId","yAxisId"];function mj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function bj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mj(Object(r),!0).forEach(function(t){xj(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mj(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function xj(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function wj(){return wj=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},wj.apply(null,arguments)}function Oj(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var jj=e=>{var{fill:t}=e;if(!t||"none"===t)return null;var{fillOpacity:r,x:n,y:i,width:a,height:o,ry:l}=e;return u.createElement("rect",{x:n,y:i,ry:l,width:a,height:o,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function Aj(e,t){var r;if(u.isValidElement(e))r=u.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var{x1:n,y1:i,x2:a,y2:o,key:l}=t,c=Oj(t,pj),s=We(c,!1),{offset:f}=s,d=Oj(s,yj);r=u.createElement("line",wj({},d,{x1:n,y1:i,x2:a,y2:o,fill:"none",key:l}))}return r}function Pj(e){var{x:t,width:r,horizontal:n=!0,horizontalPoints:i}=e;if(!n||!i||!i.length)return null;var{xAxisId:a,yAxisId:o}=e,l=Oj(e,vj),c=i.map((e,i)=>{var a=bj(bj({},l),{},{x1:t,y1:e,x2:t+r,y2:e,key:"line-".concat(i),index:i});return Aj(n,a)});return u.createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function Sj(e){var{y:t,height:r,vertical:n=!0,verticalPoints:i}=e;if(!n||!i||!i.length)return null;var{xAxisId:a,yAxisId:o}=e,l=Oj(e,gj),c=i.map((e,i)=>{var a=bj(bj({},l),{},{x1:e,y1:t,x2:e,y2:t+r,key:"line-".concat(i),index:i});return Aj(n,a)});return u.createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function Ej(e){var{horizontalFill:t,fillOpacity:r,x:n,y:i,width:a,height:o,horizontalPoints:l,horizontal:c=!0}=e;if(!c||!t||!t.length)return null;var s=l.map(e=>Math.round(e+i-i)).sort((e,t)=>e-t);i!==s[0]&&s.unshift(0);var f=s.map((e,l)=>{var c=!s[l+1]?i+o-e:s[l+1]-e;if(c<=0)return null;var f=l%t.length;return u.createElement("rect",{key:"react-".concat(l),y:e,x:n,height:c,width:a,stroke:"none",fill:t[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return u.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function kj(e){var{vertical:t=!0,verticalFill:r,fillOpacity:n,x:i,y:a,width:o,height:l,verticalPoints:c}=e;if(!t||!r||!r.length)return null;var s=c.map(e=>Math.round(e+i-i)).sort((e,t)=>e-t);i!==s[0]&&s.unshift(0);var f=s.map((e,t)=>{var c=!s[t+1]?i+o-e:s[t+1]-e;if(c<=0)return null;var f=t%r.length;return u.createElement("rect",{key:"react-".concat(t),x:e,y:a,width:c,height:l,stroke:"none",fill:r[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return u.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var Mj=(e,t)=>{var{xAxis:r,width:n,height:i,offset:a}=e;return ia(aj(bj(bj(bj({},hj.defaultProps),r),{},{ticks:aa(r),viewBox:{x:0,y:0,width:n,height:i}})),a.left,a.left+a.width,t)},Tj=(e,t)=>{var{yAxis:r,width:n,height:i,offset:a}=e;return ia(aj(bj(bj(bj({},hj.defaultProps),r),{},{ticks:aa(r),viewBox:{x:0,y:0,width:n,height:i}})),a.top,a.top+a.height,t)},Cj={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function Dj(e){var t=Ba(),r=Ka(),n=za(),i=bj(bj({},Wo(e,Cj)),{},{x:we(e.x)?e.x:n.left,y:we(e.y)?e.y:n.top,width:we(e.width)?e.width:n.width,height:we(e.height)?e.height:n.height}),{xAxisId:a,yAxisId:o,x:l,y:c,width:s,height:f,syncWithTicks:d,horizontalValues:h,verticalValues:p}=i,y=Na(),v=si(e=>Gy(e,"xAxis",a,y)),g=si(e=>Gy(e,"yAxis",o,y));if(!we(s)||s<=0||!we(f)||f<=0||!we(l)||l!==+l||!we(c)||c!==+c)return null;var m=i.verticalCoordinatesGenerator||Mj,b=i.horizontalCoordinatesGenerator||Tj,{horizontalPoints:x,verticalPoints:w}=i;if(!(x&&x.length||"function"!=typeof b)){var O=h&&h.length,j=b({yAxis:g?bj(bj({},g),{},{ticks:O?h:g.ticks}):void 0,width:t,height:r,offset:n},!!O||d);km(Array.isArray(j),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof j,"]")),Array.isArray(j)&&(x=j)}if(!(w&&w.length||"function"!=typeof m)){var A=p&&p.length,P=m({xAxis:v?bj(bj({},v),{},{ticks:A?p:v.ticks}):void 0,width:t,height:r,offset:n},!!A||d);km(Array.isArray(P),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof P,"]")),Array.isArray(P)&&(w=P)}return u.createElement("g",{className:"recharts-cartesian-grid"},u.createElement(jj,{fill:i.fill,fillOpacity:i.fillOpacity,x:i.x,y:i.y,width:i.width,height:i.height,ry:i.ry}),u.createElement(Ej,wj({},i,{horizontalPoints:x})),u.createElement(kj,wj({},i,{verticalPoints:w})),u.createElement(Pj,wj({},i,{offset:n,horizontalPoints:x,xAxis:v,yAxis:g})),u.createElement(Sj,wj({},i,{offset:n,verticalPoints:w,xAxis:v,yAxis:g})))}Dj.displayName="CartesianGrid";var Nj=(e,t,r,n)=>Jy(e,"xAxis",t,n),Ij=(e,t,r,n)=>Zy(e,"xAxis",t,n),_j=(e,t,r,n)=>Jy(e,"yAxis",r,n),Rj=(e,t,r,n)=>Zy(e,"yAxis",r,n),Lj=U([Ua,Nj,_j,Ij,Rj],(e,t,r,n,i)=>na(e,"xAxis")?ya(t,n,!1):ya(r,i,!1)),zj=U([kp,(e,t,r,n,i)=>i],(e,t)=>{if(e.some(e=>"line"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),Bj=U([Ua,Nj,_j,Ij,Rj,zj,Lj,Bd],(e,t,r,n,i,a,o,l)=>{var{chartData:c,dataStartIndex:s,dataEndIndex:u}=l;if(null!=a&&null!=t&&null!=r&&null!=n&&null!=i&&0!==n.length&&0!==i.length&&null!=o){var f,{dataKey:d,data:h}=a;if(null!=(f=null!=h&&h.length>0?h:null==c?void 0:c.slice(s,u+1)))return function(e){var{layout:t,xAxis:r,yAxis:n,xAxisTicks:i,yAxisTicks:a,dataKey:o,bandSize:l,displayedData:c}=e;return c.map((e,c)=>{var s=ra(e,o);return"horizontal"===t?{x:ua({axis:r,ticks:i,bandSize:l,entry:e,index:c}),y:Te(s)?null:n.scale(s),value:s,payload:e}:{x:Te(s)?null:r.scale(s),y:ua({axis:n,ticks:a,bandSize:l,entry:e,index:c}),value:s,payload:e}})}({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,dataKey:d,bandSize:o,displayedData:f})}}),Kj=["type","layout","connectNulls","needClip"],$j=["activeDot","animateNewValues","animationBegin","animationDuration","animationEasing","connectNulls","dot","hide","isAnimationActive","label","legendType","xAxisId","yAxisId"];function Uj(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function Fj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Wj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Fj(Object(r),!0).forEach(function(t){Hj(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Fj(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Hj(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Vj(){return Vj=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Vj.apply(null,arguments)}var qj=e=>{var{dataKey:t,name:r,stroke:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:ga(r,t),payload:e}]};function Yj(e){var{dataKey:t,data:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:l,unit:c}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,nameKey:void 0,name:ga(o,t),hide:l,type:e.tooltipType,color:e.stroke,unit:c}}}var Gj=(e,t)=>"".concat(t,"px ").concat(e-t,"px");function Xj(e,t){for(var r=e.length%2!=0?[...e,0]:e,n=[],i=0;i<t;++i)n=[...n,...r];return n}function Zj(e){var{clipPathId:t,points:r,props:n}=e,{dot:i,dataKey:a,needClip:o}=n;if(!function(e,t){return null!=e&&(!!t||1===e.length)}(r,i))return null;var l=Fe(i),c=We(n,!1),s=We(i,!0),f=r.map((e,t)=>{var n=Wj(Wj(Wj({key:"dot-".concat(t),r:3},c),s),{},{index:t,cx:e.x,cy:e.y,dataKey:a,value:e.value,payload:e.payload,points:r});return function(e,t){var r;if(u.isValidElement(e))r=u.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var n=q("recharts-line-dot","boolean"!=typeof e?e.className:"");r=u.createElement(Ub,Vj({},t,{className:n}))}return r}(i,n)}),d={clipPath:o?"url(#clipPath-".concat(l?"":"dots-").concat(t,")"):null};return u.createElement(Xe,Vj({className:"recharts-line-dots",key:"dots"},d),f)}function Jj(e){var{clipPathId:t,pathRef:r,points:n,strokeDasharray:i,props:a,showLabels:o}=e,{type:l,layout:c,connectNulls:s,needClip:f}=a,d=Uj(a,Kj),h=Wj(Wj({},We(d,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:f?"url(#clipPath-".concat(t,")"):null,points:n,type:l,layout:c,connectNulls:s,strokeDasharray:null!=i?i:a.strokeDasharray});return u.createElement(u.Fragment,null,(null==n?void 0:n.length)>1&&u.createElement(_o,Vj({},h,{pathRef:r})),u.createElement(Zj,{points:n,clipPathId:t,props:a}),o&&Kb.renderCallByParent(a,n))}function Qj(e){var{clipPathId:t,props:r,pathRef:n,previousPointsRef:i,longestAnimatedLengthRef:a}=e,{points:o,strokeDasharray:l,isAnimationActive:c,animationBegin:s,animationDuration:f,animationEasing:d,animateNewValues:h,width:p,height:y,onAnimationEnd:v,onAnimationStart:g}=r,m=i.current,b=ew(r,"recharts-line-"),[x,w]=u.useState(!1),O=u.useCallback(()=>{"function"==typeof v&&v(),w(!1)},[v]),j=u.useCallback(()=>{"function"==typeof g&&g(),w(!0)},[g]),A=function(e){try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch(t){return 0}}(n.current),P=a.current;return u.createElement(Nl,{begin:s,duration:f,isActive:c,easing:d,from:{t:0},to:{t:1},onAnimationEnd:O,onAnimationStart:j,key:b},e=>{var c,{t:s}=e,f=Ee(P,A+P),d=Math.min(f(s),A);if(l){var v="".concat(l).split(/[,\s]+/gim).map(e=>parseFloat(e));c=((e,t,r)=>{var n=r.reduce((e,t)=>e+t);if(!n)return Gj(t,e);for(var i=Math.floor(e/n),a=e%n,o=t-e,l=[],c=0,s=0;c<r.length;s+=r[c],++c)if(s+r[c]>a){l=[...r.slice(0,c),a-s];break}var u=l.length%2==0?[0,o]:[o];return[...Xj(r,i),...l,...u].map(e=>"".concat(e,"px")).join(", ")})(d,A,v)}else c=Gj(A,d);if(m){var g=m.length/o.length,b=1===s?o:o.map((e,t)=>{var r=Math.floor(t*g);if(m[r]){var n=m[r],i=Ee(n.x,e.x),a=Ee(n.y,e.y);return Wj(Wj({},e),{},{x:i(s),y:a(s)})}if(h){var o=Ee(2*p,e.x),l=Ee(y/2,e.y);return Wj(Wj({},e),{},{x:o(s),y:l(s)})}return Wj(Wj({},e),{},{x:e.x,y:e.y})});return i.current=b,u.createElement(Jj,{props:r,points:b,clipPathId:t,pathRef:n,showLabels:!x,strokeDasharray:c})}return s>0&&A>0&&(i.current=o,a.current=d),u.createElement(Jj,{props:r,points:o,clipPathId:t,pathRef:n,showLabels:!x,strokeDasharray:c})})}function eA(e){var{clipPathId:t,props:r}=e,{points:n,isAnimationActive:i}=r,a=u.useRef(null),o=u.useRef(0),l=u.useRef(null),c=a.current;return i&&n&&n.length&&c!==n?u.createElement(Qj,{props:r,clipPathId:t,previousPointsRef:a,longestAnimatedLengthRef:o,pathRef:l}):u.createElement(Jj,{props:r,points:n,clipPathId:t,pathRef:l,showLabels:!0})}var tA=(e,t)=>({x:e.x,y:e.y,value:e.value,errorVal:ra(e.payload,t)});class rA extends u.Component{constructor(){super(...arguments),Hj(this,"id",Ae("recharts-line-"))}render(){var e,{hide:t,dot:r,points:n,className:i,xAxisId:a,yAxisId:o,top:l,left:c,width:s,height:f,id:d,needClip:h,layout:p}=this.props;if(t)return null;var y=q("recharts-line",i),v=Te(d)?this.id:d,{r:g=3,strokeWidth:m=2}=null!==(e=We(r,!1))&&void 0!==e?e:{r:3,strokeWidth:2},b=Fe(r),x=2*g+m;return u.createElement(u.Fragment,null,u.createElement(Xe,{className:y},h&&u.createElement("defs",null,u.createElement(Jw,{clipPathId:v,xAxisId:a,yAxisId:o}),!b&&u.createElement("clipPath",{id:"clipPath-dots-".concat(v)},u.createElement("rect",{x:c-x/2,y:l-x/2,width:s+x,height:f+x}))),u.createElement(eA,{props:this.props,clipPathId:v}),u.createElement(Bw,{direction:"horizontal"===p?"y":"x"},u.createElement(Cw,{xAxisId:a,yAxisId:o,data:n,dataPointFormatter:tA,errorBarOffset:0},this.props.children))),u.createElement(Aw,{activeDot:this.props.activeDot,points:n,mainColor:this.props.stroke,itemDataKey:this.props.dataKey}))}}var nA={activeDot:!0,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!0,fill:"#fff",hide:!1,isAnimationActive:!Oo,label:!1,legendType:"line",stroke:"#3182bd",strokeWidth:1,xAxisId:0,yAxisId:0};function iA(e){var t=Wo(e,nA),{activeDot:r,animateNewValues:n,animationBegin:i,animationDuration:a,animationEasing:o,connectNulls:l,dot:c,hide:s,isAnimationActive:f,label:d,legendType:h,xAxisId:p,yAxisId:y}=t,v=Uj(t,$j),{needClip:g}=Zw(p,y),{height:m,width:b,x:x,y:w}=xw(),O=Fa(),j=Na(),A=u.useMemo(()=>({dataKey:e.dataKey,data:e.data}),[e.dataKey,e.data]),P=si(e=>Bj(e,p,y,j,A));return"horizontal"!==O&&"vertical"!==O?null:u.createElement(rA,Vj({},v,{connectNulls:l,dot:c,activeDot:r,animateNewValues:n,animationBegin:i,animationDuration:a,animationEasing:o,isAnimationActive:f,hide:s,label:d,legendType:h,xAxisId:p,yAxisId:y,points:P,layout:O,height:m,width:b,left:x,top:w,needClip:g}))}class aA extends u.PureComponent{render(){return u.createElement(Dw,{type:"line",data:this.props.data,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:void 0,hide:this.props.hide,barSize:void 0},u.createElement(Jx,{legendPayload:qj(this.props)}),u.createElement(Xx,{fn:Yj,args:this.props}),u.createElement(iA,this.props))}}Hj(aA,"displayName","Line"),Hj(aA,"defaultProps",nA);var oA=(e,t,r,n)=>Jy(e,"xAxis",t,n),lA=(e,t,r,n)=>Zy(e,"xAxis",t,n),cA=(e,t,r,n)=>Jy(e,"yAxis",r,n),sA=(e,t,r,n)=>Zy(e,"yAxis",r,n),uA=U([Ua,oA,cA,lA,sA],(e,t,r,n,i)=>na(e,"xAxis")?ya(t,n,!1):ya(r,i,!1)),fA=U([kp,(e,t,r,n,i)=>i],(e,t)=>{if(e.some(e=>"area"===e.type&&t.dataKey===e.dataKey&&sa(t.stackId)===e.stackId&&t.data===e.data))return t}),dA=U([Ua,oA,cA,lA,sA,(e,t,r,n,i)=>{var a,o,l=Ua(e);if(null!=(o=na(l,"xAxis")?Wp(e,"yAxis",r,n):Wp(e,"xAxis",t,n))){var{dataKey:c,stackId:s}=i;if(null!=s){var u=null===(a=o[s])||void 0===a?void 0:a.stackedData;return null==u?void 0:u.find(e=>e.key===c)}}},Bd,uA,fA],(e,t,r,n,i,a,o,l,c)=>{var{chartData:s,dataStartIndex:u,dataEndIndex:f}=o;if(null!=c&&("horizontal"===e||"vertical"===e)&&null!=t&&null!=r&&null!=n&&null!=i&&0!==n.length&&0!==i.length&&null!=l){var d,{data:h}=c;if(null!=(d=h&&h.length>0?h:null==s?void 0:s.slice(u,f+1))){return function(e){var t,{areaSettings:{connectNulls:r,baseValue:n,dataKey:i},stackedData:a,layout:o,chartBaseValue:l,xAxis:c,yAxis:s,displayedData:u,dataStartIndex:f,xAxisTicks:d,yAxisTicks:h,bandSize:p}=e,y=a&&a.length,v=NA(o,l,n,c,s),g="horizontal"===o,m=!1,b=u.map((e,t)=>{var n;y?n=a[f+t]:(n=ra(e,i),Array.isArray(n)?m=!0:n=[v,n]);var o=null==n[1]||y&&!r&&null==ra(e,i);return g?{x:ua({axis:c,ticks:d,bandSize:p,entry:e,index:t}),y:o?null:s.scale(n[1]),value:n,payload:e}:{x:o?null:c.scale(n[1]),y:ua({axis:s,ticks:h,bandSize:p,entry:e,index:t}),value:n,payload:e}});t=y||m?b.map(e=>{var t=Array.isArray(e.value)?e.value[0]:null;return g?{x:e.x,y:null!=t&&null!=e.y?s.scale(t):null}:{x:null!=t?c.scale(t):null,y:e.y}}):g?s.scale(v):c.scale(v);return{points:b,baseLine:t,isRange:m}}({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,dataStartIndex:u,areaSettings:c,stackedData:a,displayedData:d,chartBaseValue:void 0,bandSize:l})}}}),hA=["layout","type","stroke","connectNulls","isRange"],pA=["activeDot","animationBegin","animationDuration","animationEasing","connectNulls","dot","fill","fillOpacity","hide","isAnimationActive","legendType","stroke","xAxisId","yAxisId"];function yA(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function vA(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gA(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vA(Object(r),!0).forEach(function(t){mA(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vA(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function mA(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bA(){return bA=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},bA.apply(null,arguments)}function xA(e,t){return e&&"none"!==e?e:t}var wA=e=>{var{dataKey:t,name:r,stroke:n,fill:i,legendType:a,hide:o}=e;return[{inactive:o,dataKey:t,type:a,color:xA(n,i),value:ga(r,t),payload:e}]};function OA(e){var{dataKey:t,data:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:l,unit:c}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,nameKey:void 0,name:ga(o,t),hide:l,type:e.tooltipType,color:xA(n,a),unit:c}}}function jA(e){var{clipPathId:t,points:r,props:n}=e,{needClip:i,dot:a,dataKey:o}=n;if(!function(e,t){return null!=e&&(!!t||1===e.length)}(r,a))return null;var l=Fe(a),c=We(n,!1),s=We(a,!0),f=r.map((e,t)=>{var n=gA(gA(gA({key:"dot-".concat(t),r:3},c),s),{},{index:t,cx:e.x,cy:e.y,dataKey:o,value:e.value,payload:e.payload,points:r});return((e,t)=>{var r;if(u.isValidElement(e))r=u.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var n=q("recharts-area-dot","boolean"!=typeof e?e.className:"");r=u.createElement(Ub,bA({},t,{className:n}))}return r})(a,n)}),d={clipPath:i?"url(#clipPath-".concat(l?"":"dots-").concat(t,")"):void 0};return u.createElement(Xe,bA({className:"recharts-area-dots"},d),f)}function AA(e){var{points:t,baseLine:r,needClip:n,clipPathId:i,props:a,showLabels:o}=e,{layout:l,type:c,stroke:s,connectNulls:f,isRange:d}=a,h=yA(a,hA);return u.createElement(u.Fragment,null,(null==t?void 0:t.length)>1&&u.createElement(Xe,{clipPath:n?"url(#clipPath-".concat(i,")"):void 0},u.createElement(_o,bA({},We(h,!0),{points:t,connectNulls:f,type:c,baseLine:r,layout:l,stroke:"none",className:"recharts-area-area"})),"none"!==s&&u.createElement(_o,bA({},We(a,!1),{className:"recharts-area-curve",layout:l,type:c,connectNulls:f,fill:"none",points:t})),"none"!==s&&d&&u.createElement(_o,bA({},We(a,!1),{className:"recharts-area-curve",layout:l,type:c,connectNulls:f,fill:"none",points:r}))),u.createElement(jA,{points:t,props:a,clipPathId:i}),o&&Kb.renderCallByParent(a,t))}function PA(e){var{alpha:t,baseLine:r,points:n,strokeWidth:i}=e,a=n[0].y,o=n[n.length-1].y;if(!Ao(a)||!Ao(o))return null;var l=t*Math.abs(a-o),c=Math.max(...n.map(e=>e.x||0));return we(r)?c=Math.max(r,c):r&&Array.isArray(r)&&r.length&&(c=Math.max(...r.map(e=>e.x||0),c)),we(c)?u.createElement("rect",{x:0,y:a<o?a:a-l,width:c+(i?parseInt("".concat(i),10):1),height:Math.floor(l)}):null}function SA(e){var{alpha:t,baseLine:r,points:n,strokeWidth:i}=e,a=n[0].x,o=n[n.length-1].x;if(!Ao(a)||!Ao(o))return null;var l=t*Math.abs(a-o),c=Math.max(...n.map(e=>e.y||0));return we(r)?c=Math.max(r,c):r&&Array.isArray(r)&&r.length&&(c=Math.max(...r.map(e=>e.y||0),c)),we(c)?u.createElement("rect",{x:a<o?a:a-l,y:0,width:l,height:Math.floor(c+(i?parseInt("".concat(i),10):1))}):null}function EA(e){var{alpha:t,layout:r,points:n,baseLine:i,strokeWidth:a}=e;return"vertical"===r?u.createElement(PA,{alpha:t,points:n,baseLine:i,strokeWidth:a}):u.createElement(SA,{alpha:t,points:n,baseLine:i,strokeWidth:a})}function kA(e){var{needClip:t,clipPathId:r,props:n,previousPointsRef:i,previousBaselineRef:a}=e,{points:o,baseLine:l,isAnimationActive:c,animationBegin:s,animationDuration:f,animationEasing:d,onAnimationStart:h,onAnimationEnd:p}=n,y=ew(n,"recharts-area-"),[v,g]=u.useState(!0),m=u.useCallback(()=>{"function"==typeof p&&p(),g(!1)},[p]),b=u.useCallback(()=>{"function"==typeof h&&h(),g(!0)},[h]),x=i.current,w=a.current;return u.createElement(Nl,{begin:s,duration:f,isActive:c,easing:d,from:{t:0},to:{t:1},onAnimationEnd:m,onAnimationStart:b,key:y},e=>{var{t:c}=e;if(x){var s,f=x.length/o.length,d=1===c?o:o.map((e,t)=>{var r=Math.floor(t*f);if(x[r]){var n=x[r];return gA(gA({},e),{},{x:ke(n.x,e.x,c),y:ke(n.y,e.y,c)})}return e});return s=we(l)?ke(w,l,c):Te(l)||be(l)?ke(w,0,c):l.map((e,t)=>{var r=Math.floor(t*f);if(Array.isArray(w)&&w[r]){var n=w[r];return gA(gA({},e),{},{x:ke(n.x,e.x,c),y:ke(n.y,e.y,c)})}return e}),c>0&&(i.current=d,a.current=s),u.createElement(AA,{points:d,baseLine:s,needClip:t,clipPathId:r,props:n,showLabels:!v})}return c>0&&(i.current=o,a.current=l),u.createElement(Xe,null,u.createElement("defs",null,u.createElement("clipPath",{id:"animationClipPath-".concat(r)},u.createElement(EA,{alpha:c,points:o,baseLine:l,layout:n.layout,strokeWidth:n.strokeWidth}))),u.createElement(Xe,{clipPath:"url(#animationClipPath-".concat(r,")")},u.createElement(AA,{points:o,baseLine:l,needClip:t,clipPathId:r,props:n,showLabels:!0})))})}function MA(e){var{needClip:t,clipPathId:r,props:n}=e,{points:i,baseLine:a,isAnimationActive:o}=n,l=u.useRef(null),c=u.useRef(),s=l.current,f=c.current;return o&&i&&i.length&&(s!==i||f!==a)?u.createElement(kA,{needClip:t,clipPathId:r,props:n,previousPointsRef:l,previousBaselineRef:c}):u.createElement(AA,{points:i,baseLine:a,needClip:t,clipPathId:r,props:n,showLabels:!0})}class TA extends u.PureComponent{constructor(){super(...arguments),mA(this,"id",Ae("recharts-area-"))}render(){var e,{hide:t,dot:r,points:n,className:i,top:a,left:o,needClip:l,xAxisId:c,yAxisId:s,width:f,height:d,id:h,baseLine:p}=this.props;if(t)return null;var y=q("recharts-area",i),v=Te(h)?this.id:h,{r:g=3,strokeWidth:m=2}=null!==(e=We(r,!1))&&void 0!==e?e:{r:3,strokeWidth:2},b=Fe(r),x=2*g+m;return u.createElement(u.Fragment,null,u.createElement(Xe,{className:y},l&&u.createElement("defs",null,u.createElement(Jw,{clipPathId:v,xAxisId:c,yAxisId:s}),!b&&u.createElement("clipPath",{id:"clipPath-dots-".concat(v)},u.createElement("rect",{x:o-x/2,y:a-x/2,width:f+x,height:d+x}))),u.createElement(MA,{needClip:l,clipPathId:v,props:this.props})),u.createElement(Aw,{points:n,mainColor:xA(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}),this.props.isRange&&Array.isArray(p)&&u.createElement(Aw,{points:p,mainColor:xA(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}))}}var CA={activeDot:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!1,fill:"#3182bd",fillOpacity:.6,hide:!1,isAnimationActive:!Oo,legendType:"line",stroke:"#3182bd",xAxisId:0,yAxisId:0};function DA(e){var t,r=Wo(e,CA),{activeDot:n,animationBegin:i,animationDuration:a,animationEasing:o,connectNulls:l,dot:c,fill:s,fillOpacity:f,hide:d,isAnimationActive:h,legendType:p,stroke:y,xAxisId:v,yAxisId:g}=r,m=yA(r,pA),b=Fa(),x=kg(),{needClip:w}=Zw(v,g),O=Na(),j=u.useMemo(()=>({baseValue:e.baseValue,stackId:e.stackId,connectNulls:l,data:e.data,dataKey:e.dataKey}),[e.baseValue,e.stackId,l,e.data,e.dataKey]),{points:A,isRange:P,baseLine:S}=null!==(t=si(e=>dA(e,v,g,O,j)))&&void 0!==t?t:{},{height:E,width:k,x:M,y:T}=xw();return"horizontal"!==b&&"vertical"!==b||"AreaChart"!==x&&"ComposedChart"!==x?null:u.createElement(TA,bA({},m,{activeDot:n,animationBegin:i,animationDuration:a,animationEasing:o,baseLine:S,connectNulls:l,dot:c,fill:s,fillOpacity:f,height:E,hide:d,layout:b,isAnimationActive:h,isRange:P,legendType:p,needClip:w,points:A,stroke:y,width:k,left:M,top:T,xAxisId:v,yAxisId:g}))}var NA=(e,t,r,n,i)=>{var a=null!=r?r:t;if(we(a))return a;var o="horizontal"===e?i:n,l=o.scale.domain();if("number"===o.type){var c=Math.max(l[0],l[1]),s=Math.min(l[0],l[1]);return"dataMin"===a?s:"dataMax"===a||c<0?c:Math.max(Math.min(l[0],l[1]),0)}return"dataMin"===a?l[0]:"dataMax"===a?l[1]:l[0]};class IA extends u.PureComponent{render(){return u.createElement(Dw,{type:"area",data:this.props.data,dataKey:this.props.dataKey,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,stackId:this.props.stackId,hide:this.props.hide,barSize:void 0},u.createElement(Jx,{legendPayload:wA(this.props)}),u.createElement(Xx,{fn:OA,args:this.props}),u.createElement(DA,this.props))}}function _A(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function RA(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_A(Object(r),!0).forEach(function(t){LA(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_A(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function LA(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}mA(IA,"displayName","Area"),mA(IA,"defaultProps",CA);var zA=r({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=i(t.payload)},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=i(t.payload)},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=i(t.payload)},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=RA(RA({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:BA,removeXAxis:KA,addYAxis:$A,removeYAxis:UA,addZAxis:FA,removeZAxis:WA,updateYAxisWidth:HA}=zA.actions,VA=zA.reducer,qA=["children"],YA=["dangerouslySetInnerHTML","ticks"];function GA(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function XA(){return XA=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},XA.apply(null,arguments)}function ZA(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function JA(e){var t=ai(),r=u.useMemo(()=>{var{children:t}=e;return ZA(e,qA)},[e]),n=si(e=>bp(e,r.id)),i=r===n;return u.useEffect(()=>(t(BA(r)),()=>{t(KA(r))}),[r,t]),i?e.children:null}var QA=e=>{var{xAxisId:t,className:r}=e,n=si(Ca),i=Na(),a="xAxis",o=si(e=>Ny(e,a,t,i)),l=si(e=>Xy(e,a,t,i)),c=si(e=>Ky(e,t)),s=si(e=>((e,t)=>{var r=Ma(e),n=bp(e,t);if(null!=n){var i=$y(e,n.orientation,n.mirror)[t];return null==i?{x:r.left,y:0}:{x:r.left,y:i}}})(e,t));if(null==c||null==s)return null;var{dangerouslySetInnerHTML:f,ticks:d}=e,h=ZA(e,YA);return u.createElement(hj,XA({},h,{scale:o,x:s.x,y:s.y,width:c.width,height:c.height,className:q("recharts-".concat(a," ").concat(a),r),viewBox:n,ticks:l}))},eP=e=>{var t,r,n,i,a;return u.createElement(JA,{interval:null!==(t=e.interval)&&void 0!==t?t:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:null!==(r=e.includeHidden)&&void 0!==r&&r,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!==(n=e.angle)&&void 0!==n?n:0,minTickGap:null!==(i=e.minTickGap)&&void 0!==i?i:5,tick:null===(a=e.tick)||void 0===a||a,tickFormatter:e.tickFormatter},u.createElement(QA,e))};class tP extends u.Component{render(){return u.createElement(eP,this.props)}}GA(tP,"displayName","XAxis"),GA(tP,"defaultProps",{allowDataOverflow:mp.allowDataOverflow,allowDecimals:mp.allowDecimals,allowDuplicatedCategory:mp.allowDuplicatedCategory,height:mp.height,hide:!1,mirror:mp.mirror,orientation:mp.orientation,padding:mp.padding,reversed:mp.reversed,scale:mp.scale,tickCount:mp.tickCount,type:mp.type,xAxisId:0});var rP=["dangerouslySetInnerHTML","ticks"];function nP(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function iP(){return iP=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},iP.apply(null,arguments)}function aP(e){var t=ai();return u.useEffect(()=>(t($A(e)),()=>{t(UA(e))}),[e,t]),null}var oP=e=>{var t,{yAxisId:r,className:n,width:i,label:a}=e,o=u.useRef(null),l=u.useRef(null),c=si(Ca),s=Na(),f=ai(),d="yAxis",h=si(e=>Ny(e,d,r,s)),p=si(e=>Fy(e,r)),y=si(e=>((e,t)=>{var r=Ma(e),n=wp(e,t);if(null!=n){var i=Uy(e,n.orientation,n.mirror)[t];return null==i?{x:0,y:r.top}:{x:i,y:r.top}}})(e,r)),v=si(e=>Xy(e,d,r,s));if(u.useLayoutEffect(()=>{var e;if("auto"===i&&p&&!vb(a)&&!u.isValidElement(a)){var t=o.current,n=null==t||null===(e=t.tickRefs)||void 0===e?void 0:e.current,{tickSize:c,tickMargin:s}=t.props,d=(e=>{var{ticks:t,label:r,labelGapWithTick:n=5,tickSize:i=0,tickMargin:a=0}=e,o=0;if(t){t.forEach(e=>{if(e){var t=e.getBoundingClientRect();t.width>o&&(o=t.width)}});var l=r?r.getBoundingClientRect().width:0,c=o+(i+a)+l+(r?n:0);return Math.round(c)}return 0})({ticks:n,label:l.current,labelGapWithTick:5,tickSize:c,tickMargin:s});Math.round(p.width)!==Math.round(d)&&f(HA({id:r,width:d}))}},[o,null==o||null===(t=o.current)||void 0===t||null===(t=t.tickRefs)||void 0===t?void 0:t.current,null==p?void 0:p.width,p,f,a,r,i]),null==p||null==y)return null;var{dangerouslySetInnerHTML:g,ticks:m}=e,b=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,rP);return u.createElement(hj,iP({},b,{ref:o,labelRef:l,scale:h,x:y.x,y:y.y,width:p.width,height:p.height,className:q("recharts-".concat(d," ").concat(d),n),viewBox:c,ticks:v}))},lP=e=>{var t,r,n,i,a;return u.createElement(u.Fragment,null,u.createElement(aP,{interval:null!==(t=e.interval)&&void 0!==t?t:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:null!==(r=e.includeHidden)&&void 0!==r&&r,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!==(n=e.angle)&&void 0!==n?n:0,minTickGap:null!==(i=e.minTickGap)&&void 0!==i?i:5,tick:null===(a=e.tick)||void 0===a||a,tickFormatter:e.tickFormatter}),u.createElement(oP,e))},cP={allowDataOverflow:xp.allowDataOverflow,allowDecimals:xp.allowDecimals,allowDuplicatedCategory:xp.allowDuplicatedCategory,hide:!1,mirror:xp.mirror,orientation:xp.orientation,padding:xp.padding,reversed:xp.reversed,scale:xp.scale,tickCount:xp.tickCount,type:xp.type,width:xp.width,yAxisId:0};class sP extends u.Component{render(){return u.createElement(lP,this.props)}}nP(sP,"displayName","YAxis"),nP(sP,"defaultProps",cP);var uP=U([(e,t)=>t,Ua,fp,Nv,og,ug,Dg,Ma],(e,t,r,n,i,a,o,l)=>{if(e&&t&&n&&i&&a){var c=function(e,t,r,n,i){return"horizontal"===r||"vertical"===r?e>=i.left&&e<=i.left+i.width&&t>=i.top&&t<=i.top+i.height?{x:e,y:t}:null:n?Ji({x:e,y:t},n):null}(e.chartX,e.chartY,t,r,l);if(c){var s=((e,t)=>"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius)(c,t),u=((e,t,r,n,i)=>{var a,o=-1,l=null!==(a=null==t?void 0:t.length)&&void 0!==a?a:0;if(l<=1||null==e)return 0;if("angleAxis"===n&&null!=i&&Math.abs(Math.abs(i[1]-i[0])-360)<=1e-6)for(var c=0;c<l;c++){var s=c>0?r[c-1].coordinate:r[l-1].coordinate,u=r[c].coordinate,f=c>=l-1?r[0].coordinate:r[c+1].coordinate,d=void 0;if(me(u-s)!==me(f-u)){var h=[];if(me(f-u)===me(i[1]-i[0])){d=f;var p=u+i[1]-i[0];h[0]=Math.min(p,(p+s)/2),h[1]=Math.max(p,(p+s)/2)}else{d=s;var y=f+i[1]-i[0];h[0]=Math.min(u,(y+u)/2),h[1]=Math.max(u,(y+u)/2)}var v=[Math.min(u,(d+u)/2),Math.max(u,(d+u)/2)];if(e>v[0]&&e<=v[1]||e>=h[0]&&e<=h[1]){({index:o}=r[c]);break}}else{var g=Math.min(s,f),m=Math.max(s,f);if(e>(g+u)/2&&e<=(m+u)/2){({index:o}=r[c]);break}}}else if(t)for(var b=0;b<l;b++)if(0===b&&e<=(t[b].coordinate+t[b+1].coordinate)/2||b>0&&b<l-1&&e>(t[b].coordinate+t[b-1].coordinate)/2&&e<=(t[b].coordinate+t[b+1].coordinate)/2||b===l-1&&e>(t[b].coordinate+t[b-1].coordinate)/2){({index:o}=t[b]);break}return o})(s,o,a,n,i),f=((e,t,r,n)=>{var i=t.find(e=>e&&e.index===r);if(i){if("horizontal"===e)return{x:i.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:i.coordinate};if("centric"===e){var a=i.coordinate,{radius:o}=n;return ea(ea(ea({},n),Yi(n.cx,n.cy,o,a)),{},{angle:a,radius:o})}var l=i.coordinate,{angle:c}=n;return ea(ea(ea({},n),Yi(n.cx,n.cy,l,c)),{},{angle:c,radius:l})}return{x:0,y:0}})(t,a,u,c);return{activeIndex:String(u),activeCoordinate:f}}}}),fP=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}},dP=a("mouseClick"),hP=o();hP.startListening({actionCreator:dP,effect:(e,t)=>{var r=e.payload,n=uP(t.getState(),fP(r));null!=(null==n?void 0:n.activeIndex)&&t.dispatch(vv({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var pP=a("mouseMove"),yP=o();function vP(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}yP.startListening({actionCreator:pP,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=iv(n,n.tooltip.settings.shared),a=uP(n,fP(r));"axis"===i&&(null!=(null==a?void 0:a.activeIndex)?t.dispatch(yv({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate})):t.dispatch(hv()))}});var gP={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},mP=r({name:"rootProps",initialState:gP,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=null!==(r=t.payload.barGap)&&void 0!==r?r:gP.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),bP=mP.reducer,{updateOptions:xP}=mP.actions,wP=r({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:OP}=wP.actions,jP=wP.reducer,AP=a("keyDown"),PP=a("focus"),SP=o();SP.startListening({actionCreator:AP,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,i=e.payload;if("ArrowRight"===i||"ArrowLeft"===i||"Enter"===i){var a=Number(Av(n,$v(r))),o=ug(r);if("Enter"!==i){var l=a+("ArrowRight"===i?1:-1)*("left-to-right"===ev(r)?1:-1);if(!(null==o||l>=o.length||l<0)){var c=Rg(r,"axis","hover",String(l));t.dispatch(mv({active:!0,activeIndex:l.toString(),activeDataKey:void 0,activeCoordinate:c}))}}else{var s=Rg(r,"axis","hover",String(n.index));t.dispatch(mv({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:s}))}}}}}),SP.startListening({actionCreator:PP,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var i=Rg(r,"axis","hover",String("0"));t.dispatch(mv({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:i}))}}}});var EP=a("externalEvent"),kP=o();kP.startListening({actionCreator:EP,effect:(e,t)=>{if(null!=e.payload.handler){var r=t.getState(),n={activeCoordinate:xg(r),activeDataKey:gg(r),activeIndex:yg(r),activeLabel:vg(r),activeTooltipIndex:yg(r),isTooltipActive:wg(r)};e.payload.handler(n,e.payload.reactEvent)}}});var MP=U([kv],e=>e.tooltipItemPayloads),TP=U([MP,Ev,(e,t,r)=>t,(e,t,r)=>r],(e,t,r,n)=>{var i=e.find(e=>e.settings.dataKey===n);if(null!=i){var{positions:a}=i;if(null!=a)return t(a,r)}}),CP=a("touchMove"),DP=o();DP.startListening({actionCreator:CP,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=iv(n,n.tooltip.settings.shared);if("axis"===i){var a=uP(n,fP({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));null!=(null==a?void 0:a.activeIndex)&&t.dispatch(yv({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate}))}else if("item"===i){var o,l=r.touches[0],c=document.elementFromPoint(l.clientX,l.clientY);if(!c||!c.getAttribute)return;var s=c.getAttribute(Aa),u=null!==(o=c.getAttribute(Pa))&&void 0!==o?o:void 0,f=TP(t.getState(),s,u);t.dispatch(fv({activeDataKey:u,activeIndex:s,activeCoordinate:f}))}}});var NP=c({brush:KO,cartesianAxis:VA,chartData:cm,graphicalItems:Ax,layout:Ui,legend:Ga,options:tm,polarAxis:ax,polarOptions:jP,referenceElements:GO,rootProps:bP,tooltip:bv});function IP(e){var{preloadedState:t,children:r,reduxStoreName:n}=e,i=Na(),a=u.useRef(null);if(i)return r;null==a.current&&(a.current=function(e){return l({reducer:NP,preloadedState:e,middleware:e=>e({serializableCheck:!1}).concat([hP.middleware,yP.middleware,SP.middleware,kP.middleware,DP.middleware]),devTools:{serialize:{replacer:vP},name:"recharts-".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart")}})}(t,n));var o=ni;return u.createElement(s,{context:o,store:a.current},r)}function _P(e){var{layout:t,width:r,height:n,margin:i}=e,a=ai(),o=Na();return u.useEffect(()=>{o||(a(Bi(t)),a(Ki({width:r,height:n})),a(zi(i)))},[a,o,t,r,n,i]),null}function RP(e){var t=ai();return u.useEffect(()=>{t(xP(e))},[t,e]),null}var LP=["children"];function zP(){return zP=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},zP.apply(null,arguments)}var BP={width:"100%",height:"100%"},KP=u.forwardRef((e,t)=>{var r=Ba(),n=Ka(),i=jo();if(!Po(r)||!Po(n))return null;var a,o,{children:l,otherAttributes:c,title:s,desc:f}=e;return a="number"==typeof c.tabIndex?c.tabIndex:i?0:void 0,o="string"==typeof c.role?c.role:i?"application":void 0,u.createElement(qe,zP({},c,{title:s,desc:f,role:o,tabIndex:a,width:r,height:n,style:BP,ref:t}),l)}),$P=e=>{var{children:t}=e,r=si(_a);if(!r)return null;var{width:n,height:i,y:a,x:o}=r;return u.createElement(qe,{width:n,height:i,x:o,y:a},t)},UP=u.forwardRef((e,t)=>{var{children:r}=e,n=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,LP);return Na()?u.createElement($P,null,r):u.createElement(KP,zP({ref:t},n),r)});function FP(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function WP(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?FP(Object(r),!0).forEach(function(t){HP(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):FP(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function HP(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var VP=u.forwardRef((e,t)=>{var{children:r,className:n,height:i,onClick:a,onContextMenu:o,onDoubleClick:l,onMouseDown:c,onMouseEnter:s,onMouseLeave:f,onMouseMove:d,onMouseUp:h,onTouchEnd:p,onTouchMove:y,onTouchStart:v,style:g,width:m}=e,b=ai(),[x,w]=u.useState(null),[O,j]=u.useState(null);um();var A=function(){var e=ai(),[t,r]=u.useState(null),n=si(xa);return u.useEffect(()=>{if(null!=t){var r=t.getBoundingClientRect().width/t.offsetWidth;Ao(r)&&r!==n&&e($i(r))}},[t,e,n]),r}(),P=u.useCallback(e=>{A(e),"function"==typeof t&&t(e),w(e),j(e)},[A,t,w,j]),S=u.useCallback(e=>{b(dP(e)),b(EP({handler:a,reactEvent:e}))},[b,a]),E=u.useCallback(e=>{b(pP(e)),b(EP({handler:s,reactEvent:e}))},[b,s]),k=u.useCallback(e=>{b(hv()),b(EP({handler:f,reactEvent:e}))},[b,f]),M=u.useCallback(e=>{b(pP(e)),b(EP({handler:d,reactEvent:e}))},[b,d]),T=u.useCallback(()=>{b(PP())},[b]),C=u.useCallback(e=>{b(AP(e.key))},[b]),D=u.useCallback(e=>{b(EP({handler:o,reactEvent:e}))},[b,o]),N=u.useCallback(e=>{b(EP({handler:l,reactEvent:e}))},[b,l]),I=u.useCallback(e=>{b(EP({handler:c,reactEvent:e}))},[b,c]),_=u.useCallback(e=>{b(EP({handler:h,reactEvent:e}))},[b,h]),R=u.useCallback(e=>{b(EP({handler:v,reactEvent:e}))},[b,v]),L=u.useCallback(e=>{b(CP(e)),b(EP({handler:y,reactEvent:e}))},[b,y]),z=u.useCallback(e=>{b(EP({handler:p,reactEvent:e}))},[b,p]);return u.createElement(Yg.Provider,{value:x},u.createElement(Ze.Provider,{value:O},u.createElement("div",{className:q("recharts-wrapper",n),style:WP({position:"relative",cursor:"default",width:m,height:i},g),onClick:S,onContextMenu:D,onDoubleClick:N,onFocus:T,onKeyDown:C,onMouseDown:I,onMouseEnter:E,onMouseLeave:k,onMouseMove:M,onMouseUp:_,onTouchEnd:z,onTouchMove:L,onTouchStart:R,ref:P},r)))}),qP=["children","className","width","height","style","compact","title","desc"];var YP=u.forwardRef((e,t)=>{var{children:r,className:n,width:i,height:a,style:o,compact:l,title:c,desc:s}=e,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,qP),d=We(f,!1);return l?u.createElement(UP,{otherAttributes:d,title:c,desc:s},r):u.createElement(VP,{className:n,style:o,width:i,height:a,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},u.createElement(UP,{otherAttributes:d,title:c,desc:s,ref:t},u.createElement(ZO,null,r)))}),GP=["width","height"];function XP(){return XP=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},XP.apply(null,arguments)}var ZP={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},JP=u.forwardRef(function(e,t){var r,n=Wo(e.categoricalChartProps,ZP),{width:i,height:a}=n,o=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(n,GP);if(!Po(i)||!Po(a))return null;var{chartName:l,defaultTooltipEventType:c,validateTooltipEventTypes:s,tooltipPayloadSearcher:f,categoricalChartProps:d}=e,h={chartName:l,defaultTooltipEventType:c,validateTooltipEventTypes:s,tooltipPayloadSearcher:f,eventEmitter:void 0};return u.createElement(IP,{preloadedState:{options:h},reduxStoreName:null!==(r=d.id)&&void 0!==r?r:l},u.createElement(RO,{chartData:d.data}),u.createElement(_P,{width:i,height:a,layout:n.layout,margin:n.margin}),u.createElement(RP,{accessibilityLayer:n.accessibilityLayer,barCategoryGap:n.barCategoryGap,maxBarSize:n.maxBarSize,stackOffset:n.stackOffset,barGap:n.barGap,barSize:n.barSize,syncId:n.syncId,syncMethod:n.syncMethod,className:n.className}),u.createElement(YP,XP({},o,{width:i,height:a,ref:t})))}),QP=["axis"],eS=u.forwardRef((e,t)=>u.createElement(JP,{chartName:"LineChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:QP,tooltipPayloadSearcher:Qg,categoricalChartProps:e,ref:t})),tS=["axis","item"],rS=u.forwardRef((e,t)=>u.createElement(JP,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:tS,tooltipPayloadSearcher:Qg,categoricalChartProps:e,ref:t}));function nS(e){var t=ai();return u.useEffect(()=>{t(OP(e))},[t,e]),null}var iS=["width","height","layout"];function aS(){return aS=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},aS.apply(null,arguments)}var oS={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},lS=u.forwardRef(function(e,t){var r,n=Wo(e.categoricalChartProps,oS),{width:i,height:a,layout:o}=n,l=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(n,iS);if(!Po(i)||!Po(a))return null;var{chartName:c,defaultTooltipEventType:s,validateTooltipEventTypes:f,tooltipPayloadSearcher:d}=e,h={chartName:c,defaultTooltipEventType:s,validateTooltipEventTypes:f,tooltipPayloadSearcher:d,eventEmitter:void 0};return u.createElement(IP,{preloadedState:{options:h},reduxStoreName:null!==(r=n.id)&&void 0!==r?r:c},u.createElement(RO,{chartData:n.data}),u.createElement(_P,{width:i,height:a,layout:o,margin:n.margin}),u.createElement(RP,{accessibilityLayer:n.accessibilityLayer,barCategoryGap:n.barCategoryGap,maxBarSize:n.maxBarSize,stackOffset:n.stackOffset,barGap:n.barGap,barSize:n.barSize,syncId:n.syncId,syncMethod:n.syncMethod,className:n.className}),u.createElement(nS,{cx:n.cx,cy:n.cy,startAngle:n.startAngle,endAngle:n.endAngle,innerRadius:n.innerRadius,outerRadius:n.outerRadius}),u.createElement(YP,aS({width:i,height:a},l,{ref:t})))}),cS=["item"],sS={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},uS=u.forwardRef((e,t)=>{var r=Wo(e,sS);return u.createElement(lS,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:cS,tooltipPayloadSearcher:Qg,categoricalChartProps:r,ref:t})}),fS=["axis"],dS=u.forwardRef((e,t)=>u.createElement(JP,{chartName:"AreaChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:fS,tooltipPayloadSearcher:Qg,categoricalChartProps:e,ref:t}));const hS=({data:e})=>{var r,n;const i="#1890ff",a="#52c41a",o="#faad14",l="#ff4d4f",c="#722ed1",s="#13c2c2",u="#eb2f96",d=[i,a,o,l,c,s],h=[{name:"男生",value:(null==(r=null==e?void 0:e.genderDistribution)?void 0:r.male)||0,color:i},{name:"女生",value:(null==(n=null==e?void 0:e.genderDistribution)?void 0:n.female)||0,color:u}],p=((null==e?void 0:e.majorDistribution)||[]).slice(0,6).map((e,t)=>({name:(null==e?void 0:e.major)||"未知专业",value:(null==e?void 0:e.count)||0,color:d[t%d.length]})),y=Object.entries((null==e?void 0:e.gradeDistribution)||{}).map(([e,t])=>({name:e,count:t||0})),v=[{name:"奖励记录",count:(null==e?void 0:e.rewardCount)||0,color:a},{name:"惩罚记录",count:(null==e?void 0:e.punishmentCount)||0,color:l},{name:"贫困补助",count:(null==e?void 0:e.financialAidCount)||0,color:o},{name:"党建工作",count:(null==e?void 0:e.partyMemberCount)||0,color:l},{name:"心理健康",count:(null==e?void 0:e.mentalHealthRecords)||0,color:s},{name:"证明申请",count:(null==e?void 0:e.certificateApplications)||0,color:c}],g=[{name:"教师总数",count:(null==e?void 0:e.totalTeachers)||0},{name:"任课数量",count:(null==e?void 0:e.totalTeachingCourses)||0},{name:"教学材料",count:(null==e?void 0:e.totalTeachingMaterials)||0},{name:"教学成果",count:(null==e?void 0:e.totalTeachingAchievements)||0}],m=(null==e?void 0:e.gradesTrend)||[],k=(null==e?void 0:e.dormitoryTrend)||[];return t.jsxs("div",{className:"space-y-6",children:[t.jsxs(b,{gutter:[16,16],children:[t.jsx(x,{xs:24,sm:12,md:6,children:t.jsx(f,{className:"text-center hover:shadow-lg transition-shadow",children:t.jsx(w,{title:"学生总数",value:(null==e?void 0:e.totalStudents)||0,prefix:t.jsx(O,{style:{color:i}}),valueStyle:{color:i}})})}),t.jsx(x,{xs:24,sm:12,md:6,children:t.jsx(f,{className:"text-center hover:shadow-lg transition-shadow",children:t.jsx(w,{title:"班级总数",value:(null==e?void 0:e.totalClasses)||0,prefix:t.jsx(j,{style:{color:a}}),valueStyle:{color:a}})})}),t.jsx(x,{xs:24,sm:12,md:6,children:t.jsx(f,{className:"text-center hover:shadow-lg transition-shadow",children:t.jsx(w,{title:"宿舍总数",value:(null==e?void 0:e.totalDormitories)||0,prefix:t.jsx(A,{style:{color:o}}),valueStyle:{color:o}})})}),t.jsx(x,{xs:24,sm:12,md:6,children:t.jsx(f,{className:"text-center hover:shadow-lg transition-shadow",children:t.jsx(w,{title:"课程总数",value:(null==e?void 0:e.totalCourses)||0,prefix:t.jsx(P,{style:{color:c}}),valueStyle:{color:c}})})})]}),t.jsxs(b,{gutter:[16,16],children:[t.jsx(x,{xs:24,sm:12,md:8,children:t.jsxs(f,{title:"平均绩点",className:"text-center",children:[t.jsxs("div",{className:"mb-4",children:[t.jsx("div",{className:"text-3xl font-bold",style:{color:i},children:((null==e?void 0:e.averageGPA)||0).toFixed(2)}),t.jsx("div",{className:"text-gray-500",children:"满分 4.0"})]}),t.jsx(S,{type:"circle",percent:((null==e?void 0:e.averageGPA)||0)/4*100,format:()=>`${(((null==e?void 0:e.averageGPA)||0)/4*100).toFixed(1)}%`,strokeColor:i})]})}),t.jsx(x,{xs:24,sm:12,md:8,children:t.jsxs(f,{title:"通过率",className:"text-center",children:[t.jsxs("div",{className:"mb-4",children:[t.jsxs("div",{className:"text-3xl font-bold",style:{color:a},children:[((null==e?void 0:e.passRate)||0).toFixed(1),"%"]}),t.jsx("div",{className:"text-gray-500",children:"及格率统计"})]}),t.jsx(S,{type:"circle",percent:(null==e?void 0:e.passRate)||0,strokeColor:a})]})}),t.jsx(x,{xs:24,sm:12,md:8,children:t.jsxs(f,{title:"宿舍入住率",className:"text-center",children:[t.jsxs("div",{className:"mb-4",children:[t.jsxs("div",{className:"text-3xl font-bold",style:{color:o},children:[((null==e?void 0:e.dormitoryOccupancy)||0).toFixed(1),"%"]}),t.jsx("div",{className:"text-gray-500",children:"宿舍使用情况"})]}),t.jsx(S,{type:"circle",percent:(null==e?void 0:e.dormitoryOccupancy)||0,strokeColor:o})]})})]}),t.jsxs(b,{gutter:[16,16],children:[t.jsx(x,{xs:24,md:12,children:t.jsx(f,{title:"性别分布",style:{height:"400px"},children:t.jsx(Dm,{width:"100%",height:300,children:t.jsxs(uS,{children:[t.jsx(gw,{data:h,cx:"50%",cy:"50%",labelLine:!1,label:({name:e,percent:t})=>`${e} ${(100*t).toFixed(0)}%`,outerRadius:80,fill:"#8884d8",dataKey:"value",children:h.map((e,r)=>t.jsx(Nm,{fill:e.color},`cell-${r}`))}),t.jsx(mm,{}),t.jsx(oo,{})]})})})}),t.jsx(x,{xs:24,md:12,children:t.jsx(f,{title:"专业分布（前6名）",style:{height:"400px"},children:t.jsx(Dm,{width:"100%",height:300,children:t.jsxs(uS,{children:[t.jsx(gw,{data:p,cx:"50%",cy:"50%",labelLine:!1,label:({name:e,percent:t})=>`${e} ${(100*t).toFixed(0)}%`,outerRadius:80,fill:"#8884d8",dataKey:"value",children:p.map((e,r)=>t.jsx(Nm,{fill:e.color},`cell-${r}`))}),t.jsx(mm,{}),t.jsx(oo,{})]})})})})]}),t.jsx(f,{title:"年级分布",style:{height:"350px"},children:t.jsx(Dm,{width:"100%",height:280,children:t.jsxs(rS,{data:y,children:[t.jsx(Dj,{strokeDasharray:"3 3"}),t.jsx(tP,{dataKey:"name"}),t.jsx(sP,{}),t.jsx(mm,{}),t.jsx(oo,{}),t.jsx(mO,{dataKey:"count",fill:i})]})})}),t.jsx(f,{title:"学工管理统计",style:{height:"350px"},children:t.jsx(Dm,{width:"100%",height:280,children:t.jsxs(rS,{data:v,layout:"horizontal",children:[t.jsx(Dj,{strokeDasharray:"3 3"}),t.jsx(tP,{type:"number"}),t.jsx(sP,{dataKey:"name",type:"category",width:80}),t.jsx(mm,{}),t.jsx(oo,{}),t.jsx(mO,{dataKey:"count",fill:a})]})})}),t.jsx(f,{title:"教师业务统计",style:{height:"350px"},children:t.jsx(Dm,{width:"100%",height:280,children:t.jsxs(rS,{data:g,children:[t.jsx(Dj,{strokeDasharray:"3 3"}),t.jsx(tP,{dataKey:"name"}),t.jsx(sP,{}),t.jsx(mm,{}),t.jsx(oo,{}),t.jsx(mO,{dataKey:"count",fill:c})]})})}),t.jsxs(b,{gutter:[16,16],children:[t.jsx(x,{xs:24,lg:12,children:t.jsx(f,{title:"成绩趋势",style:{height:"350px"},children:t.jsx(Dm,{width:"100%",height:280,children:t.jsxs(eS,{data:m,children:[t.jsx(Dj,{strokeDasharray:"3 3"}),t.jsx(tP,{dataKey:"period"}),t.jsx(sP,{yAxisId:"left"}),t.jsx(sP,{yAxisId:"right",orientation:"right"}),t.jsx(mm,{}),t.jsx(oo,{}),t.jsx(aA,{yAxisId:"left",type:"monotone",dataKey:"averageGPA",stroke:i,strokeWidth:2,name:"平均绩点"}),t.jsx(aA,{yAxisId:"right",type:"monotone",dataKey:"passRate",stroke:a,strokeWidth:2,name:"通过率(%)"})]})})})}),t.jsx(x,{xs:24,lg:12,children:t.jsx(f,{title:"宿舍入住趋势",style:{height:"350px"},children:t.jsx(Dm,{width:"100%",height:280,children:t.jsxs(dS,{data:k,children:[t.jsx(Dj,{strokeDasharray:"3 3"}),t.jsx(tP,{dataKey:"period"}),t.jsx(sP,{}),t.jsx(mm,{}),t.jsx(oo,{}),t.jsx(IA,{type:"monotone",dataKey:"occupancyRate",stroke:o,fill:o,fillOpacity:.6,name:"入住率(%)"})]})})})})]}),t.jsxs(b,{gutter:[16,16],children:[t.jsx(x,{xs:24,sm:8,children:t.jsx(H,{warningCount:(null==e?void 0:e.academicWarnings)||0,warningDetails:(null==e?void 0:e.academicWarningDetails)||{normal:0,warning:0,serious:0}})}),t.jsx(x,{xs:24,sm:8,children:t.jsx(f,{className:"text-center",children:t.jsx(w,{title:"待处理申请",value:(null==e?void 0:e.pendingApplications)||0,prefix:t.jsx(E,{style:{color:s}}),valueStyle:{color:s},suffix:"件"})})})]})]})},{Option:pS}=D,{RangePicker:yS}=N,vS=()=>{const{message:e}=k.useApp(),[r,n]=u.useState(!0),[i,a]=u.useState("semester"),[o,l]=u.useState(null),c=async()=>{n(!0);try{console.log("🔄 开始从数据库加载统计数据...");const e=await W.getAnalyticsData();console.log("📊 数据库统计数据获取成功:",{"基础统计":{"学生总数":e.totalStudents,"班级总数":e.totalClasses,"宿舍总数":e.totalDormitories,"课程总数":e.totalCourses},"性别分布":e.genderDistribution,"专业分布":(e.majorDistribution||[]).slice(0,3),"学工统计":{"奖惩记录":(e.rewardCount||0)+(e.punishmentCount||0),"贫困补助":e.financialAidCount||0,"党建工作":e.partyMemberCount||0,"心理健康":e.mentalHealthRecords||0,"证明申请":e.certificateApplications||0},"教师统计":{"教师总数":e.totalTeachers||0,"任课总数":e.totalTeachingCourses||0,"教学材料":e.totalTeachingMaterials||0,"教学成果":e.totalTeachingAchievements||0}}),l(e)}catch(t){console.error("❌ 加载统计数据失败:",t),e.error("加载统计数据失败，请稍后重试"),console.log("API调用失败，设置默认空数据"),l({totalStudents:0,totalClasses:0,totalCourses:0,totalDormitories:0,genderDistribution:{male:0,female:0},gradeDistribution:{},majorDistribution:[],collegeDistribution:[],dormitoryOccupancy:0,averageGPA:0,passRate:0,rewardCount:0,punishmentCount:0,financialAidCount:0,partyMemberCount:0,mentalHealthRecords:0,certificateApplications:0,academicWarnings:0,pendingApplications:0,gradesTrend:[],dormitoryTrend:[],totalTeachers:0,totalTeachingCourses:0,totalTeachingMaterials:0,totalTeachingAchievements:0,totalTrainingRecords:0,totalMentorshipProjects:0,totalAcademicRecords:0})}finally{n(!1)}};return u.useEffect(()=>{console.log("🚀 Analytics组件初始化，开始加载数据..."),c()},[]),u.useEffect(()=>{o&&c()},[i]),o?t.jsxs("div",{className:"p-6 bg-gray-50 min-h-screen",children:[t.jsxs("div",{className:"mb-6",children:[t.jsxs("div",{className:"flex items-center justify-between mb-4",children:[t.jsxs("div",{children:[t.jsx("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:"📊 数据统计仪表板"}),t.jsx("p",{className:"text-gray-600",children:"学生工作数据分析和统计报表（实时数据）"})]}),t.jsxs(M,{children:[t.jsx(T,{type:"primary",icon:t.jsx(C,{}),onClick:()=>{c(),e.success("数据已刷新")},loading:r,children:"刷新数据"}),t.jsxs(D,{value:i,onChange:a,style:{width:120},children:[t.jsx(pS,{value:"semester",children:"本学期"}),t.jsx(pS,{value:"year",children:"本学年"}),t.jsx(pS,{value:"all",children:"全部"})]}),t.jsx(yS,{})]})]}),t.jsxs("div",{className:"flex items-center space-x-4 mb-6",children:[t.jsxs("span",{className:"text-sm text-green-600 bg-green-50 px-3 py-1 rounded-full",children:["✅ 数据已同步 - ",(new Date).toLocaleString()]}),t.jsx("span",{className:"text-sm text-blue-600 bg-blue-50 px-3 py-1 rounded-full",children:"🔗 数据源: MySQL数据库API"}),o&&t.jsxs("span",{className:"text-sm text-purple-600 bg-purple-50 px-3 py-1 rounded-full",children:["📊 总计: ",o.totalStudents+o.totalClasses+o.totalDormitories," 条记录"]})]})]}),t.jsx(hS,{data:o})]}):t.jsx("div",{className:"flex items-center justify-center h-64",children:t.jsx("div",{className:"text-center",children:t.jsx("div",{className:"text-gray-500",children:"正在加载统计数据..."})})})};export{vS as default};
