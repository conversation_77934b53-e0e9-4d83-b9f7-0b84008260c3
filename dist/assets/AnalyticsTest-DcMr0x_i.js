import{j as e}from"./index-DP6eZxW9.js";import{r as s,S as t,a as l,X as a,G as r,H as i,F as o,J as c,g as n,i as x,j as d,l as j,s as h}from"./antd-DYv0PFJq.js";import"./vendor-D2RBMdQ0.js";const m=()=>{const[m,u]=s.useState(!1),[g,p]=s.useState(null),f=()=>{u(!0),setTimeout(()=>{try{const e=JSON.parse(localStorage.getItem("studentsList")||"[]"),s=JSON.parse(localStorage.getItem("classesList")||"[]"),t=JSON.parse(localStorage.getItem("coursesList")||"[]"),l=JSON.parse(localStorage.getItem("dormitoriesList")||"[]");console.log("数据加载:",{students:e.length,classes:s.length,courses:t.length,dormitories:l.length}),p({totalStudents:e.length,totalClasses:s.length,totalCourses:t.length,totalDormitories:l.length}),h.success("数据加载成功")}catch(e){console.error("加载失败:",e),h.error("数据加载失败")}finally{u(!1)}},500)};return s.useEffect(()=>{f()},[]),m?e.jsxs("div",{className:"flex items-center justify-center h-64",children:[e.jsx(t,{size:"large"}),e.jsx("span",{className:"ml-3",children:"正在加载数据..."})]}):e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"数据统计测试"}),e.jsx("p",{className:"text-gray-600",children:"测试数据加载功能"})]}),e.jsx(l,{icon:e.jsx(a,{}),onClick:f,loading:m,children:"刷新数据"})]}),g&&e.jsxs(r,{gutter:16,children:[e.jsx(i,{span:6,children:e.jsx(o,{children:e.jsx(c,{title:"在校学生",value:g.totalStudents,prefix:e.jsx(n,{}),valueStyle:{color:"#3f8600"}})})}),e.jsx(i,{span:6,children:e.jsx(o,{children:e.jsx(c,{title:"班级总数",value:g.totalClasses,prefix:e.jsx(x,{}),valueStyle:{color:"#1890ff"}})})}),e.jsx(i,{span:6,children:e.jsx(o,{children:e.jsx(c,{title:"开设课程",value:g.totalCourses,prefix:e.jsx(d,{}),valueStyle:{color:"#722ed1"}})})}),e.jsx(i,{span:6,children:e.jsx(o,{children:e.jsx(c,{title:"宿舍总数",value:g.totalDormitories,prefix:e.jsx(j,{}),valueStyle:{color:"#fa541c"}})})})]}),!g&&!m&&e.jsxs("div",{className:"text-center py-8",children:[e.jsx("p",{className:"text-gray-500",children:"暂无数据"}),e.jsx(l,{type:"primary",onClick:f,children:"重新加载"})]})]})};export{m as default};
