import{j as e,r as t}from"./index-DP6eZxW9.js";import{r as s,E as r,F as a,ae as i,a as n,X as d,U as h,ay as o,Q as c,L as u,bo as l}from"./antd-DYv0PFJq.js";import{u as p}from"./useMessage-DDIprusu.js";import"./vendor-D2RBMdQ0.js";const{Title:m,Text:x}=r,j=()=>{const r=p(),[j,g]=s.useState(!1),[T,A]=s.useState([{name:"健康检查",method:"GET",path:"/health",description:"检查服务器状态",requiresAuth:!1},{name:"用户登录",method:"POST",path:"/auth/login",description:"用户登录认证",requiresAuth:!1},{name:"获取用户信息",method:"GET",path:"/auth/me",description:"获取当前用户信息",requiresAuth:!0},{name:"获取权限列表",method:"GET",path:"/auth/permissions",description:"获取用户权限",requiresAuth:!0},{name:"学生列表",method:"GET",path:"/students",description:"获取学生列表",requiresAuth:!0},{name:"学生统计",method:"GET",path:"/students/statistics",description:"获取学生统计数据",requiresAuth:!0},{name:"学生导出",method:"GET",path:"/students/export",description:"导出学生数据",requiresAuth:!0},{name:"班级列表",method:"GET",path:"/classes",description:"获取班级列表",requiresAuth:!0},{name:"宿舍列表",method:"GET",path:"/dormitories",description:"获取宿舍列表",requiresAuth:!0},{name:"奖惩列表",method:"GET",path:"/rewards",description:"获取奖惩记录",requiresAuth:!0},{name:"贫困补助列表",method:"GET",path:"/financial-aid",description:"获取贫困补助申请",requiresAuth:!0},{name:"党员列表",method:"GET",path:"/party/members",description:"获取党员列表",requiresAuth:!0},{name:"心理健康记录",method:"GET",path:"/mental-health",description:"获取心理健康记录",requiresAuth:!0},{name:"教师列表",method:"GET",path:"/teachers",description:"获取教师列表",requiresAuth:!0},{name:"课程列表",method:"GET",path:"/academics/courses",description:"获取课程列表",requiresAuth:!0},{name:"成绩列表",method:"GET",path:"/academics/grades",description:"获取成绩列表",requiresAuth:!0},{name:"学业情况",method:"GET",path:"/academic-status",description:"获取学业情况",requiresAuth:!0},{name:"证明模板",method:"GET",path:"/certificates/templates",description:"获取证明模板",requiresAuth:!0},{name:"证明申请",method:"GET",path:"/certificates",description:"获取证明申请",requiresAuth:!0},{name:"工作台数据",method:"GET",path:"/analytics/dashboard",description:"获取工作台统计数据",requiresAuth:!0}]),y=[{title:"接口名称",dataIndex:"name",key:"name",width:120},{title:"方法",dataIndex:"method",key:"method",width:80,render:t=>e.jsx(c,{color:"GET"===t?"blue":"POST"===t?"green":"orange",children:t})},{title:"路径",dataIndex:"path",key:"path",width:200,render:t=>e.jsx(x,{code:!0,children:t})},{title:"描述",dataIndex:"description",key:"description"},{title:"需要认证",dataIndex:"requiresAuth",key:"requiresAuth",width:100,render:t=>e.jsx(c,{color:t?"red":"green",children:t?"是":"否"})},{title:"状态",dataIndex:"status",key:"status",width:100,render:t=>"success"===t?e.jsx(c,{color:"green",icon:e.jsx(u,{}),children:"成功"}):"error"===t?e.jsx(c,{color:"red",icon:e.jsx(l,{}),children:"失败"}):"pending"===t?e.jsx(c,{color:"blue",children:"测试中..."}):e.jsx(c,{color:"default",children:"未测试"})},{title:"错误信息",dataIndex:"error",key:"error",width:200,render:t=>t?e.jsx(x,{type:"danger",children:t}):"-"}],q=T.filter(e=>"success"===e.status).length,E=T.filter(e=>"error"===e.status).length,G=T.length;return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx(m,{level:2,children:"API状态检查"}),e.jsx(x,{type:"secondary",children:"检查所有后端API接口的可用性"})]}),e.jsx(a,{children:e.jsxs(i,{size:"large",children:[e.jsx(n,{type:"primary",icon:e.jsx(d,{}),onClick:async()=>{g(!0);if(!localStorage.getItem("token"))return r.error("请先登录获取Token"),void g(!1);const e=[...T];for(let r=0;r<e.length;r++){const a=e[r];a.status="pending",A([...e]);try{let e;"/health"===a.path?(e=await fetch("http://localhost:3001/health"),e=await e.json()):e="/auth/login"===a.path?await t.post("/auth/login",{username:"admin",password:"admin123"}):await t.get(a.path),a.status="success",a.response=e,a.error=void 0}catch(s){a.status="error",a.error=s.message||"请求失败",a.response=void 0}A([...e]),await new Promise(e=>setTimeout(e,200))}g(!1),r.success("API状态检查完成")},loading:j,size:"large",children:"测试所有接口"}),e.jsxs("div",{children:[e.jsxs(x,{strong:!0,children:["总计: ",G]}),e.jsx(h,{type:"vertical"}),e.jsxs(x,{type:"success",children:["成功: ",q]}),e.jsx(h,{type:"vertical"}),e.jsxs(x,{type:"danger",children:["失败: ",E]})]})]})}),e.jsx(a,{title:"接口列表",children:e.jsx(o,{columns:y,dataSource:T,rowKey:"path",pagination:!1,size:"small",scroll:{x:1e3}})})]})};export{j as default};
