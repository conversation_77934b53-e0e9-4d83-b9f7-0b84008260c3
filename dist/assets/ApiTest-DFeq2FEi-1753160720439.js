import{j as s,r as e}from"./index-DXaqwR6F-1753160720439.js";import{r,E as l,ae as n,F as a,a as i,aq as c,I as t,U as o}from"./antd-lXsGnH6e-1753160720439.js";import{u as d}from"./useMessage-CR2RbGdP-1753160720439.js";import"./vendor-D2RBMdQ0-1753160720439.js";const{Title:y,Text:h,Paragraph:g}=l,{TextArea:u}=t,j=()=>{const l=d(),[j,x]=r.useState(!1),[m,f]=r.useState(""),[p,w]=r.useState("");return s.jsxs("div",{style:{padding:"24px"},children:[s.jsx(y,{level:2,children:"API 连接测试"}),s.jsx(g,{children:"测试前后端API连接是否正常。后端服务器应该运行在 http://localhost:3001"}),s.jsxs(n,{direction:"vertical",size:"large",style:{width:"100%"},children:[s.jsx(a,{title:"健康检查",size:"small",children:s.jsxs(n,{children:[s.jsx(i,{type:"primary",onClick:async()=>{x(!0);try{const s=await fetch("/api/health"),e=await s.json();w(JSON.stringify(e,null,2)),l.success("健康检查成功")}catch(s){console.error("健康检查失败:",s),w(`错误: ${s}`),l.error("健康检查失败")}finally{x(!1)}},loading:j,children:"测试健康检查"}),s.jsx(h,{type:"secondary",children:"测试后端服务器是否正常运行"})]})}),s.jsxs(a,{title:"登录测试",size:"small",children:[s.jsxs(c,{onFinish:async s=>{var r;x(!0);try{const n=await e.post("/auth/login",s);w(JSON.stringify(n,null,2)),n.success&&(null==(r=n.data)?void 0:r.token)&&(f(n.data.token),localStorage.setItem("token",n.data.token),l.success("登录成功"))}catch(n){console.error("登录失败:",n),w(`错误: ${n}`),l.error("登录失败")}finally{x(!1)}},layout:"inline",children:[s.jsx(c.Item,{name:"username",rules:[{required:!0,message:"请输入用户名"}],initialValue:"admin",children:s.jsx(t,{placeholder:"用户名"})}),s.jsx(c.Item,{name:"password",rules:[{required:!0,message:"请输入密码"}],initialValue:"admin123",children:s.jsx(t.Password,{placeholder:"密码"})}),s.jsx(c.Item,{children:s.jsx(i,{type:"primary",htmlType:"submit",loading:j,children:"测试登录"})})]}),m&&s.jsx("div",{style:{marginTop:8},children:s.jsxs(h,{type:"success",children:["已获取Token: ",m.substring(0,50),"..."]})})]}),s.jsxs(a,{title:"API测试",size:"small",children:[s.jsxs(n,{wrap:!0,children:[s.jsx(i,{onClick:async()=>{x(!0);try{const s=await e.get("/students");w(JSON.stringify(s,null,2)),l.success("获取学生列表成功")}catch(s){console.error("获取学生列表失败:",s),w(`错误: ${s}`),l.error("获取学生列表失败")}finally{x(!1)}},loading:j,children:"获取学生列表"}),s.jsx(i,{onClick:async()=>{x(!0);try{const s=await e.get("/analytics/dashboard");w(JSON.stringify(s,null,2)),l.success("获取工作台数据成功")}catch(s){console.error("获取工作台数据失败:",s),w(`错误: ${s}`),l.error("获取工作台数据失败")}finally{x(!1)}},loading:j,children:"获取工作台数据"}),s.jsx(i,{onClick:async()=>{x(!0);try{const s=await e.get("/classes");w(JSON.stringify(s,null,2)),l.success("获取班级列表成功")}catch(s){console.error("获取班级列表失败:",s),w(`错误: ${s}`),l.error("获取班级列表失败")}finally{x(!1)}},loading:j,children:"测试班级列表"}),s.jsx(i,{onClick:async()=>{x(!0);try{const s=await e.get("/dormitories");w(JSON.stringify(s,null,2)),l.success("获取宿舍列表成功")}catch(s){console.error("获取宿舍列表失败:",s),w(`错误: ${s}`),l.error("获取宿舍列表失败")}finally{x(!1)}},loading:j,children:"测试宿舍列表"}),s.jsx(i,{onClick:async()=>{x(!0);try{const s=await e.get("/rewards");w(JSON.stringify(s,null,2)),l.success("获取奖惩列表成功")}catch(s){console.error("获取奖惩列表失败:",s),w(`错误: ${s}`),l.error("获取奖惩列表失败")}finally{x(!1)}},loading:j,children:"测试奖惩列表"}),s.jsx(i,{onClick:async()=>{x(!0);try{const s=await e.get("/financial-aid");w(JSON.stringify(s,null,2)),l.success("获取贫困补助列表成功")}catch(s){console.error("获取贫困补助列表失败:",s),w(`错误: ${s}`),l.error("获取贫困补助列表失败")}finally{x(!1)}},loading:j,children:"测试贫困补助"}),s.jsx(i,{onClick:async()=>{x(!0);try{const s=await e.get("/party/members");w(JSON.stringify(s,null,2)),l.success("获取党员列表成功")}catch(s){console.error("获取党员列表失败:",s),w(`错误: ${s}`),l.error("获取党员列表失败")}finally{x(!1)}},loading:j,children:"测试党建工作"}),s.jsx(i,{onClick:async()=>{x(!0);try{const s=await e.get("/mental-health");w(JSON.stringify(s,null,2)),l.success("获取心理健康记录成功")}catch(s){console.error("获取心理健康记录失败:",s),w(`错误: ${s}`),l.error("获取心理健康记录失败")}finally{x(!1)}},loading:j,children:"测试心理健康"}),s.jsx(i,{onClick:async()=>{x(!0);try{const s=await e.get("/teachers");w(JSON.stringify(s,null,2)),l.success("获取教师列表成功")}catch(s){console.error("获取教师列表失败:",s),w(`错误: ${s}`),l.error("获取教师列表失败")}finally{x(!1)}},loading:j,children:"测试教师列表"}),s.jsx(i,{onClick:async()=>{x(!0);try{const s=await e.get("/academics/courses");w(JSON.stringify(s,null,2)),l.success("获取课程列表成功")}catch(s){console.error("获取课程列表失败:",s),w(`错误: ${s}`),l.error("获取课程列表失败")}finally{x(!1)}},loading:j,children:"测试课程列表"}),s.jsx(i,{onClick:async()=>{x(!0);try{const s=await e.get("/academics/grades");w(JSON.stringify(s,null,2)),l.success("获取成绩列表成功")}catch(s){console.error("获取成绩列表失败:",s),w(`错误: ${s}`),l.error("获取成绩列表失败")}finally{x(!1)}},loading:j,children:"测试成绩列表"})]}),s.jsx("div",{style:{marginTop:8},children:s.jsx(h,{type:"secondary",children:m?`已有Token: ${m.substring(0,50)}...`:"未登录，将使用localStorage中的token"})})]}),s.jsx(o,{}),s.jsx(a,{title:"响应结果",size:"small",children:s.jsx(u,{value:p,rows:15,placeholder:"API响应结果将显示在这里...",style:{fontFamily:"monospace"}})})]})]})};export{j as default};
