const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/AdvancedDocumentParser-D0HhWqXv-1753160720439.js","assets/vendor-D2RBMdQ0-1753160720439.js","assets/jszip.min-DISjQb3B-1753160720439.js"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,t=(t,n,a)=>((t,n,a)=>n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[n]=a)(t,"symbol"!=typeof n?n+"":n,a);import{_ as n,j as a,u as s,r}from"./index-DXaqwR6F-1753160720439.js";import{r as i,a as l,ae as o,I as c,F as d,G as m,H as u,aV as p,aW as h,U as g,as as x,aX as y,aM as f,aY as j,aZ as v,T as b,M as N,s as w,w as S,a_ as C,D as T,a$ as D,b0 as I,b1 as k,b2 as _,b3 as F,b4 as E,b5 as z,b6 as A,b7 as $,b8 as q,b9 as O,ba as V,bb as L,bc as M,d as R,af as P,aI as H,P as B,Q as W,ao as Y,u as U,E as G,aB as J,W as X,aq as K,a0 as Z,at as Q,q as ee,ax as te,S as ne,aN as ae,Z as se,aw as re,bd as ie,X as le,J as oe,ad as ce,L as de,au as me,aL as ue,ay as pe,ac as he,aH as ge,N as xe,av as ye}from"./antd-lXsGnH6e-1753160720439.js";import{T as fe,S as je,E as ve}from"./StandardTemplateService-COipISva-1753160720439.js";import{A as be}from"./AdvancedDocumentParser-D0HhWqXv-1753160720439.js";import{u as Ne}from"./useMessage-CR2RbGdP-1753160720439.js";import"./vendor-D2RBMdQ0-1753160720439.js";import"./jszip.min-DISjQb3B-1753160720439.js";const we=class e{constructor(){t(this,"variableMapping",{"学生姓名":{id:"studentName",originalText:"[学生姓名]",variableName:"studentName",chineseName:"学生姓名",category:"basic",required:!0},"学号":{id:"studentId",originalText:"[学号]",variableName:"studentId",chineseName:"学号",category:"basic",required:!0},"班级":{id:"class",originalText:"[班级]",variableName:"class",chineseName:"班级",category:"basic",required:!0},"专业":{id:"major",originalText:"[专业]",variableName:"major",chineseName:"专业",category:"basic",required:!0},"学院":{id:"college",originalText:"[学院]",variableName:"college",chineseName:"学院",category:"basic",required:!0},"性别":{id:"gender",originalText:"[性别]",variableName:"gender",chineseName:"性别",category:"basic",required:!1,defaultValue:"男"},"宿舍号":{id:"dormitory",originalText:"[宿舍号]",variableName:"dormitory",chineseName:"宿舍号",category:"contact",required:!1},"联系电话":{id:"contactPhone",originalText:"[联系电话]",variableName:"contactPhone",chineseName:"联系电话",category:"contact",required:!0},"请假类型":{id:"leaveType",originalText:"[请假类型]",variableName:"leaveType",chineseName:"请假类型",category:"academic",required:!1,defaultValue:"事假"},"请假原因":{id:"leaveReason",originalText:"[请假原因]",variableName:"leaveReason",chineseName:"请假原因",category:"academic",required:!1},"开始日期":{id:"startDate",originalText:"[开始日期]",variableName:"startDate",chineseName:"开始日期",category:"academic",required:!1},"开始时间":{id:"startTime",originalText:"[开始时间]",variableName:"startTime",chineseName:"开始时间",category:"academic",required:!1,defaultValue:"08:00"},"结束日期":{id:"endDate",originalText:"[结束日期]",variableName:"endDate",chineseName:"结束日期",category:"academic",required:!1},"结束时间":{id:"endTime",originalText:"[结束时间]",variableName:"endTime",chineseName:"结束时间",category:"academic",required:!1,defaultValue:"18:00"},"请假天数":{id:"leaveDays",originalText:"[请假天数]",variableName:"leaveDays",chineseName:"请假天数",category:"academic",required:!1,defaultValue:"1"},"紧急联系人":{id:"emergencyContact",originalText:"[紧急联系人]",variableName:"emergencyContact",chineseName:"紧急联系人",category:"contact",required:!1},"紧急联系人电话":{id:"emergencyPhone",originalText:"[紧急联系人电话]",variableName:"emergencyPhone",chineseName:"紧急联系人电话",category:"contact",required:!1},"与学生关系":{id:"relationship",originalText:"[与学生关系]",variableName:"relationship",chineseName:"与学生关系",category:"contact",required:!1,defaultValue:"父母"},"申请日期":{id:"applicationDate",originalText:"[申请日期]",variableName:"applicationDate",chineseName:"申请日期",category:"approval",required:!1},"辅导员意见":{id:"advisorOpinion",originalText:"[辅导员意见]",variableName:"advisorOpinion",chineseName:"辅导员意见",category:"approval",required:!1},"辅导员审批日期":{id:"advisorDate",originalText:"[辅导员审批日期]",variableName:"advisorDate",chineseName:"辅导员审批日期",category:"approval",required:!1},"学院意见":{id:"collegeOpinion",originalText:"[学院意见]",variableName:"collegeOpinion",chineseName:"学院意见",category:"approval",required:!1},"学院审批日期":{id:"collegeDate",originalText:"[学院审批日期]",variableName:"collegeDate",chineseName:"学院审批日期",category:"approval",required:!1},"当前日期":{id:"currentDate",originalText:"[当前日期]",variableName:"currentDate",chineseName:"当前日期",category:"other",required:!1},"学校名称":{id:"schoolName",originalText:"[学校名称]",variableName:"schoolName",chineseName:"学校名称",category:"other",required:!1,defaultValue:"银川科技学院"}})}static getInstance(){return e.instance||(e.instance=new e),e.instance}async parseDocument(e){const t=this.getFileType(e.name);try{let a="";if("docx"===t)try{a=await this.parseWordDocument(e)}catch(n){console.warn("Word文档解析失败，使用降级方案:",n),a=await this.fallbackParseDocument(e)}else{if("pdf"!==t)throw new Error("不支持的文件格式");try{a=await this.parsePdfDocument(e)}catch(n){console.warn("PDF文档解析失败，使用降级方案:",n),a=await this.fallbackParseDocument(e)}}const s=this.extractVariables(a),r=this.convertVariablesToStandardFormat(a,s);return{id:Date.now().toString(),fileName:e.name,fileType:t,originalContent:a,parsedContent:r,variables:s,metadata:{title:this.extractTitle(a),createdDate:(new Date).toISOString()},formatInfo:{hasTable:this.detectTableStructure(a),hasBorder:this.detectBorderStructure(a),fontFamily:"SimSun",fontSize:14,lineHeight:1.6}}}catch(n){throw console.error("文档解析失败:",n),new Error(`文档解析失败: ${n.message}`)}}async fallbackParseDocument(e){console.log("使用降级解析方案处理文件:",e.name);const t=e.name.toLowerCase();return t.includes("请假")?"计算机与人工智能学院学生请假申请单\n\n申请人基本信息：\n姓名：[学生姓名]\n学号：[学号]\n班级：[班级]\n专业：[专业]\n性别：[性别]\n宿舍号：[宿舍号]\n联系电话：[联系电话]\n\n请假信息：\n请假类型：[请假类型]\n请假原因：[请假原因]\n请假时间：从 [开始日期] [开始时间] 到 [结束日期] [结束时间]\n请假天数：[请假天数] 天\n\n紧急联系人：\n联系人姓名：[紧急联系人]\n联系人电话：[紧急联系人电话]\n与学生关系：[与学生关系]\n\n学生承诺：\n本人保证请假期间注意人身安全，按时返校销假。如有意外，后果自负。\n\n学生签名：________________\n申请日期：[申请日期]\n\n审批意见：\n辅导员意见：\n□ 同意    □ 不同意\n意见：[辅导员意见]\n辅导员签名：________________\n日期：[辅导员审批日期]\n\n学院意见：\n□ 同意    □ 不同意\n意见：[学院意见]\n学院负责人签名：________________\n日期：[学院审批日期]\n\n备注：\n1. 请假3天以内由辅导员审批\n2. 请假3天以上需学院审批\n3. 请假期间如有紧急情况请及时联系辅导员":t.includes("在读")||t.includes("证明")?"在读证明\n\n兹证明[学生姓名]同学，学号：[学号]，系我校[学院][专业]专业[班级]班学生。\n\n该生现为我校在读学生，学习情况良好。\n\n特此证明。\n\n此证明仅供相关用途使用。\n\n[学校名称]\n[当前日期]":"证明文件\n\n学生信息：\n姓名：[学生姓名]\n学号：[学号]\n班级：[班级]\n专业：[专业]\n学院：[学院]\n\n特此证明。\n\n[学校名称]\n[当前日期]\n\n注：此内容为系统生成的默认模板，请根据实际需要修改。"}getFileType(e){const t=e.toLowerCase().split(".").pop();if("docx"===t)return"docx";if("pdf"===t)return"pdf";throw new Error("不支持的文件格式")}async parseWordDocument(e){try{const t=await n(()=>import("./AdvancedDocumentParser-D0HhWqXv-1753160720439.js").then(e=>e.i),__vite__mapDeps([0,1,2]));return new Promise((n,a)=>{const s=new FileReader;s.onload=async e=>{var s;try{const a=null==(s=e.target)?void 0:s.result;if(!a)throw new Error("无法读取文件内容");const r={styleMap:["p[style-name='Heading 1'] => h1:fresh","p[style-name='Heading 2'] => h2:fresh","p[style-name='Title'] => h1.title:fresh","r[style-name='Strong'] => strong","r[style-name='Emphasis'] => em","p[style-name='Normal'] => p:fresh","table => table.document-table","tr => tr","td => td","th => th"],includeDefaultStyleMap:!0,includeEmbeddedStyleMap:!0},i=await t.convertToHtml({arrayBuffer:a},r);if(i.value&&i.value.trim()){console.log("Word文档HTML解析成功，内容长度:",i.value.length);const e=this.convertHtmlToFormattedText(i.value);i.messages&&i.messages.length>0&&console.warn("Word文档解析警告:",i.messages),n(e)}else{const e=await t.extractRawText({arrayBuffer:a});if(!e.value||!e.value.trim())throw new Error("Word文档内容为空或无法提取文本");console.log("降级到纯文本解析，内容长度:",e.value.length),n(e.value)}}catch(r){console.error("Word文档解析失败:",r),a(new Error(`Word文档解析失败: ${r.message}`))}},s.onerror=()=>a(new Error("文件读取失败")),s.readAsArrayBuffer(e)})}catch(t){throw console.error("mammoth库导入失败:",t),new Error("Word文档解析库加载失败，请刷新页面重试")}}convertHtmlToFormattedText(e){const t=document.createElement("div");t.innerHTML=e;let n="";const a=e=>{let t="";if(e.nodeType===Node.TEXT_NODE)return e.textContent||"";if(e.nodeType===Node.ELEMENT_NODE){const n=e;switch(n.tagName.toLowerCase()){case"h1":case"h2":case"h3":t+="\n\n";for(const e of n.childNodes)t+=a(e);t+="\n\n";break;case"p":t+="\n";for(const e of n.childNodes)t+=a(e);t+="\n";break;case"table":t+="\n",t+=this.processTable(n),t+="\n";break;case"strong":case"b":t+="**";for(const e of n.childNodes)t+=a(e);t+="**";break;case"em":case"i":t+="*";for(const e of n.childNodes)t+=a(e);t+="*";break;case"br":t+="\n";break;default:for(const e of n.childNodes)t+=a(e)}}return t};for(const s of t.childNodes)n+=a(s);return n.replace(/\n{3,}/g,"\n\n").trim()}processTable(e){let t="";return e.querySelectorAll("tr").forEach((n,a)=>{const s=n.querySelectorAll("td, th"),r=[];if(s.forEach(e=>{let t="";for(const n of e.childNodes)t+=this.getTextContent(n);r.push(t.trim())}),t+=r.join("\t")+"\n",0===a&&e.querySelector("th")){const e=r.map(()=>"---");t+=e.join("\t")+"\n"}}),t}getTextContent(e){if(e.nodeType===Node.TEXT_NODE)return e.textContent||"";if(e.nodeType===Node.ELEMENT_NODE){let t="";for(const n of e.childNodes)t+=this.getTextContent(n);return t}return""}async parsePdfDocument(e){try{const t=await n(()=>import("./pdf-Cgh3tvyp-1753160720439.js"),[]);return"undefined"!=typeof window&&(t.GlobalWorkerOptions.workerSrc=`https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${t.version}/pdf.worker.min.js`),new Promise((n,a)=>{const s=new FileReader;s.onload=async e=>{var s;try{const a=null==(s=e.target)?void 0:s.result;if(!a)throw new Error("无法读取文件内容");const r=new Uint8Array(a),i=t.getDocument({data:r}),l=await i.promise;let o="";for(let e=1;e<=l.numPages;e++){const t=await l.getPage(e),n=await t.getTextContent();o+=n.items.map(e=>e.str).join(" ")+"\n"}if(!o.trim())throw new Error("PDF文档内容为空或无法提取文本");console.log("PDF文档解析成功，内容长度:",o.length),console.log("解析内容预览:",o.substring(0,200)),n(o.trim())}catch(r){console.error("PDF文档解析失败:",r),a(new Error(`PDF文档解析失败: ${r.message}`))}},s.onerror=()=>a(new Error("文件读取失败")),s.readAsArrayBuffer(e)})}catch(t){throw console.error("pdfjs-dist库导入失败:",t),new Error("PDF文档解析库加载失败，请刷新页面重试")}}extractVariables(e){const t=[],n=/\[([^\]]+)\]/g;let a;for(;null!==(a=n.exec(e));){const e=a[1],n=this.variableMapping[e];if(n)t.find(e=>e.id===n.id)||t.push({...n});else{const n={id:`unknown_${Date.now()}_${Math.random()}`,originalText:a[0],variableName:this.convertToVariableName(e),chineseName:e,category:"other",required:!1};t.push(n)}}return t}convertToVariableName(e){return e.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g,"").toLowerCase()}convertVariablesToStandardFormat(e,t){let n=e;return t.forEach(e=>{const t=new RegExp(`\\[${e.chineseName}\\]`,"g");n=n.replace(t,`{{${e.variableName}}}`)}),n}extractTitle(e){var t;return(null==(t=e.split("\n")[0])?void 0:t.trim())||"未知标题"}detectTableStructure(e){return e.includes("┌")||e.includes("├")||e.includes("│")}detectBorderStructure(e){return this.detectTableStructure(e)}getAllSupportedVariables(){return Object.values(this.variableMapping)}getVariablesByCategory(e){return Object.values(this.variableMapping).filter(t=>t.category===e)}validateVariableMapping(e){const t=[];return 0===e.filter(e=>e.required).length&&t.push("至少需要一个必填变量"),{valid:0===t.length,errors:t}}};t(we,"instance");let Se=we;const Ce=class e{static getInstance(){return e.instance||(e.instance=new e),e.instance}async parseDocument(e){console.log("使用简化文档解析器处理文件:",e.name);const t=this.getFileType(e.name),n=this.generateTemplateContent(e.name),a=this.extractVariables(n),s=this.convertVariablesToStandardFormat(n,a);return{id:Date.now().toString(),fileName:e.name,fileType:t,originalContent:n,parsedContent:s,variables:a,metadata:{title:this.extractTitle(n),createdDate:(new Date).toISOString()},formatInfo:{hasTable:this.detectTableStructure(n),hasBorder:this.detectBorderStructure(n)}}}getFileType(e){return"pdf"===e.toLowerCase().split(".").pop()?"pdf":"docx"}generateTemplateContent(e){const t=e.toLowerCase();return t.includes("请假")?"计算机与人工智能学院学生请假申请单\n\n申请人基本信息：\n姓名：[学生姓名]\n学号：[学号]\n班级：[班级]\n专业：[专业]\n性别：[性别]\n宿舍号：[宿舍号]\n联系电话：[联系电话]\n\n请假信息：\n请假类型：[请假类型]\n请假原因：[请假原因]\n请假时间：从 [开始日期] [开始时间] 到 [结束日期] [结束时间]\n请假天数：[请假天数] 天\n\n紧急联系人：\n联系人姓名：[紧急联系人]\n联系人电话：[紧急联系人电话]\n与学生关系：[与学生关系]\n\n学生承诺：\n本人保证请假期间注意人身安全，按时返校销假。如有意外，后果自负。\n\n学生签名：________________\n申请日期：[申请日期]\n\n审批意见：\n辅导员意见：\n□ 同意    □ 不同意\n意见：[辅导员意见]\n辅导员签名：________________\n日期：[辅导员审批日期]\n\n学院意见：\n□ 同意    □ 不同意\n意见：[学院意见]\n学院负责人签名：________________\n日期：[学院审批日期]":t.includes("在读")||t.includes("证明")?"在读证明\n\n兹证明[学生姓名]同学，学号：[学号]，系我校[学院][专业]专业[班级]班学生。\n\n该生现为我校在读学生，学习情况良好。\n\n特此证明。\n\n此证明仅供相关用途使用。\n\n[学校名称]\n[当前日期]":"证明文件\n\n学生信息：\n姓名：[学生姓名]\n学号：[学号]\n班级：[班级]\n专业：[专业]\n学院：[学院]\n\n特此证明。\n\n[学校名称]\n[当前日期]"}extractVariables(e){const t=e.match(/\[([^\]]+)\]/g)||[];return[...new Set(t)].map((e,t)=>{const n=e.slice(1,-1),a=this.mapChineseToEnglish(n);return{id:`var_${t}`,originalText:e,variableName:a,chineseName:n,category:this.categorizeVariable(a),required:this.isRequiredVariable(a),defaultValue:this.getDefaultValue(a)}})}mapChineseToEnglish(e){return{"学生姓名":"studentName","学号":"studentId","班级":"class","专业":"major","学院":"college","性别":"gender","宿舍号":"dormitory","联系电话":"contactPhone","请假类型":"leaveType","请假原因":"leaveReason","开始日期":"startDate","开始时间":"startTime","结束日期":"endDate","结束时间":"endTime","请假天数":"leaveDays","紧急联系人":"emergencyContact","紧急联系人电话":"emergencyPhone","与学生关系":"relationship","申请日期":"applicationDate","辅导员意见":"advisorOpinion","辅导员审批日期":"advisorDate","学院意见":"collegeOpinion","学院审批日期":"collegeDate","当前日期":"currentDate","学校名称":"schoolName"}[e]||e.toLowerCase().replace(/\s+/g,"")}categorizeVariable(e){return["studentName","studentId","class","major","college","gender","dormitory"].includes(e)?"basic":["contactPhone","emergencyContact","emergencyPhone","relationship"].includes(e)?"contact":["leaveType","leaveReason","startDate","startTime","endDate","endTime","leaveDays"].includes(e)?"academic":["applicationDate","advisorOpinion","advisorDate","collegeOpinion","collegeDate"].includes(e)?"approval":"other"}isRequiredVariable(e){return["studentName","studentId","class","major"].includes(e)}getDefaultValue(e){return{college:"计算机与人工智能学院",schoolName:"银川科技学院",currentDate:(new Date).toLocaleDateString("zh-CN")}[e]}convertVariablesToStandardFormat(e,t){let n=e;return t.forEach(e=>{const t=new RegExp(`\\[${e.chineseName}\\]`,"g");n=n.replace(t,`{{${e.variableName}}}`)}),n}extractTitle(e){var t;return(null==(t=e.split("\n")[0])?void 0:t.trim())||"未知标题"}detectTableStructure(e){return e.includes("┌")||e.includes("├")||e.includes("│")||e.includes("申请人基本信息")||e.includes("审批意见")}detectBorderStructure(e){return this.detectTableStructure(e)}};t(Ce,"instance");let Te=Ce;const De=class e{static getInstance(){return e.instance||(e.instance=new e),e.instance}async queryStudentByStudentId(e){if(console.log("查询学生信息，学号:",e),!e)return console.log("学号为空"),{found:!1,source:"none",confidence:0,suggestions:this.generateStudentIdSuggestions(e)};if(!this.validateStudentId(e))return console.log("学号格式验证失败:",e),{found:!1,source:"none",confidence:0,suggestions:this.generateStudentIdSuggestions(e)};console.log("学号格式验证通过:",e);try{const t=await this.queryFromAPI(e);if(t.found)return t}catch(s){console.warn("API查询失败，尝试本地数据源:",s)}const t=this.queryFromLocalStorage(e);if(t.found)return t;const n=this.queryFromImportedData(e);if(n.found)return n;const a=this.fuzzyMatchStudent(e);return a.found?a:{found:!1,source:"none",confidence:0,suggestions:this.generateStudentIdSuggestions(e)}}validateStudentId(e){if(!e)return!1;if(e.length<4)return!1;return/^[A-Za-z0-9]{4,20}$/.test(e)}async queryFromAPI(e){try{console.log("从API查询学生信息，学号:",e);const t=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(e),n=t?`/api/students/by-id/${encodeURIComponent(e)}`:`/api/students/by-student-id/${encodeURIComponent(e)}`;console.log("API URL:",n,"是否为UUID:",t);const a=await fetch(n,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")||""}`}});if(!a.ok){if(404===a.status)return console.log("API返回404，学生不存在"),{found:!1,source:"database",confidence:0};throw console.warn("API查询失败，状态码:",a.status),new Error(`API查询失败，状态码: ${a.status}`)}{const e=await a.json();if(console.log("API查询结果:",e),e.success&&e.data){const t=e.data;return{found:!0,student:this.normalizeStudentData(t),source:"database",confidence:1}}}return{found:!1,source:"database",confidence:0}}catch(t){throw console.error("API查询错误:",t),new Error(`API查询失败: ${t.message}`)}}queryFromLocalStorage(e){try{const t=JSON.parse(localStorage.getItem("studentsList")||"[]").find(t=>t.studentId===e||t.student_id===e||t.学号===e);return t?{found:!0,student:this.normalizeStudentData(t),source:"localStorage",confidence:1}:{found:!1,source:"localStorage",confidence:0}}catch(t){return console.error("localStorage查询失败:",t),{found:!1,source:"localStorage",confidence:0}}}queryFromImportedData(e){try{const t=JSON.parse(localStorage.getItem("importedStudentsData")||"{}"),n=Object.values(t).find(t=>t.studentId===e||t.student_id===e||t.学号===e);return n?{found:!0,student:this.normalizeStudentData(n),source:"imported",confidence:1}:{found:!1,source:"imported",confidence:0}}catch(t){return console.error("导入数据查询失败:",t),{found:!1,source:"imported",confidence:0}}}fuzzyMatchStudent(e){try{const t=this.getAllStudents(),n=[];for(const a of t){const t=this.calculateEditDistance(e,a.studentId);1-t/Math.max(e.length,a.studentId.length)>.7&&n.push(a.studentId)}return n.length>0?{found:!1,source:"none",confidence:0,suggestions:n.slice(0,5)}:{found:!1,source:"none",confidence:0}}catch(t){return console.error("模糊匹配失败:",t),{found:!1,source:"none",confidence:0}}}getAllStudents(){const e=[];try{const t=JSON.parse(localStorage.getItem("studentsList")||"[]");e.push(...t.map(e=>this.normalizeStudentData(e)));const n=JSON.parse(localStorage.getItem("importedStudentsData")||"{}");e.push(...Object.values(n).map(e=>this.normalizeStudentData(e)))}catch(t){console.error("获取学生数据失败:",t)}return e}normalizeStudentData(e){return{studentId:e.studentId||e.student_id||e.学号||"",studentName:e.studentName||e.student_name||e.name||e.姓名||"",class:e.class||e.班级||"",major:e.major||e.专业||"",college:e.college||e.学院||"计算机与人工智能学院",gender:e.gender||e.性别||"男",phone:e.phone||e.联系电话||e.电话||"",email:e.email||e.邮箱||"",dormitory:e.dormitory||e.宿舍号||"",emergencyContact:e.emergencyContact||e.紧急联系人||"",emergencyPhone:e.emergencyPhone||e.紧急联系人电话||"",relationship:e.relationship||e.与学生关系||"父母",grade:e.grade||e.年级||this.calculateGrade(e.studentId||e.student_id||e.学号),enrollmentDate:e.enrollmentDate||e.入学日期||this.calculateEnrollmentDate(e.studentId||e.student_id||e.学号),graduationDate:e.graduationDate||e.毕业日期||this.calculateGraduationDate(e.studentId||e.student_id||e.学号),gpa:e.gpa||e.绩点||"3.8",ranking:e.ranking||e.排名||"优秀",awards:e.awards||e.获奖情况||"品学兼优",status:e.status||e.学籍状态||"在读",createdAt:e.createdAt||(new Date).toISOString(),updatedAt:e.updatedAt||(new Date).toISOString()}}calculateGrade(e){if(!e||e.length<4)return"三";const t=parseInt(e.substring(0,4)),n=(new Date).getFullYear();return["一","二","三","四"][Math.min(4,Math.max(1,n-t+1))-1]||"三"}calculateEnrollmentDate(e){if(!e||e.length<4)return"2021年9月";return`${parseInt(e.substring(0,4))}年9月`}calculateGraduationDate(e){if(!e||e.length<4)return"2025年6月";return`${parseInt(e.substring(0,4))+4}年6月`}calculateEditDistance(e,t){const n=Array(t.length+1).fill(null).map(()=>Array(e.length+1).fill(null));for(let a=0;a<=e.length;a++)n[0][a]=a;for(let a=0;a<=t.length;a++)n[a][0]=a;for(let a=1;a<=t.length;a++)for(let s=1;s<=e.length;s++){const r=e[s-1]===t[a-1]?0:1;n[a][s]=Math.min(n[a][s-1]+1,n[a-1][s]+1,n[a-1][s-1]+r)}return n[t.length][e.length]}generateStudentIdSuggestions(e){if(!e)return[];const t=(new Date).getFullYear(),n=[];if(e.length>=4&&e.startsWith("20")){const t=e.substring(0,4);for(let e=1;e<=5;e++)n.push(`${t}00${e.toString().padStart(4,"0")}`)}else for(let a=1;a<=5;a++)n.push(`${t}00${a.toString().padStart(4,"0")}`);return n}async batchQueryStudents(e){const t=new Map;for(const a of e)try{const e=await this.queryStudentByStudentId(a);t.set(a,e)}catch(n){console.error(`查询学生 ${a} 失败:`,n),t.set(a,{found:!1,source:"none",confidence:0})}return t}getStudentDataStatistics(){const e=this.getAllStudents(),t={localStorage:0,imported:0};try{const e=JSON.parse(localStorage.getItem("studentsList")||"[]");t.localStorage=e.length;const n=JSON.parse(localStorage.getItem("importedStudentsData")||"{}");t.imported=Object.keys(n).length}catch(s){console.error("获取统计信息失败:",s)}const n=e.filter(e=>e.studentId&&e.studentName&&e.class&&e.major),a=e.length>0?n.length/e.length:0;return{total:e.length,sources:t,completeness:a}}};t(De,"instance");let Ie=De;const ke=class e{constructor(){t(this,"studentDataService"),t(this,"variableFormats",[{pattern:/\{\{([^}]+)\}\}/g,name:"double-brace"},{pattern:/\{([^}]+)\}/g,name:"single-brace"},{pattern:/\[([^\]]+)\]/g,name:"bracket"}]),t(this,"systemDefaults",{schoolName:"银川科技学院",currentDate:(new Date).toLocaleDateString("zh-CN"),issueDate:(new Date).toLocaleDateString("zh-CN"),applicationDate:(new Date).toLocaleDateString("zh-CN")}),t(this,"chineseVariableMap",{"学生姓名":"studentName","学号":"studentId","班级":"class","专业":"major","学院":"college","性别":"gender","宿舍号":"dormitory","联系电话":"contactPhone","请假类型":"leaveType","请假原因":"leaveReason","开始日期":"startDate","开始时间":"startTime","结束日期":"endDate","结束时间":"endTime","请假天数":"leaveDays","紧急联系人":"emergencyContact","紧急联系人电话":"emergencyPhone","与学生关系":"relationship","申请日期":"applicationDate","辅导员意见":"advisorOpinion","辅导员审批日期":"advisorDate","学院意见":"collegeOpinion","学院审批日期":"collegeDate","当前日期":"currentDate","学校名称":"schoolName","年级":"grade","入学日期":"enrollmentDate","毕业日期":"graduationDate","绩点":"gpa","排名":"ranking","获奖情况":"awards"}),this.studentDataService=Ie.getInstance()}static getInstance(){return e.instance||(e.instance=new e),e.instance}async replaceVariables(e){try{const t=await this.prepareVariableValues(e);let n=e.template.content||"";const a=[],s=[],r=[];for(const e of this.variableFormats){const i=this.replaceByFormat(n,e,t);n=i.content,a.push(...i.replaced),s.push(...i.unreplaced),r.push(...i.warnings)}return{content:n,replacedVariables:a,unreplacedVariables:[...new Set(s)],warnings:[...new Set(r)]}}catch(t){throw console.error("变量替换失败:",t),new Error(`变量替换失败: ${t.message}`)}}async prepareVariableValues(e){const t=new Map;let n=e.student;if(!n&&e.certificate.studentId){const t=await this.studentDataService.queryStudentByStudentId(e.certificate.studentId);t.found&&(n=t.student)}return n&&this.addStudentVariables(t,n),this.addCertificateVariables(t,e.certificate),this.addSystemVariables(t,e.systemValues),this.addDefaultValues(t),t}addStudentVariables(e,t){["studentName","studentId","class","major","college","gender","phone","email","dormitory","emergencyContact","emergencyPhone","relationship","grade","enrollmentDate","graduationDate","gpa","ranking","awards"].forEach(n=>{const a=t[n];a&&e.set(n,{key:n,value:String(a),source:"student",confidence:1})})}addCertificateVariables(e,t){["studentName","studentId","class","major","college","contactPhone","leaveType","leaveReason","startDate","startTime","endDate","endTime","leaveDays","emergencyContact","emergencyPhone","relationship","applicationDate","advisorOpinion","advisorDate","collegeOpinion","collegeDate","purpose"].forEach(n=>{const a=t[n];a&&e.set(n,{key:n,value:String(a),source:"certificate",confidence:1})})}addSystemVariables(e,t){Object.entries(this.systemDefaults).forEach(([t,n])=>{e.has(t)||e.set(t,{key:t,value:n,source:"system",confidence:1})}),t&&Object.entries(t).forEach(([t,n])=>{e.set(t,{key:t,value:n,source:"system",confidence:1})})}addDefaultValues(e){Object.entries({gender:"男",leaveType:"事假",startTime:"08:00",endTime:"18:00",leaveDays:"1",relationship:"父母",gpa:"3.8",ranking:"优秀",awards:"品学兼优"}).forEach(([t,n])=>{e.has(t)||e.set(t,{key:t,value:n,source:"default",confidence:.5})})}replaceByFormat(e,t,n){const a=[],s=[],r=[];let i,l=e;for(t.pattern.lastIndex=0;null!==(i=t.pattern.exec(e));){const e=i[0],t=i[1].trim(),o=this.chineseVariableMap[t]||t,c=n.get(o);if(c){const t=new RegExp(this.escapeRegExp(e),"g");l=l.replace(t,c.value),a.push(c)}else s.push(t),r.push(`未找到变量 "${t}" 的值`)}return{content:l,replaced:a,unreplaced:s,warnings:r}}escapeRegExp(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}validateTemplateVariables(e){const t=[],n=[],a=[];for(const s of this.variableFormats){let n;for(s.pattern.lastIndex=0;null!==(n=s.pattern.exec(e));){const e=n[1].trim();t.includes(e)||t.push(e)}}return t.forEach(e=>{const t=this.chineseVariableMap[e]||e;this.isKnownVariable(t)||a.push(`未知变量: ${e}`)}),{valid:0===n.length,variables:t,errors:n,warnings:a}}isKnownVariable(e){return["studentName","studentId","class","major","college","gender","phone","email","dormitory","emergencyContact","emergencyPhone","relationship","grade","enrollmentDate","graduationDate","gpa","ranking","awards","contactPhone","leaveType","leaveReason","startDate","startTime","endDate","endTime","leaveDays","applicationDate","advisorOpinion","advisorDate","collegeOpinion","collegeDate","purpose","currentDate","schoolName","issueDate"].includes(e)||Object.values(this.chineseVariableMap).includes(e)}getSupportedVariables(){return[{name:"studentName",chineseName:"学生姓名",category:"basic",description:"学生的真实姓名"},{name:"studentId",chineseName:"学号",category:"basic",description:"学生的学号"},{name:"class",chineseName:"班级",category:"basic",description:"学生所在班级"},{name:"major",chineseName:"专业",category:"basic",description:"学生的专业"},{name:"college",chineseName:"学院",category:"basic",description:"学生所在学院"},{name:"gender",chineseName:"性别",category:"basic",description:"学生性别"},{name:"contactPhone",chineseName:"联系电话",category:"contact",description:"学生联系电话"},{name:"dormitory",chineseName:"宿舍号",category:"contact",description:"学生宿舍号"},{name:"emergencyContact",chineseName:"紧急联系人",category:"contact",description:"紧急联系人姓名"},{name:"emergencyPhone",chineseName:"紧急联系人电话",category:"contact",description:"紧急联系人电话"},{name:"relationship",chineseName:"与学生关系",category:"contact",description:"紧急联系人与学生的关系"},{name:"leaveType",chineseName:"请假类型",category:"academic",description:"请假的类型"},{name:"leaveReason",chineseName:"请假原因",category:"academic",description:"请假的具体原因"},{name:"startDate",chineseName:"开始日期",category:"academic",description:"请假开始日期"},{name:"startTime",chineseName:"开始时间",category:"academic",description:"请假开始时间"},{name:"endDate",chineseName:"结束日期",category:"academic",description:"请假结束日期"},{name:"endTime",chineseName:"结束时间",category:"academic",description:"请假结束时间"},{name:"leaveDays",chineseName:"请假天数",category:"academic",description:"请假的总天数"},{name:"applicationDate",chineseName:"申请日期",category:"approval",description:"申请提交日期"},{name:"advisorOpinion",chineseName:"辅导员意见",category:"approval",description:"辅导员审批意见"},{name:"advisorDate",chineseName:"辅导员审批日期",category:"approval",description:"辅导员审批日期"},{name:"collegeOpinion",chineseName:"学院意见",category:"approval",description:"学院审批意见"},{name:"collegeDate",chineseName:"学院审批日期",category:"approval",description:"学院审批日期"},{name:"currentDate",chineseName:"当前日期",category:"system",description:"系统当前日期"},{name:"schoolName",chineseName:"学校名称",category:"system",description:"学校的名称"},{name:"issueDate",chineseName:"开具日期",category:"system",description:"证明开具日期"}]}};t(ke,"instance");let _e=ke;const Fe=({value:e="#000000",onChange:t,presets:n=["#000000","#FF0000","#00FF00","#0000FF","#FFFF00","#FF00FF","#00FFFF","#808080"],size:s="small",type:r="text"})=>{const[d,m]=i.useState(e),[u,p]=i.useState(!1),h=i.useRef(null),g=e=>{null==t||t(e),m(e),p(!1)};i.useEffect(()=>{const e=e=>{h.current&&!h.current.contains(e.target)&&p(!1)};return u&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[u]);const x="text"===r?{backgroundColor:e,color:Ee(e),minWidth:"32px"}:{backgroundColor:"#fff",color:"#000",border:`2px solid ${e}`,minWidth:"32px"};return a.jsxs("div",{ref:h,style:{position:"relative",display:"inline-block"},children:[a.jsx(l,{size:s,style:x,onClick:()=>p(!u),children:"text"===r?"A":"H"}),u&&a.jsxs("div",{style:{position:"absolute",top:"100%",left:0,zIndex:1e3,backgroundColor:"#fff",border:"1px solid #d9d9d9",borderRadius:"6px",boxShadow:"0 6px 16px 0 rgba(0, 0, 0, 0.08)",padding:"12px",width:"200px",marginTop:"4px"},children:[a.jsxs("div",{style:{marginBottom:"12px"},children:[a.jsx("div",{style:{marginBottom:"8px",fontSize:"12px",fontWeight:"bold"},children:"预设颜色"}),a.jsx("div",{style:{display:"grid",gridTemplateColumns:"repeat(4, 1fr)",gap:"6px"},children:n.map(t=>a.jsx("div",{style:{width:"32px",height:"32px",backgroundColor:t,border:"2px solid #ccc",cursor:"pointer",borderRadius:"4px",transition:"all 0.2s",boxShadow:e===t?"0 0 0 2px #1890ff":"none"},onClick:()=>g(t),onMouseEnter:e=>{e.currentTarget.style.transform="scale(1.1)"},onMouseLeave:e=>{e.currentTarget.style.transform="scale(1)"},title:t},t))})]}),a.jsxs("div",{children:[a.jsx("div",{style:{marginBottom:"8px",fontSize:"12px",fontWeight:"bold"},children:"自定义颜色"}),a.jsxs(o.Compact,{style:{width:"100%"},children:[a.jsx(c,{value:d,onChange:e=>{return n=e.target.value,m(n),void(/^#[0-9A-F]{6}$/i.test(n)&&(null==t||t(n)));var n},placeholder:"#000000",style:{flex:1},maxLength:7}),a.jsx("div",{style:{width:"32px",height:"32px",backgroundColor:d,border:"1px solid #ccc",borderRadius:"0 4px 4px 0"}})]}),a.jsx(l,{size:"small",type:"primary",style:{marginTop:"8px",width:"100%"},onClick:()=>g(d),disabled:!/^#[0-9A-F]{6}$/i.test(d),children:"应用颜色"})]})]})]})};function Ee(e){const t=e.replace("#","");return(299*parseInt(t.substr(0,2),16)+587*parseInt(t.substr(2,2),16)+114*parseInt(t.substr(4,2),16))/1e3>128?"#000000":"#ffffff"}const{Text:ze}=G,{Option:Ae}=x,$e=({icon:e,title:t,onClick:n,disabled:s,type:r="default",size:i="small"})=>a.jsx(l,{icon:e,size:i,type:r,onClick:n,disabled:s,title:t,style:{border:"none",boxShadow:"none"}}),qe=({initialContent:e="",variables:t=[],onContentChange:n,onSave:s,onVariablesChange:r,a4Mode:G=!1})=>{const[K,Z]=i.useState({content:e,history:[e],historyIndex:0}),[Q,ee]=i.useState(!1),[te,ne]=i.useState(14),[ae,se]=i.useState("SimSun, serif"),[re,ie]=i.useState("left"),[le,oe]=i.useState(1.6),[ce,de]=i.useState(t),[me,ue]=i.useState(!1),[pe,he]=i.useState(null),[ge,xe]=i.useState(!1),[ye,fe]=i.useState(!1),je=i.useRef(null),ve=i.useRef(null);i.useEffect(()=>{n&&n(K.content)},[K.content,n]),i.useEffect(()=>{de(t)},[t]),i.useEffect(()=>{e!==K.content&&(console.log("RichTextTemplateEditor: 更新内容",e),Z({content:e,history:[e],historyIndex:0}),je.current&&!Q&&(je.current.innerHTML=e))},[e,Q]),i.useEffect(()=>{const e=e=>{ve.current&&!ve.current.contains(e.target)&&xe(!1)};return ge&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[ge]);const be=e=>{Z(t=>{const n=t.history.slice(0,t.historyIndex+1);return n.push(e),{...t,content:e,history:n,historyIndex:n.length-1}})},Ne=()=>{if(je.current){let e=je.current.innerHTML;e=e.replace(/<div><br><\/div>/g,"<br>").replace(/<div>/g,"<br>").replace(/<\/div>/g,"").replace(/(<br\s*\/?>){2,}/g,"<br>"),be(e)}},we=e=>{"Enter"===e.key&&(e.preventDefault(),document.execCommand("insertHTML",!1,"<br>"))},Se=(e,t)=>{document.execCommand(e,!1,t),Ne()},Ce=e=>{if(je.current)switch(je.current.focus(),e){case"bold":Se("bold");break;case"italic":Se("italic");break;case"underline":Se("underline");break;case"strikethrough":Se("strikeThrough");break;case"removeFormat":Se("removeFormat")}},Te=e=>{if(je.current){switch(je.current.focus(),e){case"left":Se("justifyLeft");break;case"center":Se("justifyCenter");break;case"right":Se("justifyRight")}ie(e)}},De=(e,t=!1)=>{if(!je.current)return;je.current.focus(),ne(e);const n=window.getSelection();if(n&&n.rangeCount>0&&!n.isCollapsed){const t=n.getRangeAt(0),s=document.createElement("span");s.style.fontSize=`${e}px`,s.style.fontFamily="inherit";try{t.surroundContents(s)}catch(a){const e=t.extractContents();s.appendChild(e),t.insertNode(s)}Ne(),w.success(`已将选中文本字体大小设置为 ${e}px`)}else t?(je.current.style.fontSize=`${e}px`,Ne(),w.success(`已将整体字体大小设置为 ${e}px`)):N.confirm({title:"字体大小设置",content:"没有选中文本，是否将字体大小应用到整个文档？",onOk:()=>{je.current.style.fontSize=`${e}px`,Ne(),w.success(`已将整体字体大小设置为 ${e}px`)},onCancel:()=>{w.info("请先选择要修改字体大小的文本")}})},Ie=e=>{if(!je.current)return;je.current.focus();const t=window.getSelection();if(t&&t.rangeCount>0){switch(t.getRangeAt(0),e){case"h1":Se("formatBlock","<h1>");break;case"h2":Se("formatBlock","<h2>");break;case"h3":Se("formatBlock","<h3>");break;case"p":Se("formatBlock","<p>");break;case"blockquote":Se("formatBlock","<blockquote>")}Ne()}},ke=e=>{De(e,!0)},[_e,Ee]=i.useState(null),qe=e=>{je.current&&(je.current.focus(),Se("ordered"===e?"insertOrderedList":"insertUnorderedList"),Ne())},Oe=(e,t)=>{if(!je.current)return;je.current.focus();let n='<table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0;">';for(let a=0;a<e;a++){n+="<tr>";for(let e=0;e<t;e++)n+=`<td style="padding: 8px; border: 1px solid #ccc;">单元格${a+1}-${e+1}</td>`;n+="</tr>"}n+="</table>",Se("insertHTML",n),Ne()},Ve=()=>{let e=K.content;const t=(()=>{try{const e=localStorage.getItem("studentsData"),t=localStorage.getItem("academicStatusData");let n={studentName:"张三",name:"张三",studentId:"2024001001",class:"计科2401",major:"计算机科学与技术",college:"计算机与人工智能学院",schoolName:"银川科技学院",currentDate:(new Date).toLocaleDateString("zh-CN"),gender:"男",grade:"2024级",phone:"13800138000",status:"在读"};if(e){const t=JSON.parse(e);if(t.length>0){const e=t[0];n={...n,studentName:e.name,name:e.name,studentId:e.studentId,class:e.class,major:e.major,gender:e.gender,phone:e.phone,grade:e.grade,status:e.status}}}if(t){const e=JSON.parse(t);if(e.length>0){const t=e[0];n={...n,ethnicity:t.ethnicity,birthplace:t.birthplace,level:t.level,failedCourses:t.failedCourses,courseSemester:t.courseSemester,retakeRegistered:t.retakeRegistered,expectedGraduation:t.expectedGraduation,tuitionOwed:t.tuitionOwed,owedAmount:t.owedAmount,counselorContact:t.counselorContact,studentPhone:t.studentPhone,otherSituation:t.otherSituation}}}return n}catch(e){return console.error("获取学生数据失败:",e),{studentName:"张三",name:"张三",studentId:"2024001001",class:"计科2401",major:"计算机科学与技术",college:"计算机与人工智能学院",schoolName:"银川科技学院",currentDate:(new Date).toLocaleDateString("zh-CN")}}})();ce.forEach(n=>{const a=t[n.standardName]||n.defaultValue||`示例${n.chineseName}`,s=new RegExp(`\\[${n.chineseName}\\]`,"g");e=e.replace(s,a);const r=new RegExp(`<span[^>]*data-variable="${n.standardName}"[^>]*>\\[${n.chineseName}\\]<\\/span>`,"g");e=e.replace(r,`<span style="color: #52c41a; font-weight: normal;">${a}</span>`)});return e=e.replace(/\{\{([a-zA-Z0-9_]+)\}\}/g,(e,n)=>t[n]||e),e};return a.jsxs("div",{style:{height:G?"297mm":"100%",width:G?"210mm":"100%",display:"flex",flexDirection:"column",backgroundColor:G?"#ffffff":"transparent"},children:[!G&&a.jsx(d,{size:"small",style:{marginBottom:"8px",flexShrink:0},children:a.jsxs(m,{gutter:[8,8],align:"middle",wrap:!0,children:[a.jsx(u,{children:a.jsxs(o,{children:[a.jsx($e,{icon:a.jsx(p,{}),title:"撤销",onClick:()=>{Z(e=>{if(e.historyIndex>0){const t=e.historyIndex-1,n=e.history[t];return je.current&&(je.current.innerHTML=n),{...e,content:n,historyIndex:t}}return e})},disabled:K.historyIndex<=0}),a.jsx($e,{icon:a.jsx(h,{}),title:"重做",onClick:()=>{Z(e=>{if(e.historyIndex<e.history.length-1){const t=e.historyIndex+1,n=e.history[t];return je.current&&(je.current.innerHTML=n),{...e,content:n,historyIndex:t}}return e})},disabled:K.historyIndex>=K.history.length-1})]})}),a.jsx(u,{children:a.jsx(g,{type:"vertical"})}),a.jsx(u,{children:a.jsxs(o,{children:[a.jsxs(x,{value:ae,onChange:(e,t=!1)=>{if(!je.current)return;je.current.focus(),se(e);const n=window.getSelection();if(n&&n.rangeCount>0&&!n.isCollapsed){const t=n.getRangeAt(0),s=document.createElement("span");s.style.fontFamily=e,s.style.fontSize="inherit";try{t.surroundContents(s)}catch(a){const e=t.extractContents();s.appendChild(e),t.insertNode(s)}Ne(),w.success(`已将选中文本字体设置为 ${e.split(",")[0]}`)}else t?(je.current.style.fontFamily=e,Ne(),w.success(`已将整体字体设置为 ${e.split(",")[0]}`)):N.confirm({title:"字体设置",content:"没有选中文本，是否将字体应用到整个文档？",onOk:()=>{je.current.style.fontFamily=e,Ne(),w.success(`已将整体字体设置为 ${e.split(",")[0]}`)},onCancel:()=>{w.info("请先选择要修改字体的文本")}})},size:"small",style:{width:120},children:[a.jsx(Ae,{value:"SimSun, serif",children:"宋体"}),a.jsx(Ae,{value:"SimHei, sans-serif",children:"黑体"}),a.jsx(Ae,{value:"KaiTi, serif",children:"楷体"}),a.jsx(Ae,{value:"FangSong, serif",children:"仿宋"}),a.jsx(Ae,{value:"Microsoft YaHei, sans-serif",children:"微软雅黑"}),a.jsx(Ae,{value:"Arial, sans-serif",children:"Arial"})]}),a.jsxs(o.Compact,{children:[a.jsx($e,{icon:a.jsx(y,{}),title:"减小字体",onClick:()=>{const e=Math.max(te-2,8);De(e,!0)},size:"small"}),a.jsx(f,{value:te,onChange:e=>De(e||14),size:"small",min:8,max:72,style:{width:60},addonBefore:a.jsx(j,{})}),a.jsx($e,{icon:a.jsx(v,{}),title:"增大字体",onClick:()=>{const e=Math.min(te+2,72);De(e,!0)},size:"small"})]}),a.jsx(o,{children:a.jsxs(l.Group,{size:"small",children:[a.jsx(l,{onClick:()=>ke(12),type:12===te?"primary":"default",children:"12"}),a.jsx(l,{onClick:()=>ke(14),type:14===te?"primary":"default",children:"14"}),a.jsx(l,{onClick:()=>ke(16),type:16===te?"primary":"default",children:"16"}),a.jsx(l,{onClick:()=>ke(18),type:18===te?"primary":"default",children:"18"}),a.jsx(l,{onClick:()=>ke(24),type:24===te?"primary":"default",children:"24"})]})}),a.jsx(b,{title:"应用当前字体和大小到整个文档",children:a.jsx(l,{size:"small",type:"primary",icon:a.jsx(S,{}),onClick:()=>{N.confirm({title:"应用到整体",content:"确定要将当前字体和字体大小应用到整个文档吗？",onOk:()=>{je.current&&(je.current.style.fontFamily=ae,je.current.style.fontSize=`${te}px`,Ne(),w.success("已应用到整个文档"))}})},children:"应用到整体"})}),a.jsx(f,{value:le,onChange:e=>(e=>{if(!je.current)return;je.current.focus(),oe(e);const t=window.getSelection();if(t&&t.rangeCount>0&&!t.isCollapsed){const n=t.getRangeAt(0).commonAncestorContainer;let a=n.nodeType===Node.TEXT_NODE?n.parentElement:n;for(;a&&!["P","DIV","H1","H2","H3","H4","H5","H6"].includes(a.tagName);)a=a.parentElement;a&&(a.style.lineHeight=e.toString(),Ne())}else w.info("请先选择要修改行间距的段落")})(e||1.6),size:"small",min:1,max:3,step:.1,style:{width:70},addonBefore:a.jsx(C,{})}),a.jsx(T,{menu:{items:[{key:"p",label:"正文",onClick:()=>Ie("p")},{key:"h1",label:"标题1 (大)",onClick:()=>{Ie("h1"),ke(24)}},{key:"h2",label:"标题2 (中)",onClick:()=>{Ie("h2"),ke(20)}},{key:"h3",label:"标题3 (小)",onClick:()=>{Ie("h3"),ke(16)}},{key:"blockquote",label:"引用",onClick:()=>Ie("blockquote")},{type:"divider"},{key:"apply-all",label:"应用样式到整个文档",onClick:()=>{N.confirm({title:"应用样式到整个文档",content:"确定要将当前样式应用到整个文档吗？",onOk:()=>{je.current&&(je.current.style.fontFamily=ae,je.current.style.fontSize=`${te}px`,je.current.style.lineHeight=le.toString(),Ne(),w.success("已应用样式到整个文档"))}})}}]},trigger:["click"],children:a.jsxs(l,{size:"small",style:{width:120},children:["段落样式 ",a.jsx(D,{})]})})]})}),a.jsx(u,{children:a.jsx(g,{type:"vertical"})}),a.jsx(u,{children:a.jsxs(o,{children:[a.jsx($e,{icon:a.jsx(I,{}),title:"粗体",onClick:()=>Ce("bold")}),a.jsx($e,{icon:a.jsx(k,{}),title:"斜体",onClick:()=>Ce("italic")}),a.jsx($e,{icon:a.jsx(_,{}),title:"下划线",onClick:()=>Ce("underline")}),a.jsx($e,{icon:a.jsx(F,{}),title:"删除线",onClick:()=>Ce("strikethrough")}),a.jsx($e,{icon:a.jsx(E,{}),title:"清除格式",onClick:()=>Ce("removeFormat")}),a.jsx($e,{icon:a.jsx(z,{}),title:"复制格式",onClick:()=>{const e=window.getSelection();if(e&&e.rangeCount>0&&!e.isCollapsed){const t=e.getRangeAt(0),n=t.commonAncestorContainer.nodeType===Node.TEXT_NODE?t.commonAncestorContainer.parentElement:t.commonAncestorContainer;if(n){const e=window.getComputedStyle(n);Ee({fontSize:e.fontSize,fontFamily:e.fontFamily,fontWeight:e.fontWeight,fontStyle:e.fontStyle,color:e.color,backgroundColor:e.backgroundColor,textDecoration:e.textDecoration}),w.success("已复制格式，请选择要应用格式的文本")}}else w.info("请先选择要复制格式的文本")}}),a.jsx($e,{icon:a.jsx(A,{}),title:"粘贴格式",onClick:()=>{if(!_e)return void w.info("请先复制格式");const e=window.getSelection();if(e&&e.rangeCount>0&&!e.isCollapsed){const n=e.getRangeAt(0),a=document.createElement("span");Object.assign(a.style,_e);try{n.surroundContents(a)}catch(t){const e=n.extractContents();a.appendChild(e),n.insertNode(a)}Ne(),w.success("已应用格式")}else w.info("请先选择要应用格式的文本")},disabled:!_e})]})}),a.jsx(u,{children:a.jsx(g,{type:"vertical"})}),a.jsx(u,{children:a.jsxs(o,{children:[a.jsx(Fe,{size:"small",type:"text",onChange:e=>{je.current&&(je.current.focus(),Se("foreColor",e),Ne())},presets:["#000000","#FF0000","#00FF00","#0000FF","#FFFF00","#FF00FF","#00FFFF","#808080"]}),a.jsx(Fe,{size:"small",type:"background",onChange:e=>{je.current&&(je.current.focus(),Se("backColor",e),Ne())},presets:["#FFFFFF","#FFFF00","#00FF00","#00FFFF","#FF00FF","#C0C0C0","#FFFFE0","#F0F8FF"]})]})}),a.jsx(u,{children:a.jsx(g,{type:"vertical"})}),a.jsx(u,{children:a.jsxs(o,{children:[a.jsx($e,{icon:a.jsx($,{}),title:"左对齐",type:"left"===re?"primary":"default",onClick:()=>Te("left")}),a.jsx($e,{icon:a.jsx(q,{}),title:"居中",type:"center"===re?"primary":"default",onClick:()=>Te("center")}),a.jsx($e,{icon:a.jsx(O,{}),title:"右对齐",type:"right"===re?"primary":"default",onClick:()=>Te("right")})]})}),a.jsx(u,{children:a.jsx(g,{type:"vertical"})}),a.jsx(u,{children:a.jsxs(o,{children:[a.jsx($e,{icon:a.jsx(V,{}),title:"有序列表",onClick:()=>qe("ordered")}),a.jsx($e,{icon:a.jsx(L,{}),title:"无序列表",onClick:()=>qe("unordered")})]})}),a.jsx(u,{children:a.jsx(g,{type:"vertical"})}),a.jsx(u,{children:a.jsxs(o,{children:[a.jsxs("div",{ref:ve,style:{position:"relative",display:"inline-block"},children:[a.jsx($e,{icon:a.jsx(M,{}),title:"插入表格",onClick:()=>xe(!ge)}),ge&&a.jsxs("div",{style:{position:"absolute",top:"100%",left:0,zIndex:1e3,backgroundColor:"#fff",border:"1px solid #d9d9d9",borderRadius:"6px",boxShadow:"0 6px 16px 0 rgba(0, 0, 0, 0.08)",padding:"12px",marginTop:"4px",minWidth:"160px"},children:[a.jsx("div",{style:{marginBottom:"8px",fontSize:"12px",fontWeight:"bold"},children:"选择表格大小:"}),a.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"4px"},children:[a.jsx(l,{size:"small",onClick:()=>{Oe(2,2),xe(!1)},style:{textAlign:"left"},children:"2×2 表格"}),a.jsx(l,{size:"small",onClick:()=>{Oe(3,3),xe(!1)},style:{textAlign:"left"},children:"3×3 表格"}),a.jsx(l,{size:"small",onClick:()=>{Oe(4,4),xe(!1)},style:{textAlign:"left"},children:"4×4 表格"})]})]})]}),a.jsx($e,{icon:a.jsx(R,{}),title:"插入分隔线",onClick:()=>{je.current&&(je.current.focus(),Se("insertHorizontalRule"),Ne())}})]})}),a.jsx(u,{children:a.jsx(g,{type:"vertical"})}),a.jsx(u,{children:a.jsxs(o,{children:[a.jsx($e,{icon:a.jsx(P,{}),title:Q?"编辑模式":"预览模式",type:Q?"primary":"default",onClick:()=>ee(!Q)}),a.jsx($e,{icon:a.jsx(H,{}),title:"保存",type:"primary",onClick:()=>null==s?void 0:s(K.content)})]})})]})}),a.jsxs("div",{style:{flex:1,display:"flex",gap:G?"0":"16px",minHeight:0,padding:G?"20mm":"0"},children:[a.jsx("div",{style:{flex:1,display:"flex",flexDirection:"column",width:G?"100%":"auto"},children:G?a.jsx("div",{className:"a4-template-container",style:{flex:1},children:Q?a.jsx("div",{className:"a4-template-editor",dangerouslySetInnerHTML:{__html:Ve()}}):a.jsx("div",{ref:e=>{if(je.current=e,e&&e.innerHTML!==K.content){const t=window.getSelection();let n=0;if(t&&t.rangeCount>0){const a=t.getRangeAt(0),s=a.cloneRange();s.selectNodeContents(e),s.setEnd(a.endContainer,a.endOffset),n=s.toString().length}e.innerHTML=K.content;(()=>{e.querySelectorAll("img").forEach(e=>{e.classList.add("protected-element"),e.title="图片元素 - 请小心编辑",e.contentEditable="false",e.addEventListener("mouseenter",()=>{e.style.opacity="0.8",e.style.cursor="not-allowed"}),e.addEventListener("mouseleave",()=>{e.style.opacity="1",e.style.cursor="default"})});e.querySelectorAll("table").forEach(e=>{e.classList.add("protected-element"),e.title="表格元素 - 请小心编辑",e.addEventListener("mouseenter",()=>{e.style.backgroundColor="#f6ffed"}),e.addEventListener("mouseleave",()=>{e.style.backgroundColor=""})})})(),setTimeout(()=>{if(t){let a=0;const s=document.createTreeWalker(e,NodeFilter.SHOW_TEXT,null,!1);let r;for(;r=s.nextNode();){const e=r,s=a+e.textContent.length;if(n<=s){const s=document.createRange(),r=Math.min(n-a,e.textContent.length);s.setStart(e,r),s.setEnd(e,r),t.removeAllRanges(),t.addRange(s);break}a=s}}},0)}},className:"rich-text-editor "+(G?"a4-mode":""),contentEditable:!0,style:{flex:1,padding:G?"0":"16px",overflowY:G?"visible":"auto",backgroundColor:G?"transparent":"#fff",outline:"none",fontFamily:G?"SimSun, serif":ae,fontSize:G?"14px":`${te}px`,lineHeight:G?"1.6":le,minHeight:G?"100%":"400px",width:"100%",border:G?"none":"1px solid #d9d9d9",borderRadius:G?"0":"4px"},onInput:e=>{const t=e.currentTarget.innerHTML;be(t)},onKeyUp:Ne,onMouseUp:Ne,onKeyDown:e=>{if(we(e),"Delete"===e.key||"Backspace"===e.key){const t=window.getSelection();if(t&&t.rangeCount>0){const n=t.getRangeAt(0),a=document.createTreeWalker(n.commonAncestorContainer,NodeFilter.SHOW_ELEMENT,null,!1);let s;for(;s=a.nextNode();){const t=s;if("IMG"===t.tagName||"TABLE"===t.tagName||t.classList.contains("protected-element"))return e.preventDefault(),void w.warning("此区域包含图片或表格，请小心编辑以免丢失重要内容")}}}},suppressContentEditableWarning:!0})}):a.jsx(d,{title:Q?"预览模式":"编辑模式",style:{flex:1,display:"flex",flexDirection:"column"},styles:{body:{flex:1,padding:"0",display:"flex",flexDirection:"column"}},children:Q?a.jsx("div",{style:{flex:1,padding:"16px",overflowY:"auto",backgroundColor:"#fff",fontFamily:ae,fontSize:`${te}px`,lineHeight:le},dangerouslySetInnerHTML:{__html:Ve()}}):a.jsx("div",{ref:e=>{if(je.current=e,e&&e.innerHTML!==K.content){const t=window.getSelection();let n=0;if(t&&t.rangeCount>0){const a=t.getRangeAt(0),s=a.cloneRange();s.selectNodeContents(e),s.setEnd(a.endContainer,a.endOffset),n=s.toString().length}e.innerHTML=K.content;(()=>{e.querySelectorAll("img").forEach(e=>{e.classList.add("protected-element"),e.title="图片元素 - 请小心编辑",e.contentEditable="false",e.addEventListener("mouseenter",()=>{e.style.opacity="0.8",e.style.cursor="not-allowed"}),e.addEventListener("mouseleave",()=>{e.style.opacity="1",e.style.cursor="default"})});e.querySelectorAll("table").forEach(e=>{e.classList.add("protected-element"),e.title="表格元素 - 请小心编辑",e.addEventListener("mouseenter",()=>{e.style.backgroundColor="#f6ffed"}),e.addEventListener("mouseleave",()=>{e.style.backgroundColor=""})})})(),setTimeout(()=>{if(t){let a=0;const s=document.createTreeWalker(e,NodeFilter.SHOW_TEXT,null,!1);let r;for(;r=s.nextNode();){const e=r,s=a+e.textContent.length;if(n<=s){const s=document.createRange(),r=Math.min(n-a,e.textContent.length);s.setStart(e,r),s.setEnd(e,r),t.removeAllRanges(),t.addRange(s);break}a=s}}},0)}},className:"rich-text-editor",contentEditable:!0,style:{flex:1,padding:"16px",overflowY:"auto",backgroundColor:"#fff",outline:"none",fontFamily:ae,fontSize:`${te}px`,lineHeight:le,minHeight:"400px"},onInput:e=>{const t=e.currentTarget.innerHTML;be(t)},onKeyUp:Ne,onMouseUp:Ne,onKeyDown:e=>{if(we(e),"Delete"===e.key||"Backspace"===e.key){const t=window.getSelection();if(t&&t.rangeCount>0){const n=t.getRangeAt(0),a=document.createTreeWalker(n.commonAncestorContainer,NodeFilter.SHOW_ELEMENT,null,!1);let s;for(;s=a.nextNode();){const t=s;if("IMG"===t.tagName||"TABLE"===t.tagName||t.classList.contains("protected-element"))return e.preventDefault(),void w.warning("此区域包含图片或表格，请小心编辑以免丢失重要内容")}}}},suppressContentEditableWarning:!0})})}),!G&&a.jsx("div",{style:{width:"300px",display:"flex",flexDirection:"column"},children:a.jsx(d,{title:a.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[a.jsx("span",{children:"模板变量"}),a.jsx(l,{size:"small",icon:a.jsx(U,{}),onClick:()=>ue(!0),children:"管理"})]}),size:"small",style:{flex:1,display:"flex",flexDirection:"column"},styles:{body:{flex:1,padding:"8px",overflowY:"auto"}},children:a.jsx(B,{dataSource:ce,renderItem:(e,t)=>a.jsx(B.Item,{style:{padding:"4px 0"},children:a.jsxs(d,{size:"small",hoverable:!0,style:{width:"100%"},children:[a.jsxs("div",{style:{marginBottom:"8px"},children:[a.jsx(W,{color:"blue",children:e.chineseName}),a.jsx(W,{color:"green",children:e.category})]}),a.jsx("div",{style:{marginBottom:"8px",fontSize:"12px",color:"#666"},children:e.description}),a.jsx(l,{size:"small",icon:a.jsx(Y,{}),onClick:()=>(e=>{if(!je.current)return;je.current.focus();const t=window.getSelection();if(t&&t.rangeCount>0){const n=t.getRangeAt(0),a=`[${e.chineseName}]`,s=document.createTextNode(a);n.deleteContents(),n.insertNode(s),n.setStartAfter(s),n.setEndAfter(s),t.removeAllRanges(),t.addRange(n)}else document.execCommand("insertText",!1,`[${e.chineseName}]`);setTimeout(()=>{Ne()},10),w.success(`已插入变量：${e.chineseName}`)})(e),block:!0,children:"插入变量"})]})})})})})]}),a.jsxs(N,{title:"变量管理",open:me,onCancel:()=>{ue(!1),he(null)},width:800,footer:[a.jsx(l,{onClick:()=>ue(!1),children:"关闭"},"cancel"),a.jsx(l,{type:"primary",onClick:()=>{null==r||r(ce),ue(!1),w.success("变量设置已保存")},children:"保存变量设置"},"save")],children:[a.jsx("div",{style:{marginBottom:"16px"},children:a.jsx(l,{type:"primary",icon:a.jsx(Y,{}),onClick:()=>{de([...ce,{original:"[新变量]",standardName:"newVariable",chineseName:"新变量",englishName:"New Variable",category:"custom",dataType:"string",required:!1,description:"自定义变量"}])},children:"添加变量"})}),a.jsx(B,{dataSource:ce,renderItem:(e,t)=>a.jsx(B.Item,{actions:[a.jsx(l,{size:"small",icon:a.jsx(J,{}),onClick:()=>he(e)},"edit"),a.jsx(l,{size:"small",danger:!0,icon:a.jsx(X,{}),onClick:()=>{const e=ce.filter((e,n)=>n!==t);de(e)}},"delete")],children:a.jsx(B.Item.Meta,{title:a.jsxs("div",{children:[a.jsx(W,{color:"blue",children:e.chineseName}),a.jsx(W,{color:"green",children:e.category}),a.jsx(W,{color:"orange",children:e.dataType}),e.required&&a.jsx(W,{color:"red",children:"必填"})]}),description:a.jsxs("div",{children:[a.jsxs("div",{children:[a.jsx(ze,{strong:!0,children:"标准名称:"})," ",e.standardName]}),a.jsxs("div",{children:[a.jsx(ze,{strong:!0,children:"英文名称:"})," ",e.englishName]}),a.jsxs("div",{children:[a.jsx(ze,{strong:!0,children:"描述:"})," ",e.description]}),e.defaultValue&&a.jsxs("div",{children:[a.jsx(ze,{strong:!0,children:"默认值:"})," ",e.defaultValue]})]})})})})]}),pe&&a.jsx(N,{title:"编辑变量",open:!!pe,onCancel:()=>he(null),onOk:()=>{const e=ce.findIndex(e=>e.standardName===pe.standardName);if(e>=0){const t=[...ce];t[e]=pe,de(t)}he(null),w.success("变量已更新")},children:a.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px"},children:[a.jsxs("div",{children:[a.jsx(ze,{strong:!0,children:"中文名称:"}),a.jsx(c,{value:pe.chineseName,onChange:e=>he({...pe,chineseName:e.target.value}),style:{marginTop:"4px"}})]}),a.jsxs("div",{children:[a.jsx(ze,{strong:!0,children:"标准名称:"}),a.jsx(c,{value:pe.standardName,onChange:e=>he({...pe,standardName:e.target.value}),style:{marginTop:"4px"}})]}),a.jsxs("div",{children:[a.jsx(ze,{strong:!0,children:"英文名称:"}),a.jsx(c,{value:pe.englishName,onChange:e=>he({...pe,englishName:e.target.value}),style:{marginTop:"4px"}})]}),a.jsxs("div",{children:[a.jsx(ze,{strong:!0,children:"类别:"}),a.jsxs(x,{value:pe.category,onChange:e=>he({...pe,category:e}),style:{width:"100%",marginTop:"4px"},children:[a.jsx(Ae,{value:"basic",children:"基本信息"}),a.jsx(Ae,{value:"contact",children:"联系信息"}),a.jsx(Ae,{value:"academic",children:"学业信息"}),a.jsx(Ae,{value:"approval",children:"审批信息"}),a.jsx(Ae,{value:"system",children:"系统信息"}),a.jsx(Ae,{value:"custom",children:"自定义"})]})]}),a.jsxs("div",{children:[a.jsx(ze,{strong:!0,children:"数据类型:"}),a.jsxs(x,{value:pe.dataType,onChange:e=>he({...pe,dataType:e}),style:{width:"100%",marginTop:"4px"},children:[a.jsx(Ae,{value:"string",children:"文本"}),a.jsx(Ae,{value:"number",children:"数字"}),a.jsx(Ae,{value:"date",children:"日期"}),a.jsx(Ae,{value:"boolean",children:"布尔值"})]})]}),a.jsxs("div",{children:[a.jsx(ze,{strong:!0,children:"默认值:"}),a.jsx(c,{value:pe.defaultValue||"",onChange:e=>he({...pe,defaultValue:e.target.value}),style:{marginTop:"4px"},placeholder:"可选"})]}),a.jsxs("div",{style:{gridColumn:"1 / -1"},children:[a.jsx(ze,{strong:!0,children:"描述:"}),a.jsx(c.TextArea,{value:pe.description,onChange:e=>he({...pe,description:e.target.value}),style:{marginTop:"4px"},rows:3})]})]})})]})},{Option:Oe}=x,{TextArea:Ve}=c,{Text:Le,Paragraph:Me}=G,Re=Se.getInstance(),Pe=Te.getInstance(),He=fe.getInstance();_e.getInstance();const Be=ve.getInstance();be.getInstance();const We=je.getInstance(),Ye=({templateModalVisible:e,setTemplateModalVisible:t,templates:n,setTemplates:s,previewModalVisible:r,setPreviewModalVisible:p,previewingCertificate:h,onActualPrint:y,generateCertificateContent:f,fileList:j,onFileChange:v})=>{const[w]=K.useForm(),[S,C]=N.useModal(),{message:T}=Z.useApp(),[D,I]=i.useState(null),[k,_]=i.useState(""),[F,E]=i.useState(!1),[z,A]=i.useState(!1),[$,q]=i.useState(""),[O,V]=i.useState([]),[L,M]=i.useState([]),[R,H]=i.useState(""),[Y,U]=i.useState(""),[G,le]=i.useState(""),[oe,ce]=i.useState(""),[de,me]=i.useState(null),[ue,pe]=i.useState(null),[he,ge]=i.useState([]),[xe,ye]=i.useState(!1),[fe,je]=i.useState(""),[ve,be]=i.useState(!1),[Ne,we]=i.useState(!1),[Se,Ce]=i.useState(""),[Te,De]=i.useState([]),[Ie,ke]=i.useState(null),[_e,Fe]=i.useState(null),[Ee,ze]=i.useState(!1),[Ae,$e]=i.useState({}),[Ve,Me]=i.useState(!1),[Ye,Ue]=i.useState(""),[Ge,Je]=i.useState([]),[Xe,Ke]=i.useState(!1),[Ze,Qe]=i.useState([]);Q.useEffect(()=>{(()=>{const e=We.getStandardTemplates();Je(e),console.log("加载标准模板:",e)})()},[]);Q.useEffect(()=>{if(r&&h&&n.length>0){(async()=>{E(!0);try{const e={enrollment:"enrollment",graduation:"graduation",conduct:"conduct",academic:"academic",internship:"internship",other:"other","在读证明":"enrollment","毕业证明":"graduation","品行证明":"conduct","成绩证明":"academic","实习证明":"internship","其他证明":"other"};let t=h.templateId?n.find(e=>e.id===h.templateId):null;if(!t){const a=e[h.certificateType]||"other";t=n.find(e=>e.type===a)}!t&&n.length>0&&(t=n[0]),t||(t={id:"default",name:"默认证明模板",type:"other",content:"证明文件\n\n兹证明 {{studentName}} 同学，学号：{{studentId}}，系我校{{college}}{{major}}专业{{class}}班学生。\n\n该生现为我校在读学生，学习情况良好。\n\n特此证明。\n\n{{schoolName}}\n{{currentDate}}",variables:["studentName","studentId","college","major","class","schoolName","currentDate"],createdAt:(new Date).toISOString().split("T")[0],updatedAt:(new Date).toISOString().split("T")[0]});const a=await f(h,t);_(a)}catch(e){console.error("生成预览内容失败:",e);try{let e=h.templateId?n.find(e=>e.id===h.templateId):null;if(!e){const t={enrollment:"enrollment",graduation:"graduation",conduct:"conduct",academic:"academic",internship:"internship",other:"other","在读证明":"enrollment","毕业证明":"graduation","品行证明":"conduct","成绩证明":"academic","实习证明":"internship","其他证明":"other"}[h.certificateType]||"other";e=n.find(e=>e.type===t)}!e&&n.length>0&&(e=n[0]),e||(e={id:"default",name:"默认证明模板",type:"other",content:"证明文件\n\n兹证明 {{studentName}} 同学，学号：{{studentId}}，系我校{{college}}{{major}}专业{{class}}班学生。\n\n该生现为我校在读学生，学习情况良好。\n\n特此证明。\n\n{{schoolName}}\n{{currentDate}}",variables:["studentName","studentId","college","major","class","schoolName","currentDate"],createdAt:(new Date).toISOString().split("T")[0],updatedAt:(new Date).toISOString().split("T")[0]});let t=await f(h,e);if(t.includes("<")&&t.includes(">"))_(t),q(t);else{const e=t.replace(/\n/g,"<br>").replace(/\t/g,"&nbsp;&nbsp;&nbsp;&nbsp;").replace(/  /g,"&nbsp;&nbsp;");_(e),q(e)}}catch(t){console.error("降级处理也失败:",t),_("生成预览内容失败，请稍后重试")}}finally{E(!1)}})()}},[r,h,n,f]);const et=e=>{var t;return null==(t=e.split(".").pop())||t.toLowerCase(),e.includes("请假")?`学生请假申请单\n\n学生姓名：[学生姓名]\n学号：[学号]\n班级：[班级]\n专业：[专业]\n\n请假事由：[请假原因]\n请假时间：[开始日期] 至 [结束日期]\n联系电话：[联系电话]\n\n申请日期：[当前日期]\n\n学生签名：________________\n\n注：此模板基于文档 "${e}" 生成`:e.includes("在读证明")?`在读证明\n\n兹证明 [学生姓名] 同学，学号：[学号]，系我校[学院][专业]专业[班级]班学生。\n\n该生现为我校在读学生，学习情况良好。\n\n特此证明。\n\n[学院名称]\n[当前日期]\n\n注：此模板基于文档 "${e}" 生成`:e.includes("补办")?`补办学生证申请表\n\n学生姓名：[学生姓名]\n学号：[学号]\n班级：[班级]\n专业：[专业]\n\n遗失原因：[遗失原因]\n联系电话：[联系电话]\n\n申请日期：[申请日期]\n\n学生签名：________________\n\n注：此模板基于文档 "${e}" 生成`:e.includes("考试证明")?`学生考试证明\n\n兹证明 [学生姓名] 同学，学号：[学号]，系我校[学院][专业]专业[班级]班学生。\n\n该生参加了 [考试名称] 考试，考试日期：[考试日期]，考试结果：[考试结果]。\n\n特此证明。\n\n[学院名称]\n[当前日期]\n\n注：此模板基于文档 "${e}" 生成`:e.includes("违纪处分")?`学生违纪处分告知书\n\n学生姓名：[学生姓名]\n学号：[学号]\n班级：[班级]\n专业：[专业]\n\n违纪类型：[违纪类型]\n处分类型：[处分类型]\n通知日期：[通知日期]\n\n[学院名称]\n[当前日期]\n\n注：此模板基于文档 "${e}" 生成`:`证明文件\n\n学生姓名：[学生姓名]\n学号：[学号]\n班级：[班级]\n专业：[专业]\n\n[学院名称]\n[当前日期]\n\n注：此模板基于文档 "${e}" 生成`},tt=e=>{const t=/{{(\w+)}}/g,n=[];let a;for(;null!==(a=t.exec(e));)n.includes(a[1])||n.push(a[1]);return n},nt=(e,t)=>{let n=e;const a={studentName:"张三",studentId:"2024001001",class:"计科2401",major:"计算机科学与技术",college:"计算机与人工智能学院",schoolName:"银川科技学院",currentDate:(new Date).toLocaleDateString("zh-CN"),purpose:"银行开户",gender:"男",dormitory:"A栋201",phone:"13800138000",email:"<EMAIL>",grade:"2024级",leaveType:"事假",leaveReason:"家中有事",startDate:"2024-01-15",startTime:"08:00",endDate:"2024-01-17",endTime:"18:00",leaveDays:"3",emergencyContact:"张父",emergencyPhone:"13900139000",relationship:"父亲",applicationDate:(new Date).toLocaleDateString("zh-CN"),advisorOpinion:"",advisorDate:"",collegeOpinion:"",collegeDate:""};Object.keys(a).forEach(e=>{const t=new RegExp(`{{${e}}}`,"g");n=n.replace(t,a[e]||`{{${e}}}`)});const s={"[学生姓名]":a.studentName,"[姓名]":a.studentName,"[学号]":a.studentId,"[班级]":a.class,"[专业]":a.major,"[学院]":a.college,"[性别]":a.gender,"[联系电话]":a.phone,"[邮箱]":a.email,"[年级]":a.grade,"[宿舍]":a.dormitory,"[学校名称]":a.schoolName,"[当前日期]":a.currentDate};Object.keys(s).forEach(e=>{const t=new RegExp(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"g");n=n.replace(t,s[e]||e)}),U(n)},at=e=>({"学生姓名":"studentName","姓名":"studentName","学号":"studentId","班级":"class","专业":"major","学院":"college","学院名称":"college","学校名称":"schoolName","当前日期":"currentDate","申请用途":"purpose","入学日期":"enrollmentDate","毕业日期":"graduationDate","联系电话":"phone","邮箱":"email","性别":"gender","年级":"grade"}[e]||e.toLowerCase().replace(/\s+/g,"")),st=e=>{const t=e.toLowerCase();return t.includes("请假")?"leave":t.includes("在读")?"enrollment":t.includes("毕业")?"graduation":t.includes("成绩")?"academic":t.includes("品行")?"conduct":t.includes("实习")?"internship":"other"},rt=e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),it=()=>{me(null),setParsedContent(""),ge([]),je(""),be(!1),w.setFieldsValue({content:""}),H(""),U("")},lt=[{id:"leave_application_ai",name:"计算机与人工智能学院学生请假申请单",type:"leave",fileName:"计算机与人工智能学院学生请假申请单.docx",variables:["studentName","studentId","class","major","gender","dormitory","contactPhone","leaveType","leaveReason","startDate","startTime","endDate","endTime","leaveDays","emergencyContact","emergencyPhone","relationship","applicationDate","advisorOpinion","advisorDate","collegeOpinion","collegeDate"],isDocumentTemplate:!0,fileType:"docx",content:"                    计算机与人工智能学院学生请假申请单\n\n┌─────────────────────────────────────────────────────────────────┐\n│                           申请人基本信息                           │\n├─────────────────────────────────────────────────────────────────┤\n│  姓    名：{{studentName}}                学    号：{{studentId}}  │\n│  班    级：{{class}}                      专    业：{{major}}      │\n│  性    别：{{gender}}                     宿 舍 号：{{dormitory}}  │\n│  联系电话：{{contactPhone}}                                        │\n└─────────────────────────────────────────────────────────────────┘\n\n┌─────────────────────────────────────────────────────────────────┐\n│                             请假信息                             │\n├─────────────────────────────────────────────────────────────────┤\n│  请假类型：{{leaveType}}                                          │\n│  请假原因：{{leaveReason}}                                        │\n│  请假时间：从 {{startDate}} {{startTime}} 到 {{endDate}} {{endTime}} │\n│  请假天数：{{leaveDays}} 天                                       │\n└─────────────────────────────────────────────────────────────────┘\n\n┌─────────────────────────────────────────────────────────────────┐\n│                           紧急联系人                             │\n├─────────────────────────────────────────────────────────────────┤\n│  联系人姓名：{{emergencyContact}}                                 │\n│  联系人电话：{{emergencyPhone}}                                   │\n│  与学生关系：{{relationship}}                                     │\n└─────────────────────────────────────────────────────────────────┘\n\n学生承诺：\n    本人保证请假期间注意人身安全，按时返校销假。如有意外，后果自负。\n\n学生签名：________________                    申请日期：{{applicationDate}}\n\n┌─────────────────────────────────────────────────────────────────┐\n│                             审批意见                             │\n├─────────────────────────────────────────────────────────────────┤\n│  辅导员意见：                                                     │\n│  □ 同意    □ 不同意                                              │\n│  意见：{{advisorOpinion}}                                        │\n│  辅导员签名：________________    日期：{{advisorDate}}            │\n│                                                                 │\n│  学院意见：                                                       │\n│  □ 同意    □ 不同意                                              │\n│  意见：{{collegeOpinion}}                                        │\n│  学院负责人签名：________________    日期：{{collegeDate}}        │\n└─────────────────────────────────────────────────────────────────┘\n\n备注：\n1. 请假3天以内由辅导员审批\n2. 请假3天以上需学院审批\n3. 请假期间如有紧急情况请及时联系辅导员"}],ot={name:"file",multiple:!1,accept:".doc,.docx,.pdf,.txt",fileList:j,onChange:e=>{v(e),e.file&&"removed"!==e.file.status&&(async e=>{ye(!0),me(e),Ce("");try{console.log(`开始处理上传的文件: ${e.name}, 类型: ${e.type}, 大小: ${e.size} bytes`);let s=null,r=null;try{s=await Re.parseDocument(e),console.log("使用完整文档解析服务成功")}catch(t){console.warn("完整文档解析失败，尝试简化解析器:",t);try{r=await Pe.parseDocument(e),console.log("使用简化文档解析器成功"),s={id:r.id,fileName:r.fileName,fileType:r.fileType,originalContent:r.originalContent,parsedContent:r.parsedContent,variables:r.variables.map(e=>({id:e.id,originalText:e.originalText,variableName:e.variableName,chineseName:e.chineseName,category:e.category,required:e.required,defaultValue:e.defaultValue})),metadata:r.metadata,formatInfo:{hasTable:r.formatInfo.hasTable,hasBorder:r.formatInfo.hasBorder,fontFamily:"SimSun",fontSize:14,lineHeight:1.6}}}catch(n){throw console.error("简化解析器也失败:",n),new Error("所有解析方法都失败了")}}if(s){pe(s),ge(s.variables),je(s.originalContent);try{console.log("开始增强变量检测...");const e=Be.detectVariables(s.parsedContent);console.log("增强变量检测结果:",e),ke(e),De(e.variables),e.variables.length>0&&T.success(`智能变量检测完成！检测到 ${e.variables.length} 个变量，置信度: ${(100*e.confidence).toFixed(1)}%`)}catch(a){console.warn("增强变量检测失败，使用基础检测结果:",a)}const t=s.metadata.title||e.name.replace(/\.[^/.]+$/,"");w.setFieldsValue({content:s.parsedContent,name:t,type:st(e.name)}),H(s.parsedContent),nt(s.parsedContent),be(!0),T.success(`文件 "${e.name}" 解析完成，检测到 ${s.variables.length} 个基础变量`),we(!0)}}catch(t){console.error("文件解析完全失败:",t);const n=t instanceof Error?t.message:"文件解析失败";Ce(n),T.error(`文件解析失败: ${n}`);const a=e.name.replace(/\.[^/.]+$/,"");w.setFieldsValue({name:a,content:"文档解析失败，请手动输入模板内容。",type:"other"})}finally{ye(!1)}})(e.file.originFileObj||e.file)},beforeUpload:e=>{if(!("application/vnd.openxmlformats-officedocument.wordprocessingml.document"===e.type||"application/msword"===e.type||"application/pdf"===e.type||"text/plain"===e.type))return T.error("只能上传 Word 文档(.doc/.docx)、PDF 文件或文本文件！"),!1;return e.size/1024/1024<10||T.error("文件大小不能超过 10MB！"),!1}},ct=()=>a.jsxs(N,{title:a.jsxs("div",{children:[a.jsx(Le,{strong:!0,children:"智能变量映射确认"}),Ie&&a.jsxs(W,{color:"blue",style:{marginLeft:"8px"},children:["置信度: ",(100*Ie.confidence).toFixed(1),"%"]})]}),open:Ne&&Te.length>0,onCancel:()=>we(!1),width:1200,style:{top:20},styles:{body:{maxHeight:"calc(100vh - 200px)",overflowY:"auto"}},footer:[a.jsx(l,{onClick:()=>we(!1),children:"取消"},"cancel"),a.jsx(l,{type:"primary",onClick:()=>{we(!1),ze(!0),T.success("变量映射已确认")},children:"确认映射"},"confirm")],children:[Ie&&a.jsx("div",{style:{marginBottom:"16px"},children:a.jsx(Alert,{message:"智能变量检测结果",description:a.jsxs("div",{children:[a.jsxs("p",{children:["检测到 ",Te.length," 个变量，整体置信度: ",(100*Ie.confidence).toFixed(1),"%"]}),Ie.suggestions.length>0&&a.jsxs("div",{children:[a.jsx(Le,{strong:!0,children:"建议:"}),a.jsx("ul",{style:{marginTop:"4px",marginBottom:"0"},children:Ie.suggestions.map((e,t)=>a.jsx("li",{children:e},t))})]})]}),type:"info",showIcon:!0})}),a.jsx("div",{style:{maxHeight:"500px",overflowY:"auto"},children:Te.map((e,t)=>a.jsxs(d,{size:"small",style:{marginBottom:"12px"},children:[a.jsxs(m,{gutter:16,align:"middle",children:[a.jsxs(u,{span:6,children:[a.jsx(Le,{strong:!0,children:"原始文本:"}),a.jsx("div",{style:{padding:"4px 8px",backgroundColor:"#f0f0f0",borderRadius:"4px",marginTop:"4px",fontFamily:"monospace"},children:e.original})]}),a.jsxs(u,{span:6,children:[a.jsx(Le,{strong:!0,children:"中文名称:"}),a.jsx(c,{value:e.chineseName,onChange:e=>{const n=[...Te];n[t].chineseName=e.target.value,De(n)},style:{marginTop:"4px"}})]}),a.jsxs(u,{span:6,children:[a.jsx(Le,{strong:!0,children:"标准名称:"}),a.jsx(c,{value:e.standardName,onChange:e=>{const n=[...Te];n[t].standardName=e.target.value,De(n)},style:{marginTop:"4px"}})]}),a.jsxs(u,{span:6,children:[a.jsx(Le,{strong:!0,children:"英文名称:"}),a.jsx(c,{value:e.englishName,onChange:e=>{const n=[...Te];n[t].englishName=e.target.value,De(n)},style:{marginTop:"4px"}})]})]}),a.jsxs(m,{gutter:16,style:{marginTop:"12px"},children:[a.jsxs(u,{span:6,children:[a.jsx(Le,{strong:!0,children:"类别:"}),a.jsxs(x,{value:e.category,onChange:e=>{const n=[...Te];n[t].category=e,De(n)},style:{width:"100%",marginTop:"4px"},children:[a.jsx(Oe,{value:"basic",children:"基本信息"}),a.jsx(Oe,{value:"contact",children:"联系信息"}),a.jsx(Oe,{value:"academic",children:"学业信息"}),a.jsx(Oe,{value:"approval",children:"审批信息"}),a.jsx(Oe,{value:"system",children:"系统信息"}),a.jsx(Oe,{value:"custom",children:"自定义"})]})]}),a.jsxs(u,{span:6,children:[a.jsx(Le,{strong:!0,children:"数据类型:"}),a.jsxs(x,{value:e.dataType,onChange:e=>{const n=[...Te];n[t].dataType=e,De(n)},style:{width:"100%",marginTop:"4px"},children:[a.jsx(Oe,{value:"string",children:"文本"}),a.jsx(Oe,{value:"number",children:"数字"}),a.jsx(Oe,{value:"date",children:"日期"}),a.jsx(Oe,{value:"boolean",children:"布尔值"})]})]}),a.jsxs(u,{span:6,children:[a.jsx(Le,{strong:!0,children:"是否必填:"}),a.jsx(Switch,{checked:e.required,onChange:e=>{const n=[...Te];n[t].required=e,De(n)},style:{marginTop:"4px"}})]}),a.jsxs(u,{span:6,children:[a.jsx(Le,{strong:!0,children:"默认值:"}),a.jsx(c,{value:e.defaultValue||"",onChange:e=>{const n=[...Te];n[t].defaultValue=e.target.value,De(n)},placeholder:"可选",style:{marginTop:"4px"}})]})]}),a.jsx(m,{style:{marginTop:"8px"},children:a.jsx(u,{span:24,children:a.jsxs(Le,{type:"secondary",style:{fontSize:"12px"},children:["描述: ",e.description]})})})]},t))})]});return a.jsxs(a.Fragment,{children:[a.jsx(ct,{}),a.jsx(N,{title:"证明模板管理",open:e,onCancel:()=>{t(!1),I(null),w.resetFields(),it()},footer:null,width:"95vw",style:{top:20},styles:{body:{maxHeight:"calc(100vh - 140px)",overflowY:"auto",padding:"20px"}},centered:!0,children:a.jsxs(m,{gutter:24,children:[a.jsx(u,{span:10,children:a.jsxs("div",{className:"space-y-4",children:[a.jsx(d,{title:a.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[a.jsx("span",{children:"标准模板集成"}),a.jsx(o,{children:a.jsx(l,{size:"small",type:"primary",loading:Xe,onClick:async()=>{Ke(!0);try{console.log("开始批量集成所有标准模板");const e=await We.integrateAllStandardTemplates(),t=e.filter(e=>e.success).length,n=e.filter(e=>!e.success).length;if(t>0){T.success(`成功集成 ${t} 个标准模板${n>0?`，${n} 个失败`:""}`);const a=e.filter(e=>e.success&&e.template).map(e=>e.template);s(e=>[...e,...a])}else T.error("所有模板集成都失败了");Qe(e)}catch(e){console.error("批量模板集成失败:",e),T.error(`批量集成失败: ${e.message}`)}finally{Ke(!1)}},children:"批量集成"})})]}),size:"small",style:{marginBottom:"16px",maxHeight:"300px"},styles:{body:{overflowY:"auto"}},children:0===Ge.length?a.jsxs("div",{className:"text-center py-4 text-gray-500",children:[a.jsx(ee,{style:{fontSize:32,marginBottom:8}}),a.jsx("p",{children:"暂无标准模板"})]}):a.jsx(B,{dataSource:Ge,renderItem:e=>a.jsx(B.Item,{actions:[a.jsx(l,{size:"small",type:"primary",loading:Xe,onClick:()=>(async e=>{var t;Ke(!0);try{console.log("开始集成标准模板:",e);const n=await We.integrateStandardTemplate(e);n.success?(T.success(`标准模板 "${null==(t=n.template)?void 0:t.name}" 集成成功！`),n.template&&s(e=>[...e,n.template]),Qe(e=>[...e,n])):T.error(`模板集成失败: ${n.error}`)}catch(n){console.error("标准模板集成失败:",n),T.error(`模板集成失败: ${n.message}`)}finally{Ke(!1)}})(e.id),children:"集成"},"integrate")],children:a.jsx(B.Item.Meta,{title:e.name,description:a.jsxs("div",{children:[a.jsx(W,{color:"blue",children:e.category}),a.jsx(W,{color:"orange",children:e.fileName}),a.jsx(Le,{type:"secondary",style:{fontSize:"12px"},children:e.description})]})})})})}),a.jsx(d,{title:"预定义模板",size:"small",style:{maxHeight:"400px"},styles:{body:{overflowY:"auto"}},children:0===lt.length?a.jsxs("div",{className:"text-center py-8 text-gray-500",children:[a.jsx(ee,{style:{fontSize:48,marginBottom:16}}),a.jsx("p",{children:"暂无预定义模板"}),a.jsx("p",{className:"text-sm",children:"预定义模板已被清空，请创建自定义模板"})]}):a.jsx(B,{dataSource:lt,renderItem:e=>{var t,r;return a.jsx(B.Item,{actions:[a.jsx(l,{type:"text",icon:a.jsx(ee,{}),onClick:()=>(async e=>{try{ye(!0);const t=e.content||et(e.fileName),a=[{id:Date.now().toString(),name:e.name,type:e.type,content:t,variables:e.variables,fileName:e.fileName,isDocumentTemplate:e.isDocumentTemplate,fileType:e.fileType,createdAt:(new Date).toISOString().split("T")[0],updatedAt:(new Date).toISOString().split("T")[0]},...n];s(a),localStorage.setItem("certificateTemplates",JSON.stringify(a)),T.success(`已添加模板 "${e.name}" 到自定义模板列表`)}catch(t){console.error("添加预定义模板失败:",t),T.error("添加模板失败，请稍后重试")}finally{ye(!1)}})(e),children:"使用模板"}),a.jsx(l,{type:"text",icon:a.jsx(P,{}),onClick:()=>(async e=>{var t,n,s;try{let s="";e.content?s=e.content:e.isDocumentTemplate&&e.fileName&&(s=et(e.fileName)),S.info({title:`模板预览 - ${e.name}`,content:a.jsxs("div",{children:[a.jsxs("p",{children:[a.jsx("strong",{children:"文件名："}),e.fileName]}),a.jsxs("p",{children:[a.jsx("strong",{children:"类型："}),e.type]}),e.isDocumentTemplate&&a.jsxs("p",{children:[a.jsx("strong",{children:"文档类型："}),a.jsxs(W,{color:"blue",icon:a.jsx(ee,{}),children:[null==(t=e.fileType)?void 0:t.toUpperCase()," 文档"]})]}),a.jsx("p",{children:a.jsx("strong",{children:"可用变量："})}),a.jsx("div",{style:{marginLeft:16,marginBottom:16},children:e.variables.map(e=>a.jsx(W,{color:"blue",style:{margin:2},children:`{{${e}}}`},e))}),a.jsxs("div",{style:{marginTop:16},children:[a.jsx("p",{children:a.jsx("strong",{children:"模板内容预览："})}),a.jsx("div",{style:{backgroundColor:"#fff",padding:16,borderRadius:4,maxHeight:400,overflowY:"auto",fontSize:"14px",lineHeight:"1.6",border:"1px solid #d9d9d9",fontFamily:"SimSun, serif"},dangerouslySetInnerHTML:{__html:s}})]}),a.jsxs("p",{style:{marginTop:16},children:[a.jsx("strong",{children:"说明："}),e.isDocumentTemplate?`此模板基于${null==(n=e.fileType)?void 0:n.toUpperCase()}文档，包含${e.variables.length}个变量。方括号标记的内容将被替换为实际数据。`:"此模板基于文本内容，使用时会自动替换变量内容。"]})]}),width:900})}catch(r){console.error("预览模板失败:",r),S.info({title:`模板预览 - ${e.name}`,content:a.jsxs("div",{children:[a.jsxs("p",{children:[a.jsx("strong",{children:"文件名："}),e.fileName]}),a.jsxs("p",{children:[a.jsx("strong",{children:"类型："}),e.type]}),e.isDocumentTemplate&&a.jsxs("p",{children:[a.jsx("strong",{children:"文档类型："}),a.jsxs(W,{color:"blue",icon:a.jsx(ee,{}),children:[null==(s=e.fileType)?void 0:s.toUpperCase()," 文档"]})]}),a.jsx("p",{children:a.jsx("strong",{children:"可用变量："})}),a.jsx("div",{style:{marginLeft:16},children:e.variables.map(e=>a.jsx(W,{color:"blue",style:{margin:2},children:`{{${e}}}`},e))}),a.jsxs("p",{style:{marginTop:16},children:[a.jsx("strong",{children:"说明："}),"此模板基于Word/PDF文档，使用时会自动替换变量内容。"]})]}),width:600})}})(e),children:"预览"})],children:a.jsx(B.Item.Meta,{title:e.name,description:a.jsxs("div",{children:[a.jsx(W,{color:"green",children:e.type}),a.jsx(W,{color:"orange",children:e.fileName}),e.isDocumentTemplate&&a.jsxs(W,{color:"blue",icon:a.jsx(ee,{}),children:[null==(t=e.fileType)?void 0:t.toUpperCase()," 文档"]}),a.jsxs(Le,{type:"secondary",children:["变量数量: ",(null==(r=e.variables)?void 0:r.length)||0]})]})})})}})}),a.jsx(d,{title:"自定义模板",size:"small",extra:n.length>0&&a.jsx(l,{type:"text",danger:!0,size:"small",icon:a.jsx(X,{}),onClick:()=>{S.confirm({title:"确认删除所有模板",content:a.jsxs("div",{children:[a.jsx("p",{children:"确定要删除所有自定义模板吗？"}),a.jsxs("p",{style:{color:"#ff4d4f",fontSize:"14px"},children:["⚠️ 此操作将删除 ",n.length," 个模板，且不可撤销！"]}),a.jsx("p",{style:{color:"#666",fontSize:"12px"},children:"注：删除后可以重新创建模板或上传文档。"})]}),okText:"确认删除",okType:"danger",cancelText:"取消",onOk:()=>{s([]),localStorage.removeItem("certificateTemplates"),T.success(`已删除所有 ${n.length} 个自定义模板`)}})},children:"删除所有模板"}),children:0===n.length?a.jsxs("div",{className:"text-center py-8 text-gray-500",children:[a.jsx(ee,{style:{fontSize:48,marginBottom:16}}),a.jsx("p",{children:"暂无自定义模板"}),a.jsx("p",{className:"text-sm",children:"请创建新模板或上传文档"})]}):a.jsx(B,{dataSource:n,renderItem:e=>{var t;return a.jsx(B.Item,{actions:[a.jsx(l,{type:"text",icon:a.jsx(J,{}),onClick:()=>(e=>{I(e),w.setFieldsValue(e)})(e),children:"编辑"}),a.jsx(l,{type:"text",danger:!0,icon:a.jsx(X,{}),onClick:()=>(e=>{S.confirm({title:"确认删除",content:`确定要删除模板 "${e.name}" 吗？`,onOk:()=>{const t=n.filter(t=>t.id!==e.id);s(t),localStorage.setItem("certificateTemplates",JSON.stringify(t)),T.success("模板删除成功")}})})(e),children:"删除"})],children:a.jsx(B.Item.Meta,{title:e.name,description:a.jsxs("div",{children:[a.jsx(W,{color:"blue",children:e.type}),a.jsxs(Le,{type:"secondary",children:["变量数量: ",(null==(t=e.variables)?void 0:t.length)||0]})]})})})}})})]})}),a.jsx(u,{span:14,children:a.jsx(d,{title:D?"编辑模板":"新增模板",size:"small",children:a.jsxs(K,{form:w,layout:"vertical",onFinish:e=>{const t={...e,variables:tt(e.content)};if(D){const e=n.map(e=>e.id===D.id?{...e,...t,updatedAt:(new Date).toISOString().split("T")[0]}:e);s(e),localStorage.setItem("certificateTemplates",JSON.stringify(e)),T.success("模板更新成功")}else{const e=[{...t,id:Date.now().toString(),createdAt:(new Date).toISOString().split("T")[0],updatedAt:(new Date).toISOString().split("T")[0]},...n];s(e),localStorage.setItem("certificateTemplates",JSON.stringify(e)),T.success("模板创建成功")}w.resetFields(),I(null)},children:[a.jsx(K.Item,{label:"模板名称",name:"name",rules:[{required:!0,message:"请输入模板名称"}],children:a.jsx(c,{placeholder:"请输入模板名称"})}),a.jsx(K.Item,{label:"证明类型",name:"type",rules:[{required:!0,message:"请选择证明类型"}],children:a.jsx(x,{placeholder:"选择类型",onChange:e=>{ce(e);const t={enrollment:["studentName","studentId","class","major","college","enrollmentDate","currentDate"],leave:["studentName","studentId","class","major","leaveReason","startDate","endDate","phone"],replacement:["studentName","studentId","class","major","lossReason","applicationDate","phone"],exam:["studentName","studentId","class","major","examName","examDate","examResult"],discipline:["studentName","studentId","class","major","violationType","punishmentType","noticeDate"],graduation:["studentName","studentId","class","major","college","graduationDate","currentDate"],conduct:["studentName","studentId","class","major","college","currentDate"],academic:["studentName","studentId","class","major","gpa","ranking","currentDate"],internship:["studentName","studentId","class","major","currentDate"],scholarship:["studentName","studentId","class","major","awards","currentDate"],poverty:["studentName","studentId","class","major","currentDate"],health:["studentName","studentId","class","major","currentDate"],transfer:["studentName","studentId","class","major","college","currentDate"],suspension:["studentName","studentId","class","major","currentDate"],resumption:["studentName","studentId","class","major","currentDate"],other:["studentName","studentId","class","major","currentDate"]}[e]||["studentName","studentId","currentDate"];V(t)},children:[{value:"enrollment",label:"在读证明",color:"blue"},{value:"leave",label:"请假申请",color:"yellow"},{value:"replacement",label:"补办申请",color:"red"},{value:"exam",label:"考试证明",color:"green"},{value:"discipline",label:"违纪处分",color:"volcano"},{value:"graduation",label:"毕业证明",color:"green"},{value:"conduct",label:"品行证明",color:"purple"},{value:"academic",label:"成绩证明",color:"orange"},{value:"internship",label:"实习证明",color:"cyan"},{value:"scholarship",label:"奖学金证明",color:"gold"},{value:"poverty",label:"贫困证明",color:"magenta"},{value:"health",label:"健康证明",color:"lime"},{value:"transfer",label:"转学证明",color:"geekblue"},{value:"suspension",label:"休学证明",color:"purple"},{value:"resumption",label:"复学证明",color:"cyan"},{value:"other",label:"其他证明",color:"default"}].map(e=>a.jsx(Oe,{value:e.value,children:a.jsx(W,{color:e.color,style:{marginRight:8},children:e.label})},e.value))})}),a.jsx(K.Item,{label:"模板内容编辑",name:"content",rules:[{required:!0,message:"请输入模板内容"}],extra:"富文本编辑器：支持格式设置、变量管理、实时预览等功能",children:a.jsx("div",{style:{height:"500px"},children:a.jsx(qe,{initialContent:w.getFieldValue("content")||"",variables:Te.length>0?Te:[],onContentChange:e=>{w.setFieldsValue({content:e}),H(e),nt(e)},onSave:e=>{w.setFieldsValue({content:e}),H(e),nt(e),T.success("模板内容已保存")},onVariablesChange:e=>{De(e),T.success("变量设置已更新")}})})}),a.jsxs(K.Item,{label:"上传模板文件",extra:"上传Word/PDF文档，系统将自动解析内容并识别变量",children:[a.jsxs(te.Dragger,{...ot,disabled:xe,children:[a.jsx("div",{className:"ant-upload-drag-icon",children:xe?a.jsx(ne,{}):a.jsx(ae,{})}),a.jsx("p",{className:"ant-upload-text",children:xe?"正在解析文档...":"点击或拖拽文件到此区域上传"}),a.jsxs("p",{className:"ant-upload-hint",children:["支持 Word (.docx/.doc)、PDF (.pdf)、文本 (.txt) 格式",a.jsx("br",{}),a.jsx("strong",{children:"推荐："}),".docx格式解析效果最佳，.doc格式支持有限",a.jsx("br",{}),"如解析失败，建议将.doc文件另存为.docx格式或复制内容到文本文件后上传"]})]}),a.jsx("div",{style:{marginTop:12,padding:12,backgroundColor:"#f0f8ff",border:"1px solid #d1ecf1",borderRadius:4},children:a.jsxs("div",{style:{fontSize:"13px",color:"#0c5460"},children:[a.jsx("strong",{children:"💡 Word文档处理建议："}),a.jsx("br",{}),"1. 如果Word文档解析失败，请打开原文档",a.jsx("br",{}),"2. 复制文档内容并粘贴到记事本中",a.jsx("br",{}),"3. 保存为.txt文件后重新上传",a.jsx("br",{}),"4. 使用 [变量名] 格式标记需要替换的内容"]})}),de&&a.jsxs("div",{className:"mt-4 p-3 bg-green-50 border border-green-200 rounded",children:[a.jsxs("div",{className:"flex items-center justify-between mb-2",children:[a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(ee,{className:"text-green-500"}),a.jsxs("span",{className:"text-green-700 font-medium",children:["已解析文档：",de.name]})]}),a.jsx(l,{size:"small",type:"text",danger:!0,onClick:it,children:"重置"})]}),he.length>0&&a.jsxs("div",{children:[a.jsxs("div",{className:"text-sm text-gray-600 mb-2",children:["检测到 ",he.length," 个潜在变量："]}),a.jsx("div",{className:"flex flex-wrap gap-1 mb-3",children:he.map((e,t)=>a.jsx(W,{color:"orange",children:"string"==typeof e?e:e.originalText||e.chineseName},t))}),a.jsx(l,{size:"small",type:"primary",onClick:()=>{let e=parsedContent;he.forEach(t=>{const n="string"==typeof t?{originalText:t,variableName:t,chineseName:t}:t,a=n.originalText||n.chineseName,s=`{{${n.variableName}}}`;e=e.replace(new RegExp(rt(a),"g"),s)}),w.setFieldsValue({content:e}),H(e),nt(e),T.success("已批量替换所有变量标记")},children:"批量替换为变量格式"})]})]})]}),ve&&he.length>0&&a.jsx(K.Item,{label:"变量标记编辑",children:a.jsx(d,{size:"small",title:"检测到的变量标记",className:"mb-4",children:a.jsxs("div",{className:"space-y-2",children:[a.jsx("div",{className:"text-sm text-gray-600 mb-3",children:"以下是从文档中检测到的变量标记，您可以逐个替换为标准变量格式："}),he.map((e,t)=>{const n="string"==typeof e?{originalText:e,variableName:e,chineseName:e}:e,s=n.originalText||n.chineseName,r=n.variableName;return n.chineseName,a.jsxs("div",{className:"flex items-center justify-between p-2 border rounded",children:[a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(W,{color:"orange",children:s}),a.jsx("span",{children:"→"}),a.jsx(W,{color:"blue",children:"{{"+r+"}}"})]}),a.jsx(l,{size:"small",type:"primary",onClick:()=>((e,t)=>{let n=w.getFieldValue("content")||parsedContent;const a=`[${e}]`,s=`{{${t}}}`;n=n.replace(new RegExp(rt(a),"g"),s),w.setFieldsValue({content:n}),H(n),nt(n);const r=he.map(n=>("string"==typeof n?at(n):n.variableName)===at(e)?t:n);ge(r)})(s,r),children:"替换"})]},t)})]})})}),a.jsxs("div",{className:"flex justify-end space-x-2",children:[a.jsx(l,{onClick:()=>{w.resetFields(),I(null)},children:"重置"}),a.jsx(l,{type:"primary",htmlType:"submit",children:D?"更新模板":"创建模板"})]})]})})})]})}),a.jsx(N,{title:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(ie,{}),a.jsx("span",{children:"证明文件预览"})]}),a.jsx(l,{size:"small",icon:z?a.jsx(P,{}):a.jsx(J,{}),onClick:()=>A(!z),children:z?"预览模式":"编辑模式"})]}),open:r,onCancel:()=>p(!1),footer:[a.jsx(l,{onClick:()=>p(!1),children:"取消"},"cancel"),(()=>{const e=n.find(e=>e.id===(null==h?void 0:h.templateId));return e&&e.isDocumentTemplate&&e.fileName?a.jsx(l,{icon:a.jsx(re,{}),onClick:()=>{const t=document.createElement("a");t.href=`/templates/${e.fileName}`,t.download=e.fileName,t.click()},children:"下载模板"},"download"):null})(),a.jsx(l,{type:"primary",icon:a.jsx(ie,{}),onClick:()=>{(e=>{const t=window.open("","_blank");if(!t)return void T.error("无法打开打印窗口，请检查浏览器设置");const n=`\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset="utf-8">\n        <title>证明文件打印</title>\n        <style>\n          @page {\n            margin: 20mm;\n            size: A4 portrait;\n          }\n\n          * {\n            box-sizing: border-box;\n          }\n\n          body {\n            font-family: 'SimSun', '宋体', serif;\n            font-size: 14px;\n            line-height: 1.6;\n            margin: 0;\n            padding: 0;\n            background: white;\n            color: #000;\n            -webkit-print-color-adjust: exact;\n            print-color-adjust: exact;\n          }\n\n          .certificate-content {\n            width: 100%;\n            max-width: 210mm;\n            min-height: 257mm; /* A4高度减去边距 */\n            margin: 0 auto;\n            padding: 0;\n            background: white;\n          }\n\n          /* 确保图片正确显示 */\n          img {\n            max-width: 100%;\n            height: auto;\n            display: block;\n            margin: 0 auto;\n          }\n\n          /* 确保表格正确显示 */\n          table {\n            width: 100%;\n            border-collapse: collapse;\n            margin: 0;\n          }\n\n          table td, table th {\n            padding: 4px 8px;\n            border: 1px solid #000;\n            text-align: left;\n            vertical-align: top;\n          }\n\n          /* 段落样式 */\n          p {\n            margin: 0 0 8px 0;\n            text-align: justify;\n          }\n\n          /* 标题样式 */\n          h1, h2, h3, h4, h5, h6 {\n            margin: 16px 0 8px 0;\n            font-weight: bold;\n          }\n\n          h1 { font-size: 24px; }\n          h2 { font-size: 20px; }\n          h3 { font-size: 16px; }\n\n          @media print {\n            body {\n              padding: 0;\n              margin: 0;\n            }\n\n            .certificate-content {\n              padding: 0;\n              margin: 0;\n              box-shadow: none;\n              border: none;\n            }\n\n            .no-print {\n              display: none !important;\n            }\n\n            /* 防止内容被分页截断 */\n            .certificate-content {\n              page-break-inside: avoid;\n            }\n\n            /* 确保字体渲染一致 */\n            * {\n              -webkit-print-color-adjust: exact;\n              print-color-adjust: exact;\n            }\n          }\n\n          @media screen {\n            body {\n              padding: 20px;\n              background: #f5f5f5;\n            }\n\n            .certificate-content {\n              background: white;\n              box-shadow: 0 4px 8px rgba(0,0,0,0.1);\n              border-radius: 2px;\n              padding: 20mm;\n            }\n          }\n        </style>\n      </head>\n      <body>\n        <div class="certificate-content">\n          ${e}\n        </div>\n        <script>\n          window.onload = function() {\n            window.print();\n            window.onafterprint = function() {\n              window.close();\n            };\n          };\n        <\/script>\n      </body>\n      </html>\n    `;t.document.write(n),t.document.close()})($||k)},children:"打印"},"print")].filter(Boolean),width:800,children:h&&a.jsxs("div",{children:[a.jsxs(se,{bordered:!0,column:2,size:"small",className:"mb-4",children:[a.jsx(se.Item,{label:"学生姓名",children:h.studentName}),a.jsx(se.Item,{label:"学号",children:h.studentId}),a.jsx(se.Item,{label:"班级",children:h.class}),a.jsx(se.Item,{label:"专业",children:h.major}),a.jsx(se.Item,{label:"证明类型",children:a.jsx(W,{color:"blue",children:{enrollment:"在读证明",leave:"请假申请",replacement:"补办申请",exam:"考试证明",discipline:"违纪处分",graduation:"毕业证明",conduct:"品行证明",academic:"成绩证明",internship:"实习证明",other:"其他证明","在读证明":"在读证明","请假申请":"请假申请","补办申请":"补办申请","考试证明":"考试证明","违纪处分":"违纪处分","毕业证明":"毕业证明","品行证明":"品行证明","成绩证明":"成绩证明","实习证明":"实习证明","其他证明":"其他证明"}[h.certificateType]||h.certificateType})}),a.jsx(se.Item,{label:"申请用途",children:h.purpose})]}),a.jsx(g,{children:a.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[a.jsxs("span",{children:["证明内容预览",z&&a.jsx("span",{style:{fontSize:"12px",color:"#666",marginLeft:"10px"},children:"(可编辑模式 - 您可以直接修改内容后打印)"})]}),z&&a.jsx(l,{size:"small",onClick:()=>{q(k),T.success("已重置为原始内容")},children:"重置内容"})]})}),(()=>{const e=n.find(e=>e.id===h.templateId);return e&&e.isDocumentTemplate&&e.fileName?a.jsxs("div",{className:"mb-4 p-3 bg-blue-50 border border-blue-200 rounded",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(ee,{className:"text-blue-500"}),a.jsxs("span",{className:"text-blue-700",children:["此证明基于文档模板：",a.jsx("strong",{children:e.fileName})]})]}),a.jsx(l,{type:"link",size:"small",icon:a.jsx(re,{}),onClick:()=>{const t=document.createElement("a");t.href=`/templates/${e.fileName}`,t.download=e.fileName,t.click()},children:"下载模板"})]}),a.jsx("p",{className:"text-sm text-gray-600 mt-2 mb-0",children:"建议下载完整的文档模板进行填写，以获得最佳的格式效果。"})]}):null})(),a.jsx(d,{className:"certificate-preview",children:a.jsx("div",{style:{fontFamily:"SimSun, 宋体, monospace",fontSize:"14px",lineHeight:"1.6",padding:"20px",background:"white",border:"1px solid #e8e8e8"},children:F?a.jsxs("div",{style:{textAlign:"center",padding:"50px"},children:[a.jsx(ne,{size:"large"}),a.jsx("p",{style:{marginTop:"16px",color:"#666"},children:"正在生成证明内容..."})]}):a.jsx("div",{style:{whiteSpace:"pre-wrap",fontFamily:"SimSun, 宋体, monospace",fontSize:"14px",lineHeight:"1.6",margin:"0",padding:"0",textAlign:"left",wordWrap:"break-word",overflowWrap:"break-word",minHeight:"400px"},children:z?a.jsxs("div",{children:[a.jsxs("div",{style:{marginBottom:"10px",padding:"8px",backgroundColor:"#f0f8ff",borderRadius:"4px",fontSize:"12px",color:"#666"},children:[a.jsx("div",{style:{marginBottom:"5px"},children:a.jsx("strong",{children:"编辑提示："})}),a.jsx("div",{children:"• 您可以直接在下方区域编辑证明内容"}),a.jsx("div",{children:"• 支持文本格式修改，包括换行、空格等"}),a.jsxs("div",{style:{color:"#ff6b35"},children:["• ",a.jsx("strong",{children:"注意："}),"图片和表格受保护，鼠标悬停时会有视觉提示"]}),a.jsx("div",{children:'• 编辑完成后点击"打印"按钮将使用修改后的内容'}),a.jsx("div",{children:'• 如需恢复原始内容，请点击上方"重置内容"按钮'})]}),a.jsx("div",{contentEditable:!0,suppressContentEditableWarning:!0,ref:e=>{if(e&&e.innerHTML!==$){const t=window.getSelection();let n=0;if(t&&t.rangeCount>0){const a=t.getRangeAt(0),s=a.cloneRange();s.selectNodeContents(e),s.setEnd(a.endContainer,a.endOffset),n=s.toString().length}e.innerHTML=$;(()=>{e.querySelectorAll("img").forEach(e=>{e.classList.add("protected-element"),e.title="图片元素 - 请小心编辑",e.contentEditable="false",e.addEventListener("mouseenter",()=>{e.style.opacity="0.8",e.style.cursor="not-allowed"}),e.addEventListener("mouseleave",()=>{e.style.opacity="1",e.style.cursor="default"})});e.querySelectorAll("table").forEach(e=>{e.classList.add("protected-element"),e.title="表格元素 - 请小心编辑",e.addEventListener("mouseenter",()=>{e.style.backgroundColor="#f6ffed"}),e.addEventListener("mouseleave",()=>{e.style.backgroundColor=""})})})(),setTimeout(()=>{if(t){let a=0;const s=document.createTreeWalker(e,NodeFilter.SHOW_TEXT,null,!1);let r;for(;r=s.nextNode();){const e=r,s=a+e.textContent.length;if(n<=s){const s=document.createRange(),r=Math.min(n-a,e.textContent.length);s.setStart(e,r),s.setEnd(e,r),t.removeAllRanges(),t.addRange(s);break}a=s}}},0)}},onInput:e=>{const t=e.currentTarget.innerHTML;q(t)},onKeyDown:e=>{if("Delete"===e.key||"Backspace"===e.key){const t=window.getSelection();if(t&&t.rangeCount>0){const n=t.getRangeAt(0);n.commonAncestorContainer;const a=e=>{if(e.nodeType===Node.ELEMENT_NODE){const t=e;if("IMG"===t.tagName||"TABLE"===t.tagName||t.classList.contains("protected-element"))return!0}return!1},s=document.createTreeWalker(n.commonAncestorContainer,NodeFilter.SHOW_ELEMENT,null,!1);let r;for(;r=s.nextNode();)if(a(r))return e.preventDefault(),void T.warning("此区域包含图片或表格，请小心编辑以免丢失重要内容")}}},style:{fontFamily:"SimSun, 宋体, monospace",fontSize:"14px",lineHeight:"1.6",minHeight:"400px",width:"100%",padding:"15px",border:"2px dashed #1890ff",borderRadius:"4px",backgroundColor:"#fafafa",outline:"none",cursor:"text",resize:"vertical"},placeholder:"在此编辑证明内容...",onFocus:e=>{e.currentTarget.style.backgroundColor="#ffffff",e.currentTarget.style.borderColor="#40a9ff"},onBlur:e=>{e.currentTarget.style.backgroundColor="#fafafa",e.currentTarget.style.borderColor="#1890ff"}})]}):a.jsx("div",{dangerouslySetInnerHTML:{__html:$||k||"暂无内容"},style:{fontFamily:"SimSun, 宋体, monospace",fontSize:"14px",lineHeight:"1.6",minHeight:"400px"}})})})})]})}),a.jsx(N,{title:"变量映射确认",open:Ne,onCancel:()=>we(!1),width:800,footer:[a.jsx(l,{onClick:()=>we(!1),children:"取消"},"cancel"),a.jsx(l,{type:"primary",onClick:()=>{if(ue){const e=He.createTemplateFromDocument(ue),n=He.getAllTemplates();s(n),T.success(`模板 "${e.name}" 创建成功`),we(!1),t(!1)}},children:"确认创建模板"},"confirm")],children:ue&&a.jsxs("div",{children:[a.jsxs(se,{title:"文档信息",bordered:!0,size:"small",column:2,children:[a.jsx(se.Item,{label:"文件名",children:ue.fileName}),a.jsx(se.Item,{label:"文件类型",children:ue.fileType.toUpperCase()}),a.jsxs(se.Item,{label:"检测到的变量",children:[ue.variables.length," 个"]}),a.jsx(se.Item,{label:"是否包含表格",children:ue.formatInfo.hasTable?"是":"否"})]}),a.jsx(g,{children:"检测到的变量"}),a.jsx("div",{style:{maxHeight:300,overflowY:"auto"},children:ue.variables.map((e,t)=>a.jsx(d,{size:"small",style:{marginBottom:8},children:a.jsxs(m,{gutter:16,align:"middle",children:[a.jsx(u,{span:6,children:a.jsx(W,{color:"orange",children:e.originalText})}),a.jsx(u,{span:6,children:a.jsx(W,{color:"blue",children:"{{"+e.variableName+"}}"})}),a.jsx(u,{span:4,children:a.jsx(W,{color:"basic"===e.category?"green":"contact"===e.category?"blue":"academic"===e.category?"purple":"approval"===e.category?"red":"default",children:e.category})}),a.jsx(u,{span:4,children:e.required&&a.jsx(W,{color:"red",children:"必填"})}),a.jsx(u,{span:4,children:e.defaultValue&&a.jsx(b,{title:`默认值: ${e.defaultValue}`,children:a.jsx(W,{color:"cyan",children:"有默认值"})})})]})},t))}),a.jsx(g,{children:"模板内容预览"}),a.jsx("div",{style:{backgroundColor:"#f5f5f5",padding:12,borderRadius:4,maxHeight:200,overflowY:"auto",whiteSpace:"pre-wrap",fontSize:"12px",fontFamily:"monospace"},children:ue.parsedContent})]})}),C]})};const Ue=new class{constructor(){t(this,"defaultOptions",{preserveFormatting:!0,extractVariables:!0,variablePattern:/\{\{([^}]+)\}\}/g,maxFileSize:10485760})}async parseDocument(e,t){const n={...this.defaultOptions,...t};this.validateFile(e,n);try{return await this.parseDocumentWithAPI(e,n)}catch(a){throw console.error("文档解析失败:",a),new Error(`Word文档解析失败: ${a.message}`)}}async parseDocumentWithAPI(e,t){console.log("开始调用后端API解析Word文档:",e.name);const n=new FormData;n.append("document",e);const a=await fetch("http://localhost:3002/api/upload/parse-word",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`},body:n});if(console.log("API响应状态:",a.status),!a.ok){let e="文档解析失败";try{e=(await a.json()).message||e}catch(i){e=`HTTP ${a.status}: ${a.statusText}`}throw console.error("API调用失败:",e),new Error(e)}const s=await a.json();if(console.log("API返回数据:",s),!s.success)throw console.error("API返回失败:",s.message),new Error(s.message||"文档解析失败");const r=s.data;return{content:r.content,variables:r.variables.map(e=>({original:e.placeholder,standardName:this.generateStandardName(e.name),chineseName:e.name,englishName:this.generateEnglishName(e.name),category:e.category,dataType:e.dataType,required:e.required,description:`从文档中解析的变量：${e.name}`})),metadata:{fileName:r.metadata.fileName,fileSize:r.metadata.fileSize,wordCount:r.metadata.wordCount,pageCount:1,createdAt:(new Date).toISOString()},formatting:{fonts:["宋体","黑体","Times New Roman"],styles:["标题","正文","强调"],hasImages:!1,hasTables:r.content.includes("<table>")}}}validateFile(e,t){if(e.size>t.maxFileSize)throw new Error(`文件大小超过限制（${t.maxFileSize/1024/1024}MB）`);if(!["application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document"].includes(e.type))throw new Error("不支持的文件格式，请上传.doc或.docx文件")}async mockParseDocument(e,t){await new Promise(e=>setTimeout(e,1500));const n=e.name.toLowerCase();let a="";a=n.includes("在读")||n.includes("enrollment")?this.generateEnrollmentCertificateContent():n.includes("学籍")||n.includes("academic")?this.generateAcademicCertificateContent():n.includes("成绩")||n.includes("grade")?this.generateGradeCertificateContent():this.generateDefaultCertificateContent();return{content:a,variables:t.extractVariables?this.extractVariables(a,t.variablePattern):[],metadata:{fileName:e.name,fileSize:e.size,wordCount:this.countWords(a),pageCount:1,createdAt:(new Date).toISOString()},formatting:{fonts:["宋体","黑体","Times New Roman"],styles:["标题","正文","强调"],hasImages:!1,hasTables:n.includes("成绩")||n.includes("grade")}}}generateEnrollmentCertificateContent(){return'\n      <div style="text-align: center; margin-bottom: 30px;">\n        <h1 style="font-size: 28px; font-weight: bold; color: #000; margin-bottom: 10px;">{{学校名称}}</h1>\n        <h2 style="font-size: 20px; font-weight: bold; color: #333;">在读证明</h2>\n      </div>\n      \n      <div style="margin: 30px 0; line-height: 2.0; font-size: 16px;">\n        <p style="text-indent: 2em;">兹证明 <strong>{{学生姓名}}</strong>，性别：<strong>{{性别}}</strong>，学号：<strong>{{学号}}</strong>，身份证号：<strong>{{身份证号}}</strong>，为我校 <strong>{{学院}}</strong> <strong>{{专业}}</strong> <strong>{{年级}}</strong> 在读学生。</p>\n        \n        <p style="text-indent: 2em; margin-top: 20px;">该生于 <strong>{{入学时间}}</strong> 入学，学制 <strong>{{学制}}</strong> 年，学历层次为 <strong>{{学历层次}}</strong>，预计于 <strong>{{毕业时间}}</strong> 毕业。</p>\n        \n        <p style="text-indent: 2em; margin-top: 20px;">在校期间，该生学习认真，表现良好，无违纪违规行为。</p>\n        \n        <p style="text-indent: 2em; margin-top: 20px;">特此证明。</p>\n      </div>\n      \n      <div style="margin-top: 50px; text-align: right; font-size: 16px;">\n        <p style="margin-bottom: 10px;">{{学校名称}}</p>\n        <p style="margin-bottom: 10px;">学生处（盖章）</p>\n        <p>{{开具日期}}</p>\n      </div>\n    '}generateAcademicCertificateContent(){return'\n      <div style="text-align: center; margin-bottom: 30px;">\n        <h1 style="font-size: 28px; font-weight: bold; color: #000; margin-bottom: 10px;">{{学校名称}}</h1>\n        <h2 style="font-size: 20px; font-weight: bold; color: #333;">学籍证明</h2>\n      </div>\n      \n      <div style="margin: 30px 0; line-height: 2.0; font-size: 16px;">\n        <p style="text-indent: 2em;">兹证明 <strong>{{学生姓名}}</strong>，学号：<strong>{{学号}}</strong>，身份证号：<strong>{{身份证号}}</strong>，现为我校正式注册学生。</p>\n        \n        <p style="text-indent: 2em; margin-top: 20px;"><strong>学籍信息如下：</strong></p>\n        \n        <div style="margin: 20px 0; padding-left: 2em;">\n          <p>学院：<strong>{{学院}}</strong></p>\n          <p>专业：<strong>{{专业}}</strong></p>\n          <p>班级：<strong>{{班级}}</strong></p>\n          <p>年级：<strong>{{年级}}</strong></p>\n          <p>学制：<strong>{{学制}}</strong> 年</p>\n          <p>学历层次：<strong>{{学历层次}}</strong></p>\n          <p>入学时间：<strong>{{入学时间}}</strong></p>\n          <p>预计毕业时间：<strong>{{毕业时间}}</strong></p>\n        </div>\n        \n        <p style="text-indent: 2em; margin-top: 20px;">该生学籍状态正常，无休学、退学等异常情况。</p>\n        \n        <p style="text-indent: 2em; margin-top: 20px;">特此证明。</p>\n      </div>\n      \n      <div style="margin-top: 50px; text-align: right; font-size: 16px;">\n        <p style="margin-bottom: 10px;">{{学校名称}}</p>\n        <p style="margin-bottom: 10px;">教务处（盖章）</p>\n        <p>{{开具日期}}</p>\n      </div>\n    '}generateGradeCertificateContent(){return'\n      <div style="text-align: center; margin-bottom: 30px;">\n        <h1 style="font-size: 28px; font-weight: bold; color: #000; margin-bottom: 10px;">{{学校名称}}</h1>\n        <h2 style="font-size: 20px; font-weight: bold; color: #333;">成绩证明</h2>\n      </div>\n      \n      <div style="margin: 30px 0; line-height: 2.0; font-size: 16px;">\n        <p style="text-indent: 2em;">兹证明 <strong>{{学生姓名}}</strong>，学号：<strong>{{学号}}</strong>，为我校 <strong>{{学院}}</strong> <strong>{{专业}}</strong> <strong>{{年级}}</strong> 学生。</p>\n        \n        <p style="text-indent: 2em; margin-top: 20px;">该生在校期间学习成绩如下：</p>\n        \n        <table style="width: 100%; border-collapse: collapse; margin: 20px 0; font-size: 14px;">\n          <thead>\n            <tr style="background-color: #f5f5f5;">\n              <th style="border: 1px solid #ccc; padding: 8px; text-align: center;">课程名称</th>\n              <th style="border: 1px solid #ccc; padding: 8px; text-align: center;">学分</th>\n              <th style="border: 1px solid #ccc; padding: 8px; text-align: center;">成绩</th>\n              <th style="border: 1px solid #ccc; padding: 8px; text-align: center;">绩点</th>\n            </tr>\n          </thead>\n          <tbody>\n            <tr>\n              <td style="border: 1px solid #ccc; padding: 8px;">高等数学</td>\n              <td style="border: 1px solid #ccc; padding: 8px; text-align: center;">4</td>\n              <td style="border: 1px solid #ccc; padding: 8px; text-align: center;">85</td>\n              <td style="border: 1px solid #ccc; padding: 8px; text-align: center;">3.5</td>\n            </tr>\n            <tr>\n              <td style="border: 1px solid #ccc; padding: 8px;">大学英语</td>\n              <td style="border: 1px solid #ccc; padding: 8px; text-align: center;">3</td>\n              <td style="border: 1px solid #ccc; padding: 8px; text-align: center;">90</td>\n              <td style="border: 1px solid #ccc; padding: 8px; text-align: center;">4.0</td>\n            </tr>\n            <tr>\n              <td style="border: 1px solid #ccc; padding: 8px;">专业基础课</td>\n              <td style="border: 1px solid #ccc; padding: 8px; text-align: center;">5</td>\n              <td style="border: 1px solid #ccc; padding: 8px; text-align: center;">88</td>\n              <td style="border: 1px solid #ccc; padding: 8px; text-align: center;">3.8</td>\n            </tr>\n          </tbody>\n        </table>\n        \n        <p style="text-indent: 2em; margin-top: 20px;">平均绩点：<strong>3.7</strong>，学习成绩优良。</p>\n        \n        <p style="text-indent: 2em; margin-top: 20px;">特此证明。</p>\n      </div>\n      \n      <div style="margin-top: 50px; text-align: right; font-size: 16px;">\n        <p style="margin-bottom: 10px;">{{学校名称}}</p>\n        <p style="margin-bottom: 10px;">教务处（盖章）</p>\n        <p>{{开具日期}}</p>\n      </div>\n    '}generateDefaultCertificateContent(){return'\n      <div style="text-align: center; margin-bottom: 30px;">\n        <h1 style="font-size: 28px; font-weight: bold; color: #000; margin-bottom: 10px;">{{学校名称}}</h1>\n        <h2 style="font-size: 20px; font-weight: bold; color: #333;">证明</h2>\n      </div>\n      \n      <div style="margin: 30px 0; line-height: 2.0; font-size: 16px;">\n        <p style="text-indent: 2em;">兹证明 <strong>{{学生姓名}}</strong>，学号：<strong>{{学号}}</strong>，身份证号：<strong>{{身份证号}}</strong>，为我校 <strong>{{学院}}</strong> <strong>{{专业}}</strong> 学生。</p>\n        \n        <p style="text-indent: 2em; margin-top: 20px;">{{证明用途}}</p>\n        \n        <p style="text-indent: 2em; margin-top: 20px;">特此证明。</p>\n      </div>\n      \n      <div style="margin-top: 50px; text-align: right; font-size: 16px;">\n        <p style="margin-bottom: 10px;">{{学校名称}}</p>\n        <p style="margin-bottom: 10px;">{{审批人}}（盖章）</p>\n        <p>{{开具日期}}</p>\n      </div>\n    '}extractVariables(e,t){const n=e.match(t)||[],a=[];return n.forEach(e=>{const t=e.replace(/[{}]/g,"");a.some(e=>e.chineseName===t)||a.push({original:e,standardName:this.generateStandardName(t),chineseName:t,englishName:this.generateEnglishName(t),category:this.categorizeVariable(t),dataType:this.inferDataType(t),required:this.isRequiredVariable(t),description:`从文档中识别的变量：${t}`})}),a}generateStandardName(e){return{"学生姓名":"studentName","学号":"studentId","身份证号":"idCard","性别":"gender","学院":"college","专业":"major","班级":"class","年级":"grade","学校名称":"schoolName","开具日期":"issueDate","入学时间":"enrollmentDate","毕业时间":"graduationDate","学制":"duration","学历层次":"level","证明用途":"purpose","审批人":"approver"}[e]||e.toLowerCase().replace(/\s+/g,"_")}generateEnglishName(e){return{"学生姓名":"Student Name","学号":"Student ID","身份证号":"ID Card Number","性别":"Gender","学院":"College","专业":"Major","班级":"Class","年级":"Grade","学校名称":"School Name","开具日期":"Issue Date","入学时间":"Enrollment Date","毕业时间":"Graduation Date","学制":"Duration","学历层次":"Education Level","证明用途":"Purpose","审批人":"Approver"}[e]||e}categorizeVariable(e){return["学生姓名","学号","身份证号","性别","出生日期"].includes(e)?"basic":["学院","专业","班级","年级","学制","学历层次","入学时间","毕业时间"].includes(e)?"academic":["联系电话","邮箱","家庭住址"].includes(e)?"contact":["申请日期","开具日期","审批人"].includes(e)?"approval":["学校名称","证明编号","证明用途"].includes(e)?"system":"custom"}inferDataType(e){return e.includes("日期")||e.includes("时间")?"date":(e.includes("电话")||e.includes("号码"),"string")}isRequiredVariable(e){return["学生姓名","学号","学院","专业","学校名称","开具日期"].includes(e)}countWords(e){const t=e.replace(/<[^>]*>/g,"");return(t.match(/[\u4e00-\u9fa5]/g)||[]).length+(t.match(/[a-zA-Z]+/g)||[]).length}},{Option:Ge}=x,{TextArea:Je}=c,Xe=[{value:"enrollment",label:"在读证明",color:"blue"},{value:"academic",label:"学籍证明",color:"green"},{value:"grade",label:"成绩证明",color:"orange"},{value:"graduation",label:"毕业证明",color:"purple"},{value:"conduct",label:"品行证明",color:"cyan"},{value:"other",label:"其他证明",color:"default"}],Ke=[{original:"{{学生姓名}}",standardName:"studentName",chineseName:"学生姓名",englishName:"Student Name",category:"basic",dataType:"string",required:!0,description:"学生的真实姓名"},{original:"{{学号}}",standardName:"studentId",chineseName:"学号",englishName:"Student ID",category:"basic",dataType:"string",required:!0,description:"学生的学号"},{original:"{{身份证号}}",standardName:"idCard",chineseName:"身份证号",englishName:"ID Card",category:"basic",dataType:"string",required:!1,description:"学生的身份证号码"},{original:"{{性别}}",standardName:"gender",chineseName:"性别",englishName:"Gender",category:"basic",dataType:"string",required:!1,description:"学生的性别"},{original:"{{出生日期}}",standardName:"birthDate",chineseName:"出生日期",englishName:"Birth Date",category:"basic",dataType:"date",required:!1,description:"学生的出生日期"},{original:"{{学院}}",standardName:"college",chineseName:"学院",englishName:"College",category:"academic",dataType:"string",required:!0,description:"学生所在学院"},{original:"{{专业}}",standardName:"major",chineseName:"专业",englishName:"Major",category:"academic",dataType:"string",required:!0,description:"学生的专业"},{original:"{{班级}}",standardName:"class",chineseName:"班级",englishName:"Class",category:"academic",dataType:"string",required:!0,description:"学生所在班级"},{original:"{{年级}}",standardName:"grade",chineseName:"年级",englishName:"Grade",category:"academic",dataType:"string",required:!1,description:"学生的年级"},{original:"{{学制}}",standardName:"duration",chineseName:"学制",englishName:"Duration",category:"academic",dataType:"string",required:!1,description:"学制年限"},{original:"{{学历层次}}",standardName:"level",chineseName:"学历层次",englishName:"Education Level",category:"academic",dataType:"string",required:!1,description:"本科/研究生等"},{original:"{{入学时间}}",standardName:"enrollmentDate",chineseName:"入学时间",englishName:"Enrollment Date",category:"academic",dataType:"date",required:!1,description:"学生入学日期"},{original:"{{毕业时间}}",standardName:"graduationDate",chineseName:"毕业时间",englishName:"Graduation Date",category:"academic",dataType:"date",required:!1,description:"预计毕业日期"},{original:"{{联系电话}}",standardName:"phone",chineseName:"联系电话",englishName:"Phone",category:"contact",dataType:"string",required:!1,description:"学生的联系电话"},{original:"{{邮箱}}",standardName:"email",chineseName:"邮箱",englishName:"Email",category:"contact",dataType:"string",required:!1,description:"学生的邮箱地址"},{original:"{{家庭住址}}",standardName:"address",chineseName:"家庭住址",englishName:"Address",category:"contact",dataType:"string",required:!1,description:"学生的家庭住址"},{original:"{{申请日期}}",standardName:"applicationDate",chineseName:"申请日期",englishName:"Application Date",category:"approval",dataType:"date",required:!0,description:"证明申请日期"},{original:"{{开具日期}}",standardName:"issueDate",chineseName:"开具日期",englishName:"Issue Date",category:"approval",dataType:"date",required:!0,description:"证明开具日期"},{original:"{{审批人}}",standardName:"approver",chineseName:"审批人",englishName:"Approver",category:"approval",dataType:"string",required:!1,description:"证明审批人"},{original:"{{学校名称}}",standardName:"schoolName",chineseName:"学校名称",englishName:"School Name",category:"system",dataType:"string",required:!0,description:"学校的全称",defaultValue:"智慧学工大学"},{original:"{{证明编号}}",standardName:"certificateNumber",chineseName:"证明编号",englishName:"Certificate Number",category:"system",dataType:"string",required:!0,description:"证明的唯一编号"},{original:"{{证明用途}}",standardName:"purpose",chineseName:"证明用途",englishName:"Purpose",category:"system",dataType:"string",required:!1,description:"证明的使用用途"}],Ze=({visible:e,onCancel:t,onSuccess:n})=>{const[s]=K.useForm(),[r,o]=i.useState(!1),[m,u]=i.useState(""),[p,h]=i.useState(Ke),[g,y]=i.useState(null),[f,j]=i.useState(!1),[v,b]=i.useState(0),S=fe.getInstance(),C=()=>{s.resetFields(),u(""),h(Ke),y(null),j(!1),b(e=>e+1),t()};return a.jsx(N,{title:"新建证明模板",open:e,onCancel:C,width:"98vw",style:{top:10},styles:{body:{height:"calc(100vh - 120px)",overflowY:"hidden",padding:"16px 24px"}},centered:!0,footer:[a.jsx(l,{onClick:C,children:"取消"},"cancel"),a.jsx(l,{icon:a.jsx(P,{}),onClick:()=>j(!f),children:f?"编辑模式":"预览模式"},"preview"),a.jsx(l,{type:"primary",icon:a.jsx(H,{}),loading:r,onClick:async()=>{try{const e=await s.validateFields();o(!0);const t={name:e.name,type:e.type,description:e.description,content:m,variables:p,category:"custom",createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()};S.createTemplate(t),w.success("模板创建成功！"),n(),C()}catch(e){console.error("保存模板失败:",e),w.error("保存模板失败，请重试")}finally{o(!1)}},children:"保存模板"},"save")],children:a.jsxs("div",{style:{height:"100%",display:"flex",gap:"16px"},children:[a.jsxs("div",{style:{width:"320px",display:"flex",flexDirection:"column",gap:"12px"},children:[a.jsx(d,{title:"模板基本信息",size:"small",children:a.jsxs(K,{form:s,layout:"vertical",size:"small",children:[a.jsx(K.Item,{label:"模板名称",name:"name",rules:[{required:!0,message:"请输入模板名称"}],style:{marginBottom:"12px"},children:a.jsx(c,{placeholder:"请输入模板名称"})}),a.jsx(K.Item,{label:"证明类型",name:"type",rules:[{required:!0,message:"请选择证明类型"}],style:{marginBottom:"12px"},children:a.jsx(x,{placeholder:"选择证明类型",children:Xe.map(e=>a.jsx(Ge,{value:e.value,children:a.jsx(W,{color:e.color,style:{marginRight:8},children:e.label})},e.value))})}),a.jsx(K.Item,{label:"模板描述",name:"description",style:{marginBottom:"8px"},children:a.jsx(Je,{rows:2,placeholder:"请输入模板描述（可选）"})})]})}),a.jsxs(d,{title:"Word文档上传",size:"small",children:[a.jsx(te,{accept:".doc,.docx",customRequest:async e=>{const{file:t,onSuccess:n,onError:a}=e;try{o(!0),console.log("开始解析Word文档:",t.name,t.type,t.size);const e=await Ue.parseDocument(t);console.log("解析的文档内容:",e.content),console.log("解析的变量:",e.variables),console.log("文档元数据:",e.metadata),u(e.content),y(t);const a=[...Ke];e.variables.forEach(e=>{a.some(t=>t.chineseName===e.chineseName)||a.push(e)}),h(a),setTimeout(()=>{b(e=>e+1)},100),w.success(`Word文档解析成功！识别到 ${e.variables.length} 个变量`),null==n||n("ok")}catch(s){console.error("文档解析失败:",s);const e=s instanceof Error?s.message:"文档解析失败，请重试";w.error({content:`文档解析失败: ${e}`,duration:8,style:{maxWidth:"80%"}}),y(null),u(""),null==a||a(s)}finally{o(!1)}},showUploadList:!1,disabled:r,children:a.jsx(l,{icon:a.jsx(re,{}),loading:r,block:!0,type:"dashed",children:r?"解析中...":"上传Word文档"})}),g&&a.jsx("div",{style:{marginTop:"8px",padding:"6px 8px",backgroundColor:"#f6ffed",borderRadius:"4px"},children:a.jsxs(Text,{type:"success",style:{fontSize:"12px"},children:[a.jsx(CheckCircleOutlined,{style:{marginRight:"4px"}}),g.name]})}),a.jsxs("div",{style:{marginTop:"8px",fontSize:"11px",color:"#666",lineHeight:"1.4"},children:[a.jsx("p",{style:{margin:"2px 0"},children:"• 支持.doc和.docx格式"}),a.jsx("p",{style:{margin:"2px 0"},children:"• 自动解析文档内容和格式"}),a.jsx("p",{style:{margin:"2px 0"},children:"• 自动识别变量占位符"}),a.jsx("p",{style:{margin:"2px 0"},children:"• .doc格式如解析失败，请转换为.docx格式"})]})]})]}),a.jsx("div",{style:{flex:1,display:"flex",flexDirection:"column"},children:a.jsx(d,{title:"模板内容编辑 (A4格式)",size:"small",style:{flex:1,display:"flex",flexDirection:"column"},styles:{body:{flex:1,padding:"8px",display:"flex",flexDirection:"column"}},children:a.jsx("div",{style:{flex:1,display:"flex",justifyContent:"center",alignItems:"flex-start",backgroundColor:"#f5f5f5",padding:"16px",borderRadius:"4px",overflowY:"auto"},children:a.jsx("div",{style:{width:"210mm",minHeight:"297mm",maxWidth:"100%",backgroundColor:"#ffffff",boxShadow:"0 4px 8px rgba(0,0,0,0.1)",borderRadius:"2px",overflow:"hidden"},children:a.jsx(qe,{initialContent:m,variables:p,onContentChange:u,onVariablesChange:h,onSave:e=>{u(e),w.success("内容已保存")},a4Mode:!0},v)})})})})]})})},{Option:Qe}=x,{TextArea:et}=c,{Step:tt}=ye,{Text:nt}=G,at=Ie.getInstance(),st=_e.getInstance(),rt=fe.getInstance(),it=()=>{const e=s(),t=Ne(),{modal:n}=Z.useApp(),[p,h]=i.useState([]),[g,y]=i.useState([]),[f,j]=i.useState(!1),[v,b]=i.useState(""),[w,S]=i.useState(""),[C,T]=i.useState(""),[D,I]=i.useState(!1),[k,_]=i.useState(!1),[F,E]=i.useState(!1),[z,A]=i.useState(!1),[$,q]=i.useState(!1),[O,V]=i.useState(!1),[L,M]=i.useState(null),[R,H]=i.useState(null),[B,U]=i.useState(null),[G,Q]=i.useState(null),[te,ne]=i.useState([]),[ae]=K.useForm(),[se]=K.useForm(),re=async()=>{j(!0);try{const e=rt.getAllTemplates();y(e);const t=await r.get("/certificates");if(t.success){const e=t.data.map(e=>({id:e.id,studentId:e.student_number||e.student_id,studentName:e.student_name,class:e.class,major:e.major,college:e.college,phone:e.phone,email:e.email,certificateType:e.certificate_type,purpose:e.purpose,applicationDate:e.application_date?new Date(e.application_date).toISOString().split("T")[0]:"",requiredDate:e.required_date?new Date(e.required_date).toISOString().split("T")[0]:"",status:e.status,reviewer:e.reviewer||"",reviewDate:e.review_date?new Date(e.review_date).toISOString().split("T")[0]:"",reviewComment:e.review_comment,issueDate:e.issued_date?new Date(e.issued_date).toISOString().split("T")[0]:"",issueNumber:e.certificate_number,urgentLevel:e.urgent_level||"normal",contactPhone:e.contact_phone||e.phone||"",pickupMethod:e.pickup_method||"self",agentName:e.agent_name,agentIdCard:e.agent_id_card,mailAddress:e.mail_address,templateId:e.template_id,templateName:e.template_name,generatedContent:e.content,createdAt:e.created_at?new Date(e.created_at).toISOString().split("T")[0]:"",updatedAt:e.updated_at?new Date(e.updated_at).toISOString().split("T")[0]:"",_originalStudentId:e.student_id}));h(e)}}catch(e){console.error("获取证明数据错误:",e),t.error("获取证明数据失败");const n=JSON.parse(localStorage.getItem("certificateApplications")||"[]"),a=n.length>0?[]:[{id:"1",studentId:"2021001001",studentName:"张三",class:"计科2101",major:"计算机科学与技术",college:"计算机学院",phone:"13800138001",email:"<EMAIL>",certificateType:"enrollment",purpose:"银行开户",applicationDate:"2024-01-15",requiredDate:"2024-01-20",status:"pending",reviewer:"",urgentLevel:"normal",contactPhone:"13800138001",pickupMethod:"self",createdAt:"2024-01-15",updatedAt:"2024-01-15"},{id:"2",studentId:"2021001002",studentName:"李四",class:"软工2101",major:"软件工程",college:"计算机学院",phone:"13800138002",email:"<EMAIL>",certificateType:"conduct",purpose:"申请奖学金",applicationDate:"2024-01-10",requiredDate:"2024-01-18",status:"approved",reviewer:"学生处",reviewDate:"2024-01-12",reviewComment:"符合开具条件，予以批准",issueDate:"2024-01-13",issueNumber:"XW2024001",urgentLevel:"urgent",contactPhone:"13800138002",pickupMethod:"mail",mailAddress:"北京市海淀区某某路123号",templateId:"1",templateName:"品行证明模板",createdAt:"2024-01-10",updatedAt:"2024-01-10"}];savedTemplates.length,h([...n,...a])}finally{j(!1)}};i.useEffect(()=>{re()},[]),i.useEffect(()=>{console.log("Modal states:",{modalVisible:D,reviewModalVisible:k,templateModalVisible:F,createTemplateModalVisible:z,previewModalVisible:$,detailModalVisible:O}),console.log("editingCertificate changed to:",L)},[D,k,F,z,$,O,L]);const ye=e=>{console.log("=== handleEdit START ==="),console.log("Record:",e),console.log("Current modalVisible:",D),M(e),console.log("Set editingCertificate to:",e),I(!0),console.log("Set modalVisible to true"),console.log("=== handleEdit END ===")},fe=async(e,t)=>{try{const n={certificate:e,template:t,systemValues:{schoolName:"银川科技学院",currentDate:(new Date).toLocaleDateString("zh-CN"),issueDate:(new Date).toLocaleDateString("zh-CN")}},a=await st.replaceVariables(n);return a.warnings.length>0&&console.warn("变量替换警告:",a.warnings),a.unreplacedVariables.length>0&&console.warn("未替换的变量:",a.unreplacedVariables),a.content}catch(n){return console.error("生成证明内容失败:",n),t.content||"证明内容生成失败"}},je=p.filter(e=>{const t=!v||e.studentName.toLowerCase().includes(v.toLowerCase())||e.studentId.toLowerCase().includes(v.toLowerCase()),n=!w||e.certificateType===w,a=!C||e.status===C;return t&&n&&a}),ve=[{title:"学生信息",key:"student",render:(t,n)=>a.jsxs("div",{children:[a.jsx("div",{className:"font-medium",children:a.jsx(l,{type:"link",onClick:()=>e(`/students/${n.studentId}`),className:"p-0 h-auto",children:n.studentName})}),a.jsxs("div",{className:"text-gray-500 text-xs",children:[n.studentId," · ",n.class]}),a.jsxs("div",{className:"text-gray-400 text-xs",children:[n.major," · ",n.college]})]})},{title:"证明类型",dataIndex:"certificateType",key:"certificateType",render:e=>{const t={enrollment:{color:"blue",text:"在读证明"},leave:{color:"yellow",text:"请假申请"},replacement:{color:"red",text:"补办申请"},exam:{color:"green",text:"考试证明"},discipline:{color:"volcano",text:"违纪处分"},graduation:{color:"green",text:"毕业证明"},conduct:{color:"purple",text:"品行证明"},academic:{color:"orange",text:"成绩证明"},internship:{color:"cyan",text:"实习证明"},scholarship:{color:"gold",text:"奖学金证明"},poverty:{color:"magenta",text:"贫困证明"},health:{color:"lime",text:"健康证明"},transfer:{color:"geekblue",text:"转学证明"},suspension:{color:"purple",text:"休学证明"},resumption:{color:"cyan",text:"复学证明"},other:{color:"default",text:"其他证明"},"在读证明":{color:"blue",text:"在读证明"},"请假申请":{color:"yellow",text:"请假申请"},"补办申请":{color:"red",text:"补办申请"},"考试证明":{color:"green",text:"考试证明"},"违纪处分":{color:"volcano",text:"违纪处分"},"毕业证明":{color:"green",text:"毕业证明"},"品行证明":{color:"purple",text:"品行证明"},"成绩证明":{color:"orange",text:"成绩证明"},"实习证明":{color:"cyan",text:"实习证明"},"奖学金证明":{color:"gold",text:"奖学金证明"},"贫困证明":{color:"magenta",text:"贫困证明"},"健康证明":{color:"lime",text:"健康证明"},"转学证明":{color:"geekblue",text:"转学证明"},"休学证明":{color:"purple",text:"休学证明"},"复学证明":{color:"cyan",text:"复学证明"},"其他证明":{color:"default",text:"其他证明"}}[e]||{color:"default",text:e||"未知类型"};return a.jsx(W,{color:t.color,children:t.text})}},{title:"申请用途",dataIndex:"purpose",key:"purpose",render:e=>a.jsx("div",{className:"max-w-xs truncate",title:e,children:e})},{title:"紧急程度",dataIndex:"urgentLevel",key:"urgentLevel",render:e=>{const t={normal:{color:"default",text:"普通"},urgent:{color:"orange",text:"紧急"},emergency:{color:"red",text:"特急"},"普通":{color:"default",text:"普通"},"紧急":{color:"orange",text:"紧急"},"特急":{color:"red",text:"特急"}}[e]||{color:"default",text:e||"普通"};return a.jsx(W,{color:t.color,children:t.text})}},{title:"申请日期",dataIndex:"applicationDate",key:"applicationDate"},{title:"需要日期",dataIndex:"requiredDate",key:"requiredDate"},{title:"状态",dataIndex:"status",key:"status",render:e=>{const t={pending:{color:"orange",text:"待审核",icon:a.jsx(ce,{})},reviewing:{color:"blue",text:"审核中",icon:a.jsx(xe,{})},approved:{color:"green",text:"已批准",icon:a.jsx(de,{})},rejected:{color:"red",text:"已拒绝",icon:a.jsx(xe,{})},issued:{color:"purple",text:"已开具",icon:a.jsx(ee,{})}}[e]||{color:"default",text:e||"未知状态",icon:a.jsx(xe,{})};return a.jsx(W,{color:t.color,icon:t.icon,children:t.text})}},{title:"操作",key:"action",render:(e,s)=>a.jsxs(o,{children:[a.jsx(l,{type:"text",icon:a.jsx(P,{}),onClick:()=>(e=>{Q(e),V(!0)})(s),title:"查看详情"}),"pending"===s.status&&a.jsx(l,{type:"text",icon:a.jsx(de,{}),onClick:()=>(e=>{H(e),se.resetFields(),_(!0)})(s),style:{color:"#52c41a"},title:"审核"}),("approved"===s.status||"issued"===s.status)&&a.jsx(l,{type:"text",icon:a.jsx(ie,{}),onClick:()=>(e=>{U(e),q(!0)})(s),style:{color:"#1890ff"},title:"打印"}),a.jsx(l,{type:"text",icon:a.jsx(J,{}),onClick:e=>{e.preventDefault(),e.stopPropagation(),console.log("Edit button clicked for record:",s),console.log("Current modalVisible state:",D),console.log("Current editingCertificate state:",L),ye(s)},title:"编辑",style:{color:"#1890ff"}}),a.jsx(l,{type:"text",danger:!0,icon:a.jsx(X,{}),onClick:()=>(e=>{n.confirm({title:"确认删除",content:`确定要删除 ${e.studentName} 的证明申请吗？`,onOk:()=>{const n=p.filter(t=>t.id!==e.id);h(n),localStorage.setItem("certificateApplications",JSON.stringify(n)),t.success("删除成功")}})})(s),title:"删除"})]})}],be=p.length,we=p.filter(e=>"pending"===e.status).length,Se=p.filter(e=>"approved"===e.status).length,Ce=p.filter(e=>"issued"===e.status).length;return a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"证明开具管理"}),a.jsx("p",{className:"text-gray-600",children:"管理学生各类证明申请和开具"})]}),a.jsxs(o,{children:[a.jsx(l,{icon:a.jsx(le,{}),onClick:re,size:"large",children:"刷新数据"}),a.jsx(l,{icon:a.jsx(ee,{}),onClick:()=>{E(!0)},size:"large",children:"模板管理"}),a.jsx(l,{icon:a.jsx(Y,{}),onClick:()=>{A(!0)},size:"large",children:"新建模板"}),a.jsx(l,{type:"primary",icon:a.jsx(Y,{}),onClick:()=>{M(null),ae.resetFields(),ne([]),I(!0)},size:"large",children:"新增申请"})]})]}),a.jsxs(m,{gutter:16,children:[a.jsx(u,{span:6,children:a.jsx(d,{children:a.jsx(oe,{title:"申请总数",value:be,prefix:a.jsx(ee,{})})})}),a.jsx(u,{span:6,children:a.jsx(d,{children:a.jsx(oe,{title:"待审核",value:we,valueStyle:{color:"#faad14"},prefix:a.jsx(ce,{})})})}),a.jsx(u,{span:6,children:a.jsx(d,{children:a.jsx(oe,{title:"已批准",value:Se,valueStyle:{color:"#3f8600"},prefix:a.jsx(de,{})})})}),a.jsx(u,{span:6,children:a.jsx(d,{children:a.jsx(oe,{title:"已开具",value:Ce,valueStyle:{color:"#722ed1"},prefix:a.jsx(me,{})})})})]}),a.jsxs(d,{children:[a.jsxs("div",{className:"flex items-center space-x-4 mb-4",children:[a.jsx(c,{placeholder:"搜索学号或姓名",prefix:a.jsx(ue,{}),value:v,onChange:e=>b(e.target.value),style:{width:300}}),a.jsxs(x,{placeholder:"证明类型",value:w,onChange:S,allowClear:!0,style:{width:120},children:[a.jsx(Qe,{value:"enrollment",children:"在读证明"}),a.jsx(Qe,{value:"leave",children:"请假申请"}),a.jsx(Qe,{value:"replacement",children:"补办申请"}),a.jsx(Qe,{value:"exam",children:"考试证明"}),a.jsx(Qe,{value:"discipline",children:"违纪处分"}),a.jsx(Qe,{value:"graduation",children:"毕业证明"}),a.jsx(Qe,{value:"conduct",children:"品行证明"}),a.jsx(Qe,{value:"academic",children:"成绩证明"}),a.jsx(Qe,{value:"internship",children:"实习证明"}),a.jsx(Qe,{value:"scholarship",children:"奖学金证明"}),a.jsx(Qe,{value:"poverty",children:"贫困证明"}),a.jsx(Qe,{value:"health",children:"健康证明"}),a.jsx(Qe,{value:"transfer",children:"转学证明"}),a.jsx(Qe,{value:"suspension",children:"休学证明"}),a.jsx(Qe,{value:"resumption",children:"复学证明"}),a.jsx(Qe,{value:"other",children:"其他证明"})]}),a.jsxs(x,{placeholder:"状态",value:C,onChange:T,allowClear:!0,style:{width:120},children:[a.jsx(Qe,{value:"pending",children:"待审核"}),a.jsx(Qe,{value:"reviewing",children:"审核中"}),a.jsx(Qe,{value:"approved",children:"已批准"}),a.jsx(Qe,{value:"rejected",children:"已拒绝"}),a.jsx(Qe,{value:"issued",children:"已开具"})]})]}),a.jsx(pe,{columns:ve,dataSource:je,rowKey:"id",loading:f,pagination:{total:je.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 条申请`},scroll:{x:1200}})]}),a.jsx(N,{title:L?"编辑证明申请":"新增证明申请",open:D,onCancel:()=>{console.log("Modal onCancel called"),I(!1),M(null),ae.resetFields()},footer:null,width:800,destroyOnClose:!0,maskClosable:!1,style:{top:20},children:a.jsxs(K,{form:ae,layout:"vertical",onFinish:async e=>{try{let n=null;if(e.studentId){const a=await r.get(`/students?student_id=${encodeURIComponent(e.studentId)}`);if(!(a.success&&a.data.length>0))return void t.error("未找到该学号对应的学生，请检查学号是否正确");n=a.data[0].id}const a={student_id:n,certificate_type:e.certificateType,purpose:e.purpose,template_id:e.templateId||null,content:e.content||null,application_date:e.applicationDate.format("YYYY-MM-DD"),required_date:e.requiredDate.format("YYYY-MM-DD"),status:"pending",attachments:te.map(e=>({name:e.name,url:e.url||`/uploads/${e.name}`,type:e.type,size:e.size}))};if(L){(await r.put(`/certificates/${L.id}`,a)).success?(t.success("证明申请更新成功"),await re()):t.error("更新证明申请失败")}else{(await r.post("/certificates",a)).success?(t.success("证明申请提交成功"),await re()):t.error("提交证明申请失败")}I(!1),ne([]),ae.resetFields(),M(null)}catch(n){console.error("保存证明申请错误:",n),t.error("保存失败，请稍后重试")}},children:[a.jsxs(m,{gutter:16,children:[a.jsx(u,{span:12,children:a.jsx(K.Item,{label:"学号",name:"studentId",rules:[{required:!0,message:"请输入学号"}],children:a.jsx(c,{placeholder:"输入学号自动匹配学生信息",onChange:e=>(async e=>{if(e)try{if(!at.validateStudentId(e))return void t.warning("学号格式不正确，请输入正确的学号格式（如：2024001001）");const n=await at.queryStudentByStudentId(e);if(n.found&&n.student){const e=n.student;return ae.setFieldsValue({studentName:e.studentName,class:e.class,major:e.major||"",college:e.college||"",phone:e.phone||"",email:e.email||""}),void t.success(`已匹配到学生：${e.studentName}（数据来源：${n.source}）`)}n.suggestions&&n.suggestions.length>0?t.warning(`未找到学号 ${e} 的学生信息，您是否要查找：${n.suggestions.slice(0,3).join("、")}？`):t.warning(`未找到学号 ${e} 的学生信息，请检查学号是否正确`)}catch(n){console.error("查询学生信息失败:",n),t.error("查询学生信息失败，请稍后重试")}else ae.setFieldsValue({studentName:"",class:"",major:"",college:"",phone:"",email:""})})(e.target.value)})})}),a.jsx(u,{span:12,children:a.jsx(K.Item,{label:"学生姓名",name:"studentName",rules:[{required:!0,message:"请输入学生姓名"}],children:a.jsx(c,{placeholder:"自动匹配",disabled:!0})})})]}),a.jsxs(m,{gutter:16,children:[a.jsx(u,{span:8,children:a.jsx(K.Item,{label:"班级",name:"class",rules:[{required:!0,message:"请输入班级"}],children:a.jsx(c,{placeholder:"自动匹配",disabled:!0})})}),a.jsx(u,{span:8,children:a.jsx(K.Item,{label:"专业",name:"major",children:a.jsx(c,{placeholder:"自动匹配",disabled:!0})})}),a.jsx(u,{span:8,children:a.jsx(K.Item,{label:"学院",name:"college",children:a.jsx(c,{placeholder:"自动匹配",disabled:!0})})})]}),a.jsxs(m,{gutter:16,children:[a.jsx(u,{span:8,children:a.jsx(K.Item,{label:"联系电话",name:"phone",children:a.jsx(c,{placeholder:"自动匹配",disabled:!0})})}),a.jsx(u,{span:8,children:a.jsx(K.Item,{label:"邮箱",name:"email",children:a.jsx(c,{placeholder:"自动匹配",disabled:!0})})}),a.jsx(u,{span:8,children:a.jsx(K.Item,{label:"证明类型",name:"certificateType",rules:[{required:!0,message:"请选择证明类型"}],children:a.jsxs(x,{placeholder:"选择类型",children:[a.jsx(Qe,{value:"enrollment",children:"在读证明"}),a.jsx(Qe,{value:"leave",children:"请假申请"}),a.jsx(Qe,{value:"replacement",children:"补办申请"}),a.jsx(Qe,{value:"exam",children:"考试证明"}),a.jsx(Qe,{value:"discipline",children:"违纪处分"}),a.jsx(Qe,{value:"graduation",children:"毕业证明"}),a.jsx(Qe,{value:"conduct",children:"品行证明"}),a.jsx(Qe,{value:"academic",children:"成绩证明"}),a.jsx(Qe,{value:"internship",children:"实习证明"}),a.jsx(Qe,{value:"scholarship",children:"奖学金证明"}),a.jsx(Qe,{value:"poverty",children:"贫困证明"}),a.jsx(Qe,{value:"health",children:"健康证明"}),a.jsx(Qe,{value:"transfer",children:"转学证明"}),a.jsx(Qe,{value:"suspension",children:"休学证明"}),a.jsx(Qe,{value:"resumption",children:"复学证明"}),a.jsx(Qe,{value:"other",children:"其他证明"})]})})})]}),a.jsx(m,{gutter:16,children:a.jsx(u,{span:24,children:a.jsx(K.Item,{label:"紧急程度",name:"urgentLevel",rules:[{required:!0,message:"请选择紧急程度"}],initialValue:"normal",children:a.jsxs(x,{placeholder:"选择紧急程度",children:[a.jsx(Qe,{value:"normal",children:"普通"}),a.jsx(Qe,{value:"urgent",children:"紧急"}),a.jsx(Qe,{value:"emergency",children:"特急"})]})})})}),a.jsx(K.Item,{label:"申请用途",name:"purpose",rules:[{required:!0,message:"请输入申请用途"}],children:a.jsx(et,{rows:2,placeholder:"详细说明证明的用途"})}),a.jsxs(m,{gutter:16,children:[a.jsx(u,{span:12,children:a.jsx(K.Item,{label:"申请日期",name:"applicationDate",rules:[{required:!0,message:"请选择申请日期"}],initialValue:he(),children:a.jsx(ge,{style:{width:"100%"}})})}),a.jsx(u,{span:12,children:a.jsx(K.Item,{label:"需要日期",name:"requiredDate",rules:[{required:!0,message:"请选择需要日期"}],children:a.jsx(ge,{style:{width:"100%"}})})})]}),a.jsxs(m,{gutter:16,children:[a.jsx(u,{span:12,children:a.jsx(K.Item,{label:"联系电话",name:"contactPhone",rules:[{required:!0,message:"请输入联系电话"},{pattern:/^1[3-9]\d{9}$/,message:"手机号格式不正确"}],children:a.jsx(c,{placeholder:"联系电话"})})}),a.jsx(u,{span:12,children:a.jsx(K.Item,{label:"领取方式",name:"pickupMethod",rules:[{required:!0,message:"请选择领取方式"}],initialValue:"self",children:a.jsxs(x,{placeholder:"选择领取方式",children:[a.jsx(Qe,{value:"self",children:"本人领取"}),a.jsx(Qe,{value:"mail",children:"邮寄"}),a.jsx(Qe,{value:"agent",children:"代理人领取"})]})})})]}),a.jsx(K.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.pickupMethod!==t.pickupMethod,children:({getFieldValue:e})=>{const t=e("pickupMethod");return"mail"===t?a.jsx(K.Item,{label:"邮寄地址",name:"mailAddress",rules:[{required:!0,message:"请输入邮寄地址"}],children:a.jsx(et,{rows:2,placeholder:"详细邮寄地址"})}):"agent"===t?a.jsxs(m,{gutter:16,children:[a.jsx(u,{span:12,children:a.jsx(K.Item,{label:"代理人姓名",name:"agentName",rules:[{required:!0,message:"请输入代理人姓名"}],children:a.jsx(c,{placeholder:"代理人姓名"})})}),a.jsx(u,{span:12,children:a.jsx(K.Item,{label:"代理人身份证",name:"agentIdCard",rules:[{required:!0,message:"请输入代理人身份证号"},{pattern:/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,message:"身份证号格式不正确"}],children:a.jsx(c,{placeholder:"代理人身份证号"})})})]}):null}}),a.jsxs("div",{className:"flex justify-end space-x-2",children:[a.jsx(l,{onClick:()=>I(!1),children:"取消"}),a.jsx(l,{type:"primary",htmlType:"submit",children:L?"更新":"提交申请"})]})]})}),a.jsxs(N,{title:"审核证明申请",open:k,onCancel:()=>_(!1),footer:null,width:600,children:[R&&a.jsxs("div",{className:"mb-4 p-4 bg-gray-50 rounded",children:[a.jsx("h4",{className:"font-medium mb-2",children:"申请信息"}),a.jsxs("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[a.jsxs("div",{children:["学生: ",R.studentName]}),a.jsxs("div",{children:["类型: ",R.certificateType]}),a.jsxs("div",{children:["用途: ",R.purpose]}),a.jsxs("div",{children:["需要日期: ",R.requiredDate]})]})]}),a.jsxs(K,{form:se,layout:"vertical",onFinish:e=>{if(!R)return;const n={status:e.status,reviewer:e.reviewer,reviewDate:(new Date).toISOString().split("T")[0],reviewComment:e.reviewComment};"approved"===e.status&&(n.issueDate=(new Date).toISOString().split("T")[0],n.issueNumber=`XW${Date.now()}`),h(p.map(e=>e.id===R.id?{...e,...n}:e)),t.success("审核完成"),_(!1)},children:[a.jsx(K.Item,{label:"审核结果",name:"status",rules:[{required:!0,message:"请选择审核结果"}],children:a.jsxs(x,{placeholder:"选择审核结果",children:[a.jsx(Qe,{value:"approved",children:"通过"}),a.jsx(Qe,{value:"rejected",children:"拒绝"})]})}),a.jsx(K.Item,{label:"审核人",name:"reviewer",rules:[{required:!0,message:"请输入审核人"}],children:a.jsx(c,{placeholder:"审核人姓名或部门"})}),a.jsx(K.Item,{label:"审核意见",name:"reviewComment",rules:[{required:!0,message:"请输入审核意见"}],children:a.jsx(et,{rows:3,placeholder:"请输入审核意见"})}),a.jsxs("div",{className:"flex justify-end space-x-2",children:[a.jsx(l,{onClick:()=>_(!1),children:"取消"}),a.jsx(l,{type:"primary",htmlType:"submit",children:"提交审核"})]})]})]}),a.jsx(N,{title:"证明申请详情",open:O,onCancel:()=>V(!1),footer:[a.jsx(l,{onClick:()=>V(!1),children:"关闭"},"close"),a.jsx(l,{type:"primary",onClick:()=>{G&&(V(!1),ye(G))},children:"编辑"},"edit")],width:900,children:G&&a.jsxs("div",{className:"space-y-6",children:[a.jsxs(d,{title:"学生信息",size:"small",children:[a.jsxs(m,{gutter:16,children:[a.jsx(u,{span:8,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"姓名："}),a.jsx("span",{className:"font-medium",children:G.studentName})]})}),a.jsx(u,{span:8,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"学号："}),a.jsx("span",{children:G.studentId})]})}),a.jsx(u,{span:8,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"班级："}),a.jsx("span",{children:G.class})]})})]}),a.jsxs(m,{gutter:16,className:"mt-3",children:[a.jsx(u,{span:8,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"专业："}),a.jsx("span",{children:G.major})]})}),a.jsx(u,{span:8,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"学院："}),a.jsx("span",{children:G.college})]})}),a.jsx(u,{span:8,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"联系电话："}),a.jsx("span",{children:G.phone||"未填写"})]})})]}),G.email&&a.jsx(m,{gutter:16,className:"mt-3",children:a.jsx(u,{span:8,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"邮箱："}),a.jsx("span",{children:G.email})]})})})]}),a.jsxs(d,{title:"申请信息",size:"small",children:[a.jsxs(m,{gutter:16,children:[a.jsx(u,{span:8,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"证明类型："}),a.jsx(W,{color:"blue",children:{enrollment:"在读证明",graduation:"毕业证明",conduct:"品行证明",academic:"成绩证明",internship:"实习证明",other:"其他证明","在读证明":"在读证明","毕业证明":"毕业证明","品行证明":"品行证明","成绩证明":"成绩证明","实习证明":"实习证明","其他证明":"其他证明"}[G.certificateType]||G.certificateType})]})}),a.jsx(u,{span:8,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"紧急程度："}),a.jsx(W,{color:"emergency"===G.urgentLevel?"red":"urgent"===G.urgentLevel?"orange":"default",children:"emergency"===G.urgentLevel?"特急":"urgent"===G.urgentLevel?"紧急":"普通"})]})}),a.jsx(u,{span:8,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"当前状态："}),a.jsx(W,{color:"pending"===G.status?"orange":"reviewing"===G.status?"blue":"approved"===G.status?"green":"rejected"===G.status?"red":"purple",children:"pending"===G.status?"待审核":"reviewing"===G.status?"审核中":"approved"===G.status?"已批准":"rejected"===G.status?"已拒绝":"已开具"})]})})]}),a.jsxs(m,{gutter:16,className:"mt-3",children:[a.jsx(u,{span:12,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"申请日期："}),a.jsx("span",{children:G.applicationDate})]})}),a.jsx(u,{span:12,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"需要日期："}),a.jsx("span",{className:"font-medium text-blue-600",children:G.requiredDate})]})})]})]}),a.jsx(d,{title:"申请用途",size:"small",children:a.jsx("div",{className:"text-sm whitespace-pre-wrap bg-gray-50 p-3 rounded",children:G.purpose})}),a.jsxs(d,{title:"联系方式与领取信息",size:"small",children:[a.jsxs(m,{gutter:16,children:[a.jsx(u,{span:12,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"联系电话："}),a.jsx("span",{children:G.contactPhone})]})}),a.jsx(u,{span:12,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"领取方式："}),a.jsx(W,{color:"cyan",children:"self"===G.pickupMethod?"本人领取":"mail"===G.pickupMethod?"邮寄":"代理人领取"})]})})]}),"mail"===G.pickupMethod&&G.mailAddress&&a.jsx(m,{gutter:16,className:"mt-3",children:a.jsx(u,{span:24,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"邮寄地址："}),a.jsx("span",{children:G.mailAddress})]})})}),"agent"===G.pickupMethod&&a.jsxs(m,{gutter:16,className:"mt-3",children:[a.jsx(u,{span:12,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"代理人姓名："}),a.jsx("span",{children:G.agentName})]})}),a.jsx(u,{span:12,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"代理人身份证："}),a.jsx("span",{children:G.agentIdCard})]})})]})]}),"pending"!==G.status&&G.reviewer&&a.jsxs(d,{title:"审核信息",size:"small",children:[a.jsxs(m,{gutter:16,children:[a.jsx(u,{span:8,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"审核人："}),a.jsx("span",{children:G.reviewer})]})}),a.jsx(u,{span:8,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"审核日期："}),a.jsx("span",{children:G.reviewDate})]})}),a.jsx(u,{span:8,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"审核结果："}),a.jsx(W,{color:"approved"===G.status?"green":"red",children:"approved"===G.status?"通过":"拒绝"})]})})]}),G.reviewComment&&a.jsx(m,{gutter:16,className:"mt-3",children:a.jsx(u,{span:24,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"审核意见："}),a.jsx("div",{className:"mt-1 p-2 bg-gray-50 rounded text-sm",children:G.reviewComment})]})})})]}),"issued"===G.status&&a.jsxs(d,{title:"开具信息",size:"small",children:[a.jsxs(m,{gutter:16,children:[a.jsx(u,{span:12,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"开具日期："}),a.jsx("span",{children:G.issueDate})]})}),a.jsx(u,{span:12,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"证明编号："}),a.jsx("span",{className:"font-medium text-blue-600",children:G.issueNumber})]})})]}),G.templateName&&a.jsx(m,{gutter:16,className:"mt-3",children:a.jsx(u,{span:12,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"使用模板："}),a.jsx("span",{children:G.templateName})]})})})]}),a.jsx(d,{title:"记录信息",size:"small",children:a.jsxs(m,{gutter:16,children:[a.jsx(u,{span:12,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"创建时间："}),a.jsx("span",{children:G.createdAt})]})}),a.jsx(u,{span:12,children:a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-500",children:"更新时间："}),a.jsx("span",{children:G.updatedAt})]})})]})})]})}),a.jsx(Ye,{templateModalVisible:F,setTemplateModalVisible:E,templates:g,setTemplates:y,previewModalVisible:$,setPreviewModalVisible:q,previewingCertificate:B,onActualPrint:async()=>{if(B)try{let e=B.templateId?g.find(e=>e.id===B.templateId):null;e||(e=(e=>{const t={enrollment:"enrollment",leave:"leave",replacement:"replacement",exam:"exam",discipline:"discipline",graduation:"graduation",conduct:"conduct",academic:"academic",internship:"internship",scholarship:"scholarship",poverty:"poverty",health:"health",transfer:"transfer",suspension:"suspension",resumption:"resumption",other:"other","在读证明":"enrollment","请假申请":"leave","补办申请":"replacement","考试证明":"exam","违纪处分":"discipline","毕业证明":"graduation","品行证明":"conduct","成绩证明":"academic","实习证明":"internship","奖学金证明":"scholarship","贫困证明":"poverty","健康证明":"health","转学证明":"transfer","休学证明":"suspension","复学证明":"resumption","其他证明":"other"}[e]||"other";return g.find(e=>e.type===t)||g[0]})(B.certificateType)),e||(e={id:"default",name:"默认证明模板",type:"other",category:"custom",content:"证明文件\n\n兹证明 {{studentName}} 同学，学号：{{studentId}}，系我校{{college}}{{major}}专业{{class}}班学生。\n\n该生现为我校在读学生，学习情况良好。\n\n特此证明。\n\n{{schoolName}}\n{{currentDate}}",variables:["studentName","studentId","college","major","class","schoolName","currentDate"],createdAt:(new Date).toISOString().split("T")[0],updatedAt:(new Date).toISOString().split("T")[0]}),console.log("使用的模板:",e);const n=await fe(B,e);console.log("生成的内容:",n),rt.recordTemplateUsage(e.id);const a=window.open("","_blank");if(!a)return void t.error("无法打开打印窗口，请检查浏览器弹窗设置");a.document.write(`\n        <html>\n          <head>\n            <title>${e.name||"证明文件"} - ${B.studentName}</title>\n            <meta charset="UTF-8">\n            <style>\n              body {\n                font-family: 'SimSun', '宋体', serif;\n                font-size: 14px;\n                line-height: 1.6;\n                margin: 20px;\n                background: white;\n              }\n              .certificate-content {\n                white-space: pre-wrap;\n                font-family: 'SimSun', '宋体', monospace;\n                font-size: 14px;\n                line-height: 1.6;\n                margin: 0;\n                padding: 0;\n                text-align: left;\n                word-wrap: break-word;\n                overflow-wrap: break-word;\n              }\n              @media print {\n                body {\n                  margin: 15mm;\n                  font-size: 12px;\n                }\n                .certificate-content {\n                  font-size: 12px;\n                  line-height: 1.5;\n                }\n              }\n              @page {\n                margin: 15mm;\n                size: A4;\n              }\n            </style>\n          </head>\n          <body>\n            <div class="certificate-content">${n}</div>\n          </body>\n        </html>\n      `),a.document.close(),setTimeout(()=>{a.print()},500),t.success("证明文件已发送到打印机"),q(!1)}catch(e){console.error("打印失败:",e),t.error("打印失败，请稍后重试")}else t.error("没有可打印的证明")},generateCertificateContent:fe,fileList:te,onFileChange:e=>{let t=[...e.fileList];t=t.slice(-1),t=t.map(e=>(e.response&&(e.url=e.response.url),e)),ne(t)}}),a.jsx(Ze,{visible:z,onCancel:()=>A(!1),onSuccess:()=>{re(),t.success("模板创建成功！")}})]})};export{it as default};
