import{c as e,u as s,j as a,r as t}from"./index-DP6eZxW9.js";import{r as l,S as n,a as r,aC as i,aB as c,F as d,G as o,H as x,Z as j,Q as h,J as u,g as m,ai as p,ay as y,j as v,m as g,l as f,k as I,A as k,ae as b,ag as S}from"./antd-DYv0PFJq.js";import"./vendor-D2RBMdQ0.js";const N=()=>{const{id:N}=e(),z=s(),[w,_]=l.useState(!0),[C,R]=l.useState(null),[$,A]=l.useState([]);l.useEffect(()=>{(async()=>{if(N){_(!0);try{const e=await t.get(`/classes/${N}`);if(e.success){const s=e.data,a={id:s.id,className:s.class_name,grade:s.grade,major:s.major,college:s.college,counselor:s.counselor||"未分配",counselorPhone:s.counselor_phone||"",monitor:s.monitor||"待选举",monitorPhone:s.monitor_phone||"",totalStudents:parseInt(s.actual_students)||0,maleStudents:parseInt(s.actual_male)||0,femaleStudents:parseInt(s.actual_female)||0,status:s.status,createdAt:s.created_at?new Date(s.created_at).toISOString().split("T")[0]:""};R(a);const l=await t.get(`/students?class=${encodeURIComponent(s.class_name)}`);if(l.success){const e=l.data.map(e=>({id:e.id,studentId:e.student_id,name:e.name,gender:e.gender,phone:e.phone||"",dormitory:e.dormitory||"未知",room:e.room||"未知",status:"active",avatar:e.avatar}));A(e)}}else console.error("获取班级详情失败"),R(null)}catch(e){console.error("获取班级详情错误:",e),R(null)}finally{_(!1)}}})()},[N]);const P=[{title:"头像",dataIndex:"avatar",key:"avatar",width:60,render:(e,s)=>a.jsx(k,{src:e,icon:a.jsx(m,{})})},{title:"学号",dataIndex:"studentId",key:"studentId"},{title:"姓名",dataIndex:"name",key:"name",render:(e,s)=>a.jsx(r,{type:"link",onClick:()=>z(`/students/${s.studentId}`),className:"p-0 h-auto",children:e})},{title:"性别",dataIndex:"gender",key:"gender",render:e=>"male"===e?"男":"女"},{title:"联系电话",dataIndex:"phone",key:"phone"},{title:"宿舍信息",key:"dormitory",render:(e,s)=>`${s.dormitory} ${s.room}`},{title:"状态",dataIndex:"status",key:"status",render:e=>{const s={active:{color:"green",text:"正常"},warning:{color:"orange",text:"预警"},inactive:{color:"red",text:"异常"}},t=s[e]||s.active;return a.jsx(h,{color:t.color,children:t.text})}},{title:"操作",key:"action",render:(e,s)=>a.jsx(b,{children:a.jsx(r,{type:"link",size:"small",onClick:()=>z(`/students/${s.studentId}`),children:"查看详情"})})}],K=[{title:"科目",dataIndex:"subject",key:"subject"},{title:"平均分",dataIndex:"average",key:"average",render:e=>e.toFixed(1)},{title:"及格率",dataIndex:"passRate",key:"passRate",render:e=>a.jsxs("div",{className:"flex items-center",children:[a.jsx(S,{percent:e,size:"small",style:{width:100,marginRight:8}}),a.jsxs("span",{children:[e,"%"]})]})}];return w?a.jsx("div",{className:"flex items-center justify-center h-64",children:a.jsx(n,{size:"large"})}):C?a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{className:"flex items-center space-x-4",children:[a.jsx(r,{icon:a.jsx(i,{}),onClick:()=>z("/classes"),children:"返回"}),a.jsxs("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"班级详情"}),a.jsx("p",{className:"text-gray-600",children:"查看和管理班级的详细信息"})]})]}),a.jsx(r,{type:"primary",icon:a.jsx(c,{}),onClick:()=>z(`/classes/${N}/edit`),children:"编辑班级"})]}),a.jsx(d,{title:"班级基本信息",children:a.jsxs(o,{gutter:[16,16],children:[a.jsx(x,{span:18,children:a.jsxs(j,{column:2,children:[a.jsx(j.Item,{label:"班级名称",children:C.className}),a.jsxs(j.Item,{label:"年级",children:[C.grade,"级"]}),a.jsx(j.Item,{label:"专业",children:C.major}),a.jsx(j.Item,{label:"学院",children:C.college}),a.jsx(j.Item,{label:"辅导员",children:C.counselor}),a.jsx(j.Item,{label:"辅导员电话",children:C.counselorPhone}),a.jsx(j.Item,{label:"班长",children:C.monitor}),a.jsx(j.Item,{label:"班长电话",children:C.monitorPhone}),a.jsx(j.Item,{label:"创建时间",children:C.createdAt}),a.jsx(j.Item,{label:"状态",children:a.jsx(h,{color:"active"===C.status?"green":"red",children:"active"===C.status?"正常":"停用"})})]})}),a.jsx(x,{span:6,children:a.jsxs("div",{className:"space-y-4",children:[a.jsx(u,{title:"班级总人数",value:C.totalStudents,prefix:a.jsx(m,{})}),a.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[a.jsx(u,{title:"男生",value:C.maleStudents,valueStyle:{fontSize:"16px"}}),a.jsx(u,{title:"女生",value:C.femaleStudents,valueStyle:{fontSize:"16px"}})]})]})})]})}),a.jsx(d,{children:a.jsx(p,{defaultActiveKey:"students",items:[{key:"students",label:a.jsxs("span",{children:[a.jsx(m,{}),"学生名单"]}),children:a.jsx(y,{columns:P,dataSource:$,rowKey:"id",pagination:{pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 名学生`}})},{key:"grades",label:a.jsxs("span",{children:[a.jsx(v,{}),"成绩统计"]}),children:a.jsxs("div",{className:"space-y-4",children:[a.jsxs(o,{gutter:16,children:[a.jsx(x,{span:6,children:a.jsx(d,{size:"small",children:a.jsx(u,{title:"班级平均GPA",value:3.2,precision:2,valueStyle:{color:"#3f8600"}})})}),a.jsx(x,{span:6,children:a.jsx(d,{size:"small",children:a.jsx(u,{title:"优秀率",value:25,suffix:"%",valueStyle:{color:"#cf1322"}})})}),a.jsx(x,{span:6,children:a.jsx(d,{size:"small",children:a.jsx(u,{title:"及格率",value:91,suffix:"%",valueStyle:{color:"#1890ff"}})})}),a.jsx(x,{span:6,children:a.jsx(d,{size:"small",children:a.jsx(u,{title:"不及格人数",value:3,suffix:"人",valueStyle:{color:"#faad14"}})})})]}),a.jsx(y,{columns:K,dataSource:[{subject:"高等数学",average:85.2,passRate:92},{subject:"程序设计",average:88.5,passRate:95},{subject:"英语",average:78.3,passRate:88},{subject:"数据结构",average:82.1,passRate:90}],rowKey:"subject",pagination:!1,size:"small"})]})},{key:"awards",label:a.jsxs("span",{children:[a.jsx(g,{}),"奖惩记录"]}),children:a.jsx("div",{className:"text-center py-8",children:a.jsx("p",{className:"text-gray-500",children:"暂无奖惩记录"})})},{key:"dormitory",label:a.jsxs("span",{children:[a.jsx(f,{}),"宿舍分布"]}),children:a.jsx("div",{className:"space-y-4",children:a.jsxs(o,{gutter:16,children:[a.jsx(x,{span:8,children:a.jsx(d,{size:"small",title:"1号楼",children:a.jsx(u,{value:15,suffix:"人"})})}),a.jsx(x,{span:8,children:a.jsx(d,{size:"small",title:"2号楼",children:a.jsx(u,{value:12,suffix:"人"})})}),a.jsx(x,{span:8,children:a.jsx(d,{size:"small",title:"3号楼",children:a.jsx(u,{value:5,suffix:"人"})})})]})})},{key:"analytics",label:a.jsxs("span",{children:[a.jsx(I,{}),"数据分析"]}),children:a.jsx("div",{className:"text-center py-8",children:a.jsx("p",{className:"text-gray-500",children:"数据分析功能开发中"})})}]})})]}):a.jsxs("div",{className:"text-center",children:[a.jsx("p",{children:"班级信息不存在"}),a.jsx(r,{onClick:()=>z("/classes"),children:"返回班级列表"})]})};export{N as default};
