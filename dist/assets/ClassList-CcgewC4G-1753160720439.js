import{u as e,j as s,r as a}from"./index-DXaqwR6F-1753160720439.js";import{M as l,r as t,aq as r,ae as n,a as c,X as o,y as i,W as d,ao as x,G as h,H as u,F as j,J as m,i as p,g,I as y,aL as v,as as f,ay as w,Q as k,T as S,af as C,aB as N}from"./antd-lXsGnH6e-1753160720439.js";import{u as I}from"./useMessage-CR2RbGdP-1753160720439.js";import"./vendor-D2RBMdQ0-1753160720439.js";const{Option:b}=f,$=()=>{const[$,_]=l.useModal(),P=e(),T=I(),[q,F]=t.useState([]),[L,z]=t.useState(!1),[M,O]=t.useState(""),[K,J]=t.useState(""),[Q,V]=t.useState(""),[A,B]=t.useState(!1),[D,E]=t.useState(null),[G,H]=t.useState([]),[R]=r.useForm(),W=async()=>{z(!0);try{const e=await a.get("/classes");if(e.success){const s=e.data.map(e=>({id:e.id,className:e.class_name,grade:e.grade,major:e.major,college:e.college,counselor:e.counselor||"",counselorPhone:e.counselor_phone||"",monitor:e.monitor||"",monitorPhone:e.monitor_phone||"",totalStudents:parseInt(e.actual_students)||0,maleStudents:parseInt(e.actual_male)||0,femaleStudents:parseInt(e.actual_female)||0,status:e.status,createdAt:e.created_at?new Date(e.created_at).toISOString().split("T")[0]:""}));F(s)}else T.error("获取班级列表失败")}catch(e){console.error("获取班级列表错误:",e),T.error("获取班级列表失败")}finally{z(!1)}};t.useEffect(()=>{W()},[]);const X=q.filter(e=>{const s=!M||e.className.toLowerCase().includes(M.toLowerCase())||e.major.toLowerCase().includes(M.toLowerCase())||e.counselor.toLowerCase().includes(M.toLowerCase()),a=!K||e.grade===K,l=!Q||e.major===Q;return s&&a&&l}),U=[{title:"班级名称",dataIndex:"className",key:"className",render:(e,a)=>s.jsx(c,{type:"link",onClick:()=>P(`/classes/${a.id}`),className:"p-0 h-auto",children:e})},{title:"年级",dataIndex:"grade",key:"grade",render:e=>`${e}级`},{title:"专业",dataIndex:"major",key:"major"},{title:"学院",dataIndex:"college",key:"college"},{title:"辅导员",dataIndex:"counselor",key:"counselor",render:(e,a)=>s.jsxs("div",{children:[s.jsx("div",{children:e}),s.jsx("div",{className:"text-gray-500 text-xs",children:a.counselorPhone})]})},{title:"班长",dataIndex:"monitor",key:"monitor",render:(e,a)=>s.jsxs("div",{children:[s.jsx("div",{children:e}),s.jsx("div",{className:"text-gray-500 text-xs",children:a.monitorPhone})]})},{title:"学生人数",dataIndex:"totalStudents",key:"totalStudents",render:(e,a)=>s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"font-semibold",children:e}),s.jsxs("div",{className:"text-xs text-gray-500",children:["男",a.maleStudents," 女",a.femaleStudents]})]})},{title:"状态",dataIndex:"status",key:"status",render:e=>s.jsx(k,{color:"active"===e?"green":"red",children:"active"===e?"正常":"停用"})},{title:"操作",key:"action",render:(e,l)=>s.jsxs(n,{children:[s.jsx(S,{title:"查看详情",children:s.jsx(c,{type:"text",icon:s.jsx(C,{}),onClick:()=>P(`/classes/${l.id}`)})}),s.jsx(S,{title:"编辑",children:s.jsx(c,{type:"text",icon:s.jsx(N,{}),onClick:()=>(e=>{E(e),R.setFieldsValue(e),B(!0)})(l)})}),s.jsx(S,{title:"删除",children:s.jsx(c,{type:"text",danger:!0,icon:s.jsx(d,{}),onClick:()=>(e=>{$.confirm({title:"确认删除",content:s.jsxs("div",{children:[s.jsxs("p",{children:["确定要删除班级 ",e.className," 吗？"]}),s.jsx("p",{className:"text-red-600 mt-2",children:"⚠️ 删除班级后，该班级下的学生将不再关联到此班级。"})]}),okText:"确认删除",cancelText:"取消",okType:"danger",onOk:async()=>{try{(await a.delete(`/classes/${e.id}`)).success?(T.success(`班级 ${e.className} 已删除`),await W()):T.error("删除班级失败")}catch(s){console.error("删除班级错误:",s),T.error("删除班级失败")}}})})(l)})})]})}],Y=[...new Set(q.map(e=>e.grade))],Z=[...new Set(q.map(e=>e.major))],ee={selectedRowKeys:G,onChange:e=>{H(e)}};return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"班级管理"}),s.jsx("p",{className:"text-gray-600",children:"管理学院的班级信息和班级成员"})]}),s.jsxs(n,{children:[s.jsx(c,{icon:s.jsx(o,{}),onClick:W,size:"large",children:"刷新数据"}),s.jsx(c,{icon:s.jsx(i,{}),onClick:async()=>{z(!0);try{const e=await a.post("/classes/sync-from-students");if(e.success){const{syncedCount:s,skippedCount:a,totalFound:l}=e.data;T.success(`同步完成：新增 ${s} 个班级，跳过 ${a} 个已存在班级，共发现 ${l} 个班级`),await W()}else T.error("同步班级失败")}catch(e){console.error("同步班级错误:",e),T.error("同步班级失败")}finally{z(!1)}},size:"large",loading:L,children:"同步学生班级"}),G.length>0&&s.jsxs(c,{danger:!0,icon:s.jsx(d,{}),onClick:()=>{if(0===G.length)return void T.warning("请选择要删除的班级");const e=q.filter(e=>G.includes(e.id)).map(e=>e.className).join("、");$.confirm({title:"批量删除确认",content:s.jsxs("div",{children:[s.jsxs("p",{children:["确定要删除以下 ",G.length," 个班级吗？"]}),s.jsxs("p",{className:"text-gray-600 mt-2",children:["班级：",e]}),s.jsx("p",{className:"text-red-600 mt-2",children:"⚠️ 删除班级后，这些班级下的学生将不再关联到对应班级。"})]}),okText:"确认删除",cancelText:"取消",okType:"danger",width:500,onOk:async()=>{try{const e=G.map(e=>a.delete(`/classes/${e}`)),s=(await Promise.allSettled(e)).filter(e=>"fulfilled"===e.status&&e.value.success).length;s>0&&(T.success(`成功删除 ${s} 个班级`),await W()),s<G.length&&T.warning(G.length-s+" 个班级删除失败"),H([])}catch(e){console.error("批量删除班级错误:",e),T.error("批量删除失败")}}})},size:"large",children:["批量删除 (",G.length,")"]}),s.jsx(c,{type:"primary",icon:s.jsx(x,{}),onClick:()=>{E(null),R.resetFields(),B(!0)},size:"large",children:"新建班级"})]})]}),s.jsxs(h,{gutter:16,children:[s.jsx(u,{span:6,children:s.jsx(j,{children:s.jsx(m,{title:"班级总数",value:q.length,prefix:s.jsx(p,{})})})}),s.jsx(u,{span:6,children:s.jsx(j,{children:s.jsx(m,{title:"学生总数",value:q.reduce((e,s)=>e+s.totalStudents,0),prefix:s.jsx(g,{})})})}),s.jsx(u,{span:6,children:s.jsx(j,{children:s.jsx(m,{title:"平均班级人数",value:Math.round(q.reduce((e,s)=>e+s.totalStudents,0)/q.length||0),suffix:"人"})})}),s.jsx(u,{span:6,children:s.jsx(j,{children:s.jsx(m,{title:"活跃班级",value:q.filter(e=>"active"===e.status).length,valueStyle:{color:"#3f8600"}})})})]}),s.jsxs(j,{children:[s.jsxs("div",{className:"flex items-center space-x-4 mb-4",children:[s.jsx(y,{placeholder:"搜索班级名称、专业或辅导员",prefix:s.jsx(v,{}),value:M,onChange:e=>O(e.target.value),style:{width:300}}),s.jsx(f,{placeholder:"选择年级",value:K,onChange:J,allowClear:!0,style:{width:120},children:Y.map(e=>s.jsxs(b,{value:e,children:[e,"级"]},e))}),s.jsx(f,{placeholder:"选择专业",value:Q,onChange:V,allowClear:!0,style:{width:200},children:Z.map(e=>s.jsx(b,{value:e,children:e},e))})]}),s.jsx(w,{rowSelection:ee,columns:U,dataSource:X,rowKey:"id",loading:L,pagination:{total:X.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 个班级`}})]}),s.jsx(l,{title:D?"编辑班级":"新建班级",open:A,onCancel:()=>B(!1),footer:null,width:600,children:s.jsxs(r,{form:R,layout:"vertical",onFinish:async e=>{try{if(D){const s={class_name:e.className,grade:e.grade,major:e.major,college:e.college,counselor:e.counselor,counselor_phone:e.counselorPhone,monitor:e.monitor,monitor_phone:e.monitorPhone,status:e.status};(await a.put(`/classes/${D.id}`,s)).success?(T.success("班级信息更新成功"),await W()):T.error("更新班级失败")}else{const s={class_name:e.className,grade:e.grade,major:e.major,college:e.college,counselor:e.counselor,counselor_phone:e.counselorPhone,monitor:e.monitor,monitor_phone:e.monitorPhone,status:e.status||"active"};(await a.post("/classes",s)).success?(T.success("班级创建成功"),await W()):T.error("创建班级失败")}B(!1),R.resetFields(),E(null)}catch(s){console.error("提交班级信息错误:",s),T.error("操作失败，请重试")}},children:[s.jsxs(h,{gutter:16,children:[s.jsx(u,{span:12,children:s.jsx(r.Item,{label:"班级名称",name:"className",rules:[{required:!0,message:"请输入班级名称"}],children:s.jsx(y,{placeholder:"如：计科2101"})})}),s.jsx(u,{span:12,children:s.jsx(r.Item,{label:"年级",name:"grade",rules:[{required:!0,message:"请选择年级"}],children:s.jsxs(f,{placeholder:"选择年级",children:[s.jsx(b,{value:"2021",children:"2021级"}),s.jsx(b,{value:"2022",children:"2022级"}),s.jsx(b,{value:"2023",children:"2023级"}),s.jsx(b,{value:"2024",children:"2024级"})]})})})]}),s.jsxs(h,{gutter:16,children:[s.jsx(u,{span:12,children:s.jsx(r.Item,{label:"专业",name:"major",rules:[{required:!0,message:"请输入专业名称"}],children:s.jsx(y,{placeholder:"如：计算机科学与技术"})})}),s.jsx(u,{span:12,children:s.jsx(r.Item,{label:"学院",name:"college",rules:[{required:!0,message:"请输入学院名称"}],children:s.jsx(y,{placeholder:"如：计算机学院"})})})]}),s.jsxs(h,{gutter:16,children:[s.jsx(u,{span:12,children:s.jsx(r.Item,{label:"辅导员",name:"counselor",rules:[{required:!0,message:"请输入辅导员姓名"}],children:s.jsx(y,{placeholder:"辅导员姓名"})})}),s.jsx(u,{span:12,children:s.jsx(r.Item,{label:"辅导员电话",name:"counselorPhone",rules:[{required:!0,message:"请输入辅导员电话"},{pattern:/^1[3-9]\d{9}$/,message:"手机号格式不正确"}],children:s.jsx(y,{placeholder:"辅导员联系电话"})})})]}),s.jsxs(h,{gutter:16,children:[s.jsx(u,{span:12,children:s.jsx(r.Item,{label:"班长",name:"monitor",children:s.jsx(y,{placeholder:"班长姓名"})})}),s.jsx(u,{span:12,children:s.jsx(r.Item,{label:"班长电话",name:"monitorPhone",rules:[{pattern:/^1[3-9]\d{9}$/,message:"手机号格式不正确"}],children:s.jsx(y,{placeholder:"班长联系电话"})})})]}),s.jsx(r.Item,{label:"状态",name:"status",initialValue:"active",children:s.jsxs(f,{children:[s.jsx(b,{value:"active",children:"正常"}),s.jsx(b,{value:"inactive",children:"停用"})]})}),s.jsxs("div",{className:"flex justify-end space-x-2",children:[s.jsx(c,{onClick:()=>B(!1),children:"取消"}),s.jsx(c,{type:"primary",htmlType:"submit",children:D?"更新":"创建"})]})]})}),_]})};export{$ as default};
