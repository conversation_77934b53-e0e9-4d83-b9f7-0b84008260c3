import{u as e,r as s,j as r}from"./index-BbdVriMF-1753162495593.js";import{M as l,r as a,aq as t,a as c,ao as i,G as n,H as d,F as o,J as x,j as h,ad as u,I as j,aL as m,as as p,ay as y,aM as v,Q as g,ae as f,T as w,af as b,aB as C,W as I}from"./antd-w1cSjLgF-1753162495593.js";import{u as S}from"./useMessage-Dr3f4067-1753162495593.js";import"./vendor-D2RBMdQ0-1753162495593.js";const{Option:k}=p,{TextArea:N}=j,q=()=>{const[q,T]=l.useModal(),L=e(),$=S(),[M,F]=a.useState([]),[z,A]=a.useState(!1),[H,J]=a.useState(""),[K,O]=a.useState(""),[P,Q]=a.useState(""),[V,B]=a.useState(!1),[E,G]=a.useState(null),[W]=t.useForm();a.useEffect(()=>{D()},[]);const D=async()=>{A(!0);try{const e=await s.get("/academics/courses");e.success?F(e.data):$.error("获取课程列表失败")}catch(e){console.error("获取课程列表错误:",e),$.error("获取课程列表失败")}finally{A(!1)}},R=M.filter(e=>{const s=!H||e.courseName.toLowerCase().includes(H.toLowerCase())||e.courseCode.toLowerCase().includes(H.toLowerCase())||e.teacher.toLowerCase().includes(H.toLowerCase()),r=!K||e.courseType===K,l=!P||e.semester===P;return s&&r&&l}),U=[{title:"课程代码",dataIndex:"courseCode",key:"courseCode",width:100},{title:"课程名称",dataIndex:"courseName",key:"courseName",render:(e,s)=>r.jsx(c,{type:"link",onClick:()=>L(`/courses/${s.id}`),className:"p-0 h-auto",children:e})},{title:"课程类型",dataIndex:"courseType",key:"courseType",render:e=>{const s={required:{color:"red",text:"必修"},elective:{color:"blue",text:"选修"},public:{color:"green",text:"公共"}}[e];return r.jsx(g,{color:s.color,children:s.text})}},{title:"学分/学时",key:"creditHours",render:(e,s)=>r.jsxs("div",{className:"text-center",children:[r.jsxs("div",{children:[s.credit,"学分"]}),r.jsxs("div",{className:"text-gray-500 text-xs",children:[s.hours,"学时"]})]})},{title:"任课教师",dataIndex:"teacher",key:"teacher",render:(e,s)=>r.jsxs("div",{children:[r.jsx("div",{children:e}),r.jsx("div",{className:"text-gray-500 text-xs",children:s.teacherPhone})]})},{title:"学期",dataIndex:"semester",key:"semester"},{title:"选课人数",key:"students",render:(e,s)=>r.jsxs("div",{className:"text-center",children:[r.jsxs("div",{className:"font-semibold",children:[s.currentStudents,"/",s.maxStudents]}),r.jsxs("div",{className:"text-xs text-gray-500",children:[Math.round(s.currentStudents/s.maxStudents*100),"%"]})]})},{title:"教室",dataIndex:"classroom",key:"classroom"},{title:"状态",dataIndex:"status",key:"status",render:e=>{const s={active:{color:"green",text:"进行中"},inactive:{color:"red",text:"未开课"},finished:{color:"gray",text:"已结课"}}[e];return r.jsx(g,{color:s.color,children:s.text})}},{title:"操作",key:"action",render:(e,l)=>r.jsxs(f,{children:[r.jsx(w,{title:"查看详情",children:r.jsx(c,{type:"text",icon:r.jsx(b,{}),onClick:()=>L(`/courses/${l.id}`)})}),r.jsx(w,{title:"编辑",children:r.jsx(c,{type:"text",icon:r.jsx(C,{}),onClick:()=>(e=>{G(e),W.setFieldsValue(e),B(!0)})(l)})}),r.jsx(w,{title:"删除",children:r.jsx(c,{type:"text",danger:!0,icon:r.jsx(I,{}),onClick:()=>(e=>{q.confirm({title:"确认删除",content:`确定要删除课程 ${e.courseName} 吗？`,onOk:async()=>{try{await s.delete(`/academics/courses/${e.id}`),F(M.filter(s=>s.id!==e.id)),$.success("删除成功")}catch(r){console.error("删除课程错误:",r),$.error("删除失败")}}})})(l)})})]})}],X=[...new Set(M.map(e=>e.semester))];return r.jsxs("div",{className:"space-y-6",children:[r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"课程管理"}),r.jsx("p",{className:"text-gray-600",children:"管理学院的课程信息和选课情况"})]}),r.jsx(c,{type:"primary",icon:r.jsx(i,{}),onClick:()=>{G(null),W.resetFields(),B(!0)},size:"large",children:"新建课程"})]}),r.jsxs(n,{gutter:16,children:[r.jsx(d,{span:6,children:r.jsx(o,{children:r.jsx(x,{title:"课程总数",value:M.length,prefix:r.jsx(h,{})})})}),r.jsx(d,{span:6,children:r.jsx(o,{children:r.jsx(x,{title:"进行中课程",value:M.filter(e=>"active"===e.status).length,valueStyle:{color:"#3f8600"}})})}),r.jsx(d,{span:6,children:r.jsx(o,{children:r.jsx(x,{title:"总学时",value:M.reduce((e,s)=>e+s.hours,0),prefix:r.jsx(u,{})})})}),r.jsx(d,{span:6,children:r.jsx(o,{children:r.jsx(x,{title:"平均选课率",value:Math.round(M.reduce((e,s)=>e+s.currentStudents/s.maxStudents,0)/M.length*100),suffix:"%"})})})]}),r.jsxs(o,{children:[r.jsxs("div",{className:"flex items-center space-x-4 mb-4",children:[r.jsx(j,{placeholder:"搜索课程名称、代码或教师",prefix:r.jsx(m,{}),value:H,onChange:e=>J(e.target.value),style:{width:300}}),r.jsxs(p,{placeholder:"课程类型",value:K,onChange:O,allowClear:!0,style:{width:120},children:[r.jsx(k,{value:"required",children:"必修"}),r.jsx(k,{value:"elective",children:"选修"}),r.jsx(k,{value:"public",children:"公共"})]}),r.jsx(p,{placeholder:"学期",value:P,onChange:Q,allowClear:!0,style:{width:120},children:X.map(e=>r.jsx(k,{value:e,children:e},e))})]}),r.jsx(y,{columns:U,dataSource:R,rowKey:"id",loading:z,pagination:{total:R.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 门课程`},scroll:{x:1200}})]}),r.jsx(l,{title:E?"编辑课程":"新建课程",open:V,onCancel:()=>B(!1),footer:null,width:800,children:r.jsxs(t,{form:W,layout:"vertical",onFinish:async e=>{try{if(E){(await s.put(`/academics/courses/${E.id}`,e)).success&&(F(M.map(s=>s.id===E.id?{...s,...e,id:E.id}:s)),$.success("课程信息更新成功"))}else{const r=await s.post("/academics/courses",e);r.success&&(F([r.data,...M]),$.success("课程创建成功"))}B(!1)}catch(r){console.error("保存课程信息错误:",r),$.error("保存失败")}},children:[r.jsxs(n,{gutter:16,children:[r.jsx(d,{span:12,children:r.jsx(t.Item,{label:"课程代码",name:"courseCode",rules:[{required:!0,message:"请输入课程代码"}],children:r.jsx(j,{placeholder:"如：CS101"})})}),r.jsx(d,{span:12,children:r.jsx(t.Item,{label:"课程名称",name:"courseName",rules:[{required:!0,message:"请输入课程名称"}],children:r.jsx(j,{placeholder:"如：程序设计基础"})})})]}),r.jsxs(n,{gutter:16,children:[r.jsx(d,{span:8,children:r.jsx(t.Item,{label:"课程类型",name:"courseType",rules:[{required:!0,message:"请选择课程类型"}],children:r.jsxs(p,{placeholder:"选择类型",children:[r.jsx(k,{value:"required",children:"必修"}),r.jsx(k,{value:"elective",children:"选修"}),r.jsx(k,{value:"public",children:"公共"})]})})}),r.jsx(d,{span:8,children:r.jsx(t.Item,{label:"学分",name:"credit",rules:[{required:!0,message:"请输入学分"}],children:r.jsx(v,{min:1,max:10,placeholder:"学分",style:{width:"100%"}})})}),r.jsx(d,{span:8,children:r.jsx(t.Item,{label:"学时",name:"hours",rules:[{required:!0,message:"请输入学时"}],children:r.jsx(v,{min:16,max:128,placeholder:"学时",style:{width:"100%"}})})})]}),r.jsxs(n,{gutter:16,children:[r.jsx(d,{span:12,children:r.jsx(t.Item,{label:"任课教师",name:"teacher",rules:[{required:!0,message:"请输入任课教师"}],children:r.jsx(j,{placeholder:"教师姓名"})})}),r.jsx(d,{span:12,children:r.jsx(t.Item,{label:"教师电话",name:"teacherPhone",rules:[{required:!0,message:"请输入教师电话"},{pattern:/^1[3-9]\d{9}$/,message:"手机号格式不正确"}],children:r.jsx(j,{placeholder:"教师联系电话"})})})]}),r.jsxs(n,{gutter:16,children:[r.jsx(d,{span:8,children:r.jsx(t.Item,{label:"学期",name:"semester",rules:[{required:!0,message:"请选择学期"}],children:r.jsxs(p,{placeholder:"选择学期",children:[r.jsx(k,{value:"2024-1",children:"2024-1"}),r.jsx(k,{value:"2024-2",children:"2024-2"}),r.jsx(k,{value:"2025-1",children:"2025-1"})]})})}),r.jsx(d,{span:8,children:r.jsx(t.Item,{label:"最大人数",name:"maxStudents",rules:[{required:!0,message:"请输入最大人数"}],children:r.jsx(v,{min:10,max:200,placeholder:"最大人数",style:{width:"100%"}})})}),r.jsx(d,{span:8,children:r.jsx(t.Item,{label:"教室",name:"classroom",rules:[{required:!0,message:"请输入教室"}],children:r.jsx(j,{placeholder:"如：A101"})})})]}),r.jsxs(n,{gutter:16,children:[r.jsx(d,{span:12,children:r.jsx(t.Item,{label:"学院",name:"college",rules:[{required:!0,message:"请输入学院"}],children:r.jsx(j,{placeholder:"如：计算机学院"})})}),r.jsx(d,{span:12,children:r.jsx(t.Item,{label:"适用专业",name:"major",rules:[{required:!0,message:"请输入适用专业"}],children:r.jsx(j,{placeholder:"如：计算机科学与技术"})})})]}),r.jsx(t.Item,{label:"上课时间",name:"schedule",rules:[{required:!0,message:"请输入上课时间"}],children:r.jsx(j,{placeholder:"如：周一 1-2节，周三 3-4节"})}),r.jsx(t.Item,{label:"课程描述",name:"description",children:r.jsx(N,{rows:3,placeholder:"课程简介和教学目标"})}),r.jsx(t.Item,{label:"状态",name:"status",initialValue:"active",children:r.jsxs(p,{children:[r.jsx(k,{value:"active",children:"进行中"}),r.jsx(k,{value:"inactive",children:"未开课"}),r.jsx(k,{value:"finished",children:"已结课"})]})}),r.jsxs("div",{className:"flex justify-end space-x-2",children:[r.jsx(c,{onClick:()=>B(!1),children:"取消"}),r.jsx(c,{type:"primary",htmlType:"submit",children:E?"更新":"创建"})]})]})}),T]})};export{q as default};
