import{j as e,r as t,u as s}from"./index-DXaqwR6F-1753160720439.js";import{a0 as a,r as i,G as r,H as l,F as n,J as d,a9 as c,N as o,aa as x,ab as m,O as u,a as h,P as j,ac as p,ad as g,ae as f,Q as y,B as v,T as N,af as b,L as w,ag as S,ah as k,X as C,ai as A,p as I,aj as M,n as $,ak as z,k as R,A as Y,q as T,g as D,m as B,i as E,al as P,h as _,am as H,an as W,ao as L,ap as O,a4 as J,j as F}from"./antd-lXsGnH6e-1753160720439.js";import{g as K}from"./vendor-D2RBMdQ0-1753160720439.js";import{s as U}from"./securityService-DjlJ9x7B-1753160720439.js";import{u as q}from"./useMessage-CR2RbGdP-1753160720439.js";var G,Q={exports:{}};const X=K(G?Q.exports:(G=1,Q.exports=function(e,t,s){e=e||{};var a=t.prototype,i={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function r(e,t,s,i){return a.fromToBase(e,t,s,i)}s.en.relativeTime=i,a.fromToBase=function(t,a,r,l,n){for(var d,c,o,x=r.$locale().relativeTime||i,m=e.thresholds||[{l:"s",r:44,d:"second"},{l:"m",r:89},{l:"mm",r:44,d:"minute"},{l:"h",r:89},{l:"hh",r:21,d:"hour"},{l:"d",r:35},{l:"dd",r:25,d:"day"},{l:"M",r:45},{l:"MM",r:10,d:"month"},{l:"y",r:17},{l:"yy",d:"year"}],u=m.length,h=0;h<u;h+=1){var j=m[h];j.d&&(d=l?s(t).diff(r,j.d,!0):r.diff(t,j.d,!0));var p=(e.rounding||Math.round)(Math.abs(d));if(o=d>0,p<=j.r||!j.r){p<=1&&h>0&&(j=m[h-1]);var g=x[j.l];n&&(p=n(""+p)),c="string"==typeof g?g.replace("%d",p):g(p,a,j.l,o);break}}if(a)return c;var f=o?x.future:x.past;return"function"==typeof f?f(c):f.replace("%s",c)},a.to=function(e,t){return r(e,t,this,!0)},a.from=function(e,t){return r(e,t,this)};var l=function(e){return e.$u?s.utc():s()};a.toNow=function(e){return this.to(l(this),e)},a.fromNow=function(e){return this.from(l(this),e)}})),V=({style:t})=>{const{modal:s}=a.useApp(),[C,A]=i.useState([]),[I,M]=i.useState(null),[$,z]=i.useState(!1);i.useEffect(()=>{R();const e=setInterval(R,3e4);return()=>clearInterval(e)},[]);const R=async()=>{z(!0);try{const[e,t]=await Promise.all([U.getSecurityEvents(1,10,{resolved:!1}),U.getSecurityReport("1d")]);A(e.events),M(t)}catch(e){console.error("获取安全数据失败:",e)}finally{z(!1)}},Y=e=>({low:"#52c41a",medium:"#faad14",high:"#fa8c16",critical:"#ff4d4f"}[e]||"#d9d9d9"),T=e=>({login_fail:"登录失败",suspicious_activity:"可疑活动",policy_violation:"策略违规",security_alert:"安全警报"}[e]||e),D=C.filter(e=>"critical"===e.severity).length,B=C.filter(e=>"high"===e.severity).length,E=C.filter(e=>!e.resolved).length;return e.jsxs("div",{style:t,children:[e.jsxs(r,{gutter:16,style:{marginBottom:24},children:[e.jsx(l,{span:6,children:e.jsx(n,{children:e.jsx(d,{title:"安全状态",value:0===D?"正常":"警告",valueStyle:{color:0===D?"#52c41a":"#ff4d4f",fontSize:20},prefix:0===D?e.jsx(c,{style:{color:"#52c41a"}}):e.jsx(o,{style:{color:"#ff4d4f"}})})})}),e.jsx(l,{span:6,children:e.jsx(n,{children:e.jsx(d,{title:"待处理事件",value:E,valueStyle:{color:E>0?"#fa8c16":"#52c41a"},prefix:e.jsx(x,{})})})}),e.jsx(l,{span:6,children:e.jsx(n,{children:e.jsx(d,{title:"严重事件",value:D,valueStyle:{color:D>0?"#ff4d4f":"#52c41a"},prefix:e.jsx(o,{})})})}),e.jsx(l,{span:6,children:e.jsx(n,{children:e.jsx(d,{title:"今日登录",value:(null==I?void 0:I.loginAttempts)||0,prefix:e.jsx(m,{})})})})]}),D>0&&e.jsx(u,{message:"安全警报",description:`检测到 ${D} 个严重安全事件，请立即处理！`,type:"error",showIcon:!0,style:{marginBottom:24},action:e.jsx(h,{size:"small",danger:!0,children:"立即处理"})}),B>0&&0===D&&e.jsx(u,{message:"安全提醒",description:`检测到 ${B} 个高风险安全事件，建议及时处理。`,type:"warning",showIcon:!0,style:{marginBottom:24}}),e.jsxs(r,{gutter:16,children:[e.jsx(l,{span:16,children:e.jsx(n,{title:"最近安全事件",loading:$,extra:e.jsx(h,{type:"link",onClick:R,children:"刷新"}),children:e.jsx(j,{dataSource:C.slice(0,8),renderItem:t=>e.jsx(j.Item,{actions:[e.jsx(N,{title:"查看详情",children:e.jsx(h,{type:"link",size:"small",icon:e.jsx(b,{}),onClick:()=>{s.info({title:"事件详情",width:600,content:e.jsxs("div",{style:{marginTop:16},children:[e.jsxs("p",{children:[e.jsx("strong",{children:"事件类型:"})," ",T(t.type)]}),e.jsxs("p",{children:[e.jsx("strong",{children:"严重程度:"}),e.jsx(y,{color:Y(t.severity),style:{marginLeft:8},children:t.severity.toUpperCase()})]}),e.jsxs("p",{children:[e.jsx("strong",{children:"用户:"})," ",t.username||"-"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"IP地址:"})," ",t.ip]}),e.jsxs("p",{children:[e.jsx("strong",{children:"描述:"})," ",t.description]}),e.jsxs("p",{children:[e.jsx("strong",{children:"时间:"})," ",p(t.timestamp).format("YYYY-MM-DD HH:mm:ss")]}),t.details&&e.jsxs(e.Fragment,{children:[e.jsx("p",{children:e.jsx("strong",{children:"详细信息:"})}),e.jsx("pre",{style:{background:"#f5f5f5",padding:8,borderRadius:4,fontSize:12},children:JSON.stringify(t.details,null,2)})]})]})})}})}),!t.resolved&&e.jsx(N,{title:"标记为已解决",children:e.jsx(h,{type:"link",size:"small",icon:e.jsx(w,{}),onClick:()=>(async e=>{try{await U.resolveSecurityEvent(e,"admin"),R()}catch(t){console.error("解决事件失败:",t)}})(t.id)})})].filter(Boolean),children:e.jsx(j.Item.Meta,{avatar:e.jsx(v,{dot:!0,color:Y(t.severity),children:e.jsx("div",{style:{width:8,height:8}})}),title:e.jsxs(f,{children:[e.jsx("span",{children:T(t.type)}),e.jsx(y,{color:Y(t.severity),size:"small",children:t.severity.toUpperCase()}),!t.resolved&&e.jsx(y,{color:"orange",size:"small",children:"待处理"})]}),description:e.jsxs("div",{children:[e.jsx("div",{children:t.description}),e.jsxs("div",{style:{fontSize:12,color:"#999",marginTop:4},children:[e.jsx(g,{style:{marginRight:4}}),p(t.timestamp).fromNow()," • IP: ",t.ip,t.username&&` • 用户: ${t.username}`]})]})})})})})}),e.jsxs(l,{span:8,children:[e.jsx(n,{title:"威胁分析",loading:$,children:(null==I?void 0:I.topThreats)&&I.topThreats.length>0?e.jsx("div",{children:I.topThreats.slice(0,5).map((t,s)=>e.jsxs("div",{style:{marginBottom:16},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:4},children:[e.jsx("span",{style:{fontSize:14},children:T(t.type)}),e.jsxs("span",{style:{fontSize:12,color:"#666"},children:[t.count,"次"]})]}),e.jsx(S,{percent:t.percentage,size:"small",strokeColor:t.percentage>50?"#ff4d4f":"#1890ff"})]},s))}):e.jsxs("div",{style:{textAlign:"center",color:"#999",padding:20},children:[e.jsx(k,{style:{fontSize:32,marginBottom:8}}),e.jsx("div",{children:"暂无威胁数据"})]})}),e.jsx(n,{title:"安全建议",style:{marginTop:16},children:e.jsx(j,{size:"small",dataSource:["定期检查用户权限设置","监控异常登录活动","及时处理安全事件","保持系统更新","备份重要数据"],renderItem:(t,s)=>e.jsx(j.Item,{children:e.jsxs("div",{style:{fontSize:12},children:[e.jsx(v,{color:"#1890ff",style:{marginRight:8}}),t]})})})})]})]})]})};class Z{static async getAllData(){try{const[e,s,a,i,r,l,n,d,c,o]=await Promise.allSettled([t.get("/students"),t.get("/rewards"),t.get("/financial-aid"),t.get("/party/members"),t.get("/mental-health"),t.get("/classes"),t.get("/dormitories"),t.get("/teachers"),t.get("/certificates"),t.get("/academic-status")]);return{students:"fulfilled"===e.status&&e.value.success?e.value.data:[],rewardPunishments:"fulfilled"===s.status&&s.value.success?s.value.data:[],financialAid:"fulfilled"===a.status&&a.value.success?a.value.data:[],partyMembers:"fulfilled"===i.status&&i.value.success?i.value.data:[],mentalHealth:"fulfilled"===r.status&&r.value.success?r.value.data:[],classes:"fulfilled"===l.status&&l.value.success?l.value.data:[],dormitories:"fulfilled"===n.status&&n.value.success?n.value.data:[],teachers:"fulfilled"===d.status&&d.value.success?d.value.data:[],certificates:"fulfilled"===c.status&&c.value.success?c.value.data:[],academicStatus:"fulfilled"===o.status&&o.value.success?o.value.data:[]}}catch(e){throw console.error("获取工作台数据失败:",e),e}}static calculateStatistics(e){const{students:t,rewardPunishments:s,financialAid:a,partyMembers:i,mentalHealth:r,certificates:l,academicStatus:n,classes:d,dormitories:c,teachers:o}=e,x=t.length,m=t.filter(e=>"active"===e.status).length,u=t.filter(e=>"warning"===e.status).length,h=i.length,j=i.filter(e=>"formal"===e.partyStatus).length,g=i.filter(e=>"probationary"===e.partyStatus).length,f=i.filter(e=>"activist"===e.partyStatus).length,y=s.filter(e=>"reward"===e.type).length,v=s.filter(e=>"reward"===e.type&&p(e.date).isAfter(p().subtract(1,"month"))).length,N=a.length,b=a.filter(e=>"approved"===e.reviewStatus).length,w=a.filter(e=>"pending"===e.reviewStatus).length,S=r.length,k=r.filter(e=>"crisis"===e.status).length,C=r.filter(e=>"attention"===e.status).length,A=l.length,I=l.filter(e=>"pending"===e.status).length,M=l.filter(e=>"approved"===e.status).length,$=n.filter(e=>"预警"===e.status||"严重"===e.status).length,z=n.filter(e=>"严重"===e.status).length,R=d.length,Y=c.length,T=o.length,D=p().format("YYYY-MM-DD");return{totalStudents:x,activeStudents:m,warningStudents:u,totalPartyMembers:h,formalMembers:j,probationaryMembers:g,activists:f,totalRewards:y,monthlyRewards:v,totalAidApplications:N,approvedAid:b,pendingAid:w,totalMentalRecords:S,crisisCount:k,attentionCount:C,totalCertificates:A,pendingCertificates:I,approvedCertificates:M,academicWarnings:$,seriousWarnings:z,totalClasses:R,totalDormitories:Y,totalTeachers:T,todayRewards:s.filter(e=>p(e.date).format("YYYY-MM-DD")===D).length,todayApplications:a.filter(e=>p(e.submittedAt).format("YYYY-MM-DD")===D).length}}static async getRecentActivities(){try{const e=await t.get("/analytics/recent-activities");return e.success?e.data.map(e=>({...e,time:p(e.time).fromNow()})):[]}catch(e){return console.error("获取最近活动失败:",e),this.generateRecentActivities({})}}static generateRecentActivities(e){const t=[],{rewardPunishments:s,financialAid:a,partyMembers:i,mentalHealth:r,certificates:l,academicStatus:n,students:d}=e;s.slice(0,3).forEach(e=>{t.push({id:`reward_${e.id}`,title:`${e.studentName||"学生"}${"reward"===e.type?"获得":"受到"}${e.title||e.reason||"奖惩"}`,description:`${e.class||""} · ${e.major||""}`,time:p(e.createdAt||e.date).fromNow(),type:"reward"===e.type?"award":"punishment",studentId:e.studentId,module:"rewards-punishments"})}),a.slice(0,3).forEach(e=>{t.push({id:`aid_${e.id}`,title:`${e.studentName||"学生"}申请${"national"===e.aidType?"国家":"school"===e.aidType?"校级":"社会"}资助`,description:`申请金额：¥${e.amount?e.amount.toLocaleString():"未知"}`,time:p(e.submittedAt||e.createdAt).fromNow(),type:"financial",studentId:e.studentId,module:"financial-aid"})}),r.slice(0,2).forEach(e=>{t.push({id:`mental_${e.id}`,title:`${e.studentName||"学生"}心理健康评估`,description:`评估结果：${{excellent:"优秀",good:"良好",normal:"正常",concerning:"关注",crisis:"危险"}[e.mentalState]||e.status||"正常"}`,time:p(e.createdAt).fromNow(),type:"psychology",studentId:e.studentId,module:"mental-health"})}),l.slice(0,2).forEach(e=>{t.push({id:`cert_${e.id}`,title:`${e.studentName||"学生"}申请${e.certificateType||"证明"}开具`,description:"状态："+("pending"===e.status?"待审核":"approved"===e.status?"已通过":"rejected"===e.status?"已拒绝":"处理中"),time:p(e.createdAt||e.applicationDate).fromNow(),type:"certificate",studentId:e.studentId,module:"certificates"})}),i.slice(0,2).forEach(e=>{const s={none:"申请入党",activist:"成为积极分子",probationary:"成为预备党员",formal:"转为正式党员"};t.push({id:`party_${e.id}`,title:`${e.studentName||"学生"}${s[e.partyStatus]||"党建活动"}`,description:`当前状态：${s[e.partyStatus]||e.partyStatus||"未知"}`,time:p(e.updatedAt||e.createdAt).fromNow(),type:"party",studentId:e.studentId,module:"party-work"})}),n.slice(0,2).forEach(e=>{t.push({id:`academic_${e.id}`,title:`${e.studentName||"学生"}学业状态更新`,description:`状态：${e.status||"正常"}${e.failedCourses?` · 挂科：${e.failedCourses}`:""}`,time:p(e.updatedAt||e.createdAt).fromNow(),type:"严重"===e.status?"warning":"academic",studentId:e.studentId,module:"academic-status"})});return d.filter(e=>e.updatedAt&&p(e.updatedAt).isAfter(p().subtract(7,"days"))).slice(0,2).forEach(e=>{t.push({id:`student_${e.id}`,title:`${e.name||"学生"}信息更新`,description:`${e.class||""} · ${e.major||""}`,time:p(e.updatedAt).fromNow(),type:"student",studentId:e.id,module:"students"})}),t.filter(e=>e.time).sort((e,t)=>{const s=p(e.time.includes("前")?p().subtract(1,"hour"):e.time);return p(t.time.includes("前")?p().subtract(1,"hour"):t.time).valueOf()-s.valueOf()}).slice(0,12)}static getAttentionStudents(e){const{students:t,mentalHealth:s}=e,a=[];return t.filter(e=>"warning"===e.status).forEach(e=>{a.push({...e,reason:"学业预警",level:"warning",type:"academic"})}),s.filter(e=>"crisis"===e.status||"attention"===e.status).forEach(e=>{const s=t.find(t=>t.studentId===e.studentId);s&&a.push({...s,reason:"crisis"===e.status?"心理危机":"心理关注",level:"crisis"===e.status?"danger":"attention",type:"mental",mentalRecord:e})}),a.slice(0,10)}static getWeatherInfo(){return{temperature:"22°C",weather:"晴",humidity:"65%",windSpeed:"3级"}}static getTodaySchedule(){return[{time:"09:00",title:"学生工作例会",type:"meeting"},{time:"14:00",title:"心理健康咨询",type:"counseling"},{time:"16:00",title:"党建活动筹备",type:"party"}]}static getNotifications(){return[{id:1,title:"新的奖学金申请待审核",type:"info",time:"5分钟前"},{id:2,title:"学生张三心理评估异常",type:"warning",time:"10分钟前"},{id:3,title:"宿舍安全检查提醒",type:"info",time:"1小时前"}]}}p.extend(X);const ee=()=>{const t=s(),a=q(),[d,o]=i.useState(!1),[m,u]=i.useState("overview"),[f,N]=i.useState({students:[],rewardPunishments:[],financialAid:[],partyMembers:[],mentalHealth:[],classes:[],dormitories:[],teachers:[],certificates:[],academicStatus:[]}),[b,k]=i.useState({temperature:"22°C",weather:"晴",humidity:"65%",windSpeed:"3级"}),[K,U]=i.useState([{time:"09:00",title:"学生工作例会",type:"meeting"},{time:"14:00",title:"心理健康咨询",type:"counseling"},{time:"16:00",title:"党建活动筹备",type:"party"}]),[G,Q]=i.useState([{id:1,title:"新的奖学金申请待审核",type:"info",time:"5分钟前"},{id:2,title:"学生张三心理评估异常",type:"warning",time:"10分钟前"},{id:3,title:"宿舍安全检查提醒",type:"info",time:"1小时前"}]),X=async()=>{o(!0);try{const[e,t]=await Promise.all([Z.getAllData(),Z.getRecentActivities()]);N(e),se(t),k(Z.getWeatherInfo()),U(Z.getTodaySchedule()),Q(Z.getNotifications()),console.log("✅ 工作台数据加载完成:",{"学生数量":e.students.length,"最近活动":t.length})}catch(e){console.error("加载数据失败:",e),a.error("加载工作台数据失败")}finally{o(!1)}};i.useEffect(()=>{X()},[]);const ee=Z.calculateStatistics(f),[te,se]=i.useState([]),ae=t=>{switch(t){case"award":return e.jsx(B,{className:"text-yellow-500"});case"punishment":return e.jsx(x,{className:"text-red-500"});case"psychology":return e.jsx(I,{className:"text-pink-500"});case"financial":return e.jsx($,{className:"text-green-500"});case"warning":return e.jsx(M,{className:"text-red-500"});case"party":return e.jsx(E,{className:"text-red-500"});case"certificate":return e.jsx(P,{className:"text-purple-500"});case"academic":return e.jsx(F,{className:"text-blue-500"});case"student":return e.jsx(D,{className:"text-cyan-500"});default:return e.jsx(T,{className:"text-gray-500"})}},ie=t=>{switch(t){case"award":return e.jsx(y,{color:"gold",children:"奖励"});case"punishment":return e.jsx(y,{color:"red",children:"处分"});case"psychology":return e.jsx(y,{color:"magenta",children:"心理健康"});case"financial":return e.jsx(y,{color:"green",children:"贫困补助"});case"warning":return e.jsx(y,{color:"orange",children:"学业预警"});case"party":return e.jsx(y,{color:"red",children:"党建工作"});case"certificate":return e.jsx(y,{color:"purple",children:"证明开具"});case"academic":return e.jsx(y,{color:"blue",children:"学业状态"});case"student":return e.jsx(y,{color:"cyan",children:"学生信息"});default:return e.jsx(y,{children:"其他"})}},re=[{title:"在校学生总数",value:ee.totalStudents,icon:e.jsx(D,{className:"text-blue-500"}),color:"blue",trend:"+2.5%",onClick:()=>t("/students")},{title:"党员人数",value:ee.totalPartyMembers,icon:e.jsx(E,{className:"text-red-500"}),color:"red",trend:"+1.2%",onClick:()=>t("/party-work")},{title:"学业预警",value:ee.academicWarnings,icon:e.jsx(M,{className:"text-orange-500"}),color:"orange",trend:"-0.8%",onClick:()=>t("/academic-status")},{title:"心理关注",value:ee.crisisCount+ee.attentionCount,icon:e.jsx(I,{className:"text-pink-500"}),color:"pink",trend:"-1.5%",onClick:()=>t("/mental-health")}],le=[{title:"今日奖惩",value:ee.todayRewards,icon:e.jsx(B,{className:"text-yellow-500"}),color:"yellow"},{title:"今日申请",value:ee.todayApplications,icon:e.jsx(T,{className:"text-green-500"}),color:"green"},{title:"待审证明",value:ee.pendingCertificates,icon:e.jsx(P,{className:"text-purple-500"}),color:"purple"},{title:"教师总数",value:ee.totalTeachers,icon:e.jsx(F,{className:"text-cyan-500"}),color:"cyan"}],ne=Z.getAttentionStudents(f);return e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-lg mb-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold mb-2",children:"👋 欢迎回来！"}),e.jsxs("p",{className:"text-blue-100 text-lg",children:["今天是 ",p().format("YYYY年MM月DD日 dddd"),"，",b.weather," ",b.temperature]}),e.jsxs("div",{className:"flex items-center space-x-4 mt-3 text-sm text-blue-100",children:[e.jsxs("span",{children:["💧 湿度 ",b.humidity]}),e.jsxs("span",{children:["🌪️ 风力 ",b.windSpeed]}),e.jsx("span",{children:"📊 系统运行正常"})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx(h,{icon:e.jsx(C,{}),onClick:X,loading:d,size:"large",className:"mb-3",children:"刷新数据"}),e.jsx("div",{className:"text-blue-100",children:e.jsxs("div",{children:["上次更新: ",p().format("HH:mm:ss")]})})]})]})}),e.jsx(A,{activeKey:m,onChange:u,size:"large",items:[{key:"overview",label:e.jsxs("span",{children:[e.jsx(_,{}),"数据概览"]}),children:e.jsxs("div",{className:"space-y-6",children:[(ee.crisisCount>0||ee.academicWarnings>0||ee.pendingAid>0)&&e.jsx(n,{className:"border-red-200 bg-gradient-to-r from-red-50 to-orange-50",style:{borderRadius:"12px"},title:e.jsxs("div",{className:"flex items-center",children:[e.jsx(z,{className:"text-red-500 mr-2"}),e.jsx("span",{className:"text-red-700 font-bold",children:"紧急提醒"})]}),children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[ee.crisisCount>0&&e.jsxs("div",{className:"bg-red-100 p-4 rounded-lg flex items-center space-x-3",children:[e.jsx("div",{className:"bg-red-500 text-white p-2 rounded-full",children:e.jsx(I,{})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-red-700 font-medium",children:"心理危机"}),e.jsxs("div",{className:"text-red-600",children:[ee.crisisCount,"名学生需要紧急干预"]})]}),e.jsx(h,{type:"primary",danger:!0,size:"small",onClick:()=>t("/mental-health"),children:"处理"})]}),ee.academicWarnings>0&&e.jsxs("div",{className:"bg-orange-100 p-4 rounded-lg flex items-center space-x-3",children:[e.jsx("div",{className:"bg-orange-500 text-white p-2 rounded-full",children:e.jsx(M,{})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-orange-700 font-medium",children:"学业预警"}),e.jsxs("div",{className:"text-orange-600",children:[ee.academicWarnings,"名学生学业异常"]})]}),e.jsx(h,{type:"primary",style:{background:"#fa8c16"},size:"small",onClick:()=>t("/academic-status"),children:"处理"})]}),ee.pendingAid>0&&e.jsxs("div",{className:"bg-blue-100 p-4 rounded-lg flex items-center space-x-3",children:[e.jsx("div",{className:"bg-blue-500 text-white p-2 rounded-full",children:e.jsx($,{})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-blue-700 font-medium",children:"补助申请"}),e.jsxs("div",{className:"text-blue-600",children:[ee.pendingAid,"份申请待审核"]})]}),e.jsx(h,{type:"primary",size:"small",onClick:()=>t("/financial-aid"),children:"处理"})]})]})}),e.jsx(n,{title:e.jsxs("div",{className:"flex items-center",children:[e.jsx(R,{className:"mr-2 text-blue-500"}),e.jsx("span",{children:"核心数据"})]}),style:{borderRadius:"12px"},children:e.jsx(r,{gutter:[24,24],children:re.map((t,s)=>{var a;return e.jsx(l,{xs:24,sm:12,lg:6,children:e.jsxs(n,{className:"hover:shadow-lg transition-all duration-300 cursor-pointer hover:scale-105",style:{borderRadius:"12px",background:`linear-gradient(135deg, var(--ant-${t.color}-1) 0%, var(--ant-${t.color}-2) 100%)`},onClick:t.onClick,styles:{body:{padding:"20px"}},children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("div",{className:"text-2xl",children:t.icon}),e.jsx(y,{color:(null==(a=t.trend)?void 0:a.startsWith("+"))?"green":"red",className:"text-xs",children:t.trend})]}),e.jsx("div",{className:"text-2xl font-bold mb-1",style:{color:`var(--ant-${t.color}-6)`},children:t.value}),e.jsx("div",{className:"text-gray-600 text-sm",children:t.title})]})},s)})})}),e.jsx(n,{title:e.jsxs("div",{className:"flex items-center",children:[e.jsx(g,{className:"mr-2 text-green-500"}),e.jsx("span",{children:"今日数据"})]}),style:{borderRadius:"12px"},children:e.jsx(r,{gutter:[16,16],children:le.map((t,s)=>e.jsx(l,{xs:12,sm:6,children:e.jsxs("div",{className:"text-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[e.jsx("div",{className:"text-2xl mb-2",children:t.icon}),e.jsx("div",{className:"text-xl font-bold mb-1",style:{color:`var(--ant-${t.color}-6)`},children:t.value}),e.jsx("div",{className:"text-gray-600 text-sm",children:t.title})]})},s))})}),e.jsxs(r,{gutter:[24,24],children:[e.jsx(l,{xs:24,lg:8,children:e.jsx(n,{title:"学业情况概览",extra:e.jsx(h,{type:"link",onClick:()=>t("/students"),children:"查看更多"}),className:"h-full",style:{borderRadius:"8px"},children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("span",{className:"text-gray-600",children:"正常学生"}),e.jsxs("span",{className:"font-semibold text-green-600",children:[ee.activeStudents,"人 (",ee.totalStudents>0?Math.round(ee.activeStudents/ee.totalStudents*100):0,"%)"]})]}),e.jsx(S,{percent:ee.totalStudents>0?Math.round(ee.activeStudents/ee.totalStudents*100):0,status:"active",strokeColor:"#52c41a",size:"small",style:{marginBottom:0}})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("span",{className:"text-gray-600",children:"预警学生"}),e.jsxs("span",{className:"font-semibold text-red-600",children:[ee.warningStudents,"人 (",ee.totalStudents>0?Math.round(ee.warningStudents/ee.totalStudents*100):0,"%)"]})]}),e.jsx(S,{percent:ee.totalStudents>0?Math.round(ee.warningStudents/ee.totalStudents*100):0,status:"active",strokeColor:"#ff4d4f",size:"small",style:{marginBottom:0}})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("span",{className:"text-gray-600",children:"心理关注"}),e.jsxs("span",{className:"font-semibold text-orange-600",children:[ee.attentionCount+ee.crisisCount,"人"]})]}),e.jsx(S,{percent:ee.totalStudents>0?Math.round((ee.attentionCount+ee.crisisCount)/ee.totalStudents*100):0,status:"active",strokeColor:"#faad14",size:"small",style:{marginBottom:0}})]})]})})}),e.jsx(l,{xs:24,lg:8,children:e.jsx(n,{title:"党建工作概览",extra:e.jsx(h,{type:"link",onClick:()=>t("/party-work"),children:"查看更多"}),className:"h-full",style:{borderRadius:"8px"},children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center p-3 bg-red-50 rounded-lg",children:[e.jsx("span",{className:"text-gray-700",children:"正式党员"}),e.jsxs("span",{className:"text-xl font-bold text-red-600",children:[ee.formalMembers,"人"]})]}),e.jsxs("div",{className:"flex justify-between items-center p-3 bg-orange-50 rounded-lg",children:[e.jsx("span",{className:"text-gray-700",children:"预备党员"}),e.jsxs("span",{className:"text-xl font-bold text-orange-600",children:[ee.probationaryMembers,"人"]})]}),e.jsxs("div",{className:"flex justify-between items-center p-3 bg-blue-50 rounded-lg",children:[e.jsx("span",{className:"text-gray-700",children:"入党积极分子"}),e.jsxs("span",{className:"text-xl font-bold text-blue-600",children:[ee.activists,"人"]})]}),e.jsxs("div",{className:"flex justify-between items-center p-3 bg-green-50 rounded-lg",children:[e.jsx("span",{className:"text-gray-700",children:"党员总数"}),e.jsxs("span",{className:"text-xl font-bold text-green-600",children:[ee.totalPartyMembers,"人"]})]})]})})}),e.jsx(l,{xs:24,lg:8,children:e.jsx(n,{title:"资助工作概览",extra:e.jsx(h,{type:"link",onClick:()=>t("/financial-aid"),children:"查看更多"}),className:"h-full",style:{borderRadius:"8px"},children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center p-3 bg-blue-50 rounded-lg",children:[e.jsx("span",{className:"text-gray-700",children:"申请总数"}),e.jsxs("span",{className:"text-xl font-bold text-blue-600",children:[ee.totalAidApplications,"人"]})]}),e.jsxs("div",{className:"flex justify-between items-center p-3 bg-green-50 rounded-lg",children:[e.jsx("span",{className:"text-gray-700",children:"已批准"}),e.jsxs("span",{className:"text-xl font-bold text-green-600",children:[ee.approvedAid,"人"]})]}),e.jsxs("div",{className:"flex justify-between items-center p-3 bg-orange-50 rounded-lg",children:[e.jsx("span",{className:"text-gray-700",children:"待审核"}),e.jsxs("span",{className:"text-xl font-bold text-orange-600",children:[ee.pendingAid,"人"]})]}),e.jsxs("div",{className:"flex justify-between items-center p-3 bg-purple-50 rounded-lg",children:[e.jsx("span",{className:"text-gray-700",children:"通过率"}),e.jsxs("span",{className:"text-xl font-bold text-purple-600",children:[ee.totalAidApplications>0?Math.round(ee.approvedAid/ee.totalAidApplications*100):0,"%"]})]})]})})})]}),e.jsxs(r,{gutter:[24,24],children:[e.jsx(l,{xs:24,lg:12,children:e.jsx(n,{title:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(x,{className:"text-orange-500"}),e.jsx("span",{children:"需要关注的学生"}),e.jsx(v,{count:ne.length})]}),extra:e.jsx(h,{type:"link",onClick:()=>t("/students"),children:"查看全部"}),style:{borderRadius:"8px"},children:ne.length>0?e.jsx(j,{itemLayout:"horizontal",dataSource:ne,renderItem:s=>e.jsx(j.Item,{className:"hover:bg-gray-50 px-4 py-3 rounded-lg transition-colors cursor-pointer",onClick:()=>t(`/students/${s.studentId}`),children:e.jsx(j.Item.Meta,{avatar:e.jsx(Y,{icon:"mental"===s.type?e.jsx(I,{}):e.jsx(M,{}),style:{backgroundColor:"danger"===s.level?"#ff4d4f":"warning"===s.level?"#faad14":"#1890ff"}}),title:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"font-medium",children:s.name}),e.jsx(y,{color:"danger"===s.level?"red":"warning"===s.level?"orange":"blue",children:s.reason})]}),description:e.jsxs("div",{className:"text-sm text-gray-600",children:[e.jsxs("div",{children:[s.studentId," · ",s.class]}),e.jsx("div",{children:s.major})]})})})}):e.jsxs("div",{className:"text-center py-8 text-gray-500",children:[e.jsx(w,{className:"text-4xl mb-2"}),e.jsx("div",{children:"暂无需要特别关注的学生"})]})})}),e.jsx(l,{xs:24,lg:12,children:e.jsx(n,{title:"最近活动",extra:e.jsx(h,{type:"link",children:"查看全部"}),style:{borderRadius:"8px"},children:te.length>0?e.jsx(j,{itemLayout:"horizontal",dataSource:te,renderItem:s=>e.jsx(j.Item,{className:"hover:bg-gray-50 px-4 py-3 rounded-lg transition-colors cursor-pointer",onClick:()=>{var e;(e=s).studentId?t(`/students/${e.studentId}`):e.module&&t(`/${e.module}`)},children:e.jsx(j.Item.Meta,{avatar:e.jsx(Y,{icon:ae(s.type),size:40,className:"flex items-center justify-center"}),title:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("span",{className:"font-medium text-gray-800",children:s.title}),ie(s.type)]}),description:e.jsxs("div",{className:"mt-1",children:[e.jsx("div",{className:"text-gray-600",children:s.description}),e.jsx("div",{className:"text-gray-400 text-sm mt-2",children:s.time})]})})})}):e.jsxs("div",{className:"text-center py-8 text-gray-500",children:[e.jsx(T,{className:"text-4xl mb-2"}),e.jsx("div",{children:"暂无最近活动"})]})})})]}),e.jsx(n,{title:e.jsxs("div",{className:"flex items-center",children:[e.jsx(z,{className:"mr-2 text-purple-500"}),e.jsx("span",{children:"快速操作"})]}),style:{borderRadius:"12px"},children:e.jsxs(r,{gutter:[16,16],children:[e.jsx(l,{xs:12,sm:8,md:6,lg:4,children:e.jsx("div",{className:"p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg cursor-pointer hover:shadow-md transition-all duration-300 hover:scale-105",onClick:()=>t("/students"),children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl text-blue-500 mb-2",children:e.jsx(D,{})}),e.jsx("div",{className:"font-medium text-gray-700",children:"学生管理"}),e.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"查看和管理学生信息"})]})})}),e.jsx(l,{xs:12,sm:8,md:6,lg:4,children:e.jsx("div",{className:"p-4 bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg cursor-pointer hover:shadow-md transition-all duration-300 hover:scale-105",onClick:()=>t("/rewards-punishments"),children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl text-yellow-500 mb-2",children:e.jsx(B,{})}),e.jsx("div",{className:"font-medium text-gray-700",children:"奖惩管理"}),e.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"记录学生奖惩情况"})]})})}),e.jsx(l,{xs:12,sm:8,md:6,lg:4,children:e.jsx("div",{className:"p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg cursor-pointer hover:shadow-md transition-all duration-300 hover:scale-105",onClick:()=>t("/financial-aid"),children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl text-green-500 mb-2",children:e.jsx($,{})}),e.jsx("div",{className:"font-medium text-gray-700",children:"贫困补助"}),e.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"管理资助申请"})]})})}),e.jsx(l,{xs:12,sm:8,md:6,lg:4,children:e.jsx("div",{className:"p-4 bg-gradient-to-br from-red-50 to-red-100 rounded-lg cursor-pointer hover:shadow-md transition-all duration-300 hover:scale-105",onClick:()=>t("/party-work"),children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl text-red-500 mb-2",children:e.jsx(E,{})}),e.jsx("div",{className:"font-medium text-gray-700",children:"党建工作"}),e.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"党员发展管理"})]})})}),e.jsx(l,{xs:12,sm:8,md:6,lg:4,children:e.jsx("div",{className:"p-4 bg-gradient-to-br from-pink-50 to-pink-100 rounded-lg cursor-pointer hover:shadow-md transition-all duration-300 hover:scale-105",onClick:()=>t("/mental-health"),children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl text-pink-500 mb-2",children:e.jsx(I,{})}),e.jsx("div",{className:"font-medium text-gray-700",children:"心理健康"}),e.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"心理状态跟踪"})]})})}),e.jsx(l,{xs:12,sm:8,md:6,lg:4,children:e.jsx("div",{className:"p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg cursor-pointer hover:shadow-md transition-all duration-300 hover:scale-105",onClick:()=>t("/certificates"),children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl text-purple-500 mb-2",children:e.jsx(P,{})}),e.jsx("div",{className:"font-medium text-gray-700",children:"证明开具"}),e.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"学生证明管理"})]})})})]})})]})},{key:"schedule",label:e.jsxs("span",{children:[e.jsx(O,{}),"日程安排"]}),children:e.jsx("div",{className:"space-y-6",children:e.jsxs(r,{gutter:[24,24],children:[e.jsx(l,{xs:24,lg:12,children:e.jsx(n,{title:e.jsxs("div",{className:"flex items-center",children:[e.jsx(O,{className:"mr-2 text-blue-500"}),e.jsx("span",{children:"今日日程"})]}),style:{borderRadius:"12px"},extra:e.jsx(h,{type:"primary",size:"small",icon:e.jsx(L,{}),children:"添加日程"}),children:K.length>0?e.jsx(H,{children:K.map((t,s)=>e.jsx(H.Item,{color:"meeting"===t.type?"blue":"counseling"===t.type?"green":"red",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:t.title}),e.jsx("div",{className:"text-gray-500 text-sm",children:t.time})]}),e.jsx(y,{color:"meeting"===t.type?"blue":"counseling"===t.type?"green":"red",children:"meeting"===t.type?"会议":"counseling"===t.type?"咨询":"活动"})]})},s))}):e.jsx(W,{description:"今日暂无安排"})})}),e.jsx(l,{xs:24,lg:12,children:e.jsx(n,{title:e.jsxs("div",{className:"flex items-center",children:[e.jsx(J,{className:"mr-2 text-orange-500"}),e.jsx("span",{children:"通知消息"}),e.jsx(v,{count:G.length,className:"ml-2"})]}),style:{borderRadius:"12px"},extra:e.jsx(h,{type:"link",size:"small",children:"查看全部"}),children:e.jsx(j,{dataSource:G,renderItem:t=>e.jsx(j.Item,{className:"hover:bg-gray-50 px-3 py-2 rounded-lg transition-colors",children:e.jsx(j.Item.Meta,{avatar:e.jsx(Y,{icon:e.jsx(J,{}),style:{backgroundColor:"warning"===t.type?"#faad14":"#1890ff"}}),title:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"font-medium",children:t.title}),e.jsx("span",{className:"text-gray-400 text-xs",children:t.time})]})})})})})})]})})},{key:"security",label:e.jsxs("span",{children:[e.jsx(c,{}),"安全监控"]}),children:e.jsx(V,{})}]})]})};export{ee as default};
