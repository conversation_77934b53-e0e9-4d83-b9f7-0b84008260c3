import{j as e}from"./index-DP6eZxW9.js";import{r as s,K as a,a as t,X as r,F as n,by as l,ae as c,Q as i}from"./antd-DYv0PFJq.js";import"./vendor-D2RBMdQ0.js";const{Panel:d}=l,o=()=>{const[o,m]=s.useState([]),[x,h]=s.useState(!1),y=()=>{h(!0);const e=[{key:"studentsList",name:"学生列表"},{key:"importedStudentsData",name:"导入学生数据"},{key:"classesList",name:"班级列表"},{key:"coursesList",name:"课程列表"},{key:"dormitoriesList",name:"宿舍列表"},{key:"rewardPunishmentRecords",name:"奖惩记录"},{key:"financialAidApplications",name:"贫困补助申请"},{key:"partyWorkRecords",name:"党建工作记录"},{key:"mentalHealthRecords",name:"心理健康记录"},{key:"certificateApplications",name:"证明申请"},{key:"academicStatusList",name:"学业状态列表"},{key:"academicRecordsList",name:"学业记录列表"},{key:"academicRecordsData",name:"学业记录数据"},{key:"teacherBasicInfo",name:"教师基础信息"},{key:"courseAssignments",name:"任课情况"},{key:"teachingMaterials",name:"教学材料"},{key:"teachingAchievements",name:"教学成果"},{key:"trainingRecords",name:"师资培训记录"},{key:"mentorshipProjects",name:"青蓝筑梦项目"}].map(({key:e,name:s})=>{const a=localStorage.getItem(e);let t=null,r=0,n="array";if(a)try{t=JSON.parse(a),Array.isArray(t)?(r=t.length,n="array"):"object"==typeof t&&null!==t&&(r=Object.keys(t).length,n="object")}catch(l){console.error(`解析 ${e} 失败:`,l),t={error:"数据解析失败"}}return{key:e,name:s,data:t,count:r,type:n}});m(e),h(!1)};s.useEffect(()=>{y()},[]);const j=s=>{if(!s.data)return e.jsx("div",{className:"text-gray-500",children:"无数据"});if(s.data.error)return e.jsx("div",{className:"text-red-500",children:s.data.error});if("array"===s.type&&Array.isArray(s.data)){if(0===s.data.length)return e.jsx("div",{className:"text-gray-500",children:"空数组"});const a=s.data.slice(0,3);return e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2",children:[e.jsx(i,{color:"blue",children:"数组"}),e.jsxs(i,{color:"green",children:[s.data.length," 条记录"]})]}),e.jsx("pre",{className:"bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40",children:JSON.stringify(a,null,2)})]})}if("object"===s.type){const a=Object.keys(s.data);if(0===a.length)return e.jsx("div",{className:"text-gray-500",children:"空对象"});const t=a.slice(0,3).reduce((e,a)=>(e[a]=s.data[a],e),{});return e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2",children:[e.jsx(i,{color:"orange",children:"对象"}),e.jsxs(i,{color:"green",children:[a.length," 个键"]})]}),e.jsxs("div",{className:"mb-2 text-xs text-gray-600",children:["键名: ",a.slice(0,5).join(", "),a.length>5?"...":""]}),e.jsx("pre",{className:"bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40",children:JSON.stringify(t,null,2)})]})}return e.jsx("div",{className:"text-gray-500",children:"未知数据类型"})},g=o.filter(e=>e.count>0),u=o.filter(e=>0===e.count);return e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-800 flex items-center",children:[e.jsx(a,{className:"mr-3 text-blue-500"}),"数据库调试工具"]}),e.jsx("p",{className:"text-gray-600",children:"检查localStorage中的所有数据模块"})]}),e.jsx(t,{type:"primary",icon:e.jsx(r,{}),onClick:y,loading:x,children:"刷新数据"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs(n,{title:"数据概览",className:"text-center",children:[e.jsx("div",{className:"text-3xl font-bold text-blue-600",children:o.length}),e.jsx("div",{className:"text-gray-600",children:"总模块数"})]}),e.jsxs(n,{title:"有数据模块",className:"text-center",children:[e.jsx("div",{className:"text-3xl font-bold text-green-600",children:g.length}),e.jsx("div",{className:"text-gray-600",children:"包含数据"})]}),e.jsxs(n,{title:"空模块",className:"text-center",children:[e.jsx("div",{className:"text-3xl font-bold text-orange-600",children:u.length}),e.jsx("div",{className:"text-gray-600",children:"无数据"})]})]}),g.length>0&&e.jsx(n,{title:"有数据的模块",className:"mb-6",children:e.jsx(l,{children:g.map(s=>e.jsx(d,{header:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{children:s.name}),e.jsxs(c,{children:[e.jsxs(i,{color:"blue",children:[s.count," 条"]}),e.jsx(i,{color:"array"===s.type?"green":"orange",children:s.type})]})]}),children:j(s)},s.key))})}),u.length>0&&e.jsx(n,{title:"空模块",className:"mb-6",children:e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2",children:u.map(s=>e.jsx("div",{className:"p-2 bg-gray-50 rounded text-sm text-gray-600",children:s.name},s.key))})})]})};export{o as default};
