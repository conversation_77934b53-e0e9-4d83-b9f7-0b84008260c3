import{u as e,j as s,r as a}from"./index-DP6eZxW9.js";import{r as l,aq as r,ae as t,a as n,X as i,y as c,W as d,ao as o,G as x,H as m,F as u,J as h,l as j,g as p,aa as y,I as g,aL as f,as as v,ay as b,M as w,aM as C,ag as N,Q as k,T as I,af as S,aB as _}from"./antd-DYv0PFJq.js";import{u as F,a as $}from"./useMessage-DDIprusu.js";import"./vendor-D2RBMdQ0.js";const{Option:q}=v,z=()=>{const z=e(),O=F(),T=$(),[L,M]=l.useState([]),[P,W]=l.useState(!1),[J,K]=l.useState(""),[B,D]=l.useState(""),[Q,V]=l.useState(""),[A,E]=l.useState(!1),[G,H]=l.useState(!1),[X,R]=l.useState(null),[U,Y]=l.useState(null),[Z,ee]=l.useState([]),[se]=r.useForm(),ae=async()=>{W(!0);try{const e=await a.get("/dormitories");if(e.success){const s=e.data.map(e=>({id:e.id,buildingName:e.building_name,roomNumber:e.room_number,floor:e.floor,roomType:e.room_type,maxCapacity:e.max_capacity,currentOccupancy:parseInt(e.actual_occupancy)||0,gender:e.gender,facilities:"string"==typeof e.facilities?JSON.parse(e.facilities||"[]"):e.facilities||[],manager:e.manager||"待分配",managerPhone:e.manager_phone||"",monthlyFee:e.monthly_fee||0,status:e.status,createdAt:e.created_at?new Date(e.created_at).toISOString().split("T")[0]:""}));M(s)}else O.error("获取宿舍列表失败")}catch(e){console.error("获取宿舍列表错误:",e),O.error("获取宿舍列表失败")}finally{W(!1)}};l.useEffect(()=>{ae()},[]);const le=L.filter(e=>{const s=!J||e.buildingName.toLowerCase().includes(J.toLowerCase())||e.roomNumber.toLowerCase().includes(J.toLowerCase())||e.manager.toLowerCase().includes(J.toLowerCase()),a=!B||e.buildingName===B,l=!Q||e.status===Q;return s&&a&&l}),re=[{title:"宿舍信息",key:"dormInfo",render:(e,a)=>s.jsxs("div",{children:[s.jsxs("div",{className:"font-medium",children:[a.buildingName," ",a.roomNumber]}),s.jsxs("div",{className:"text-gray-500 text-xs",children:[a.floor,"楼 · ",a.roomType]})]})},{title:"入住情况",key:"occupancy",render:(e,a)=>s.jsxs("div",{className:"text-center",children:[s.jsxs("div",{className:"font-semibold",children:[a.currentOccupancy,"/",a.maxCapacity]}),s.jsx(N,{percent:Math.round(a.currentOccupancy/a.maxCapacity*100),size:"small",status:a.currentOccupancy===a.maxCapacity?"success":"active"})]})},{title:"性别限制",dataIndex:"gender",key:"gender",render:e=>{const a={male:{color:"blue",text:"男生"},female:{color:"pink",text:"女生"},mixed:{color:"purple",text:"混合"}}[e];return s.jsx(k,{color:a.color,children:a.text})}},{title:"设施",dataIndex:"facilities",key:"facilities",render:e=>s.jsxs("div",{className:"flex flex-wrap gap-1",children:[e.slice(0,3).map((e,a)=>s.jsx(k,{size:"small",children:e},a)),e.length>3&&s.jsxs(k,{size:"small",children:["+",e.length-3]})]})},{title:"宿管",key:"manager",render:(e,a)=>s.jsxs("div",{children:[s.jsx("div",{children:a.manager}),s.jsx("div",{className:"text-gray-500 text-xs",children:a.managerPhone})]})},{title:"月租费",dataIndex:"monthlyFee",key:"monthlyFee",render:e=>`¥${e}`},{title:"状态",dataIndex:"status",key:"status",render:e=>{const a={available:{color:"green",text:"可入住"},full:{color:"orange",text:"已满员"},maintenance:{color:"blue",text:"维修中"},closed:{color:"red",text:"已关闭"}}[e];return s.jsx(k,{color:a.color,children:a.text})}},{title:"操作",key:"action",render:(e,l)=>s.jsxs(t,{children:[s.jsx(I,{title:"查看住宿学生",children:s.jsx(n,{type:"text",icon:s.jsx(S,{}),onClick:()=>(async e=>{Y(e),H(!0);try{const s=await a.get(`/dormitories/${e.id}/students`);if(s.success){const e=s.data.students.map((e,s)=>({id:e.id,studentId:e.student_id,name:e.name,gender:e.gender,major:e.major||"未知专业",class:e.class||"未知班级",phone:e.phone||"",bedNumber:`${s+1}号床`}));ee(e)}else O.error("获取宿舍学生信息失败"),ee([])}catch(s){console.error("获取宿舍学生信息错误:",s),O.error("获取宿舍学生信息失败"),ee([])}})(l)})}),s.jsx(I,{title:"编辑",children:s.jsx(n,{type:"text",icon:s.jsx(_,{}),onClick:()=>(e=>{R(e),se.setFieldsValue(e),E(!0)})(l)})}),s.jsx(I,{title:"删除",children:s.jsx(n,{type:"text",danger:!0,icon:s.jsx(d,{}),onClick:()=>(e=>{T.confirm({title:"确认删除",content:s.jsxs("div",{children:[s.jsxs("p",{children:["确定要删除宿舍 ",e.buildingName," ",e.roomNumber," 吗？"]}),s.jsx("p",{className:"text-red-600 mt-2",children:"⚠️ 删除宿舍后，该宿舍的学生住宿信息将被清空。"})]}),onOk:async()=>{try{(await a.delete(`/dormitories/${e.id}`)).success?(O.success("删除成功"),await ae()):O.error("删除失败")}catch(s){console.error("删除宿舍错误:",s),O.error("删除失败")}}})})(l)})})]})}],te=[{title:"学号",dataIndex:"studentId",key:"studentId"},{title:"姓名",dataIndex:"name",key:"name",render:(e,a)=>s.jsx(n,{type:"link",onClick:()=>z(`/students/${a.studentId}`),className:"p-0 h-auto",children:e})},{title:"专业班级",key:"majorClass",render:(e,a)=>s.jsxs("div",{children:[s.jsx("div",{children:a.major}),s.jsx("div",{className:"text-gray-500 text-xs",children:a.class})]})},{title:"床位号",dataIndex:"bedNumber",key:"bedNumber"},{title:"联系电话",dataIndex:"phone",key:"phone"}],ne=[...new Set(L.map(e=>e.buildingName))],ie=L.length,ce=L.reduce((e,s)=>e+s.maxCapacity,0),de=L.reduce((e,s)=>e+s.currentOccupancy,0),oe=ce>0?de/ce*100:0,xe=L.filter(e=>"available"===e.status).length;return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"宿舍管理"}),s.jsx("p",{className:"text-gray-600",children:"管理学生宿舍信息和住宿安排"})]}),s.jsxs(t,{children:[s.jsx(n,{icon:s.jsx(i,{}),onClick:ae,size:"large",children:"刷新数据"}),s.jsx(n,{icon:s.jsx(c,{}),onClick:async()=>{W(!0);try{const e=await a.post("/dormitories/sync-from-students");if(e.success){const{syncedCount:s,skippedCount:a,totalFound:l}=e.data;O.success(`同步完成：新增 ${s} 间宿舍，更新 ${a} 间已存在宿舍，共发现 ${l} 间宿舍`),await ae()}else O.error("同步宿舍失败")}catch(e){console.error("同步宿舍错误:",e),O.error("同步宿舍失败")}finally{W(!1)}},size:"large",loading:P,children:"同步学生宿舍"}),s.jsx(n,{danger:!0,icon:s.jsx(d,{}),onClick:async()=>{T.confirm({title:"确认清理",content:"确定要清理所有没有学生入住的宿舍吗？此操作不可撤销。",onOk:async()=>{W(!0);try{const e=await a.post("/dormitories/cleanup-empty");if(e.success){const{deletedCount:s,deletedDormitories:a}=e.data;O.success(`清理完成：删除了 ${s} 间空宿舍`),console.log("已删除的宿舍:",a),await ae()}else O.error("清理失败")}catch(e){console.error("清理空宿舍错误:",e),O.error("清理失败")}finally{W(!1)}}})},size:"large",loading:P,children:"清理空宿舍"}),s.jsx(n,{type:"primary",icon:s.jsx(o,{}),onClick:()=>{R(null),se.resetFields(),E(!0)},size:"large",children:"新建宿舍"})]})]}),s.jsxs(x,{gutter:16,children:[s.jsx(m,{span:6,children:s.jsx(u,{children:s.jsx(h,{title:"宿舍总数",value:ie,prefix:s.jsx(j,{})})})}),s.jsx(m,{span:6,children:s.jsx(u,{children:s.jsx(h,{title:"总床位数",value:ce,prefix:s.jsx(p,{})})})}),s.jsx(m,{span:6,children:s.jsx(u,{children:s.jsx(h,{title:"入住率",value:oe,precision:1,suffix:"%",valueStyle:{color:oe>90?"#cf1322":"#3f8600"}})})}),s.jsx(m,{span:6,children:s.jsx(u,{children:s.jsx(h,{title:"可入住宿舍",value:xe,valueStyle:{color:"#1890ff"},prefix:s.jsx(y,{})})})})]}),s.jsxs(u,{children:[s.jsxs("div",{className:"flex items-center space-x-4 mb-4",children:[s.jsx(g,{placeholder:"搜索宿舍楼、房间号或宿管",prefix:s.jsx(f,{}),value:J,onChange:e=>K(e.target.value),style:{width:300}}),s.jsx(v,{placeholder:"选择宿舍楼",value:B,onChange:D,allowClear:!0,style:{width:150},children:ne.map(e=>s.jsx(q,{value:e,children:e},e))}),s.jsxs(v,{placeholder:"宿舍状态",value:Q,onChange:V,allowClear:!0,style:{width:120},children:[s.jsx(q,{value:"available",children:"可入住"},"available"),s.jsx(q,{value:"full",children:"已满员"},"full"),s.jsx(q,{value:"maintenance",children:"维修中"},"maintenance"),s.jsx(q,{value:"closed",children:"已关闭"},"closed")]})]}),s.jsx(b,{columns:re,dataSource:le,rowKey:"id",loading:P,pagination:{total:le.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 间宿舍`},scroll:{x:1200}})]}),s.jsx(w,{title:X?"编辑宿舍":"新建宿舍",open:A,onCancel:()=>E(!1),footer:null,width:600,children:s.jsxs(r,{form:se,layout:"vertical",onFinish:async e=>{try{const s={building_name:e.buildingName,room_number:e.roomNumber,floor:e.floor,room_type:e.roomType,max_capacity:e.maxCapacity,gender:e.gender,facilities:e.facilities,manager:e.manager,manager_phone:e.managerPhone,monthly_fee:e.monthlyFee,status:e.status||"available"};if(X){(await a.put(`/dormitories/${X.id}`,s)).success?(O.success("宿舍信息更新成功"),await ae()):O.error("更新宿舍失败")}else{(await a.post("/dormitories",s)).success?(O.success("宿舍创建成功"),await ae()):O.error("创建宿舍失败")}E(!1),se.resetFields(),R(null)}catch(s){console.error("保存宿舍信息错误:",s),O.error("保存失败")}},children:[s.jsxs(x,{gutter:16,children:[s.jsx(m,{span:12,children:s.jsx(r.Item,{label:"宿舍楼",name:"buildingName",rules:[{required:!0,message:"请输入宿舍楼名称"}],children:s.jsx(g,{placeholder:"如：1号楼"})})}),s.jsx(m,{span:12,children:s.jsx(r.Item,{label:"房间号",name:"roomNumber",rules:[{required:!0,message:"请输入房间号"}],children:s.jsx(g,{placeholder:"如：101"})})})]}),s.jsxs(x,{gutter:16,children:[s.jsx(m,{span:8,children:s.jsx(r.Item,{label:"楼层",name:"floor",rules:[{required:!0,message:"请输入楼层"}],children:s.jsx(C,{min:1,max:30,placeholder:"楼层",style:{width:"100%"}})})}),s.jsx(m,{span:8,children:s.jsx(r.Item,{label:"房间类型",name:"roomType",rules:[{required:!0,message:"请选择房间类型"}],children:s.jsxs(v,{placeholder:"选择类型",children:[s.jsx(q,{value:"2人间",children:"2人间"},"2人间"),s.jsx(q,{value:"4人间",children:"4人间"},"4人间"),s.jsx(q,{value:"6人间",children:"6人间"},"6人间"),s.jsx(q,{value:"8人间",children:"8人间"},"8人间")]})})}),s.jsx(m,{span:8,children:s.jsx(r.Item,{label:"最大容量",name:"maxCapacity",rules:[{required:!0,message:"请输入最大容量"}],children:s.jsx(C,{min:1,max:8,placeholder:"人数",style:{width:"100%"}})})})]}),s.jsxs(x,{gutter:16,children:[s.jsx(m,{span:12,children:s.jsx(r.Item,{label:"性别限制",name:"gender",rules:[{required:!0,message:"请选择性别限制"}],children:s.jsxs(v,{placeholder:"选择性别限制",children:[s.jsx(q,{value:"male",children:"男生"},"male"),s.jsx(q,{value:"female",children:"女生"},"female"),s.jsx(q,{value:"mixed",children:"混合"},"mixed")]})})}),s.jsx(m,{span:12,children:s.jsx(r.Item,{label:"月租费",name:"monthlyFee",rules:[{required:!0,message:"请输入月租费"}],children:s.jsx(C,{min:0,max:5e3,placeholder:"月租费",style:{width:"100%"},addonBefore:"¥"})})})]}),s.jsxs(x,{gutter:16,children:[s.jsx(m,{span:12,children:s.jsx(r.Item,{label:"宿管姓名",name:"manager",rules:[{required:!0,message:"请输入宿管姓名"}],children:s.jsx(g,{placeholder:"宿管姓名"})})}),s.jsx(m,{span:12,children:s.jsx(r.Item,{label:"宿管电话",name:"managerPhone",rules:[{required:!0,message:"请输入宿管电话"},{pattern:/^1[3-9]\d{9}$/,message:"手机号格式不正确"}],children:s.jsx(g,{placeholder:"宿管联系电话"})})})]}),s.jsx(r.Item,{label:"设施配置",name:"facilities",rules:[{required:!0,message:"请选择设施配置"}],children:s.jsxs(v,{mode:"multiple",placeholder:"选择设施",children:[s.jsx(q,{value:"空调",children:"空调"},"空调"),s.jsx(q,{value:"热水器",children:"热水器"},"热水器"),s.jsx(q,{value:"独立卫浴",children:"独立卫浴"},"独立卫浴"),s.jsx(q,{value:"公共卫浴",children:"公共卫浴"},"公共卫浴"),s.jsx(q,{value:"WiFi",children:"WiFi"},"WiFi"),s.jsx(q,{value:"阳台",children:"阳台"},"阳台"),s.jsx(q,{value:"洗衣机",children:"洗衣机"},"洗衣机"),s.jsx(q,{value:"冰箱",children:"冰箱"},"冰箱")]})}),s.jsx(r.Item,{label:"状态",name:"status",initialValue:"available",children:s.jsxs(v,{children:[s.jsx(q,{value:"available",children:"可入住"},"available"),s.jsx(q,{value:"full",children:"已满员"},"full"),s.jsx(q,{value:"maintenance",children:"维修中"},"maintenance"),s.jsx(q,{value:"closed",children:"已关闭"},"closed")]})}),s.jsxs("div",{className:"flex justify-end space-x-2",children:[s.jsx(n,{onClick:()=>E(!1),children:"取消"}),s.jsx(n,{type:"primary",htmlType:"submit",children:X?"更新":"创建"})]})]})}),s.jsx(w,{title:`${null==U?void 0:U.buildingName} ${null==U?void 0:U.roomNumber} - 住宿学生`,open:G,onCancel:()=>H(!1),footer:[s.jsx(n,{onClick:()=>H(!1),children:"关闭"},"close")],width:800,children:s.jsx(b,{columns:te,dataSource:Z,rowKey:"id",pagination:!1,size:"small"})})]})};export{z as default};
