import{u as e,j as s,r as a}from"./index-BbdVriMF-1753162495593.js";import{M as t,r as l,aq as i,ae as r,a as n,X as d,y as c,ao as o,G as m,H as x,F as h,J as u,q as j,ad as p,L as g,I as v,aL as y,as as f,ay as w,aM as N,aH as b,ax as S,aN as I,Q as C,g as _,T as k,af as D,aB as T,W as M,N as q,ac as F}from"./antd-w1cSjLgF-1753162495593.js";import{h as L}from"./studentMatcher-BV_8mGlB-1753162495593.js";import{u as z}from"./useMessage-Dr3f4067-1753162495593.js";import"./vendor-D2RBMdQ0-1753162495593.js";const{Option:$}=f,{TextArea:O}=v,B=()=>{const B=e(),A=z(),[J,W]=t.useModal(),[Y,H]=l.useState([]),[K,P]=l.useState(!1),[Q,R]=l.useState(""),[U,E]=l.useState(""),[G,V]=l.useState(""),[X,Z]=l.useState(!1),[ee,se]=l.useState(!1),[ae,te]=l.useState(!1),[le,ie]=l.useState(null),[re,ne]=l.useState(null),[de,ce]=l.useState(null),[oe,me]=l.useState([]),[xe]=i.useForm(),[he]=i.useForm(),ue=async()=>{P(!0);try{const e=await a.get("/financial-aid");if(e.success){const s=e.data.map(e=>({id:e.id,studentId:e.student_number||e.student_id,studentName:e.student_name,class:e.class||"",major:e.major||"",college:e.college||"",phone:e.phone||"",email:e.email||"",aidType:e.aid_type,amount:e.amount||0,applicationDate:e.application_date,reason:e.reason||"",familyIncome:e.family_income||0,familyMembers:e.family_members||0,documents:"string"==typeof e.documents?JSON.parse(e.documents||"[]"):e.documents||[],attachments:"string"==typeof e.attachments?JSON.parse(e.attachments||"[]"):e.attachments||[],reviewStatus:e.review_status,reviewer:e.reviewer||"",reviewDate:e.review_date?new Date(e.review_date).toISOString().split("T")[0]:"",reviewComment:e.review_comment||"",disbursementStatus:e.disbursement_status||"pending",disbursementDate:e.disbursement_date?new Date(e.disbursement_date).toISOString().split("T")[0]:"",submittedBy:e.submitted_by||"",submittedAt:e.created_at?new Date(e.created_at).toISOString().split("T")[0]:"",createdAt:e.created_at?new Date(e.created_at).toISOString().split("T")[0]:""}));H(s)}else A.error("获取贫困补助申请失败")}catch(e){console.error("获取贫困补助申请错误:",e),A.error("获取贫困补助申请失败")}finally{P(!1)}};l.useEffect(()=>{ue()},[]);const je=Y.filter(e=>{const s=!Q||e.studentName.toLowerCase().includes(Q.toLowerCase())||e.studentId.toLowerCase().includes(Q.toLowerCase()),a=!U||e.aidType===U,t=!G||e.reviewStatus===G;return s&&a&&t}),pe=[{title:"学生信息",key:"student",render:(e,a)=>s.jsxs("div",{children:[s.jsx("div",{className:"font-medium",children:s.jsx(n,{type:"link",onClick:()=>B(`/students/${a.studentId}`),className:"p-0 h-auto",children:a.studentName})}),s.jsxs("div",{className:"text-gray-500 text-xs",children:[a.studentId," · ",a.class]}),s.jsxs("div",{className:"text-gray-400 text-xs",children:[a.major," · ",a.college]})]})},{title:"资助类型",dataIndex:"aidType",key:"aidType",render:e=>{const a={national:{color:"red",text:"国家资助"},school:{color:"blue",text:"校级资助"},social:{color:"green",text:"社会资助"},temporary:{color:"orange",text:"临时困难"}}[e];return s.jsx(C,{color:a.color,children:a.text})}},{title:"申请金额",dataIndex:"amount",key:"amount",render:e=>s.jsxs("span",{className:"font-semibold text-green-600",children:["¥",e.toLocaleString()]})},{title:"家庭情况",key:"family",render:(e,a)=>s.jsxs("div",{className:"text-sm",children:[s.jsxs("div",{children:["年收入: ¥",a.familyIncome.toLocaleString()]}),s.jsxs("div",{className:"text-gray-500",children:["家庭人数: ",a.familyMembers,"人"]})]})},{title:"申请日期",dataIndex:"applicationDate",key:"applicationDate"},{title:"审核状态",dataIndex:"reviewStatus",key:"reviewStatus",render:e=>{const a={pending:{color:"orange",text:"待审核",icon:s.jsx(p,{})},reviewing:{color:"blue",text:"审核中",icon:s.jsx(q,{})},approved:{color:"green",text:"已通过",icon:s.jsx(g,{})},rejected:{color:"red",text:"已拒绝",icon:s.jsx(q,{})}}[e];return s.jsx(C,{color:a.color,icon:a.icon,children:a.text})}},{title:"发放状态",dataIndex:"disbursementStatus",key:"disbursementStatus",render:e=>{const a={pending:{color:"default",text:"待发放"},processing:{color:"blue",text:"处理中"},completed:{color:"green",text:"已发放"}}[e];return s.jsx(C,{color:a.color,children:a.text})}},{title:"操作",key:"action",render:(e,t)=>s.jsxs(r,{children:[s.jsx(k,{title:"查看详情",children:s.jsx(n,{type:"text",icon:s.jsx(D,{}),onClick:()=>(e=>{ce(e),te(!0)})(t)})}),"pending"===t.reviewStatus&&s.jsx(k,{title:"审核",children:s.jsx(n,{type:"text",icon:s.jsx(g,{}),onClick:()=>(e=>{ne(e),he.resetFields(),se(!0)})(t),style:{color:"#52c41a"}})}),s.jsx(k,{title:"编辑",children:s.jsx(n,{type:"text",icon:s.jsx(T,{}),onClick:()=>(e=>{ie(e),xe.setFieldsValue({...e,applicationDate:F(e.applicationDate)}),Z(!0)})(t)})}),s.jsx(k,{title:"删除",children:s.jsx(n,{type:"text",danger:!0,icon:s.jsx(M,{}),onClick:()=>(e=>{J.confirm({title:"确认删除",content:`确定要删除 ${e.studentName} 的资助申请吗？`,onOk:async()=>{try{(await a.delete(`/financial-aid/${e.id}`)).success?(A.success("删除成功"),await ue()):A.error("删除失败")}catch(s){console.error("删除贫困补助申请错误:",s),A.error("删除失败")}}})})(t)})})]})}],ge=Y.length,ve=Y.filter(e=>"pending"===e.reviewStatus).length,ye=Y.filter(e=>"approved"===e.reviewStatus).length,fe=Y.filter(e=>"approved"===e.reviewStatus).reduce((e,s)=>e+s.amount,0);return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"贫困补助管理"}),s.jsx("p",{className:"text-gray-600",children:"管理学生贫困补助申请和审核"})]}),s.jsxs(r,{children:[s.jsx(n,{icon:s.jsx(d,{}),onClick:ue,size:"large",children:"刷新数据"}),s.jsx(n,{icon:s.jsx(c,{}),onClick:async()=>{P(!0);try{await ue(),A.success("学生信息同步完成")}catch(e){console.error("同步学生信息错误:",e),A.error("同步学生信息失败")}finally{P(!1)}},size:"large",loading:K,children:"同步学生信息"}),s.jsx(n,{type:"primary",icon:s.jsx(o,{}),onClick:()=>{ie(null),xe.resetFields(),me([]),Z(!0)},size:"large",children:"新增申请"})]})]}),s.jsxs(m,{gutter:16,children:[s.jsx(x,{span:6,children:s.jsx(h,{children:s.jsx(u,{title:"申请总数",value:ge,prefix:s.jsx(j,{})})})}),s.jsx(x,{span:6,children:s.jsx(h,{children:s.jsx(u,{title:"待审核",value:ve,valueStyle:{color:"#faad14"},prefix:s.jsx(p,{})})})}),s.jsx(x,{span:6,children:s.jsx(h,{children:s.jsx(u,{title:"已批准",value:ye,valueStyle:{color:"#3f8600"},prefix:s.jsx(g,{})})})}),s.jsx(x,{span:6,children:s.jsx(h,{children:s.jsx(u,{title:"资助总额",value:fe,precision:0,prefix:"¥",valueStyle:{color:"#1890ff"}})})})]}),s.jsxs(h,{children:[s.jsxs("div",{className:"flex items-center space-x-4 mb-4",children:[s.jsx(v,{placeholder:"搜索学号或姓名",prefix:s.jsx(y,{}),value:Q,onChange:e=>R(e.target.value),style:{width:300}}),s.jsxs(f,{placeholder:"资助类型",value:U,onChange:E,allowClear:!0,style:{width:120},children:[s.jsx($,{value:"national",children:"国家资助"}),s.jsx($,{value:"school",children:"校级资助"}),s.jsx($,{value:"social",children:"社会资助"}),s.jsx($,{value:"temporary",children:"临时困难"})]}),s.jsxs(f,{placeholder:"审核状态",value:G,onChange:V,allowClear:!0,style:{width:120},children:[s.jsx($,{value:"pending",children:"待审核"}),s.jsx($,{value:"reviewing",children:"审核中"}),s.jsx($,{value:"approved",children:"已通过"}),s.jsx($,{value:"rejected",children:"已拒绝"})]})]}),s.jsx(w,{columns:pe,dataSource:je,rowKey:"id",loading:K,pagination:{total:je.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 条申请`},scroll:{x:1200}})]}),s.jsx(t,{title:le?"编辑申请":"新增申请",open:X,onCancel:()=>Z(!1),footer:null,width:800,children:s.jsxs(i,{form:xe,layout:"vertical",onFinish:async e=>{try{let s=null;if(e.studentId){const t=await a.get(`/students?student_id=${encodeURIComponent(e.studentId)}`);if(!(t.success&&t.data.length>0))return void A.error("未找到该学号对应的学生，请检查学号是否正确");s=t.data[0].id}const t=oe.map((e,s)=>({id:`file_${Date.now()}_${s}`,name:e.name,url:e.url||`/uploads/${e.name}`,type:e.type,size:e.size,uploadedAt:(new Date).toISOString()})),l={student_id:s,aid_type:e.aidType,amount:e.amount,application_date:e.applicationDate.format("YYYY-MM-DD"),reason:e.reason,family_income:e.familyIncome,family_members:e.familyMembers,documents:e.documents||[],attachments:t,review_status:e.reviewStatus||"pending",submitted_by:e.studentName};if(le){(await a.put(`/financial-aid/${le.id}`,l)).success?(A.success("申请更新成功"),await ue()):A.error("更新申请失败")}else{(await a.post("/financial-aid",l)).success?(A.success("申请提交成功"),await ue()):A.error("提交申请失败")}Z(!1),me([]),xe.resetFields(),ie(null)}catch(s){console.error("保存贫困补助申请错误:",s),A.error("保存失败")}},children:[s.jsxs(m,{gutter:16,children:[s.jsx(x,{span:12,children:s.jsx(i.Item,{label:"学号",name:"studentId",rules:[{required:!0,message:"请输入学号"}],children:s.jsx(v,{placeholder:"输入学号自动匹配学生信息",onChange:e=>(async e=>{await L(e,xe)})(e.target.value)})})}),s.jsx(x,{span:12,children:s.jsx(i.Item,{label:"学生姓名",name:"studentName",rules:[{required:!0,message:"请输入学生姓名"}],children:s.jsx(v,{placeholder:"自动匹配",disabled:!0})})})]}),s.jsxs(m,{gutter:16,children:[s.jsx(x,{span:8,children:s.jsx(i.Item,{label:"班级",name:"class",rules:[{required:!0,message:"请输入班级"}],children:s.jsx(v,{placeholder:"自动匹配",disabled:!0})})}),s.jsx(x,{span:8,children:s.jsx(i.Item,{label:"专业",name:"major",children:s.jsx(v,{placeholder:"自动匹配",disabled:!0})})}),s.jsx(x,{span:8,children:s.jsx(i.Item,{label:"学院",name:"college",children:s.jsx(v,{placeholder:"自动匹配",disabled:!0})})})]}),s.jsxs(m,{gutter:16,children:[s.jsx(x,{span:8,children:s.jsx(i.Item,{label:"联系电话",name:"phone",children:s.jsx(v,{placeholder:"自动匹配",disabled:!0})})}),s.jsx(x,{span:8,children:s.jsx(i.Item,{label:"邮箱",name:"email",children:s.jsx(v,{placeholder:"自动匹配",disabled:!0})})}),s.jsx(x,{span:8,children:s.jsx(i.Item,{label:"资助类型",name:"aidType",rules:[{required:!0,message:"请选择资助类型"}],children:s.jsxs(f,{placeholder:"选择类型",children:[s.jsx($,{value:"national",children:"国家资助"}),s.jsx($,{value:"school",children:"校级资助"}),s.jsx($,{value:"social",children:"社会资助"}),s.jsx($,{value:"temporary",children:"临时困难"})]})})})]}),s.jsxs(m,{gutter:16,children:[s.jsx(x,{span:8,children:s.jsx(i.Item,{label:"申请金额",name:"amount",rules:[{required:!0,message:"请输入申请金额"}],children:s.jsx(N,{min:100,max:1e4,placeholder:"申请金额",style:{width:"100%"},addonBefore:"¥"})})}),s.jsx(x,{span:8,children:s.jsx(i.Item,{label:"申请日期",name:"applicationDate",rules:[{required:!0,message:"请选择申请日期"}],children:s.jsx(b,{style:{width:"100%"}})})}),s.jsx(x,{span:8,children:s.jsx(i.Item,{label:"家庭人数",name:"familyMembers",rules:[{required:!0,message:"请输入家庭人数"}],children:s.jsx(N,{min:1,max:20,placeholder:"家庭人数",style:{width:"100%"},addonAfter:"人"})})})]}),s.jsx(i.Item,{label:"家庭年收入",name:"familyIncome",rules:[{required:!0,message:"请输入家庭年收入"}],children:s.jsx(N,{min:0,placeholder:"家庭年收入",style:{width:"100%"},addonBefore:"¥"})}),s.jsx(i.Item,{label:"申请理由",name:"reason",rules:[{required:!0,message:"请输入申请理由"}],children:s.jsx(O,{rows:4,placeholder:"详细说明家庭经济困难情况"})}),s.jsx(i.Item,{label:"相关证明材料",name:"documents",children:s.jsxs(f,{mode:"tags",placeholder:"添加证明材料",children:[s.jsx($,{value:"身份证",children:"身份证"}),s.jsx($,{value:"户口本",children:"户口本"}),s.jsx($,{value:"收入证明",children:"收入证明"}),s.jsx($,{value:"失业证明",children:"失业证明"}),s.jsx($,{value:"医疗证明",children:"医疗证明"}),s.jsx($,{value:"贫困证明",children:"贫困证明"}),s.jsx($,{value:"学生证",children:"学生证"}),s.jsx($,{value:"银行卡",children:"银行卡"})]})}),s.jsx(i.Item,{label:"上传证明文件",extra:"支持上传身份证、户口本、收入证明等相关材料，支持PDF、图片、Word文档，单个文件不超过10MB",children:s.jsxs(S.Dragger,{name:"file",multiple:!0,fileList:oe,onChange:e=>{let s=[...e.fileList];s=s.slice(-5),s=s.map(e=>(e.response&&(e.url=e.response.url),e)),me(s)},beforeUpload:e=>{if(!("application/pdf"===e.type||e.type.startsWith("image/")||"application/msword"===e.type||"application/vnd.openxmlformats-officedocument.wordprocessingml.document"===e.type))return A.error("只能上传 PDF、图片、Word 文档！"),!1;return e.size/1024/1024<10||A.error("文件大小不能超过 10MB！"),!1},onRemove:e=>{const s=oe.filter(s=>s.uid!==e.uid);me(s)},children:[s.jsx("p",{className:"ant-upload-drag-icon",children:s.jsx(I,{})}),s.jsx("p",{className:"ant-upload-text",children:"点击或拖拽文件到此区域上传"}),s.jsx("p",{className:"ant-upload-hint",children:"支持单个或批量上传，最多上传5个文件"})]})}),s.jsxs("div",{className:"flex justify-end space-x-2",children:[s.jsx(n,{onClick:()=>Z(!1),children:"取消"}),s.jsx(n,{type:"primary",htmlType:"submit",children:le?"更新":"提交申请"})]})]})}),s.jsxs(t,{title:"审核申请",open:ee,onCancel:()=>se(!1),footer:null,width:600,children:[re&&s.jsxs("div",{className:"mb-4 p-4 bg-gray-50 rounded",children:[s.jsx("h4",{className:"font-medium mb-2",children:"申请信息"}),s.jsxs("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[s.jsxs("div",{children:["学生: ",re.studentName]}),s.jsxs("div",{children:["金额: ¥",re.amount.toLocaleString()]}),s.jsxs("div",{children:["类型: ",re.aidType]}),s.jsxs("div",{children:["申请日期: ",re.applicationDate]})]}),s.jsxs("div",{className:"mt-2",children:[s.jsx("div",{className:"text-sm text-gray-600",children:"申请理由:"}),s.jsx("div",{className:"text-sm",children:re.reason})]})]}),s.jsxs(i,{form:he,layout:"vertical",onFinish:async e=>{if(re)try{(await a.patch(`/financial-aid/${re.id}/review`,{review_status:e.reviewStatus,reviewer:e.reviewer,review_comment:e.reviewComment,disbursement_status:"approved"===e.reviewStatus?"processing":"pending"})).success?(A.success("审核完成"),await ue(),se(!1),he.resetFields(),ne(null)):A.error("审核失败")}catch(s){console.error("审核贫困补助申请错误:",s),A.error("审核失败")}},children:[s.jsx(i.Item,{label:"审核结果",name:"reviewStatus",rules:[{required:!0,message:"请选择审核结果"}],children:s.jsxs(f,{placeholder:"选择审核结果",children:[s.jsx($,{value:"approved",children:"通过"}),s.jsx($,{value:"rejected",children:"拒绝"})]})}),s.jsx(i.Item,{label:"审核人",name:"reviewer",rules:[{required:!0,message:"请输入审核人"}],children:s.jsx(v,{placeholder:"审核人姓名或部门"})}),s.jsx(i.Item,{label:"审核意见",name:"reviewComment",rules:[{required:!0,message:"请输入审核意见"}],children:s.jsx(O,{rows:3,placeholder:"请输入审核意见"})}),s.jsxs("div",{className:"flex justify-end space-x-2",children:[s.jsx(n,{onClick:()=>se(!1),children:"取消"}),s.jsx(n,{type:"primary",htmlType:"submit",children:"提交审核"})]})]})]}),s.jsx(t,{title:s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(_,{}),s.jsx("span",{children:"贫困补助申请详情"})]}),open:ae,onCancel:()=>te(!1),footer:[s.jsx(n,{onClick:()=>te(!1),children:"关闭"},"close")],width:800,children:de&&s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{children:[s.jsx("h4",{className:"font-medium mb-3 text-gray-800",children:"学生基本信息"}),s.jsxs("div",{className:"grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded",children:[s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"学生姓名："}),s.jsx("span",{className:"font-medium",children:de.studentName})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"学号："}),s.jsx("span",{className:"font-medium",children:de.studentId})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"班级："}),s.jsx("span",{className:"font-medium",children:de.class})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"专业："}),s.jsx("span",{className:"font-medium",children:de.major})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"学院："}),s.jsx("span",{className:"font-medium",children:de.college})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"联系电话："}),s.jsx("span",{className:"font-medium",children:de.phone||"-"})]})]})]}),s.jsxs("div",{children:[s.jsx("h4",{className:"font-medium mb-3 text-gray-800",children:"申请信息"}),s.jsxs("div",{className:"grid grid-cols-2 gap-4 p-4 bg-blue-50 rounded",children:[s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"资助类型："}),s.jsx(C,{color:"national"===de.aidType?"red":"school"===de.aidType?"blue":"social"===de.aidType?"green":"orange",children:"national"===de.aidType?"国家资助":"school"===de.aidType?"校级资助":"social"===de.aidType?"社会资助":"临时困难"})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"申请金额："}),s.jsxs("span",{className:"font-semibold text-green-600",children:["¥",de.amount.toLocaleString()]})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"申请日期："}),s.jsx("span",{className:"font-medium",children:de.applicationDate})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"提交人："}),s.jsx("span",{className:"font-medium",children:de.submittedBy})]})]})]}),s.jsxs("div",{children:[s.jsx("h4",{className:"font-medium mb-3 text-gray-800",children:"家庭情况"}),s.jsxs("div",{className:"grid grid-cols-2 gap-4 p-4 bg-yellow-50 rounded",children:[s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"家庭年收入："}),s.jsxs("span",{className:"font-medium",children:["¥",de.familyIncome.toLocaleString()]})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"家庭人数："}),s.jsxs("span",{className:"font-medium",children:[de.familyMembers,"人"]})]}),s.jsxs("div",{className:"col-span-2",children:[s.jsx("span",{className:"text-gray-600",children:"申请理由："}),s.jsx("div",{className:"mt-2 p-3 bg-white rounded border",children:de.reason})]})]})]}),s.jsxs("div",{children:[s.jsx("h4",{className:"font-medium mb-3 text-gray-800",children:"证明材料"}),s.jsxs("div",{className:"p-4 bg-green-50 rounded",children:[s.jsx("div",{className:"mb-2",children:s.jsx("span",{className:"text-gray-600",children:"材料清单："})}),s.jsx("div",{className:"flex flex-wrap gap-2",children:de.documents.map((e,a)=>s.jsx(C,{color:"blue",children:e},a))}),de.attachments&&de.attachments.length>0&&s.jsxs("div",{className:"mt-4",children:[s.jsx("div",{className:"text-gray-600 mb-2",children:"上传文件："}),s.jsx("div",{className:"space-y-2",children:de.attachments.map(e=>s.jsxs("div",{className:"flex items-center justify-between p-2 bg-white rounded border",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(j,{}),s.jsx("span",{children:e.name}),s.jsxs("span",{className:"text-gray-500 text-sm",children:["(",(e.size/1024/1024).toFixed(2)," MB)"]})]}),s.jsx(n,{type:"link",size:"small",children:"下载"})]},e.id))})]})]})]}),s.jsxs("div",{children:[s.jsx("h4",{className:"font-medium mb-3 text-gray-800",children:"审核信息"}),s.jsxs("div",{className:"grid grid-cols-2 gap-4 p-4 bg-purple-50 rounded",children:[s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"审核状态："}),s.jsx(C,{color:"pending"===de.reviewStatus?"orange":"reviewing"===de.reviewStatus?"blue":"approved"===de.reviewStatus?"green":"red",children:"pending"===de.reviewStatus?"待审核":"reviewing"===de.reviewStatus?"审核中":"approved"===de.reviewStatus?"已通过":"已拒绝"})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"发放状态："}),s.jsx(C,{color:"pending"===de.disbursementStatus?"default":"processing"===de.disbursementStatus?"blue":"green",children:"pending"===de.disbursementStatus?"待发放":"processing"===de.disbursementStatus?"处理中":"已发放"})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"审核人："}),s.jsx("span",{className:"font-medium",children:de.reviewer||"-"})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"审核日期："}),s.jsx("span",{className:"font-medium",children:de.reviewDate||"-"})]}),de.reviewComment&&s.jsxs("div",{className:"col-span-2",children:[s.jsx("span",{className:"text-gray-600",children:"审核意见："}),s.jsx("div",{className:"mt-2 p-3 bg-white rounded border",children:de.reviewComment})]})]})]})]})}),W]})};export{B as default};
