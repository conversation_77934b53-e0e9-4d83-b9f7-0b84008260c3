import{u as e,r as s,j as a}from"./index-Cu_U9Dm3.js";import{M as l,r as t,aq as r,ae as n,a as i,aw as c,au as d,ao as o,G as x,H as h,F as u,J as j,k as m,m as p,I as g,aL as y,as as v,ay as f,aM as w,Q as C,T as I,aB as N,W as b}from"./antd-DYv0PFJq.js";import{u as k}from"./useMessage-DDIprusu.js";import"./vendor-D2RBMdQ0.js";const{Option:S}=v,q=()=>{const[q,F]=l.useModal(),L=e(),T=k(),[z,$]=t.useState([]),[B,W]=t.useState(!1),[A,M]=t.useState(""),[D,V]=t.useState(""),[G,J]=t.useState(""),[K,O]=t.useState(""),[Q,E]=t.useState(!1),[H,P]=t.useState(null),[R]=r.useForm();t.useEffect(()=>{U()},[]);const U=async()=>{W(!0);try{const e=await s.get("/academics/grades");e.success?$(e.data):T.error("获取成绩列表失败")}catch(e){console.error("获取成绩列表错误:",e),T.error("获取成绩列表失败")}finally{W(!1)}},X=z.filter(e=>{const s=!A||e.studentName.toLowerCase().includes(A.toLowerCase())||e.studentId.toLowerCase().includes(A.toLowerCase())||e.courseName.toLowerCase().includes(A.toLowerCase()),a=!D||e.courseCode===D,l=!G||e.semester===G,t=!K||e.examType===K;return s&&a&&l&&t}),Y=[{title:"学号",dataIndex:"studentId",key:"studentId",width:120},{title:"姓名",dataIndex:"studentName",key:"studentName",render:(e,s)=>a.jsx(i,{type:"link",onClick:()=>L(`/students/${s.studentId}`),className:"p-0 h-auto",children:e})},{title:"课程",key:"course",render:(e,s)=>a.jsxs("div",{children:[a.jsx("div",{className:"font-medium",children:s.courseName}),a.jsx("div",{className:"text-gray-500 text-xs",children:s.courseCode})]})},{title:"考试类型",dataIndex:"examType",key:"examType",render:e=>{const s={midterm:{color:"blue",text:"期中"},final:{color:"red",text:"期末"},quiz:{color:"green",text:"测验"},assignment:{color:"orange",text:"作业"}}[e];return a.jsx(C,{color:s.color,children:s.text})}},{title:"成绩",key:"score",render:(e,s)=>a.jsxs("div",{className:"text-center",children:[a.jsxs("div",{className:"font-bold text-lg",children:[s.score,"/",s.totalScore]}),a.jsxs("div",{className:"text-gray-500 text-xs",children:[s.percentage.toFixed(1),"%"]})]})},{title:"等级",dataIndex:"grade",key:"grade",render:(e,s)=>{let l="default";return l=e.startsWith("A")?"red":e.startsWith("B")?"orange":e.startsWith("C")?"blue":e.startsWith("D")?"green":"gray",a.jsxs("div",{className:"text-center",children:[a.jsx(C,{color:l,className:"font-bold",children:e}),a.jsxs("div",{className:"text-xs text-gray-500",children:["GPA: ",s.gpa]})]})}},{title:"学期",dataIndex:"semester",key:"semester"},{title:"任课教师",dataIndex:"teacher",key:"teacher"},{title:"状态",dataIndex:"status",key:"status",render:e=>{const s={published:{color:"green",text:"已发布"},draft:{color:"orange",text:"草稿"},reviewing:{color:"blue",text:"审核中"}}[e];return a.jsx(C,{color:s.color,children:s.text})}},{title:"操作",key:"action",render:(e,l)=>a.jsxs(n,{children:[a.jsx(I,{title:"编辑",children:a.jsx(i,{type:"text",icon:a.jsx(N,{}),onClick:()=>(e=>{P(e),R.setFieldsValue(e),E(!0)})(l)})}),a.jsx(I,{title:"删除",children:a.jsx(i,{type:"text",danger:!0,icon:a.jsx(b,{}),onClick:()=>(e=>{q.confirm({title:"确认删除",content:`确定要删除 ${e.studentName} 的 ${e.courseName} 成绩吗？`,onOk:async()=>{try{await s.delete(`/academics/grades/${e.id}`),$(z.filter(s=>s.id!==e.id)),T.success("删除成功")}catch(a){console.error("删除成绩错误:",a),T.error("删除失败")}}})})(l)})})]})}],Z=[...new Set(z.map(e=>e.courseCode))],_=[...new Set(z.map(e=>e.semester))],ee=z.length>0?z.reduce((e,s)=>e+s.percentage,0)/z.length:0,se=z.length>0?z.filter(e=>e.percentage>=60).length/z.length*100:0,ae=z.length>0?z.filter(e=>e.percentage>=90).length/z.length*100:0;return a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"成绩管理"}),a.jsx("p",{className:"text-gray-600",children:"管理学生的考试成绩和学业表现"})]}),a.jsxs(n,{children:[a.jsx(i,{icon:a.jsx(c,{}),onClick:()=>{T.info("批量导入功能开发中")},children:"批量导入"}),a.jsx(i,{icon:a.jsx(d,{}),onClick:()=>{T.info("导出功能开发中")},children:"导出成绩"}),a.jsx(i,{type:"primary",icon:a.jsx(o,{}),onClick:()=>{P(null),R.resetFields(),E(!0)},size:"large",children:"录入成绩"})]})]}),a.jsxs(x,{gutter:16,children:[a.jsx(h,{span:6,children:a.jsx(u,{children:a.jsx(j,{title:"成绩记录总数",value:z.length,prefix:a.jsx(m,{})})})}),a.jsx(h,{span:6,children:a.jsx(u,{children:a.jsx(j,{title:"平均分",value:ee,precision:1,valueStyle:{color:"#3f8600"}})})}),a.jsx(h,{span:6,children:a.jsx(u,{children:a.jsx(j,{title:"及格率",value:se,precision:1,suffix:"%",valueStyle:{color:"#1890ff"}})})}),a.jsx(h,{span:6,children:a.jsx(u,{children:a.jsx(j,{title:"优秀率",value:ae,precision:1,suffix:"%",valueStyle:{color:"#cf1322"},prefix:a.jsx(p,{})})})})]}),a.jsxs(u,{children:[a.jsxs("div",{className:"flex items-center space-x-4 mb-4",children:[a.jsx(g,{placeholder:"搜索学号、姓名或课程",prefix:a.jsx(y,{}),value:A,onChange:e=>M(e.target.value),style:{width:300}}),a.jsx(v,{placeholder:"选择课程",value:D,onChange:V,allowClear:!0,style:{width:150},children:Z.map(e=>a.jsx(S,{value:e,children:e},e))}),a.jsx(v,{placeholder:"选择学期",value:G,onChange:J,allowClear:!0,style:{width:120},children:_.map(e=>a.jsx(S,{value:e,children:e},e))}),a.jsxs(v,{placeholder:"考试类型",value:K,onChange:O,allowClear:!0,style:{width:120},children:[a.jsx(S,{value:"midterm",children:"期中"}),a.jsx(S,{value:"final",children:"期末"}),a.jsx(S,{value:"quiz",children:"测验"}),a.jsx(S,{value:"assignment",children:"作业"})]})]}),a.jsx(f,{columns:Y,dataSource:X,rowKey:"id",loading:B,pagination:{total:X.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 条成绩记录`},scroll:{x:1200}})]}),a.jsx(l,{title:H?"编辑成绩":"录入成绩",open:Q,onCancel:()=>E(!1),footer:null,width:600,children:a.jsxs(r,{form:R,layout:"vertical",onFinish:async e=>{const a=e.score/e.totalScore*100;let l="F",t=0;a>=90?(l="A",t=4):a>=85?(l="A-",t=3.7):a>=80?(l="B+",t=3.3):a>=75?(l="B",t=3):a>=70?(l="B-",t=2.7):a>=65?(l="C+",t=2.3):a>=60?(l="C",t=2):a>=55?(l="C-",t=1.7):a>=50&&(l="D",t=1);const r={...e,percentage:a,grade:l,gpa:t};try{if(H){(await s.put(`/academics/grades/${H.id}`,r)).success&&($(z.map(e=>e.id===H.id?{...e,...r,id:H.id}:e)),T.success("成绩更新成功"))}else{const e=await s.post("/academics/grades",r);e.success&&($([e.data,...z]),T.success("成绩录入成功"))}E(!1)}catch(n){console.error("保存成绩错误:",n),T.error("保存失败")}},children:[a.jsxs(x,{gutter:16,children:[a.jsx(h,{span:12,children:a.jsx(r.Item,{label:"学号",name:"studentId",rules:[{required:!0,message:"请输入学号"}],children:a.jsx(g,{placeholder:"学生学号"})})}),a.jsx(h,{span:12,children:a.jsx(r.Item,{label:"学生姓名",name:"studentName",rules:[{required:!0,message:"请输入学生姓名"}],children:a.jsx(g,{placeholder:"学生姓名"})})})]}),a.jsxs(x,{gutter:16,children:[a.jsx(h,{span:12,children:a.jsx(r.Item,{label:"课程代码",name:"courseCode",rules:[{required:!0,message:"请输入课程代码"}],children:a.jsx(g,{placeholder:"如：CS101"})})}),a.jsx(h,{span:12,children:a.jsx(r.Item,{label:"课程名称",name:"courseName",rules:[{required:!0,message:"请输入课程名称"}],children:a.jsx(g,{placeholder:"课程名称"})})})]}),a.jsxs(x,{gutter:16,children:[a.jsx(h,{span:8,children:a.jsx(r.Item,{label:"学期",name:"semester",rules:[{required:!0,message:"请选择学期"}],children:a.jsxs(v,{placeholder:"选择学期",children:[a.jsx(S,{value:"2024-1",children:"2024-1"}),a.jsx(S,{value:"2024-2",children:"2024-2"}),a.jsx(S,{value:"2025-1",children:"2025-1"})]})})}),a.jsx(h,{span:8,children:a.jsx(r.Item,{label:"考试类型",name:"examType",rules:[{required:!0,message:"请选择考试类型"}],children:a.jsxs(v,{placeholder:"选择类型",children:[a.jsx(S,{value:"midterm",children:"期中"}),a.jsx(S,{value:"final",children:"期末"}),a.jsx(S,{value:"quiz",children:"测验"}),a.jsx(S,{value:"assignment",children:"作业"})]})})}),a.jsx(h,{span:8,children:a.jsx(r.Item,{label:"考试日期",name:"examDate",rules:[{required:!0,message:"请选择考试日期"}],children:a.jsx(g,{type:"date"})})})]}),a.jsxs(x,{gutter:16,children:[a.jsx(h,{span:12,children:a.jsx(r.Item,{label:"得分",name:"score",rules:[{required:!0,message:"请输入得分"}],children:a.jsx(w,{min:0,max:1e3,placeholder:"得分",style:{width:"100%"}})})}),a.jsx(h,{span:12,children:a.jsx(r.Item,{label:"总分",name:"totalScore",rules:[{required:!0,message:"请输入总分"}],initialValue:100,children:a.jsx(w,{min:1,max:1e3,placeholder:"总分",style:{width:"100%"}})})})]}),a.jsxs(x,{gutter:16,children:[a.jsx(h,{span:12,children:a.jsx(r.Item,{label:"任课教师",name:"teacher",rules:[{required:!0,message:"请输入任课教师"}],children:a.jsx(g,{placeholder:"任课教师"})})}),a.jsx(h,{span:12,children:a.jsx(r.Item,{label:"状态",name:"status",initialValue:"draft",children:a.jsxs(v,{children:[a.jsx(S,{value:"draft",children:"草稿"}),a.jsx(S,{value:"reviewing",children:"审核中"}),a.jsx(S,{value:"published",children:"已发布"})]})})})]}),a.jsxs("div",{className:"flex justify-end space-x-2",children:[a.jsx(i,{onClick:()=>E(!1),children:"取消"}),a.jsx(i,{type:"primary",htmlType:"submit",children:H?"更新":"录入"})]})]})}),F]})};export{q as default};
