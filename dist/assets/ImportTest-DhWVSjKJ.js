import{j as e,r as i}from"./index-Cu_U9Dm3.js";import{r,E as a,F as t,ae as s,a as o,U as n}from"./antd-DYv0PFJq.js";import{u as l}from"./useMessage-DDIprusu.js";import"./vendor-D2RBMdQ0.js";const{Title:d,Text:c,Paragraph:h}=a,m=()=>{const a=l(),[m,u]=r.useState(!1),[g,p]=r.useState("");return e.jsxs("div",{style:{padding:"24px",maxWidth:"800px",margin:"0 auto"},children:[e.jsx(d,{level:2,children:"学生导入测试页面"}),e.jsx(t,{title:"导入测试",style:{marginBottom:"16px"},children:e.jsxs(s,{children:[e.jsx(o,{type:"primary",onClick:async()=>{u(!0);try{const e=[{studentId:"2024001001",name:"测试学生1",gender:"男",ethnicity:"汉族",birthplace:"北京",politicalStatus:"团员",position:"",college:"计算机学院",level:"本科",grade:"2024",major:"计算机科学与技术",class:"计科2401",qq:"123456789",wechat:"test1",phone:"13800138001",idCard:"110101200001011234",dormBuilding:"1号楼",dormRoom:"101",householdLocation:"北京市",homeAddress:"北京市朝阳区xxx街道",fatherName:"张父",fatherPhone:"13900139001",fatherWork:"某公司",motherName:"李母",motherPhone:"13900139002",motherWork:"某单位",parentsDisabled:"否",singleParent:"否",guardianName:"",guardianRelation:"",familyMembers:"",tuitionSource:"家庭",martyrChild:"否",orphan:"否",caregiverName:"",caregiverRelation2:"",caregiverPhone:"",orphanDescription:"",disabled:"否",disabilityType:"",disabilityLevel:"",disabilityDescription2:"",healthCondition:"",dayStudent:"否",academicStatus:"正常",disciplinaryAction:"否",minorProgram:"否",religiousBelief:"否",religiousActivity:"否",familyReligious:"否",tuitionPaid:"是",hasLoan:"否",hasGrant:"否",hasPassport:"否",abroadExperience:"否",graduateProgram:"否",microStudyAbroad:"否",counselorName:"张老师",counselorPhone:"13700137001",otherNotes:"",remarks:""},{studentId:"**********",name:"测试学生2",gender:"女",ethnicity:"汉族",birthplace:"上海",politicalStatus:"团员",position:"",college:"计算机学院",level:"本科",grade:"2024",major:"软件工程",class:"软工2401",qq:"*********",wechat:"test2",phone:"13800138002",idCard:"310101200001011235",dormBuilding:"2号楼",dormRoom:"201",householdLocation:"上海市",homeAddress:"上海市浦东新区xxx路",fatherName:"王父",fatherPhone:"13900139003",fatherWork:"某企业",motherName:"赵母",motherPhone:"13900139004",motherWork:"某机构",parentsDisabled:"否",singleParent:"否",guardianName:"",guardianRelation:"",familyMembers:"",tuitionSource:"家庭",martyrChild:"否",orphan:"否",caregiverName:"",caregiverRelation2:"",caregiverPhone:"",orphanDescription:"",disabled:"否",disabilityType:"",disabilityLevel:"",disabilityDescription2:"",healthCondition:"",dayStudent:"否",academicStatus:"正常",disciplinaryAction:"否",minorProgram:"否",religiousBelief:"否",religiousActivity:"否",familyReligious:"否",tuitionPaid:"是",hasLoan:"否",hasGrant:"否",hasPassport:"否",abroadExperience:"否",graduateProgram:"否",microStudyAbroad:"否",counselorName:"李老师",counselorPhone:"13700137002",otherNotes:"",remarks:""}],r=await i.post("/students/import",{students:e});p(JSON.stringify(r,null,2)),r.success?a.success("导入测试成功！"):a.error("导入测试失败")}catch(e){console.error("导入测试失败:",e),p(`错误: ${e}`),a.error("导入测试失败")}finally{u(!1)}},loading:m,children:"测试导入2个学生"}),e.jsx(o,{onClick:async()=>{try{const e=await i.get("/students");p(JSON.stringify(e,null,2)),a.success("获取学生列表成功")}catch(e){console.error("获取学生列表失败:",e),p(`错误: ${e}`),a.error("获取学生列表失败")}},children:"查看学生列表"})]})}),e.jsx(t,{title:"响应结果",children:e.jsx("pre",{style:{background:"#f5f5f5",padding:"16px",borderRadius:"4px",overflow:"auto",maxHeight:"400px"},children:g||"暂无数据"})}),e.jsxs(t,{title:"说明",style:{marginTop:"16px"},children:[e.jsx(h,{children:e.jsx(c,{strong:!0,children:"测试步骤："})}),e.jsxs("ol",{children:[e.jsx("li",{children:"确保已登录系统"}),e.jsx("li",{children:'点击"测试导入2个学生"按钮'}),e.jsx("li",{children:"查看导入结果"}),e.jsx("li",{children:'点击"查看学生列表"验证数据是否已保存到数据库'})]}),e.jsx(n,{}),e.jsx(h,{children:e.jsx(c,{strong:!0,children:"预期结果："})}),e.jsxs("ul",{children:[e.jsx("li",{children:"导入成功，返回成功计数"}),e.jsx("li",{children:"数据库中新增2个学生记录"}),e.jsx("li",{children:"学生列表API返回包含新学生的数据"})]})]})]})};export{m as default};
