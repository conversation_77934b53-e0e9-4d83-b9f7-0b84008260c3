import{u as e,j as r,r as t}from"./index-Cu_U9Dm3.js";import{r as o,aq as a,I as s,g as n,ar as l,af as i,ab as d,as as g,i as c,a as x}from"./antd-DYv0PFJq.js";import{u as p}from"./userService-kUY0fGJp.js";import{u}from"./useMessage-DDIprusu.js";import"./vendor-D2RBMdQ0.js";const{Option:b}=g,h=()=>{const h=e(),y=u(),[m,f]=o.useState(!1),[j,T]=o.useState(!1),[v,S]=o.useState(new Date);o.useEffect(()=>{const e=setInterval(()=>{S(new Date)},1e3);return()=>clearInterval(e)},[]);return r.jsxs("div",{style:{minHeight:"100vh",position:"relative",overflow:"hidden",background:"linear-gradient(135deg, #60A5FA 0%, #A855F7 50%, #EC4899 100%)"},children:[r.jsxs("div",{style:{position:"absolute",top:0,left:0,right:0,bottom:0,background:"linear-gradient(to top, rgba(0,0,0,0.2) 0%, transparent 100%)"},children:[r.jsx("div",{style:{position:"absolute",top:"25%",left:"25%",width:"384px",height:"384px",background:"rgba(255,255,255,0.1)",borderRadius:"50%",filter:"blur(60px)",animation:"pulse 2s infinite"}}),r.jsx("div",{style:{position:"absolute",bottom:"25%",right:"25%",width:"320px",height:"320px",background:"rgba(147,197,253,0.2)",borderRadius:"50%",filter:"blur(60px)",animation:"pulse 2s infinite 1s"}})]}),r.jsxs("div",{style:{position:"relative",zIndex:10,minHeight:"100vh",display:"flex",flexDirection:"column"},children:[r.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"24px",color:"rgba(255,255,255,0.9)"},children:[r.jsx("div",{style:{fontSize:"14px",fontWeight:"500"},children:v.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"})}),r.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"4px"},children:[r.jsx("div",{style:{width:"16px",height:"8px",background:"rgba(255,255,255,0.6)",borderRadius:"2px"}}),r.jsx("div",{style:{width:"24px",height:"12px",border:"1px solid rgba(255,255,255,0.6)",borderRadius:"2px",position:"relative"},children:r.jsx("div",{style:{width:"16px",height:"6px",background:"rgba(255,255,255,0.8)",borderRadius:"1px",margin:"2px"}})})]})]}),r.jsx("div",{style:{flex:1,display:"flex",alignItems:"center",justifyContent:"center",padding:"48px 24px"},children:r.jsxs("div",{style:{width:"100%",maxWidth:"448px",margin:"0 auto"},children:[r.jsxs("div",{style:{textAlign:"center",marginBottom:"64px"},children:[r.jsx("div",{style:{marginBottom:"32px"},children:r.jsx("div",{className:"float-animation",style:{width:"96px",height:"96px",background:"rgba(255,255,255,0.15)",backdropFilter:"blur(24px)",borderRadius:"24px",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 auto 32px",boxShadow:"0 25px 50px -12px rgba(0,0,0,0.25), 0 0 0 1px rgba(255,255,255,0.1)",border:"1px solid rgba(255,255,255,0.2)",transition:"all 0.5s ease",cursor:"pointer"},onMouseEnter:e=>{e.currentTarget.style.transform="scale(1.1)",e.currentTarget.style.background="rgba(255,255,255,0.2)",e.currentTarget.style.boxShadow="0 32px 64px rgba(255,255,255,0.15), 0 0 0 1px rgba(255,255,255,0.2)"},onMouseLeave:e=>{e.currentTarget.style.transform="scale(1)",e.currentTarget.style.background="rgba(255,255,255,0.15)",e.currentTarget.style.boxShadow="0 25px 50px -12px rgba(0,0,0,0.25), 0 0 0 1px rgba(255,255,255,0.1)"},children:r.jsx("span",{style:{color:"white",fontSize:"36px",fontWeight:"300"},children:"学"})})}),r.jsx("h1",{className:"animate-fade-in",style:{fontSize:"36px",fontWeight:"300",color:"white",marginBottom:"12px",letterSpacing:"0.05em"},children:"智慧学工"}),r.jsx("p",{className:"animate-fade-in-delay",style:{color:"rgba(255,255,255,0.7)",fontSize:"18px",fontWeight:"300"},children:"学生工作管理平台"})]}),r.jsx("div",{className:"slide-up",style:{background:"rgba(255,255,255,0.1)",backdropFilter:"blur(24px)",borderRadius:"24px",padding:"40px",boxShadow:"0 25px 50px -12px rgba(0,0,0,0.25), 0 0 0 1px rgba(255,255,255,0.1)",border:"1px solid rgba(255,255,255,0.2)",transition:"all 0.5s ease"},onMouseEnter:e=>{e.currentTarget.style.transform="translateY(-8px) scale(1.02)",e.currentTarget.style.background="rgba(255,255,255,0.15)",e.currentTarget.style.boxShadow="0 32px 64px rgba(255,255,255,0.15), 0 0 0 1px rgba(255,255,255,0.2)"},onMouseLeave:e=>{e.currentTarget.style.transform="translateY(0) scale(1)",e.currentTarget.style.background="rgba(255,255,255,0.1)",e.currentTarget.style.boxShadow="0 25px 50px -12px rgba(0,0,0,0.25), 0 0 0 1px rgba(255,255,255,0.1)"},children:r.jsxs(a,{name:"login",onFinish:async e=>{f(!0);try{const r=await t.post("/auth/login",{username:e.username,password:e.password});if(r.success){const{user:t,token:o}=r.data;if(t.role!==e.role)return void y.error("角色选择错误，请选择正确的角色");y.success("登录成功！");const a=p.getRolePermissions(t.role);localStorage.setItem("user",JSON.stringify({id:t.id,username:t.username,realName:t.name,role:t.role,department:t.department||"",permissions:a,email:t.email,phone:t.phone})),localStorage.setItem("permissions",JSON.stringify(a)),localStorage.setItem("token",o),h("/")}else y.error(r.message||"登录失败")}catch(r){console.error("登录错误:",r),y.error(r.message||"登录失败，请检查用户名和密码")}finally{f(!1)}},autoComplete:"off",layout:"vertical",style:{display:"flex",flexDirection:"column",gap:"24px"},children:[r.jsx(a.Item,{name:"username",rules:[{required:!0,message:"请输入用户名"}],style:{marginBottom:0},children:r.jsx("div",{style:{position:"relative"},children:r.jsx(s,{prefix:r.jsx(n,{style:{color:"rgba(255,255,255,0.6)",fontSize:"18px"}}),placeholder:"用户名",style:{height:"64px",background:"transparent",border:"none",borderBottom:"2px solid rgba(255,255,255,0.3)",color:"white",fontSize:"17px",fontWeight:"400",borderRadius:"0",paddingLeft:"0",paddingRight:"0",transition:"all 0.3s ease"},onMouseEnter:e=>{e.currentTarget.style.borderBottomColor="rgba(255,255,255,0.5)",e.currentTarget.style.transform="translateY(-2px)",e.currentTarget.style.boxShadow="0 8px 30px rgba(255,255,255,0.1)"},onMouseLeave:e=>{e.currentTarget.style.borderBottomColor="rgba(255,255,255,0.3)",e.currentTarget.style.transform="translateY(0)",e.currentTarget.style.boxShadow="none"},onFocus:e=>{e.currentTarget.style.borderBottomColor="rgba(255,255,255,0.8)",e.currentTarget.style.transform="translateY(-3px)",e.currentTarget.style.boxShadow="0 12px 40px rgba(255,255,255,0.15)"},onBlur:e=>{e.currentTarget.style.borderBottomColor="rgba(255,255,255,0.3)",e.currentTarget.style.transform="translateY(0)",e.currentTarget.style.boxShadow="none"}})})}),r.jsx(a.Item,{name:"password",rules:[{required:!0,message:"请输入密码"}],style:{marginBottom:0},children:r.jsx("div",{style:{position:"relative"},children:r.jsx(s,{type:j?"text":"password",prefix:r.jsx(d,{style:{color:"rgba(255,255,255,0.6)",fontSize:"18px"}}),suffix:r.jsx("button",{type:"button",onClick:()=>T(!j),style:{color:"rgba(255,255,255,0.6)",background:"none",border:"none",cursor:"pointer",transition:"all 0.3s ease"},onMouseEnter:e=>{e.currentTarget.style.color="rgba(255,255,255,0.9)",e.currentTarget.style.transform="scale(1.1)"},onMouseLeave:e=>{e.currentTarget.style.color="rgba(255,255,255,0.6)",e.currentTarget.style.transform="scale(1)"},children:j?r.jsx(l,{}):r.jsx(i,{})}),placeholder:"密码",style:{height:"64px",background:"transparent",border:"none",borderBottom:"2px solid rgba(255,255,255,0.3)",color:"white",fontSize:"17px",fontWeight:"400",borderRadius:"0",paddingLeft:"0",paddingRight:"0",transition:"all 0.3s ease"},onMouseEnter:e=>{e.currentTarget.style.borderBottomColor="rgba(255,255,255,0.5)",e.currentTarget.style.transform="translateY(-2px)",e.currentTarget.style.boxShadow="0 8px 30px rgba(255,255,255,0.1)"},onMouseLeave:e=>{e.currentTarget.style.borderBottomColor="rgba(255,255,255,0.3)",e.currentTarget.style.transform="translateY(0)",e.currentTarget.style.boxShadow="none"},onFocus:e=>{e.currentTarget.style.borderBottomColor="rgba(255,255,255,0.8)",e.currentTarget.style.transform="translateY(-3px)",e.currentTarget.style.boxShadow="0 12px 40px rgba(255,255,255,0.15)"},onBlur:e=>{e.currentTarget.style.borderBottomColor="rgba(255,255,255,0.3)",e.currentTarget.style.transform="translateY(0)",e.currentTarget.style.boxShadow="none"}})})}),r.jsx(a.Item,{name:"role",rules:[{required:!0,message:"请选择角色"}],style:{marginBottom:0,marginTop:"24px"},children:r.jsxs(g,{placeholder:"选择角色",style:{height:"64px",background:"transparent",border:"none",borderBottom:"2px solid rgba(255,255,255,0.3)",color:"white",fontSize:"17px",fontWeight:"400",borderRadius:"0"},suffixIcon:r.jsx(c,{style:{color:"rgba(255,255,255,0.6)",fontSize:"18px"}}),children:[r.jsx(b,{value:"admin",children:r.jsxs("div",{style:{display:"flex",alignItems:"center",padding:"8px 0"},children:[r.jsx(c,{style:{marginRight:"12px",color:"#ff4d4f",fontSize:"16px"}}),r.jsx("span",{style:{color:"#000000"},children:"超级管理员"})]})}),r.jsx(b,{value:"counselor",children:r.jsxs("div",{style:{display:"flex",alignItems:"center",padding:"8px 0"},children:[r.jsx(n,{style:{marginRight:"12px",color:"#1890ff",fontSize:"16px"}}),r.jsx("span",{style:{color:"#000000"},children:"辅导员"})]})}),r.jsx(b,{value:"teacher",children:r.jsxs("div",{style:{display:"flex",alignItems:"center",padding:"8px 0"},children:[r.jsx(n,{style:{marginRight:"12px",color:"#52c41a",fontSize:"16px"}}),r.jsx("span",{style:{color:"#000000"},children:"教师"})]})}),r.jsx(b,{value:"party_secretary",children:r.jsxs("div",{style:{display:"flex",alignItems:"center",padding:"8px 0"},children:[r.jsx(c,{style:{marginRight:"12px",color:"#fa8c16",fontSize:"16px"}}),r.jsx("span",{style:{color:"#000000"},children:"党建专员"})]})}),r.jsx(b,{value:"dean",children:r.jsxs("div",{style:{display:"flex",alignItems:"center",padding:"8px 0"},children:[r.jsx(n,{style:{marginRight:"12px",color:"#722ed1",fontSize:"16px"}}),r.jsx("span",{style:{color:"#000000"},children:"院长"})]})})]})}),r.jsx(a.Item,{style:{marginBottom:0,marginTop:"40px"},children:r.jsx(x,{type:"primary",htmlType:"submit",loading:m,style:{width:"100%",height:"64px",background:"rgba(255,255,255,0.2)",border:"1px solid rgba(255,255,255,0.3)",color:"white",fontSize:"18px",fontWeight:"500",borderRadius:"16px",backdropFilter:"blur(10px)",boxShadow:"0 12px 40px rgba(255,255,255,0.15)",transition:"all 0.5s cubic-bezier(0.4, 0, 0.2, 1)",cursor:"pointer"},onMouseEnter:e=>{e.currentTarget.style.background="rgba(255,255,255,0.35)",e.currentTarget.style.borderColor="rgba(255,255,255,0.5)",e.currentTarget.style.transform="translateY(-4px) scale(1.02)",e.currentTarget.style.boxShadow="0 20px 60px rgba(255,255,255,0.2)"},onMouseLeave:e=>{e.currentTarget.style.background="rgba(255,255,255,0.2)",e.currentTarget.style.borderColor="rgba(255,255,255,0.3)",e.currentTarget.style.transform="translateY(0) scale(1)",e.currentTarget.style.boxShadow="0 12px 40px rgba(255,255,255,0.15)"},onMouseDown:e=>{e.currentTarget.style.transform="translateY(-1px) scale(0.98)"},onMouseUp:e=>{e.currentTarget.style.transform="translateY(-4px) scale(1.02)"},children:m?"登录中...":"登录"})})]})})]})}),r.jsx("div",{style:{paddingBottom:"32px",paddingLeft:"24px",paddingRight:"24px"},children:r.jsxs("div",{style:{textAlign:"center"},children:[r.jsxs("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",gap:"24px",marginBottom:"16px"},children:[r.jsx("button",{style:{color:"rgba(255,255,255,0.7)",fontSize:"14px",fontWeight:"300",background:"none",border:"none",cursor:"pointer",transition:"color 0.3s ease"},onMouseEnter:e=>{e.currentTarget.style.color="white"},onMouseLeave:e=>{e.currentTarget.style.color="rgba(255,255,255,0.7)"},children:"忘记密码?"}),r.jsx("div",{style:{width:"1px",height:"16px",background:"rgba(255,255,255,0.3)"}}),r.jsx("button",{style:{color:"rgba(255,255,255,0.7)",fontSize:"14px",fontWeight:"300",background:"none",border:"none",cursor:"pointer",transition:"color 0.3s ease"},onMouseEnter:e=>{e.currentTarget.style.color="white"},onMouseLeave:e=>{e.currentTarget.style.color="rgba(255,255,255,0.7)"},children:"联系管理员"})]}),r.jsx("p",{style:{color:"rgba(255,255,255,0.5)",fontSize:"12px",fontWeight:"300"},children:"© 2024 智慧学工系统. All rights reserved."})]})})]})]})};export{h as default};
