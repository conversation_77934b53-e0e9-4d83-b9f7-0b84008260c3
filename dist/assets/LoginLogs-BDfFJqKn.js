import{j as e}from"./index-DP6eZxW9.js";import{r as s,ac as l,G as i,H as t,F as a,J as r,bq as n,L as o,N as d,g as c,O as x,I as u,as as h,aH as j,ae as m,a as f,aL as g,X as p,ay as y,M as w,Z as v,B as b,Q as k,T as I,bt as S,af as C,ad as T}from"./antd-DYv0PFJq.js";import"./vendor-D2RBMdQ0.js";const{RangePicker:z}=j,{Option:M}=h,{Search:W}=u,A=()=>{var u;const[j,A]=s.useState([]),[N,Y]=s.useState(!1),[H,P]=s.useState(""),[L,$]=s.useState(""),[B,O]=s.useState(""),[R,D]=s.useState(null),[K,_]=s.useState(!1),[E,F]=s.useState(null),G={success:{text:"成功",color:"success",icon:e.jsx(o,{})},failed:{text:"失败",color:"error",icon:e.jsx(d,{})},logout:{text:"登出",color:"default",icon:e.jsx(T,{})}},X=[{key:"admin",name:"超级管理员"},{key:"counselor",name:"辅导员"},{key:"teacher",name:"教师"},{key:"party_secretary",name:"党建专员"},{key:"dean",name:"院长"}];s.useEffect(()=>{J()},[]);const J=async()=>{Y(!0);try{A([{id:"1",userId:"1",username:"admin",realName:"系统管理员",role:"admin",loginTime:"2024-01-15 10:30:00",logoutTime:"2024-01-15 18:30:00",duration:480,ip:"*************",location:"北京市海淀区",device:"PC",browser:"Chrome 120.0",os:"Windows 11",status:"logout",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"},{id:"2",userId:"2",username:"counselor001",realName:"张辅导员",role:"counselor",loginTime:"2024-01-15 09:15:00",ip:"*************",location:"北京市朝阳区",device:"PC",browser:"Firefox 121.0",os:"Windows 10",status:"success",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0"},{id:"3",userId:"3",username:"teacher001",realName:"李老师",role:"teacher",loginTime:"2024-01-15 08:45:00",ip:"*************",location:"北京市西城区",device:"Mobile",browser:"Safari 17.0",os:"iOS 17.2",status:"success",userAgent:"Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15"},{id:"4",userId:"4",username:"hacker",realName:"未知用户",role:"",loginTime:"2024-01-15 02:30:00",ip:"123.456.789.0",location:"未知地区",device:"PC",browser:"Chrome 120.0",os:"Linux",status:"failed",failReason:"用户名或密码错误",userAgent:"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"},{id:"5",userId:"5",username:"party001",realName:"王专员",role:"party_secretary",loginTime:"2024-01-14 16:20:00",logoutTime:"2024-01-14 17:45:00",duration:85,ip:"*************",location:"北京市东城区",device:"Tablet",browser:"Edge 120.0",os:"Windows 11",status:"logout",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0"}])}catch(e){console.error("获取登录日志失败:",e)}finally{Y(!1)}},Q=[{title:"用户信息",key:"userInfo",width:180,render:(s,l)=>e.jsxs(m,{direction:"vertical",size:"small",children:[e.jsxs(m,{children:[e.jsx(c,{style:{color:"#1890ff"}}),e.jsx("span",{style:{fontWeight:500},children:l.realName})]}),e.jsxs("div",{style:{fontSize:"12px",color:"#666"},children:["@",l.username]})]})},{title:"角色",dataIndex:"role",key:"role",width:100,render:s=>{const l=X.find(e=>e.key===s);return l?e.jsx(k,{color:"blue",children:l.name}):e.jsx(k,{color:"default",children:"未知"})}},{title:"登录时间",dataIndex:"loginTime",key:"loginTime",width:150,sorter:(e,s)=>l(e.loginTime).unix()-l(s.loginTime).unix(),render:s=>e.jsx("div",{style:{fontSize:"12px"},children:l(s).format("YYYY-MM-DD HH:mm:ss")})},{title:"在线时长",key:"duration",width:100,render:(s,l)=>{if("failed"===l.status)return"-";if(l.duration){return`${Math.floor(l.duration/60)}h ${l.duration%60}m`}return"success"===l.status?e.jsx(k,{color:"green",children:"在线中"}):"-"}},{title:"IP地址",dataIndex:"ip",key:"ip",width:120,render:s=>e.jsx(I,{title:s,children:e.jsx("code",{style:{fontSize:"12px"},children:s})})},{title:"地理位置",dataIndex:"location",key:"location",width:120,render:s=>e.jsxs(m,{children:[e.jsx(S,{style:{color:"#52c41a"}}),e.jsx("span",{style:{fontSize:"12px"},children:s})]})},{title:"设备信息",key:"deviceInfo",width:150,render:(s,l)=>e.jsxs("div",{style:{fontSize:"12px"},children:[e.jsxs("div",{children:[l.device," - ",l.os]}),e.jsx("div",{style:{color:"#666"},children:l.browser})]})},{title:"状态",dataIndex:"status",key:"status",width:100,render:(s,l)=>{const i=G[s];return e.jsxs(m,{children:[e.jsx(b,{status:i.color,text:i.text}),"failed"===s&&l.failReason&&e.jsx(I,{title:l.failReason,children:e.jsx(d,{style:{color:"#ff4d4f"}})})]})}},{title:"操作",key:"action",width:80,fixed:"right",render:(s,l)=>e.jsx(I,{title:"查看详情",children:e.jsx(f,{type:"text",size:"small",icon:e.jsx(C,{}),onClick:()=>U(l)})})}],U=e=>{F(e),_(!0)},q=()=>{J()},Z=j.filter(e=>{const s=!H||e.realName.toLowerCase().includes(H.toLowerCase())||e.username.toLowerCase().includes(H.toLowerCase())||e.ip.includes(H),i=!L||e.status===L,t=!B||e.role===B,a=!R||l(e.loginTime).isAfter(R[0].startOf("day"))&&l(e.loginTime).isBefore(R[1].endOf("day"));return s&&i&&t&&a}),V={total:j.length,success:j.filter(e=>"success"===e.status).length,failed:j.filter(e=>"failed"===e.status).length,online:j.filter(e=>"success"===e.status&&!e.logoutTime).length};return e.jsxs("div",{children:[e.jsxs(i,{gutter:16,style:{marginBottom:24},children:[e.jsx(t,{span:6,children:e.jsx(a,{children:e.jsx(r,{title:"总登录次数",value:V.total,prefix:e.jsx(n,{}),valueStyle:{color:"#1890ff"}})})}),e.jsx(t,{span:6,children:e.jsx(a,{children:e.jsx(r,{title:"成功登录",value:V.success,prefix:e.jsx(o,{}),valueStyle:{color:"#52c41a"}})})}),e.jsx(t,{span:6,children:e.jsx(a,{children:e.jsx(r,{title:"登录失败",value:V.failed,prefix:e.jsx(d,{}),valueStyle:{color:"#f5222d"}})})}),e.jsx(t,{span:6,children:e.jsx(a,{children:e.jsx(r,{title:"当前在线",value:V.online,prefix:e.jsx(c,{}),valueStyle:{color:"#fa8c16"}})})})]}),V.failed>0&&e.jsx(x,{message:"安全提醒",description:`检测到 ${V.failed} 次登录失败记录，请注意账户安全。`,type:"warning",showIcon:!0,style:{marginBottom:16}}),e.jsx(a,{style:{marginBottom:16},children:e.jsxs(i,{gutter:16,align:"middle",children:[e.jsx(t,{span:6,children:e.jsx(W,{placeholder:"搜索用户名、姓名或IP",allowClear:!0,value:H,onChange:e=>P(e.target.value),onSearch:q})}),e.jsx(t,{span:4,children:e.jsxs(h,{placeholder:"选择状态",allowClear:!0,style:{width:"100%"},value:L,onChange:$,children:[e.jsx(M,{value:"success",children:"成功"}),e.jsx(M,{value:"failed",children:"失败"}),e.jsx(M,{value:"logout",children:"登出"})]})}),e.jsx(t,{span:4,children:e.jsx(h,{placeholder:"选择角色",allowClear:!0,style:{width:"100%"},value:B,onChange:O,children:X.map(s=>e.jsx(M,{value:s.key,children:s.name},s.key))})}),e.jsx(t,{span:6,children:e.jsx(z,{style:{width:"100%"},value:R,onChange:D,placeholder:["开始日期","结束日期"]})}),e.jsx(t,{span:4,children:e.jsxs(m,{children:[e.jsx(f,{type:"primary",icon:e.jsx(g,{}),onClick:q,children:"搜索"}),e.jsx(f,{icon:e.jsx(p,{}),onClick:()=>{P(""),$(""),O(""),D(null),J()},children:"重置"})]})})]})}),e.jsxs(a,{children:[e.jsxs("div",{style:{marginBottom:16,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsxs("span",{style:{fontSize:16,fontWeight:500},children:["登录日志 (",Z.length,")"]}),e.jsx(f,{icon:e.jsx(p,{}),onClick:()=>{J()},loading:N,children:"刷新"})]}),e.jsx(y,{columns:Q,dataSource:Z,rowKey:"id",loading:N,scroll:{x:1200},pagination:{total:Z.length,pageSize:20,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,s)=>`第 ${s[0]}-${s[1]} 条/共 ${e} 条`}})]}),e.jsx(w,{title:"登录详情",open:K,onCancel:()=>_(!1),footer:null,width:600,children:E&&e.jsxs("div",{children:[e.jsxs(v,{column:2,bordered:!0,size:"small",children:[e.jsx(v.Item,{label:"用户名",children:E.username}),e.jsx(v.Item,{label:"真实姓名",children:E.realName}),e.jsx(v.Item,{label:"角色",children:(null==(u=X.find(e=>e.key===E.role))?void 0:u.name)||"未知"}),e.jsx(v.Item,{label:"状态",children:e.jsx(b,{status:G[E.status].color,text:G[E.status].text})}),e.jsx(v.Item,{label:"登录时间",children:l(E.loginTime).format("YYYY-MM-DD HH:mm:ss")}),e.jsx(v.Item,{label:"登出时间",children:E.logoutTime?l(E.logoutTime).format("YYYY-MM-DD HH:mm:ss"):"未登出"}),e.jsx(v.Item,{label:"在线时长",children:E.duration?`${Math.floor(E.duration/60)}小时${E.duration%60}分钟`:"success"===E.status?"在线中":"-"}),e.jsx(v.Item,{label:"IP地址",children:e.jsx("code",{children:E.ip})}),e.jsx(v.Item,{label:"设备类型",children:E.device}),e.jsx(v.Item,{label:"操作系统",children:E.os})]}),e.jsxs(v,{column:1,bordered:!0,size:"small",style:{marginTop:8},children:[e.jsx(v.Item,{label:"地理位置",children:E.location}),e.jsx(v.Item,{label:"浏览器",children:E.browser}),E.failReason&&e.jsx(v.Item,{label:"失败原因",children:e.jsx("span",{style:{color:"#f5222d"},children:E.failReason})}),e.jsx(v.Item,{label:"User Agent",children:e.jsx("div",{style:{fontSize:"12px",wordBreak:"break-all",maxHeight:60,overflow:"auto",backgroundColor:"#f5f5f5",padding:8,borderRadius:4},children:E.userAgent})})]})]})})]})};export{A as default};
