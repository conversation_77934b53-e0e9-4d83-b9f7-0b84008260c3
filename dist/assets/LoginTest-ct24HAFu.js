import{j as e,r as s}from"./index-Cu_U9Dm3.js";import{r,E as l,F as t,ae as a,I as i,a as n,U as o}from"./antd-DYv0PFJq.js";import{g as c}from"./userService-kUY0fGJp.js";import{u as d}from"./useMessage-DDIprusu.js";import"./vendor-D2RBMdQ0.js";const{Title:m,Text:g,Paragraph:x}=l,h=()=>{const l=d(),[h,p]=r.useState("admin"),[u,j]=r.useState("admin123"),[S,f]=r.useState(!1),[y,v]=r.useState("");return e.jsxs("div",{style:{padding:"24px",maxWidth:"800px",margin:"0 auto"},children:[e.jsx(m,{level:2,children:"登录测试页面"}),e.jsx(t,{title:"登录测试",style:{marginBottom:"16px"},children:e.jsxs(a,{direction:"vertical",style:{width:"100%"},children:[e.jsxs("div",{children:[e.jsx(g,{strong:!0,children:"用户名："}),e.jsx(i,{value:h,onChange:e=>p(e.target.value),placeholder:"用户名",style:{width:"200px",marginLeft:"8px"}})]}),e.jsxs("div",{children:[e.jsx(g,{strong:!0,children:"密码："}),e.jsx(i.Password,{value:u,onChange:e=>j(e.target.value),placeholder:"密码",style:{width:"200px",marginLeft:"8px"}})]}),e.jsxs(a,{children:[e.jsx(n,{type:"primary",onClick:async()=>{f(!0);try{const e=await s.post("/auth/login",{username:h,password:u});if(e.success){const{user:s,token:r}=e.data,t=c(s.role);localStorage.setItem("user",JSON.stringify({id:s.id,username:s.username,realName:s.name,role:s.role,department:s.department||"",permissions:t,email:s.email,phone:s.phone})),localStorage.setItem("permissions",JSON.stringify(t)),localStorage.setItem("token",r),v(JSON.stringify({success:!0,user:s,token:r.substring(0,50)+"...",permissions:t},null,2)),l.success("登录成功！")}else v(JSON.stringify(e,null,2)),l.error("登录失败")}catch(e){console.error("登录测试失败:",e),v(`错误: ${e}`),l.error("登录测试失败")}finally{f(!1)}},loading:S,children:"测试登录"}),e.jsx(n,{onClick:()=>{const e=localStorage.getItem("user"),s=localStorage.getItem("permissions"),r=localStorage.getItem("token");v(JSON.stringify({user:e?JSON.parse(e):null,permissions:s?JSON.parse(s):null,token:r?r.substring(0,50)+"...":null},null,2))},children:"检查localStorage"}),e.jsx(n,{onClick:()=>{localStorage.removeItem("user"),localStorage.removeItem("permissions"),localStorage.removeItem("token"),v("localStorage已清空"),l.info("localStorage已清空")},danger:!0,children:"清空localStorage"})]})]})}),e.jsx(t,{title:"响应结果",children:e.jsx("pre",{style:{background:"#f5f5f5",padding:"16px",borderRadius:"4px",overflow:"auto",maxHeight:"400px"},children:y||"暂无数据"})}),e.jsxs(t,{title:"说明",style:{marginTop:"16px"},children:[e.jsx(x,{children:e.jsx(g,{strong:!0,children:"测试步骤："})}),e.jsxs("ol",{children:[e.jsx("li",{children:'点击"测试登录"按钮，使用admin/admin123登录'}),e.jsx("li",{children:"检查返回的用户信息和token"}),e.jsx("li",{children:'点击"检查localStorage"查看存储的数据'}),e.jsx("li",{children:"验证权限是否正确设置"})]}),e.jsx(o,{}),e.jsx(x,{children:e.jsx(g,{strong:!0,children:"预期结果："})}),e.jsxs("ul",{children:[e.jsx("li",{children:"登录成功，返回用户信息和token"}),e.jsx("li",{children:"localStorage中正确存储用户信息、权限和token"}),e.jsx("li",{children:"admin角色应该有所有权限（包含'*'或具体权限列表）"})]})]})]})};export{h as default};
