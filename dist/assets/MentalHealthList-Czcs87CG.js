import{u as e,j as s,r as l}from"./index-DP6eZxW9.js";import{a0 as a,r as t,aq as n,ae as r,a as i,X as c,y as d,ao as o,O as x,aa as m,G as h,H as j,F as u,J as p,g as v,L as g,N as y,I as f,aL as N,as as w,ay as b,M as I,aH as S,aS as k,aT as _,Q as L,ag as C,T as D,af as T,aB as z,W as q,ac as A,aU as F}from"./antd-DYv0PFJq.js";import{h as U}from"./studentMatcher-GgRWGHhb.js";import{u as $}from"./useMessage-DDIprusu.js";import"./vendor-D2RBMdQ0.js";const{Option:O}=w,{TextArea:V}=f,Y=e=>({low:"normal",medium:"attention",high:"intervention",critical:"crisis"}[e]||"normal"),M=(e,s=null)=>{if(!e)return s;if("string"!=typeof e)return e;try{return e.startsWith("[")||e.startsWith("{")?JSON.parse(e):Array.isArray(s)?[e]:e}catch(l){return console.warn("JSON解析失败:",e,l),Array.isArray(s)?[e]:s}},J=e=>{switch(e){case"low":return 3;case"medium":return 6;case"high":return 9;default:return 5}},Q=()=>{const Q=e(),W=$(),{modal:H}=a.useApp(),[K,P]=t.useState([]),[B,E]=t.useState(!1),[G,R]=t.useState(""),[X,Z]=t.useState(""),[ee,se]=t.useState(""),[le,ae]=t.useState(!1),[te,ne]=t.useState(!1),[re,ie]=t.useState(null),[ce,de]=t.useState(null),[oe]=n.useForm(),xe=async()=>{E(!0);try{const e=await l.get("/mental-health");if(e.success){const s=e.data.map(e=>{return{id:e.id,studentId:e.student_number||e.student_id,studentName:e.student_name||"",class:e.class||"",major:e.major||"",college:e.college||"",phone:e.phone||"",email:e.email||"",assessmentDate:e.assessment_date?new Date(e.assessment_date).toISOString().split("T")[0]:"",assessmentType:e.assessment_type||"routine",mentalState:(s=e.risk_level,{low:"excellent",medium:"normal",high:"concerning",critical:"critical"}[s]||"normal"),stressLevel:J(e.stress_level),anxietyLevel:J(e.anxiety_level),depressionLevel:J(e.depression_level),sleepQuality:e.sleep_quality||3,socialAdaptation:e.social_adaptation||3,academicPressure:e.academic_pressure||3,symptoms:M(e.symptoms,[]),riskFactors:M(e.risk_factors,[]),interventions:M(e.interventions,[]),counselor:e.counselor||"",followUpDate:e.follow_up_date?new Date(e.follow_up_date).toISOString().split("T")[0]:"",notes:e.recommendations||e.notes||"",status:Y(e.risk_level)||"normal",createdAt:e.created_at?new Date(e.created_at).toISOString().split("T")[0]:"",updatedAt:e.updated_at?new Date(e.updated_at).toISOString().split("T")[0]:""};var s});P(s)}else W.error("获取心理健康记录失败")}catch(e){console.error("获取心理健康记录错误:",e),W.error("获取心理健康记录失败")}finally{E(!1)}};t.useEffect(()=>{xe()},[]);const me=e=>{ie(e),oe.setFieldsValue({...e,assessmentDate:A(e.assessmentDate),followUpDate:e.followUpDate?A(e.followUpDate):null}),ae(!0)},he=K.filter(e=>{const s=!G||e.studentName.toLowerCase().includes(G.toLowerCase())||e.studentId.toLowerCase().includes(G.toLowerCase()),l=!X||e.status===X,a=!ee||e.assessmentType===ee;return s&&l&&a}),je=[{title:"学生信息",key:"student",render:(e,l)=>s.jsxs("div",{children:[s.jsx("div",{className:"font-medium",children:s.jsx(i,{type:"link",onClick:()=>Q(`/students/${l.studentId}`),className:"p-0 h-auto",children:l.studentName})}),s.jsxs("div",{className:"text-gray-500 text-xs",children:[l.studentId," · ",l.class]}),s.jsxs("div",{className:"text-gray-400 text-xs",children:[l.major," · ",l.college]})]})},{title:"评估类型",dataIndex:"assessmentType",key:"assessmentType",render:e=>{const l={routine:{color:"blue",text:"常规评估"},special:{color:"orange",text:"专项评估"},crisis:{color:"red",text:"危机评估"},"follow-up":{color:"green",text:"跟踪评估"}}[e];return s.jsx(L,{color:l.color,children:l.text})}},{title:"心理状态",dataIndex:"mentalState",key:"mentalState",render:e=>{const l={excellent:{color:"green",text:"优秀"},good:{color:"blue",text:"良好"},normal:{color:"default",text:"正常"},concerning:{color:"orange",text:"关注"},critical:{color:"red",text:"危险"}}[e];return s.jsx(L,{color:l.color,children:l.text})}},{title:"压力指标",key:"levels",render:(e,l)=>s.jsxs("div",{className:"text-sm space-y-1",children:[s.jsxs("div",{className:"flex items-center",children:[s.jsx("span",{className:"w-12 text-xs",children:"压力:"}),s.jsx(C,{percent:10*l.stressLevel,size:"small",style:{width:60},strokeColor:l.stressLevel>7?"#ff4d4f":l.stressLevel>5?"#faad14":"#52c41a"}),s.jsxs("span",{className:"ml-1 text-xs",children:[l.stressLevel,"/10"]})]}),s.jsxs("div",{className:"flex items-center",children:[s.jsx("span",{className:"w-12 text-xs",children:"焦虑:"}),s.jsx(C,{percent:10*l.anxietyLevel,size:"small",style:{width:60},strokeColor:l.anxietyLevel>7?"#ff4d4f":l.anxietyLevel>5?"#faad14":"#52c41a"}),s.jsxs("span",{className:"ml-1 text-xs",children:[l.anxietyLevel,"/10"]})]})]})},{title:"评估日期",dataIndex:"assessmentDate",key:"assessmentDate"},{title:"咨询师",dataIndex:"counselor",key:"counselor"},{title:"状态",dataIndex:"status",key:"status",render:e=>{const l={normal:{color:"green",text:"正常",icon:s.jsx(g,{})},attention:{color:"orange",text:"关注",icon:s.jsx(y,{})},intervention:{color:"blue",text:"干预",icon:s.jsx(F,{})},crisis:{color:"red",text:"危机",icon:s.jsx(m,{})}}[e];return s.jsx(L,{color:l.color,icon:l.icon,children:l.text})}},{title:"操作",key:"action",render:(e,a)=>s.jsxs(r,{children:[s.jsx(D,{title:"查看详情",children:s.jsx(i,{type:"text",icon:s.jsx(T,{}),onClick:()=>(e=>{de(e),ne(!0)})(a)})}),s.jsx(D,{title:"编辑",children:s.jsx(i,{type:"text",icon:s.jsx(z,{}),onClick:()=>me(a)})}),s.jsx(D,{title:"删除",children:s.jsx(i,{type:"text",danger:!0,icon:s.jsx(q,{}),onClick:()=>(e=>{H.confirm({title:"确认删除",content:`确定要删除 ${e.studentName} 的心理健康记录吗？`,onOk:async()=>{try{(await l.delete(`/mental-health/${e.id}`)).success?(W.success("删除成功"),await xe()):W.error("删除失败")}catch(s){console.error("删除心理健康记录错误:",s),W.error("删除失败")}}})})(a)})})]})}],ue=K.length,pe=K.filter(e=>"normal"===e.status).length,ve=K.filter(e=>"attention"===e.status).length,ge=K.filter(e=>"crisis"===e.status).length;return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"心理健康管理"}),s.jsx("p",{className:"text-gray-600",children:"管理学生心理健康评估和干预记录"})]}),s.jsxs(r,{children:[s.jsx(i,{icon:s.jsx(c,{}),onClick:xe,size:"large",children:"刷新数据"}),s.jsx(i,{icon:s.jsx(d,{}),onClick:async()=>{E(!0);try{await xe(),W.success("学生信息同步完成")}catch(e){console.error("同步学生信息错误:",e),W.error("同步学生信息失败")}finally{E(!1)}},size:"large",loading:B,children:"同步学生信息"}),s.jsx(i,{type:"primary",icon:s.jsx(o,{}),onClick:()=>{ie(null),oe.resetFields(),ae(!0)},size:"large",children:"新增记录"})]})]}),ge>0&&s.jsx(x,{message:"危机预警",description:`当前有 ${ge} 名学生处于心理危机状态，请及时关注和处理！`,type:"error",showIcon:!0,icon:s.jsx(m,{}),action:s.jsx(i,{size:"small",danger:!0,children:"查看详情"})}),s.jsxs(h,{gutter:16,children:[s.jsx(j,{span:6,children:s.jsx(u,{children:s.jsx(p,{title:"评估总数",value:ue,prefix:s.jsx(v,{})})})}),s.jsx(j,{span:6,children:s.jsx(u,{children:s.jsx(p,{title:"正常状态",value:pe,valueStyle:{color:"#3f8600"},prefix:s.jsx(g,{})})})}),s.jsx(j,{span:6,children:s.jsx(u,{children:s.jsx(p,{title:"需要关注",value:ve,valueStyle:{color:"#faad14"},prefix:s.jsx(y,{})})})}),s.jsx(j,{span:6,children:s.jsx(u,{children:s.jsx(p,{title:"危机状态",value:ge,valueStyle:{color:"#ff4d4f"},prefix:s.jsx(m,{})})})})]}),s.jsxs(u,{children:[s.jsxs("div",{className:"flex items-center space-x-4 mb-4",children:[s.jsx(f,{placeholder:"搜索学号或姓名",prefix:s.jsx(N,{}),value:G,onChange:e=>R(e.target.value),style:{width:300}}),s.jsxs(w,{placeholder:"评估类型",value:ee,onChange:se,allowClear:!0,style:{width:120},children:[s.jsx(O,{value:"routine",children:"常规评估"}),s.jsx(O,{value:"special",children:"专项评估"}),s.jsx(O,{value:"crisis",children:"危机评估"}),s.jsx(O,{value:"follow-up",children:"跟踪评估"})]}),s.jsxs(w,{placeholder:"状态",value:X,onChange:Z,allowClear:!0,style:{width:120},children:[s.jsx(O,{value:"normal",children:"正常"}),s.jsx(O,{value:"attention",children:"关注"}),s.jsx(O,{value:"intervention",children:"干预"}),s.jsx(O,{value:"crisis",children:"危机"})]})]}),s.jsx(b,{columns:je,dataSource:he,rowKey:"id",loading:B,pagination:{total:he.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 条记录`},scroll:{x:1400}})]}),s.jsx(I,{title:re?"编辑心理健康记录":"新增心理健康记录",open:le,onCancel:()=>ae(!1),footer:null,width:800,children:s.jsxs(n,{form:oe,layout:"vertical",onFinish:async e=>{var s,a,t;try{if(e.studentId){const s=await l.get(`/students?student_id=${encodeURIComponent(e.studentId)}`);if(!s.success||0===s.data.length)return void W.error("未找到该学号对应的学生，请检查学号是否正确")}const a=e=>e<=3?"low":e<=7?"medium":"high",t={student_id:e.studentId,assessment_date:e.assessmentDate.format("YYYY-MM-DD"),assessment_type:e.assessmentType,counselor:e.counselor,mood_score:"excellent"===e.mentalState?10:"good"===e.mentalState?8:"normal"===e.mentalState?6:"concerning"===e.mentalState?4:2,anxiety_level:a(e.anxietyLevel||1),depression_level:a(e.depressionLevel||1),stress_level:a(e.stressLevel||1),risk_level:"normal"===e.status?"low":"attention"===e.status?"medium":"intervention"===e.status?"high":"critical",symptoms:Array.isArray(e.symptoms)?e.symptoms.join(", "):e.symptoms||"",recommendations:e.notes||"",follow_up_needed:!!e.followUpDate,follow_up_date:(null==(s=e.followUpDate)?void 0:s.format("YYYY-MM-DD"))||null,status:"active",confidential:!0};if(re){(await l.put(`/mental-health/${re.id}`,t)).success?(W.success("心理健康记录更新成功"),await xe()):W.error("更新心理健康记录失败")}else{(await l.post("/mental-health",t)).success?(W.success("心理健康记录创建成功"),await xe()):W.error("创建心理健康记录失败")}ae(!1),oe.resetFields(),ie(null)}catch(n){console.error("保存心理健康记录错误:",n);const e=(null==(t=null==(a=null==n?void 0:n.response)?void 0:a.data)?void 0:t.message)||(null==n?void 0:n.message)||"保存失败";W.error(e)}},children:[s.jsxs(h,{gutter:16,children:[s.jsx(j,{span:12,children:s.jsx(n.Item,{label:"学号",name:"studentId",rules:[{required:!0,message:"请输入学号"}],children:s.jsx(f,{placeholder:"输入学号自动匹配学生信息",onChange:e=>(async e=>{await U(e,oe,void 0,W)})(e.target.value)})})}),s.jsx(j,{span:12,children:s.jsx(n.Item,{label:"学生姓名",name:"studentName",rules:[{required:!0,message:"请输入学生姓名"}],children:s.jsx(f,{placeholder:"自动匹配",disabled:!0})})})]}),s.jsxs(h,{gutter:16,children:[s.jsx(j,{span:8,children:s.jsx(n.Item,{label:"班级",name:"class",rules:[{required:!0,message:"请输入班级"}],children:s.jsx(f,{placeholder:"自动匹配",disabled:!0})})}),s.jsx(j,{span:8,children:s.jsx(n.Item,{label:"专业",name:"major",children:s.jsx(f,{placeholder:"自动匹配",disabled:!0})})}),s.jsx(j,{span:8,children:s.jsx(n.Item,{label:"学院",name:"college",children:s.jsx(f,{placeholder:"自动匹配",disabled:!0})})})]}),s.jsxs(h,{gutter:16,children:[s.jsx(j,{span:8,children:s.jsx(n.Item,{label:"联系电话",name:"phone",children:s.jsx(f,{placeholder:"自动匹配",disabled:!0})})}),s.jsx(j,{span:8,children:s.jsx(n.Item,{label:"邮箱",name:"email",children:s.jsx(f,{placeholder:"自动匹配",disabled:!0})})}),s.jsx(j,{span:8,children:s.jsx(n.Item,{label:"评估类型",name:"assessmentType",rules:[{required:!0,message:"请选择评估类型"}],children:s.jsxs(w,{placeholder:"选择类型",children:[s.jsx(O,{value:"routine",children:"常规评估"}),s.jsx(O,{value:"consultation",children:"咨询评估"}),s.jsx(O,{value:"crisis",children:"危机评估"}),s.jsx(O,{value:"follow_up",children:"跟踪评估"})]})})})]}),s.jsx(h,{gutter:16,children:s.jsx(j,{span:24,children:s.jsx(n.Item,{label:"评估日期",name:"assessmentDate",rules:[{required:!0,message:"请选择评估日期"}],children:s.jsx(S,{style:{width:"100%"}})})})}),s.jsxs(h,{gutter:16,children:[s.jsx(j,{span:12,children:s.jsx(n.Item,{label:"心理状态",name:"mentalState",rules:[{required:!0,message:"请选择心理状态"}],children:s.jsxs(w,{placeholder:"选择状态",children:[s.jsx(O,{value:"excellent",children:"优秀"}),s.jsx(O,{value:"good",children:"良好"}),s.jsx(O,{value:"normal",children:"正常"}),s.jsx(O,{value:"concerning",children:"关注"}),s.jsx(O,{value:"critical",children:"危险"})]})})}),s.jsx(j,{span:12,children:s.jsx(n.Item,{label:"咨询师",name:"counselor",rules:[{required:!0,message:"请输入咨询师"}],children:s.jsx(f,{placeholder:"咨询师姓名"})})})]}),s.jsxs(h,{gutter:16,children:[s.jsx(j,{span:8,children:s.jsx(n.Item,{label:"压力水平 (1-10)",name:"stressLevel",initialValue:5,rules:[{required:!0,message:"请选择压力水平"}],children:s.jsx(k,{min:1,max:10,marks:{1:"1",5:"5",10:"10"}})})}),s.jsx(j,{span:8,children:s.jsx(n.Item,{label:"焦虑水平 (1-10)",name:"anxietyLevel",initialValue:5,rules:[{required:!0,message:"请选择焦虑水平"}],children:s.jsx(k,{min:1,max:10,marks:{1:"1",5:"5",10:"10"}})})}),s.jsx(j,{span:8,children:s.jsx(n.Item,{label:"抑郁水平 (1-10)",name:"depressionLevel",initialValue:5,rules:[{required:!0,message:"请选择抑郁水平"}],children:s.jsx(k,{min:1,max:10,marks:{1:"1",5:"5",10:"10"}})})})]}),s.jsxs(h,{gutter:16,children:[s.jsx(j,{span:8,children:s.jsx(n.Item,{label:"睡眠质量",name:"sleepQuality",initialValue:3,rules:[{required:!0,message:"请评价睡眠质量"}],children:s.jsx(_,{count:5})})}),s.jsx(j,{span:8,children:s.jsx(n.Item,{label:"社会适应",name:"socialAdaptation",initialValue:3,rules:[{required:!0,message:"请评价社会适应"}],children:s.jsx(_,{count:5})})}),s.jsx(j,{span:8,children:s.jsx(n.Item,{label:"学业压力",name:"academicPressure",initialValue:3,rules:[{required:!0,message:"请评价学业压力"}],children:s.jsx(_,{count:5})})})]}),s.jsxs(h,{gutter:16,children:[s.jsx(j,{span:12,children:s.jsx(n.Item,{label:"症状表现",name:"symptoms",children:s.jsxs(w,{mode:"tags",placeholder:"添加症状",children:[s.jsx(O,{value:"失眠",children:"失眠"}),s.jsx(O,{value:"焦虑",children:"焦虑"}),s.jsx(O,{value:"抑郁情绪",children:"抑郁情绪"}),s.jsx(O,{value:"注意力不集中",children:"注意力不集中"}),s.jsx(O,{value:"食欲不振",children:"食欲不振"}),s.jsx(O,{value:"自伤倾向",children:"自伤倾向"})]})})}),s.jsx(j,{span:12,children:s.jsx(n.Item,{label:"风险因素",name:"riskFactors",children:s.jsxs(w,{mode:"tags",placeholder:"添加风险因素",children:[s.jsx(O,{value:"学业压力",children:"学业压力"}),s.jsx(O,{value:"家庭问题",children:"家庭问题"}),s.jsx(O,{value:"人际关系",children:"人际关系"}),s.jsx(O,{value:"经济困难",children:"经济困难"}),s.jsx(O,{value:"就业压力",children:"就业压力"}),s.jsx(O,{value:"感情问题",children:"感情问题"})]})})})]}),s.jsxs(h,{gutter:16,children:[s.jsx(j,{span:12,children:s.jsx(n.Item,{label:"干预措施",name:"interventions",children:s.jsxs(w,{mode:"tags",placeholder:"添加干预措施",children:[s.jsx(O,{value:"心理咨询",children:"心理咨询"}),s.jsx(O,{value:"个体咨询",children:"个体咨询"}),s.jsx(O,{value:"团体咨询",children:"团体咨询"}),s.jsx(O,{value:"放松训练",children:"放松训练"}),s.jsx(O,{value:"危机干预",children:"危机干预"}),s.jsx(O,{value:"医疗转介",children:"医疗转介"}),s.jsx(O,{value:"家长联系",children:"家长联系"})]})})}),s.jsx(j,{span:12,children:s.jsx(n.Item,{label:"跟踪日期",name:"followUpDate",children:s.jsx(S,{style:{width:"100%"}})})})]}),s.jsx(n.Item,{label:"备注",name:"notes",children:s.jsx(V,{rows:3,placeholder:"详细记录评估情况和建议"})}),s.jsx(n.Item,{label:"状态",name:"status",initialValue:"normal",children:s.jsxs(w,{children:[s.jsx(O,{value:"normal",children:"正常"}),s.jsx(O,{value:"attention",children:"关注"}),s.jsx(O,{value:"intervention",children:"干预"}),s.jsx(O,{value:"crisis",children:"危机"})]})}),s.jsxs("div",{className:"flex justify-end space-x-2",children:[s.jsx(i,{onClick:()=>ae(!1),children:"取消"}),s.jsx(i,{type:"primary",htmlType:"submit",children:re?"更新":"创建"})]})]})}),s.jsx(I,{title:"心理健康记录详情",open:te,onCancel:()=>ne(!1),footer:[s.jsx(i,{onClick:()=>ne(!1),children:"关闭"},"close"),s.jsx(i,{type:"primary",onClick:()=>{ce&&(ne(!1),me(ce))},children:"编辑"},"edit")],width:800,children:ce&&s.jsxs("div",{className:"space-y-6",children:[s.jsxs(u,{title:"学生信息",size:"small",children:[s.jsxs(h,{gutter:16,children:[s.jsx(j,{span:8,children:s.jsxs("div",{className:"text-sm",children:[s.jsx("span",{className:"text-gray-500",children:"姓名："}),s.jsx("span",{className:"font-medium",children:ce.studentName})]})}),s.jsx(j,{span:8,children:s.jsxs("div",{className:"text-sm",children:[s.jsx("span",{className:"text-gray-500",children:"学号："}),s.jsx("span",{children:ce.studentId})]})}),s.jsx(j,{span:8,children:s.jsxs("div",{className:"text-sm",children:[s.jsx("span",{className:"text-gray-500",children:"班级："}),s.jsx("span",{children:ce.class})]})})]}),s.jsxs(h,{gutter:16,className:"mt-3",children:[s.jsx(j,{span:8,children:s.jsxs("div",{className:"text-sm",children:[s.jsx("span",{className:"text-gray-500",children:"专业："}),s.jsx("span",{children:ce.major})]})}),s.jsx(j,{span:8,children:s.jsxs("div",{className:"text-sm",children:[s.jsx("span",{className:"text-gray-500",children:"学院："}),s.jsx("span",{children:ce.college})]})}),s.jsx(j,{span:8,children:s.jsxs("div",{className:"text-sm",children:[s.jsx("span",{className:"text-gray-500",children:"联系电话："}),s.jsx("span",{children:ce.phone||"未填写"})]})})]})]}),s.jsx(u,{title:"评估信息",size:"small",children:s.jsxs(h,{gutter:16,children:[s.jsx(j,{span:8,children:s.jsxs("div",{className:"text-sm",children:[s.jsx("span",{className:"text-gray-500",children:"评估日期："}),s.jsx("span",{children:ce.assessmentDate})]})}),s.jsx(j,{span:8,children:s.jsxs("div",{className:"text-sm",children:[s.jsx("span",{className:"text-gray-500",children:"评估类型："}),s.jsx(L,{color:"blue",children:"routine"===ce.assessmentType?"常规评估":"special"===ce.assessmentType?"专项评估":"crisis"===ce.assessmentType?"危机评估":"跟踪评估"})]})}),s.jsx(j,{span:8,children:s.jsxs("div",{className:"text-sm",children:[s.jsx("span",{className:"text-gray-500",children:"咨询师："}),s.jsx("span",{children:ce.counselor})]})})]})}),s.jsxs(u,{title:"心理状态评估",size:"small",children:[s.jsxs(h,{gutter:16,children:[s.jsx(j,{span:12,children:s.jsxs("div",{className:"text-sm mb-3",children:[s.jsx("span",{className:"text-gray-500",children:"整体心理状态："}),s.jsx(L,{color:"excellent"===ce.mentalState?"green":"good"===ce.mentalState?"blue":"normal"===ce.mentalState?"default":"concerning"===ce.mentalState?"orange":"red",children:"excellent"===ce.mentalState?"优秀":"good"===ce.mentalState?"良好":"normal"===ce.mentalState?"正常":"concerning"===ce.mentalState?"关注":"危险"})]})}),s.jsx(j,{span:12,children:s.jsxs("div",{className:"text-sm mb-3",children:[s.jsx("span",{className:"text-gray-500",children:"当前状态："}),s.jsx(L,{color:"normal"===ce.status?"green":"attention"===ce.status?"orange":"intervention"===ce.status?"blue":"red",children:"normal"===ce.status?"正常":"attention"===ce.status?"关注":"intervention"===ce.status?"干预":"危机"})]})})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{children:[s.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"压力水平："}),s.jsx(C,{percent:10*ce.stressLevel,strokeColor:ce.stressLevel>7?"#ff4d4f":ce.stressLevel>5?"#faad14":"#52c41a",format:()=>`${ce.stressLevel}/10`})]}),s.jsxs("div",{children:[s.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"焦虑水平："}),s.jsx(C,{percent:10*ce.anxietyLevel,strokeColor:ce.anxietyLevel>7?"#ff4d4f":ce.anxietyLevel>5?"#faad14":"#52c41a",format:()=>`${ce.anxietyLevel}/10`})]}),s.jsxs("div",{children:[s.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"抑郁水平："}),s.jsx(C,{percent:10*ce.depressionLevel,strokeColor:ce.depressionLevel>7?"#ff4d4f":ce.depressionLevel>5?"#faad14":"#52c41a",format:()=>`${ce.depressionLevel}/10`})]})]})]}),(ce.symptoms.length>0||ce.riskFactors.length>0)&&s.jsxs(u,{title:"症状与风险因素",size:"small",children:[ce.symptoms.length>0&&s.jsxs("div",{className:"mb-3",children:[s.jsx("div",{className:"text-sm text-gray-500 mb-2",children:"主要症状："}),s.jsx("div",{className:"space-x-1",children:ce.symptoms.map((e,l)=>s.jsx(L,{color:"orange",children:e},l))})]}),ce.riskFactors.length>0&&s.jsxs("div",{children:[s.jsx("div",{className:"text-sm text-gray-500 mb-2",children:"风险因素："}),s.jsx("div",{className:"space-x-1",children:ce.riskFactors.map((e,l)=>s.jsx(L,{color:"red",children:e},l))})]})]}),ce.interventions.length>0&&s.jsx(u,{title:"干预措施",size:"small",children:s.jsx("div",{className:"space-x-1",children:ce.interventions.map((e,l)=>s.jsx(L,{color:"blue",children:e},l))})}),ce.notes&&s.jsx(u,{title:"详细记录",size:"small",children:s.jsx("div",{className:"text-sm whitespace-pre-wrap bg-gray-50 p-3 rounded",children:ce.notes})}),ce.followUpDate&&s.jsx(u,{title:"跟踪安排",size:"small",children:s.jsxs("div",{className:"text-sm",children:[s.jsx("span",{className:"text-gray-500",children:"下次跟踪日期："}),s.jsx("span",{className:"font-medium text-blue-600",children:ce.followUpDate})]})}),s.jsx(u,{title:"记录信息",size:"small",children:s.jsxs(h,{gutter:16,children:[s.jsx(j,{span:12,children:s.jsxs("div",{className:"text-sm",children:[s.jsx("span",{className:"text-gray-500",children:"创建时间："}),s.jsx("span",{children:ce.createdAt})]})}),s.jsx(j,{span:12,children:s.jsxs("div",{className:"text-sm",children:[s.jsx("span",{className:"text-gray-500",children:"更新时间："}),s.jsx("span",{children:ce.updatedAt})]})})]})})]})})]})};export{Q as default};
