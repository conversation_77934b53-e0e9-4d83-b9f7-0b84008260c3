import{u as s,j as e}from"./index-DP6eZxW9.js";import{a7 as r,a as t}from"./antd-DYv0PFJq.js";import"./vendor-D2RBMdQ0.js";const a=()=>{const a=s();return e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:e.jsx(r,{status:"404",title:"404",subTitle:"抱歉，您访问的页面不存在。",extra:e.jsxs("div",{className:"space-x-4",children:[e.jsx(t,{type:"primary",onClick:()=>a("/"),children:"返回首页"}),e.jsx(t,{onClick:()=>a(-1),children:"返回上一页"})]})})})};export{a as default};
