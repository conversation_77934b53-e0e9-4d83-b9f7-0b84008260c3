import{j as e}from"./index-BbdVriMF-1753162495593.js";import{r as s,ac as a,G as t,H as l,F as n,J as i,bu as r,L as o,N as d,ad as c,O as u,I as x,as as m,aH as h,ae as p,a as j,aL as y,X as f,ay as g,M as w,Z as k,B as v,ao as I,aB as S,W as C,af as b,au as z,aw as N,g as W,Q as T,T as A}from"./antd-w1cSjLgF-1753162495593.js";import"./vendor-D2RBMdQ0-1753162495593.js";const{RangePicker:M}=h,{Option:O}=m,{Search:B}=x,L=()=>{var x,h;const[L,V]=s.useState([]),[Y,$]=s.useState(!1),[H,P]=s.useState(""),[D,F]=s.useState(""),[J,K]=s.useState(""),[U,E]=s.useState(""),[G,Q]=s.useState(null),[R,X]=s.useState(!1),[_,Z]=s.useState(null),q=[{key:"student",name:"学生管理"},{key:"teacher",name:"教师管理"},{key:"class",name:"班级管理"},{key:"academic",name:"学业管理"},{key:"award",name:"奖惩管理"},{key:"financial",name:"贫困补助"},{key:"party",name:"党建工作"},{key:"psychology",name:"心理健康"},{key:"certificate",name:"证明开具"},{key:"system",name:"系统管理"}],ee=[{key:"create",name:"新增",icon:e.jsx(I,{}),color:"green"},{key:"update",name:"修改",icon:e.jsx(S,{}),color:"blue"},{key:"delete",name:"删除",icon:e.jsx(C,{}),color:"red"},{key:"view",name:"查看",icon:e.jsx(b,{}),color:"default"},{key:"export",name:"导出",icon:e.jsx(z,{}),color:"purple"},{key:"import",name:"导入",icon:e.jsx(N,{}),color:"orange"}],se={success:{text:"成功",color:"success",icon:e.jsx(o,{})},failed:{text:"失败",color:"error",icon:e.jsx(d,{})},warning:{text:"警告",color:"warning",icon:e.jsx(c,{})}};s.useEffect(()=>{ae()},[]);const ae=async()=>{$(!0);try{V([{id:"1",userId:"1",username:"admin",realName:"系统管理员",role:"admin",module:"student",action:"create",resource:"学生信息",resourceId:"STU001",description:"新增学生：张三 (学号: 2024001)",ip:"*************",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",timestamp:"2024-01-15 10:30:00",status:"success",duration:1200,details:{studentName:"张三",studentId:"2024001",class:"计算机2024-1班"}},{id:"2",userId:"2",username:"counselor001",realName:"张辅导员",role:"counselor",module:"student",action:"update",resource:"学生信息",resourceId:"STU002",description:"修改学生信息：李四的联系方式",ip:"*************",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",timestamp:"2024-01-15 09:15:00",status:"success",duration:800,oldValue:{phone:"13800138000"},newValue:{phone:"13900139000"}},{id:"3",userId:"3",username:"teacher001",realName:"李老师",role:"teacher",module:"teacher",action:"update",resource:"教师档案",resourceId:"TEA001",description:"更新个人教学档案",ip:"*************",userAgent:"Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15",timestamp:"2024-01-15 08:45:00",status:"success",duration:2400},{id:"4",userId:"2",username:"counselor001",realName:"张辅导员",role:"counselor",module:"student",action:"delete",resource:"学生信息",resourceId:"STU003",description:"删除学生记录：王五",ip:"*************",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",timestamp:"2024-01-14 16:20:00",status:"failed",duration:500,details:{error:"该学生存在关联数据，无法删除"}},{id:"5",userId:"1",username:"admin",realName:"系统管理员",role:"admin",module:"student",action:"export",resource:"学生数据",description:"导出学生信息Excel文件",ip:"*************",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",timestamp:"2024-01-14 14:30:00",status:"success",duration:3200,details:{fileName:"学生信息_20240114.xlsx",recordCount:1250}}])}catch(e){console.error("获取操作日志失败:",e)}finally{$(!1)}},te=[{title:"操作人",key:"operator",width:150,render:(s,a)=>e.jsxs(p,{direction:"vertical",size:"small",children:[e.jsxs(p,{children:[e.jsx(W,{style:{color:"#1890ff"}}),e.jsx("span",{style:{fontWeight:500},children:a.realName})]}),e.jsxs("div",{style:{fontSize:"12px",color:"#666"},children:["@",a.username]})]})},{title:"模块",dataIndex:"module",key:"module",width:100,render:s=>{const a=q.find(e=>e.key===s);return e.jsx(T,{color:"blue",children:(null==a?void 0:a.name)||s})}},{title:"操作",dataIndex:"action",key:"action",width:80,render:s=>{const a=ee.find(e=>e.key===s);return a?e.jsx(T,{color:a.color,icon:a.icon,children:a.name}):e.jsx(T,{children:s})}},{title:"操作描述",dataIndex:"description",key:"description",ellipsis:!0,render:s=>e.jsx(A,{title:s,children:e.jsx("span",{children:s})})},{title:"操作时间",dataIndex:"timestamp",key:"timestamp",width:150,sorter:(e,s)=>a(e.timestamp).unix()-a(s.timestamp).unix(),render:s=>e.jsx("div",{style:{fontSize:"12px"},children:a(s).format("YYYY-MM-DD HH:mm:ss")})},{title:"耗时",dataIndex:"duration",key:"duration",width:80,render:s=>e.jsx("span",{style:{fontSize:"12px"},children:s<1e3?`${s}ms`:`${(s/1e3).toFixed(1)}s`})},{title:"IP地址",dataIndex:"ip",key:"ip",width:120,render:s=>e.jsx("code",{style:{fontSize:"12px"},children:s})},{title:"状态",dataIndex:"status",key:"status",width:80,render:s=>{const a=se[s];return e.jsx(v,{status:a.color,text:a.text})}},{title:"操作",key:"action",width:80,fixed:"right",render:(s,a)=>e.jsx(A,{title:"查看详情",children:e.jsx(j,{type:"text",size:"small",icon:e.jsx(b,{}),onClick:()=>le(a)})})}],le=e=>{Z(e),X(!0)},ne=()=>{ae()},ie=L.filter(e=>{const s=!H||e.realName.toLowerCase().includes(H.toLowerCase())||e.username.toLowerCase().includes(H.toLowerCase())||e.description.toLowerCase().includes(H.toLowerCase()),t=!D||e.module===D,l=!J||e.action===J,n=!U||e.status===U,i=!G||a(e.timestamp).isAfter(G[0].startOf("day"))&&a(e.timestamp).isBefore(G[1].endOf("day"));return s&&t&&l&&n&&i}),re={total:L.length,success:L.filter(e=>"success"===e.status).length,failed:L.filter(e=>"failed"===e.status).length,warning:L.filter(e=>"warning"===e.status).length};return e.jsxs("div",{children:[e.jsxs(t,{gutter:16,style:{marginBottom:24},children:[e.jsx(l,{span:6,children:e.jsx(n,{children:e.jsx(i,{title:"总操作数",value:re.total,prefix:e.jsx(r,{}),valueStyle:{color:"#1890ff"}})})}),e.jsx(l,{span:6,children:e.jsx(n,{children:e.jsx(i,{title:"成功操作",value:re.success,prefix:e.jsx(o,{}),valueStyle:{color:"#52c41a"}})})}),e.jsx(l,{span:6,children:e.jsx(n,{children:e.jsx(i,{title:"失败操作",value:re.failed,prefix:e.jsx(d,{}),valueStyle:{color:"#f5222d"}})})}),e.jsx(l,{span:6,children:e.jsx(n,{children:e.jsx(i,{title:"警告操作",value:re.warning,prefix:e.jsx(c,{}),valueStyle:{color:"#fa8c16"}})})})]}),re.failed>0&&e.jsx(u,{message:"操作风险提醒",description:`检测到 ${re.failed} 次操作失败记录，请关注系统安全。`,type:"warning",showIcon:!0,style:{marginBottom:16}}),e.jsx(n,{style:{marginBottom:16},children:e.jsxs(t,{gutter:16,align:"middle",children:[e.jsx(l,{span:5,children:e.jsx(B,{placeholder:"搜索用户、操作描述",allowClear:!0,value:H,onChange:e=>P(e.target.value),onSearch:ne})}),e.jsx(l,{span:3,children:e.jsx(m,{placeholder:"选择模块",allowClear:!0,style:{width:"100%"},value:D,onChange:F,children:q.map(s=>e.jsx(O,{value:s.key,children:s.name},s.key))})}),e.jsx(l,{span:3,children:e.jsx(m,{placeholder:"选择操作",allowClear:!0,style:{width:"100%"},value:J,onChange:K,children:ee.map(s=>e.jsx(O,{value:s.key,children:s.name},s.key))})}),e.jsx(l,{span:3,children:e.jsxs(m,{placeholder:"选择状态",allowClear:!0,style:{width:"100%"},value:U,onChange:E,children:[e.jsx(O,{value:"success",children:"成功"}),e.jsx(O,{value:"failed",children:"失败"}),e.jsx(O,{value:"warning",children:"警告"})]})}),e.jsx(l,{span:6,children:e.jsx(M,{style:{width:"100%"},value:G,onChange:Q,placeholder:["开始日期","结束日期"]})}),e.jsx(l,{span:4,children:e.jsxs(p,{children:[e.jsx(j,{type:"primary",icon:e.jsx(y,{}),onClick:ne,children:"搜索"}),e.jsx(j,{icon:e.jsx(f,{}),onClick:()=>{P(""),F(""),K(""),E(""),Q(null),ae()},children:"重置"})]})})]})}),e.jsxs(n,{children:[e.jsxs("div",{style:{marginBottom:16,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsxs("span",{style:{fontSize:16,fontWeight:500},children:["操作审计 (",ie.length,")"]}),e.jsx(j,{icon:e.jsx(f,{}),onClick:()=>{ae()},loading:Y,children:"刷新"})]}),e.jsx(g,{columns:te,dataSource:ie,rowKey:"id",loading:Y,scroll:{x:1200},pagination:{total:ie.length,pageSize:20,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,s)=>`第 ${s[0]}-${s[1]} 条/共 ${e} 条`}})]}),e.jsx(w,{title:"操作详情",open:R,onCancel:()=>X(!1),footer:null,width:800,children:_&&e.jsxs("div",{children:[e.jsxs(k,{column:2,bordered:!0,size:"small",children:[e.jsxs(k.Item,{label:"操作人",span:1,children:[_.realName," (@",_.username,")"]}),e.jsx(k.Item,{label:"角色",span:1,children:_.role}),e.jsx(k.Item,{label:"模块",span:1,children:null==(x=q.find(e=>e.key===_.module))?void 0:x.name}),e.jsx(k.Item,{label:"操作类型",span:1,children:null==(h=ee.find(e=>e.key===_.action))?void 0:h.name}),e.jsx(k.Item,{label:"操作对象",span:1,children:_.resource}),e.jsx(k.Item,{label:"对象ID",span:1,children:_.resourceId||"-"}),e.jsx(k.Item,{label:"操作时间",span:1,children:a(_.timestamp).format("YYYY-MM-DD HH:mm:ss")}),e.jsx(k.Item,{label:"耗时",span:1,children:_.duration<1e3?`${_.duration}ms`:`${(_.duration/1e3).toFixed(1)}s`}),e.jsx(k.Item,{label:"IP地址",span:1,children:e.jsx("code",{children:_.ip})}),e.jsx(k.Item,{label:"状态",span:1,children:e.jsx(v,{status:se[_.status].color,text:se[_.status].text})}),e.jsx(k.Item,{label:"操作描述",span:2,children:_.description})]}),(_.oldValue||_.newValue)&&e.jsxs("div",{style:{marginTop:16},children:[e.jsx("h4",{children:"变更详情"}),e.jsxs(t,{gutter:16,children:[_.oldValue&&e.jsx(l,{span:12,children:e.jsx(n,{title:"修改前",size:"small",children:e.jsx("pre",{style:{fontSize:"12px",margin:0},children:JSON.stringify(_.oldValue,null,2)})})}),_.newValue&&e.jsx(l,{span:12,children:e.jsx(n,{title:"修改后",size:"small",children:e.jsx("pre",{style:{fontSize:"12px",margin:0},children:JSON.stringify(_.newValue,null,2)})})})]})]}),_.details&&e.jsxs("div",{style:{marginTop:16},children:[e.jsx("h4",{children:"操作详情"}),e.jsx(n,{size:"small",children:e.jsx("pre",{style:{fontSize:"12px",margin:0},children:JSON.stringify(_.details,null,2)})})]}),e.jsxs("div",{style:{marginTop:16},children:[e.jsx("h4",{children:"浏览器信息"}),e.jsx("div",{style:{fontSize:"12px",wordBreak:"break-all",backgroundColor:"#f5f5f5",padding:8,borderRadius:4},children:_.userAgent})]})]})})]})};export{L as default};
