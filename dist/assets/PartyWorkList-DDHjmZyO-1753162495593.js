import{j as e,u as s,r as t}from"./index-BbdVriMF-1753162495593.js";import{M as a,ai as l,Z as r,Q as i,am as n,P as d,A as c,ap as o,q as x,a as h,ay as m,j,g as u,aq as p,I as y,G as v,H as b,as as g,aH as f,aM as I,ax as D,aN as w,s as S,av as N,r as C,ae as M,X as k,y as F,ao as q,F as _,J as V,i as Y,ad as T,o as z,aL as A,ag as L,T as $,af as R,aw as B,n as O,aB as P,W,ac as K}from"./antd-w1cSjLgF-1753162495593.js";import{h as H}from"./studentMatcher-BV_8mGlB-1753162495593.js";import{u as J}from"./useMessage-Dr3f4067-1753162495593.js";import"./vendor-D2RBMdQ0-1753162495593.js";const{Option:Q}=g,{TextArea:U}=y,{Step:E}=N,G=({detailModalVisible:s,setDetailModalVisible:t,selectedMember:N,activeTab:C,setActiveTab:M,activityModalVisible:k,setActivityModalVisible:F,activityForm:q,onActivitySubmit:_,documentModalVisible:V,setDocumentModalVisible:Y,documentForm:T,onDocumentSubmit:z,fileList:A,onFileChange:L,feeModalVisible:$,setFeeModalVisible:R,feeForm:B,onFeeSubmit:O})=>{var P;const W={name:"file",multiple:!0,fileList:A,onChange:L,beforeUpload:e=>{if(!("application/pdf"===e.type||e.type.startsWith("image/")||"application/msword"===e.type||"application/vnd.openxmlformats-officedocument.wordprocessingml.document"===e.type))return S.error("只能上传 PDF、图片、Word 文档！"),!1;return e.size/1024/1024<10||S.error("文件大小不能超过 10MB！"),!1}};return e.jsxs(e.Fragment,{children:[e.jsx(a,{title:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(u,{}),e.jsx("span",{children:"党员详细信息"})]}),open:s,onCancel:()=>t(!1),footer:[e.jsx(h,{onClick:()=>t(!1),children:"关闭"},"close")],width:1e3,children:N&&e.jsx(l,{activeKey:C,onChange:M,items:[{key:"basic",label:"基本信息",children:e.jsxs(r,{bordered:!0,column:2,children:[e.jsx(r.Item,{label:"学生姓名",children:N.studentName}),e.jsx(r.Item,{label:"学号",children:N.studentId}),e.jsx(r.Item,{label:"班级",children:N.class}),e.jsx(r.Item,{label:"专业",children:N.major}),e.jsx(r.Item,{label:"学院",children:N.college}),e.jsx(r.Item,{label:"联系电话",children:N.phone||"-"}),e.jsx(r.Item,{label:"党员状态",children:e.jsx(i,{color:"formal"===N.partyStatus?"red":"probationary"===N.partyStatus?"orange":"activist"===N.partyStatus?"blue":"default",children:"formal"===N.partyStatus?"正式党员":"probationary"===N.partyStatus?"预备党员":"activist"===N.partyStatus?"入党积极分子":"群众"})}),e.jsx(r.Item,{label:"党支部",children:N.branch}),e.jsx(r.Item,{label:"党内职务",children:N.position||"-"}),e.jsx(r.Item,{label:"指导老师",children:N.mentor}),e.jsx(r.Item,{label:"入党申请日期",children:N.applicationDate||"-"}),e.jsx(r.Item,{label:"积极分子确定日期",children:N.activistDate||"-"}),e.jsx(r.Item,{label:"预备党员发展日期",children:N.probationaryDate||"-"}),e.jsx(r.Item,{label:"正式党员转正日期",children:N.formalDate||"-"})]})},{key:"development",label:"发展历程",children:e.jsx(n,{children:null==(P=N.developmentProcess)?void 0:P.map(s=>e.jsxs(n.Item,{children:[e.jsx("div",{className:"font-medium",children:s.description}),e.jsxs("div",{className:"text-gray-500 text-sm",children:[s.date," · 批准人：",s.approvedBy]}),s.voteResult&&e.jsxs("div",{className:"text-sm mt-2",children:["投票结果：同意 ",s.voteResult.agree,"票，反对 ",s.voteResult.disagree,"票，弃权 ",s.voteResult.abstain,"票"]})]},s.id))})},{key:"activities",label:"党建活动",children:e.jsx(d,{dataSource:N.activities||[],renderItem:s=>e.jsxs(d.Item,{children:[e.jsx(d.Item.Meta,{avatar:e.jsx(c,{icon:e.jsx(o,{})}),title:s.title,description:e.jsxs("div",{children:[e.jsx("div",{children:s.description}),e.jsxs("div",{className:"text-gray-500 text-sm mt-1",children:[s.date," · ",s.duration,"小时 · ",s.location]})]})}),e.jsx(i,{color:"completed"===s.status?"green":"ongoing"===s.status?"blue":"orange",children:"completed"===s.status?"已完成":"ongoing"===s.status?"进行中":"计划中"})]})})},{key:"documents",label:"党员材料",children:e.jsx(d,{dataSource:N.documents||[],renderItem:s=>e.jsx(d.Item,{actions:[e.jsx(h,{type:"link",size:"small",children:"下载"}),e.jsx(i,{color:"approved"===s.status?"green":"rejected"===s.status?"red":"orange",children:"approved"===s.status?"已审核":"rejected"===s.status?"已拒绝":"待审核"})],children:e.jsx(d.Item.Meta,{avatar:e.jsx(c,{icon:e.jsx(x,{})}),title:s.title,description:e.jsxs("div",{children:[e.jsx("div",{children:s.description}),e.jsxs("div",{className:"text-gray-500 text-sm",children:["上传时间：",s.uploadDate," · 上传人：",s.uploadedBy]})]})})})})},{key:"fees",label:"党费缴纳",children:e.jsx(m,{dataSource:N.feeRecords||[],columns:[{title:"缴费时间",dataIndex:"paymentDate",key:"paymentDate"},{title:"年月",key:"period",render:(e,s)=>`${s.year}年${s.month}月`},{title:"金额",dataIndex:"amount",key:"amount",render:e=>`¥${e}`},{title:"缴费方式",dataIndex:"paymentMethod",key:"paymentMethod",render:e=>({cash:"现金",transfer:"转账",deduction:"代扣"}[e]||e)},{title:"状态",dataIndex:"status",key:"status",render:s=>e.jsx(i,{color:"paid"===s?"green":"red",children:"paid"===s?"已缴费":"未缴费"})}],pagination:!1,size:"small"})},{key:"organizational",label:"组织生活",children:e.jsx(d,{dataSource:N.organizationalLife||[],renderItem:s=>e.jsx(d.Item,{children:e.jsx(d.Item.Meta,{avatar:e.jsx(c,{icon:e.jsx(j,{})}),title:s.title,description:e.jsxs("div",{children:[e.jsx("div",{children:s.summary}),e.jsxs("div",{className:"text-gray-500 text-sm mt-1",children:[s.date," · ",s.location," · 主持人：",s.host]}),e.jsx(i,{color:"present"===s.attendance?"green":"red",children:"present"===s.attendance?"出席":"缺席"})]})})})})}]})}),e.jsx(a,{title:"添加党建活动",open:k,onCancel:()=>F(!1),footer:null,width:600,children:e.jsxs(p,{form:q,layout:"vertical",onFinish:_,children:[e.jsx(p.Item,{label:"活动标题",name:"title",rules:[{required:!0,message:"请输入活动标题"}],children:e.jsx(y,{placeholder:"请输入活动标题"})}),e.jsxs(v,{gutter:16,children:[e.jsx(b,{span:12,children:e.jsx(p.Item,{label:"活动类型",name:"type",rules:[{required:!0,message:"请选择活动类型"}],children:e.jsxs(g,{placeholder:"选择类型",children:[e.jsx(Q,{value:"study",children:"理论学习"}),e.jsx(Q,{value:"volunteer",children:"志愿服务"}),e.jsx(Q,{value:"meeting",children:"组织会议"}),e.jsx(Q,{value:"training",children:"培训教育"}),e.jsx(Q,{value:"social",children:"社会实践"}),e.jsx(Q,{value:"practice",children:"实践活动"})]})})}),e.jsx(b,{span:12,children:e.jsx(p.Item,{label:"活动状态",name:"status",rules:[{required:!0,message:"请选择活动状态"}],children:e.jsxs(g,{placeholder:"选择状态",children:[e.jsx(Q,{value:"planned",children:"计划中"}),e.jsx(Q,{value:"ongoing",children:"进行中"}),e.jsx(Q,{value:"completed",children:"已完成"}),e.jsx(Q,{value:"cancelled",children:"已取消"})]})})})]}),e.jsxs(v,{gutter:16,children:[e.jsx(b,{span:12,children:e.jsx(p.Item,{label:"活动日期",name:"date",rules:[{required:!0,message:"请选择活动日期"}],children:e.jsx(f,{style:{width:"100%"}})})}),e.jsx(b,{span:12,children:e.jsx(p.Item,{label:"活动时长",name:"duration",rules:[{required:!0,message:"请输入活动时长"}],children:e.jsx(I,{min:.5,max:24,step:.5,placeholder:"小时",style:{width:"100%"}})})})]}),e.jsxs(v,{gutter:16,children:[e.jsx(b,{span:12,children:e.jsx(p.Item,{label:"活动地点",name:"location",rules:[{required:!0,message:"请输入活动地点"}],children:e.jsx(y,{placeholder:"请输入活动地点"})})}),e.jsx(b,{span:12,children:e.jsx(p.Item,{label:"组织者",name:"organizer",rules:[{required:!0,message:"请输入组织者"}],children:e.jsx(y,{placeholder:"请输入组织者"})})})]}),e.jsx(p.Item,{label:"活动描述",name:"description",rules:[{required:!0,message:"请输入活动描述"}],children:e.jsx(U,{rows:4,placeholder:"请详细描述活动内容和目的"})}),e.jsx(p.Item,{label:"参与人员",name:"participants",children:e.jsxs(g,{mode:"tags",placeholder:"输入参与人员姓名",children:[e.jsx(Q,{value:"张三",children:"张三"}),e.jsx(Q,{value:"李四",children:"李四"}),e.jsx(Q,{value:"王五",children:"王五"})]})}),e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(h,{onClick:()=>F(!1),children:"取消"}),e.jsx(h,{type:"primary",htmlType:"submit",children:"添加活动"})]})]})}),e.jsx(a,{title:"上传党员材料",open:V,onCancel:()=>Y(!1),footer:null,width:600,children:e.jsxs(p,{form:T,layout:"vertical",onFinish:z,children:[e.jsx(p.Item,{label:"材料标题",name:"title",rules:[{required:!0,message:"请输入材料标题"}],children:e.jsx(y,{placeholder:"请输入材料标题"})}),e.jsx(p.Item,{label:"材料类型",name:"type",rules:[{required:!0,message:"请选择材料类型"}],children:e.jsxs(g,{placeholder:"选择类型",children:[e.jsx(Q,{value:"application",children:"入党申请书"}),e.jsx(Q,{value:"autobiography",children:"个人自传"}),e.jsx(Q,{value:"recommendation",children:"推荐信"}),e.jsx(Q,{value:"assessment",children:"考察材料"}),e.jsx(Q,{value:"certificate",children:"证书证明"}),e.jsx(Q,{value:"other",children:"其他材料"})]})}),e.jsx(p.Item,{label:"材料描述",name:"description",rules:[{required:!0,message:"请输入材料描述"}],children:e.jsx(U,{rows:3,placeholder:"请详细描述材料内容"})}),e.jsx(p.Item,{label:"上传文件",extra:"支持上传PDF、图片、Word文档，单个文件不超过10MB",children:e.jsxs(D.Dragger,{...W,children:[e.jsx("p",{className:"ant-upload-drag-icon",children:e.jsx(w,{})}),e.jsx("p",{className:"ant-upload-text",children:"点击或拖拽文件到此区域上传"}),e.jsx("p",{className:"ant-upload-hint",children:"支持单个文件上传"})]})}),e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(h,{onClick:()=>Y(!1),children:"取消"}),e.jsx(h,{type:"primary",htmlType:"submit",children:"上传材料"})]})]})}),e.jsx(a,{title:"添加党费缴纳记录",open:$,onCancel:()=>R(!1),footer:null,width:500,children:e.jsxs(p,{form:B,layout:"vertical",onFinish:O,children:[e.jsxs(v,{gutter:16,children:[e.jsx(b,{span:12,children:e.jsx(p.Item,{label:"缴费年份",name:"year",rules:[{required:!0,message:"请选择缴费年份"}],children:e.jsx(g,{placeholder:"选择年份",children:Array.from({length:5},(s,t)=>{const a=(new Date).getFullYear()-t;return e.jsxs(Q,{value:a,children:[a,"年"]},a)})})})}),e.jsx(b,{span:12,children:e.jsx(p.Item,{label:"缴费月份",name:"month",rules:[{required:!0,message:"请选择缴费月份"}],children:e.jsx(g,{placeholder:"选择月份",children:Array.from({length:12},(s,t)=>e.jsxs(Q,{value:t+1,children:[t+1,"月"]},t+1))})})})]}),e.jsxs(v,{gutter:16,children:[e.jsx(b,{span:12,children:e.jsx(p.Item,{label:"缴费金额",name:"amount",rules:[{required:!0,message:"请输入缴费金额"}],children:e.jsx(I,{min:1,max:1e3,placeholder:"金额",style:{width:"100%"},addonBefore:"¥"})})}),e.jsx(b,{span:12,children:e.jsx(p.Item,{label:"缴费日期",name:"paymentDate",rules:[{required:!0,message:"请选择缴费日期"}],children:e.jsx(f,{style:{width:"100%"}})})})]}),e.jsxs(v,{gutter:16,children:[e.jsx(b,{span:12,children:e.jsx(p.Item,{label:"缴费方式",name:"paymentMethod",rules:[{required:!0,message:"请选择缴费方式"}],children:e.jsxs(g,{placeholder:"选择方式",children:[e.jsx(Q,{value:"cash",children:"现金"}),e.jsx(Q,{value:"transfer",children:"转账"}),e.jsx(Q,{value:"deduction",children:"代扣"})]})})}),e.jsx(b,{span:12,children:e.jsx(p.Item,{label:"收费人",name:"collector",rules:[{required:!0,message:"请输入收费人"}],children:e.jsx(y,{placeholder:"收费人姓名"})})})]}),e.jsx(p.Item,{label:"备注",name:"notes",children:e.jsx(U,{rows:2,placeholder:"备注信息（可选）"})}),e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(h,{onClick:()=>R(!1),children:"取消"}),e.jsx(h,{type:"primary",htmlType:"submit",children:"添加记录"})]})]})})]})},{Option:X}=g,{TextArea:Z}=y,{Step:ee}=N,se=()=>{const l=s(),r=J(),[n,d]=a.useModal(),[c,x]=C.useState([]),[j,I]=C.useState(!1),[D,w]=C.useState(""),[S,N]=C.useState(""),[Q,U]=C.useState(""),[E,Z]=C.useState(!1),[ee,se]=C.useState(!1),[te,ae]=C.useState(!1),[le,re]=C.useState(!1),[ie,ne]=C.useState(!1),[de,ce]=C.useState(null),[oe,xe]=C.useState(null),[he,me]=C.useState([]),[je,ue]=C.useState("basic"),[pe]=p.useForm(),[ye]=p.useForm(),[ve]=p.useForm(),[be]=p.useForm(),ge=async()=>{I(!0);try{const e=await t.get("/party/members");if(e.success){const s=e.data.map(e=>({id:e.id,studentId:e.student_number||e.student_id,studentName:e.student_name,class:e.class||"",major:e.major||"",college:e.college||"",phone:e.phone||"",email:e.email||"",partyStatus:e.party_status||"none",applicationDate:e.application_date,activistDate:e.activist_date,probationaryDate:e.probationary_date,formalDate:e.formal_date,mentor:e.mentor||"",branch:e.branch||"",position:e.position||"",activities:[],assessments:[],documents:[],feeRecords:[],developmentProcess:[],organizationalLife:[],createdAt:e.created_at?new Date(e.created_at).toISOString().split("T")[0]:"",updatedAt:e.updated_at?new Date(e.updated_at).toISOString().split("T")[0]:""}));x(s)}else r.error("获取党员列表失败")}catch(e){console.error("获取党员列表错误:",e),r.error("获取党员列表失败")}finally{I(!1)}};C.useEffect(()=>{ge()},[]);const fe=c.filter(e=>{const s=!D||e.studentName.toLowerCase().includes(D.toLowerCase())||e.studentId.toLowerCase().includes(D.toLowerCase())||e.class.toLowerCase().includes(D.toLowerCase()),t=!S||e.partyStatus===S,a=!Q||e.branch===Q;return s&&t&&a}),Ie=[{title:"学生信息",key:"student",render:(s,t)=>e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:e.jsx(h,{type:"link",onClick:()=>l(`/students/${t.studentId}`),className:"p-0 h-auto",children:t.studentName})}),e.jsxs("div",{className:"text-gray-500 text-xs",children:[t.studentId," · ",t.class]}),e.jsxs("div",{className:"text-gray-400 text-xs",children:[t.major," · ",t.college]})]})},{title:"党员状态",dataIndex:"partyStatus",key:"partyStatus",render:s=>{const t={none:{color:"default",text:"群众"},activist:{color:"blue",text:"入党积极分子"},probationary:{color:"orange",text:"预备党员"},formal:{color:"red",text:"正式党员"}}[s];return e.jsx(i,{color:t.color,children:t.text})}},{title:"发展历程",key:"progress",render:(s,t)=>{const a=[];return t.applicationDate&&a.push("申请"),t.activistDate&&a.push("积极分子"),t.probationaryDate&&a.push("预备党员"),t.formalDate&&a.push("正式党员"),e.jsxs("div",{className:"text-sm",children:[e.jsx(L,{percent:25*a.length,size:"small",status:4===a.length?"success":"active"}),e.jsx("div",{className:"text-xs text-gray-500 mt-1",children:a.join(" → ")})]})}},{title:"指导老师",dataIndex:"mentor",key:"mentor"},{title:"活动参与",key:"activities",render:(s,t)=>e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"font-semibold",children:t.activities.length}),e.jsx("div",{className:"text-xs text-gray-500",children:"次活动"})]})},{title:"最新评估",key:"assessment",render:(s,t)=>{const a=t.assessments[0];return a?e.jsxs("div",{className:"text-sm",children:[e.jsxs("div",{className:"font-semibold",children:["分数: ",a.score]}),e.jsx("div",{className:"text-xs text-gray-500",children:a.period})]}):e.jsx("span",{className:"text-gray-400",children:"暂无评估"})}},{title:"操作",key:"action",render:(s,a)=>e.jsxs(M,{children:[e.jsx($,{title:"查看详情",children:e.jsx(h,{type:"text",icon:e.jsx(R,{}),onClick:()=>(xe(a),ue("basic"),void se(!0))})}),e.jsx($,{title:"添加活动",children:e.jsx(h,{type:"text",icon:e.jsx(o,{}),onClick:()=>(xe(a),ye.resetFields(),void ae(!0))})}),e.jsx($,{title:"上传材料",children:e.jsx(h,{type:"text",icon:e.jsx(B,{}),onClick:()=>(xe(a),ve.resetFields(),me([]),void re(!0))})}),e.jsx($,{title:"党费记录",children:e.jsx(h,{type:"text",icon:e.jsx(O,{}),onClick:()=>(xe(a),be.resetFields(),void ne(!0))})}),e.jsx($,{title:"编辑",children:e.jsx(h,{type:"text",icon:e.jsx(P,{}),onClick:()=>(e=>{ce(e),pe.setFieldsValue({...e,applicationDate:e.applicationDate?K(e.applicationDate):null,activistDate:e.activistDate?K(e.activistDate):null,probationaryDate:e.probationaryDate?K(e.probationaryDate):null,formalDate:e.formalDate?K(e.formalDate):null}),Z(!0)})(a)})}),e.jsx($,{title:"删除",children:e.jsx(h,{type:"text",danger:!0,icon:e.jsx(W,{}),onClick:()=>(e=>{n.confirm({title:"确认删除",content:`确定要删除 ${e.studentName} 的党建记录吗？`,onOk:async()=>{try{(await t.delete(`/party/members/${e.id}`)).success?(r.success("删除成功"),await ge()):r.error("删除失败")}catch(s){console.error("删除党员记录错误:",s),r.error("删除失败")}}})})(a)})})]})}],De=c.length,we=c.filter(e=>"activist"===e.partyStatus).length,Se=c.filter(e=>"probationary"===e.partyStatus).length,Ne=c.filter(e=>"formal"===e.partyStatus).length;return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"党建工作管理"}),e.jsx("p",{className:"text-gray-600",children:"管理学生党员发展和党建活动"})]}),e.jsxs(M,{children:[e.jsx(h,{icon:e.jsx(k,{}),onClick:ge,size:"large",children:"刷新数据"}),e.jsx(h,{icon:e.jsx(F,{}),onClick:async()=>{I(!0);try{await ge(),r.success("学生信息同步完成")}catch(e){console.error("同步学生信息错误:",e),r.error("同步学生信息失败")}finally{I(!1)}},size:"large",loading:j,children:"同步学生信息"}),e.jsx(h,{type:"primary",icon:e.jsx(q,{}),onClick:()=>{ce(null),pe.resetFields(),me([]),Z(!0)},size:"large",children:"新增记录"})]})]}),e.jsxs(v,{gutter:16,children:[e.jsx(b,{span:6,children:e.jsx(_,{children:e.jsx(V,{title:"总人数",value:De,prefix:e.jsx(u,{})})})}),e.jsx(b,{span:6,children:e.jsx(_,{children:e.jsx(V,{title:"入党积极分子",value:we,valueStyle:{color:"#1890ff"},prefix:e.jsx(Y,{})})})}),e.jsx(b,{span:6,children:e.jsx(_,{children:e.jsx(V,{title:"预备党员",value:Se,valueStyle:{color:"#faad14"},prefix:e.jsx(T,{})})})}),e.jsx(b,{span:6,children:e.jsx(_,{children:e.jsx(V,{title:"正式党员",value:Ne,valueStyle:{color:"#f5222d"},prefix:e.jsx(z,{})})})})]}),e.jsxs(_,{children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-4",children:[e.jsx(y,{placeholder:"搜索学号、姓名或班级",prefix:e.jsx(A,{}),value:D,onChange:e=>w(e.target.value),style:{width:300}}),e.jsxs(g,{placeholder:"党员状态",value:S,onChange:N,allowClear:!0,style:{width:150},children:[e.jsx(X,{value:"none",children:"群众"}),e.jsx(X,{value:"activist",children:"入党积极分子"}),e.jsx(X,{value:"probationary",children:"预备党员"}),e.jsx(X,{value:"formal",children:"正式党员"})]}),e.jsxs(g,{placeholder:"党支部",value:Q,onChange:U,allowClear:!0,style:{width:200},children:[e.jsx(X,{value:"计算机学院学生第一党支部",children:"计算机学院学生第一党支部"}),e.jsx(X,{value:"计算机学院学生第二党支部",children:"计算机学院学生第二党支部"}),e.jsx(X,{value:"软件学院学生党支部",children:"软件学院学生党支部"}),e.jsx(X,{value:"网络工程学院学生党支部",children:"网络工程学院学生党支部"})]})]}),e.jsx(m,{columns:Ie,dataSource:fe,rowKey:"id",loading:j,pagination:{total:fe.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 条记录`},scroll:{x:1200}})]}),e.jsx(a,{title:de?"编辑党建记录":"新增党建记录",open:E,onCancel:()=>Z(!1),footer:null,width:600,children:e.jsxs(p,{form:pe,layout:"vertical",onFinish:async e=>{var s,a,l,i;try{if(e.studentId){const s=await t.get(`/students?student_id=${encodeURIComponent(e.studentId)}`);if(!s.success||0===s.data.length)return void r.error("未找到该学号对应的学生，请检查学号是否正确")}const n={student_id:e.studentId,party_status:e.partyStatus,application_date:null==(s=e.applicationDate)?void 0:s.format("YYYY-MM-DD"),activist_date:null==(a=e.activistDate)?void 0:a.format("YYYY-MM-DD"),probationary_date:null==(l=e.probationaryDate)?void 0:l.format("YYYY-MM-DD"),formal_date:null==(i=e.formalDate)?void 0:i.format("YYYY-MM-DD"),mentor:e.mentor,branch:e.branch,position:e.position};if(console.log("提交的表单数据:",n),de){(await t.put(`/party/members/${de.id}`,n)).success?(r.success("党员信息更新成功"),await ge()):r.error("更新党员信息失败")}else{(await t.post("/party/members",n)).success?(r.success("党员记录创建成功"),await ge()):r.error("创建党员记录失败")}Z(!1),me([]),pe.resetFields(),ce(null)}catch(n){console.error("保存党员记录错误:",n),r.error("保存失败")}},children:[e.jsxs(v,{gutter:16,children:[e.jsx(b,{span:12,children:e.jsx(p.Item,{label:"学号",name:"studentId",rules:[{required:!0,message:"请输入学号"}],children:e.jsx(y,{placeholder:"输入学号自动匹配学生信息",onChange:e=>(async e=>{await H(e,pe)})(e.target.value)})})}),e.jsx(b,{span:12,children:e.jsx(p.Item,{label:"学生姓名",name:"studentName",rules:[{required:!0,message:"请输入学生姓名"}],children:e.jsx(y,{placeholder:"自动匹配",disabled:!0})})})]}),e.jsxs(v,{gutter:16,children:[e.jsx(b,{span:8,children:e.jsx(p.Item,{label:"班级",name:"class",rules:[{required:!0,message:"请输入班级"}],children:e.jsx(y,{placeholder:"自动匹配",disabled:!0})})}),e.jsx(b,{span:8,children:e.jsx(p.Item,{label:"专业",name:"major",children:e.jsx(y,{placeholder:"自动匹配",disabled:!0})})}),e.jsx(b,{span:8,children:e.jsx(p.Item,{label:"学院",name:"college",children:e.jsx(y,{placeholder:"自动匹配",disabled:!0})})})]}),e.jsxs(v,{gutter:16,children:[e.jsx(b,{span:8,children:e.jsx(p.Item,{label:"联系电话",name:"phone",children:e.jsx(y,{placeholder:"自动匹配",disabled:!0})})}),e.jsx(b,{span:8,children:e.jsx(p.Item,{label:"邮箱",name:"email",children:e.jsx(y,{placeholder:"自动匹配",disabled:!0})})}),e.jsx(b,{span:8,children:e.jsx(p.Item,{label:"党员状态",name:"partyStatus",rules:[{required:!0,message:"请选择党员状态"}],children:e.jsxs(g,{placeholder:"选择状态",children:[e.jsx(X,{value:"none",children:"群众"}),e.jsx(X,{value:"activist",children:"入党积极分子"}),e.jsx(X,{value:"probationary",children:"预备党员"}),e.jsx(X,{value:"formal",children:"正式党员"})]})})})]}),e.jsxs(v,{gutter:16,children:[e.jsx(b,{span:8,children:e.jsx(p.Item,{label:"指导老师",name:"mentor",rules:[{required:!0,message:"请输入指导老师"}],children:e.jsx(y,{placeholder:"指导老师姓名"})})}),e.jsx(b,{span:8,children:e.jsx(p.Item,{label:"党支部",name:"branch",rules:[{required:!0,message:"请选择党支部"}],children:e.jsxs(g,{placeholder:"选择党支部",children:[e.jsx(X,{value:"计算机学院学生第一党支部",children:"计算机学院学生第一党支部"}),e.jsx(X,{value:"计算机学院学生第二党支部",children:"计算机学院学生第二党支部"}),e.jsx(X,{value:"软件学院学生党支部",children:"软件学院学生党支部"}),e.jsx(X,{value:"网络工程学院学生党支部",children:"网络工程学院学生党支部"})]})})}),e.jsx(b,{span:8,children:e.jsx(p.Item,{label:"党内职务",name:"position",children:e.jsxs(g,{placeholder:"选择职务（可选）",allowClear:!0,children:[e.jsx(X,{value:"书记",children:"书记"}),e.jsx(X,{value:"副书记",children:"副书记"}),e.jsx(X,{value:"组织委员",children:"组织委员"}),e.jsx(X,{value:"宣传委员",children:"宣传委员"}),e.jsx(X,{value:"纪检委员",children:"纪检委员"}),e.jsx(X,{value:"青年委员",children:"青年委员"})]})})})]}),e.jsxs(v,{gutter:16,children:[e.jsx(b,{span:12,children:e.jsx(p.Item,{label:"入党申请日期",name:"applicationDate",children:e.jsx(f,{style:{width:"100%"}})})}),e.jsx(b,{span:12,children:e.jsx(p.Item,{label:"积极分子确定日期",name:"activistDate",children:e.jsx(f,{style:{width:"100%"}})})})]}),e.jsxs(v,{gutter:16,children:[e.jsx(b,{span:12,children:e.jsx(p.Item,{label:"预备党员发展日期",name:"probationaryDate",children:e.jsx(f,{style:{width:"100%"}})})}),e.jsx(b,{span:12,children:e.jsx(p.Item,{label:"正式党员转正日期",name:"formalDate",children:e.jsx(f,{style:{width:"100%"}})})})]}),e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(h,{onClick:()=>Z(!1),children:"取消"}),e.jsx(h,{type:"primary",htmlType:"submit",children:de?"更新":"创建"})]})]})}),e.jsx(G,{detailModalVisible:ee,setDetailModalVisible:se,selectedMember:oe,activeTab:je,setActiveTab:ue,activityModalVisible:te,setActivityModalVisible:ae,activityForm:ye,onActivitySubmit:async e=>{if(oe)try{r.success("党建活动添加成功"),ae(!1),ye.resetFields(),await ge()}catch(s){console.error("添加党建活动错误:",s),r.error("添加党建活动失败")}},documentModalVisible:le,setDocumentModalVisible:re,documentForm:ve,onDocumentSubmit:async e=>{if(oe)try{r.success("党员材料上传成功"),re(!1),me([]),ve.resetFields(),await ge()}catch(s){console.error("上传党员材料错误:",s),r.error("上传党员材料失败")}},fileList:he,onFileChange:e=>{let s=[...e.fileList];s=s.slice(-5),s=s.map(e=>(e.response&&(e.url=e.response.url),e)),me(s)},feeModalVisible:ie,setFeeModalVisible:ne,feeForm:be,onFeeSubmit:async e=>{if(oe)try{r.success("党费缴纳记录添加成功"),ne(!1),be.resetFields(),await ge()}catch(s){console.error("添加党费缴纳记录错误:",s),r.error("添加党费缴纳记录失败")}}}),d]})};export{se as default};
