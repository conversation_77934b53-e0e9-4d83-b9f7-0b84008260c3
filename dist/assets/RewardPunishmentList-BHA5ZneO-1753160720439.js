import{j as e,u as s,r as l}from"./index-DXaqwR6F-1753160720439.js";import{a0 as t,r as a,M as r,aq as n,G as i,H as d,I as c,as as o,aH as x,aM as h,ax as u,aN as m,a as j,s as p,E as g,Z as v,Q as y,P as b,ae as w,A as f,au as C,aO as I,aP as S,aQ as N,q as k,X as q,y as _,aw as O,ao as F,F as M,J as D,m as B,aa as L,aL as z,ay as A,T,af as $,aB as Y,W as P,ac as J}from"./antd-lXsGnH6e-1753160720439.js";import{h as V}from"./studentMatcher-D9nJCSdu-1753160720439.js";import{u as W}from"./useMessage-CR2RbGdP-1753160720439.js";import"./vendor-D2RBMdQ0-1753160720439.js";const{Option:H}=o,{TextArea:K}=c,{Dragger:Q}=u,R=({visible:s,onCancel:l,onSubmit:u,form:g,fileList:v,onFileChange:y,rewardCategories:b})=>{var w;const{message:f}=t.useApp(),[C,I]=a.useState(""),S={name:"file",multiple:!0,fileList:v,onChange:y,beforeUpload:e=>{if(!("application/pdf"===e.type||e.type.startsWith("image/")||"application/msword"===e.type||"application/vnd.openxmlformats-officedocument.wordprocessingml.document"===e.type))return p.error("只能上传 PDF、图片、Word 文档！"),!1;return e.size/1024/1024<10||p.error("文件大小不能超过 10MB！"),!1},onRemove:e=>{const s=v.filter(s=>s.uid!==e.uid);y({fileList:s})}};return e.jsx(r,{title:"提交获奖资料",open:s,onCancel:l,footer:null,width:800,destroyOnHidden:!0,children:e.jsxs(n,{form:g,layout:"vertical",onFinish:u,children:[e.jsxs(i,{gutter:16,children:[e.jsx(d,{span:12,children:e.jsx(n.Item,{label:"学号",name:"studentId",rules:[{required:!0,message:"请输入学号"}],children:e.jsx(c,{placeholder:"输入学号自动匹配学生信息",onChange:e=>(async e=>{await V(e,g,void 0,f)})(e.target.value)})})}),e.jsx(d,{span:12,children:e.jsx(n.Item,{label:"学生姓名",name:"studentName",rules:[{required:!0,message:"请输入学生姓名"}],children:e.jsx(c,{placeholder:"自动匹配",disabled:!0})})})]}),e.jsxs(i,{gutter:16,children:[e.jsx(d,{span:8,children:e.jsx(n.Item,{label:"班级",name:"class",rules:[{required:!0,message:"请输入班级"}],children:e.jsx(c,{placeholder:"自动匹配",disabled:!0})})}),e.jsx(d,{span:8,children:e.jsx(n.Item,{label:"专业",name:"major",rules:[{required:!0,message:"请输入专业"}],children:e.jsx(c,{placeholder:"自动匹配",disabled:!0})})}),e.jsx(d,{span:8,children:e.jsx(n.Item,{label:"学院",name:"college",rules:[{required:!0,message:"请输入学院"}],children:e.jsx(c,{placeholder:"自动匹配",disabled:!0})})})]}),e.jsxs(i,{gutter:16,children:[e.jsx(d,{span:8,children:e.jsx(n.Item,{label:"奖励类型",name:"type",initialValue:"reward",children:e.jsx(o,{disabled:!0,children:e.jsx(H,{value:"reward",children:"奖励"})})})}),e.jsx(d,{span:8,children:e.jsx(n.Item,{label:"奖励分类",name:"category",rules:[{required:!0,message:"请选择奖励分类"}],children:e.jsx(o,{placeholder:"选择分类",onChange:I,children:Object.entries(b).map(([s,l])=>e.jsx(H,{value:s,children:l.name},s))})})}),e.jsx(d,{span:8,children:e.jsx(n.Item,{label:"具体类别",name:"subCategory",rules:[{required:!0,message:"请选择具体类别"}],children:e.jsx(o,{placeholder:"选择具体类别",children:C&&(null==(w=b[C])?void 0:w.subCategories.map(s=>e.jsx(H,{value:s,children:s},s)))})})})]}),e.jsxs(i,{gutter:16,children:[e.jsx(d,{span:8,children:e.jsx(n.Item,{label:"奖励级别",name:"level",rules:[{required:!0,message:"请选择奖励级别"}],children:e.jsxs(o,{placeholder:"选择级别",children:[e.jsx(H,{value:"national",children:"国家级"}),e.jsx(H,{value:"provincial",children:"省级"}),e.jsx(H,{value:"municipal",children:"市级"}),e.jsx(H,{value:"school",children:"校级"}),e.jsx(H,{value:"college",children:"院级"}),e.jsx(H,{value:"department",children:"系级"}),e.jsx(H,{value:"class",children:"班级"})]})})}),e.jsx(d,{span:8,children:e.jsx(n.Item,{label:"获奖日期",name:"date",rules:[{required:!0,message:"请选择获奖日期"}],children:e.jsx(x,{style:{width:"100%"}})})}),e.jsx(d,{span:8,children:e.jsx(n.Item,{label:"预期积分",name:"points",rules:[{required:!0,message:"请输入预期积分"}],children:e.jsx(h,{min:1,max:50,placeholder:"积分",style:{width:"100%"}})})})]}),e.jsx(n.Item,{label:"奖项标题",name:"title",rules:[{required:!0,message:"请输入奖项标题"}],children:e.jsx(c,{placeholder:"请输入完整的奖项名称"})}),e.jsx(n.Item,{label:"详细描述",name:"description",rules:[{required:!0,message:"请输入详细描述"}],children:e.jsx(K,{rows:4,placeholder:"请详细描述获奖情况、参赛过程、获奖意义等"})}),e.jsx(n.Item,{label:"审批部门",name:"approver",rules:[{required:!0,message:"请输入审批部门"}],children:e.jsx(c,{placeholder:"如：教务处、学生处、团委等"})}),e.jsx(n.Item,{label:"上传获奖资料",extra:"支持上传获奖证书、奖状、参赛作品等相关材料，支持PDF、图片、Word文档，单个文件不超过10MB",children:e.jsxs(Q,{...S,children:[e.jsx("p",{className:"ant-upload-drag-icon",children:e.jsx(m,{})}),e.jsx("p",{className:"ant-upload-text",children:"点击或拖拽文件到此区域上传"}),e.jsx("p",{className:"ant-upload-hint",children:"支持单个或批量上传，最多上传5个文件"})]})}),e.jsxs("div",{className:"flex justify-end space-x-2 mt-6",children:[e.jsx(j,{onClick:l,children:"取消"}),e.jsx(j,{type:"primary",htmlType:"submit",children:"提交申请"})]})]})})},{Title:E,Paragraph:G}=g,U=({visible:s,onCancel:l,record:t,rewardCategories:a,punishmentCategories:n})=>{var i,d,c,o;if(!t)return null;const x=e=>{if(0===e)return"0 Bytes";const s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["Bytes","KB","MB","GB"][s]},h=("reward"===t.type?a:n)[t.category],u={national:{color:"red",text:"国家级"},provincial:{color:"volcano",text:"省级"},municipal:{color:"orange",text:"市级"},school:{color:"gold",text:"校级"},college:{color:"blue",text:"院级"},department:{color:"cyan",text:"系级"},class:{color:"green",text:"班级"}},m={pending:{color:"orange",text:"待审批"},approved:{color:"green",text:"已通过"},rejected:{color:"red",text:"已拒绝"}};return e.jsx(r,{title:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(y,{color:"reward"===t.type?"green":"red",children:"reward"===t.type?"奖励":"处分"}),e.jsx("span",{children:t.title})]}),open:s,onCancel:l,footer:[e.jsx(j,{onClick:l,children:"关闭"},"close")],width:800,children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx(E,{level:5,children:"基本信息"}),e.jsxs(v,{bordered:!0,column:3,size:"small",children:[e.jsx(v.Item,{label:"学生姓名",children:t.studentName}),e.jsx(v.Item,{label:"学号",children:t.studentId}),e.jsx(v.Item,{label:"班级",children:t.class}),e.jsx(v.Item,{label:"专业",children:t.major}),e.jsx(v.Item,{label:"学院",children:t.college}),e.jsx(v.Item,{label:"类型",children:e.jsx(y,{color:"reward"===t.type?"green":"red",children:"reward"===t.type?"奖励":"处分"})}),e.jsx(v.Item,{label:"分类",children:(null==h?void 0:h.name)||t.category}),e.jsx(v.Item,{label:"具体类别",children:t.subCategory||"-"}),e.jsx(v.Item,{label:"级别",children:e.jsx(y,{color:null==(i=u[t.level])?void 0:i.color,children:(null==(d=u[t.level])?void 0:d.text)||t.level})}),e.jsx(v.Item,{label:"积分",children:e.jsxs("span",{className:t.points>0?"text-green-600":"text-red-600",children:[t.points>0?"+":"",t.points]})}),e.jsx(v.Item,{label:"日期",children:t.date}),e.jsx(v.Item,{label:"状态",children:e.jsx(y,{color:null==(c=m[t.status])?void 0:c.color,children:null==(o=m[t.status])?void 0:o.text})})]})]}),e.jsxs("div",{children:[e.jsx(E,{level:5,children:"详细描述"}),e.jsx(G,{children:t.description})]}),e.jsxs("div",{children:[e.jsx(E,{level:5,children:"审批信息"}),e.jsxs(v,{bordered:!0,column:2,size:"small",children:[e.jsx(v.Item,{label:"审批部门",children:t.approver}),e.jsx(v.Item,{label:"提交人",children:t.submittedBy}),e.jsx(v.Item,{label:"提交时间",children:t.submittedAt}),e.jsx(v.Item,{label:"审批人",children:t.reviewedBy||"-"}),e.jsx(v.Item,{label:"审批时间",children:t.reviewedAt||"-"})]}),e.jsx(v,{bordered:!0,column:1,size:"small",style:{marginTop:8},children:e.jsx(v.Item,{label:"审批意见",children:t.reviewComments||"-"})})]}),t.attachments&&t.attachments.length>0&&e.jsxs("div",{children:[e.jsx(E,{level:5,children:"相关附件"}),e.jsx(b,{dataSource:t.attachments,renderItem:s=>{return e.jsx(b.Item,{actions:[e.jsx(j,{type:"link",icon:e.jsx(C,{}),onClick:()=>{p.info(`下载文件: ${s.name}`)},children:"下载"},"download")],children:e.jsx(b.Item.Meta,{avatar:e.jsx(f,{icon:(l=s.type,l.includes("pdf")?e.jsx(I,{style:{color:"#ff4d4f"}}):l.includes("image")?e.jsx(S,{style:{color:"#52c41a"}}):l.includes("word")||l.includes("document")?e.jsx(N,{style:{color:"#1890ff"}}):e.jsx(k,{}))}),title:s.name,description:e.jsxs(w,{children:[e.jsx("span",{children:x(s.size)}),e.jsxs("span",{children:["上传时间: ",s.uploadedAt]})]})})});var l}})]})]})})},{Option:X}=o,{TextArea:Z}=c,ee={academic:{name:"学术竞赛",subCategories:["数学建模竞赛","程序设计竞赛","创新创业大赛","学科竞赛","科研项目","学术论文","专利发明","其他学术奖项"]},cultural:{name:"文体活动",subCategories:["文艺比赛","体育竞赛","演讲比赛","辩论赛","摄影比赛","书法绘画","音乐舞蹈","其他文体活动"]},social:{name:"社会实践",subCategories:["志愿服务","社会调研","实习实践","公益活动","社团活动","社会工作","其他社会实践"]},leadership:{name:"组织管理",subCategories:["学生干部","优秀团员","优秀党员","班级建设","组织活动","其他组织管理"]},scholarship:{name:"奖学金",subCategories:["国家奖学金","国家励志奖学金","校级奖学金","企业奖学金","专项奖学金","其他奖学金"]},honor:{name:"荣誉称号",subCategories:["三好学生","优秀学生干部","优秀毕业生","先进个人","道德模范","其他荣誉称号"]}},se={discipline:{name:"违纪行为",subCategories:["考试违纪","学术不端","违反校规","宿舍违纪","网络违规","其他违纪行为"]},academic:{name:"学业问题",subCategories:["旷课缺勤","学业警告","补考重修","延期毕业","其他学业问题"]},safety:{name:"安全问题",subCategories:["消防安全","交通安全","人身安全","财产安全","其他安全问题"]}},le=()=>{var t,h;const u=s(),m=W(),[p,g]=r.useModal(),[v,b]=a.useState([]),[f,C]=a.useState(!1),[I,S]=a.useState(""),[N,H]=a.useState(""),[K,Q]=a.useState(""),[E,G]=a.useState(""),[le,te]=a.useState(!1),[ae,re]=a.useState(!1),[ne,ie]=a.useState(!1),[de,ce]=a.useState(null),[oe,xe]=a.useState(null),[he,ue]=a.useState([]),[me]=n.useForm(),[je]=n.useForm(),pe=async()=>{C(!0);try{const e=await l.get("/rewards");if(e.success){const s=e.data.map(e=>({id:e.id,studentId:e.student_number||e.student_id,studentName:e.student_name,class:e.class||"",major:e.major||"",college:e.college||"",type:e.type,category:e.category,subCategory:e.sub_category,title:e.title,description:e.description,level:e.level,date:e.date,approver:e.approver||"",status:e.status,points:e.points||0,attachments:"string"==typeof e.attachments?JSON.parse(e.attachments||"[]"):e.attachments||[],submittedBy:e.submitted_by||"",submittedAt:e.created_at?new Date(e.created_at).toISOString().split("T")[0]:"",reviewedBy:e.reviewed_by||"",reviewedAt:e.reviewed_at?new Date(e.reviewed_at).toISOString().split("T")[0]:"",reviewComments:e.review_comments||"",createdAt:e.created_at?new Date(e.created_at).toISOString().split("T")[0]:""}));b(s)}else m.error("获取奖惩记录失败")}catch(e){console.error("获取奖惩记录错误:",e),m.error("获取奖惩记录失败")}finally{C(!1)}};a.useEffect(()=>{pe()},[]);const ge=async(e,s)=>{try{(await l.patch(`/rewards/${e.id}/approve`,{status:s,review_comments:"rejected"===s?"审批拒绝":"审批通过"})).success?(m.success("approved"===s?"审批通过":"审批拒绝"),await pe()):m.error("审批失败")}catch(t){console.error("审批奖惩记录错误:",t),m.error("审批失败")}},ve=v.filter(e=>{const s=!I||e.studentName.toLowerCase().includes(I.toLowerCase())||e.studentId.toLowerCase().includes(I.toLowerCase())||e.title.toLowerCase().includes(I.toLowerCase())||e.class.toLowerCase().includes(I.toLowerCase()),l=!N||e.type===N,t=!K||e.status===K,a=!E||e.category===E;return s&&l&&t&&a}),ye=[{title:"学生信息",key:"student",render:(s,l)=>e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:e.jsx(j,{type:"link",onClick:()=>u(`/students/${l.studentId}`),className:"p-0 h-auto",children:l.studentName})}),e.jsxs("div",{className:"text-gray-500 text-xs",children:[l.studentId," · ",l.class]}),e.jsxs("div",{className:"text-gray-400 text-xs",children:[l.major," · ",l.college]})]})},{title:"类型",dataIndex:"type",key:"type",render:s=>e.jsx(y,{color:"reward"===s?"green":"red",children:"reward"===s?"奖励":"处分"})},{title:"类别",key:"category",render:(s,l)=>{const t=("reward"===l.type?ee:se)[l.category];return e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:(null==t?void 0:t.name)||l.category}),l.subCategory&&e.jsx("div",{className:"text-gray-500 text-xs",children:l.subCategory})]})}},{title:"标题",dataIndex:"title",key:"title",render:s=>e.jsx("div",{className:"max-w-xs truncate",title:s,children:s})},{title:"级别",dataIndex:"level",key:"level",render:s=>{const l={national:{color:"red",text:"国家级"},provincial:{color:"volcano",text:"省级"},municipal:{color:"orange",text:"市级"},school:{color:"gold",text:"校级"},college:{color:"blue",text:"院级"},department:{color:"cyan",text:"系级"},class:{color:"green",text:"班级"}}[s]||{color:"default",text:s};return e.jsx(y,{color:l.color,children:l.text})}},{title:"积分",dataIndex:"points",key:"points",render:s=>e.jsxs("span",{className:s>0?"text-green-600":"text-red-600",children:[s>0?"+":"",s]})},{title:"日期",dataIndex:"date",key:"date"},{title:"审批人",dataIndex:"approver",key:"approver"},{title:"状态",dataIndex:"status",key:"status",render:s=>{const l={pending:{color:"orange",text:"待审批"},approved:{color:"green",text:"已通过"},rejected:{color:"red",text:"已拒绝"}}[s];return e.jsx(y,{color:l.color,children:l.text})}},{title:"操作",key:"action",render:(s,t)=>e.jsxs(w,{children:[e.jsx(T,{title:"查看详情",children:e.jsx(j,{type:"text",icon:e.jsx($,{}),onClick:()=>(e=>{xe(e),re(!0)})(t)})}),"pending"===t.status&&e.jsxs(e.Fragment,{children:[e.jsx(T,{title:"审批通过",children:e.jsx(j,{type:"text",icon:e.jsx(B,{}),onClick:()=>ge(t,"approved"),style:{color:"#52c41a"}})}),e.jsx(T,{title:"审批拒绝",children:e.jsx(j,{type:"text",icon:e.jsx(L,{}),onClick:()=>ge(t,"rejected"),style:{color:"#ff4d4f"}})})]}),e.jsx(T,{title:"编辑",children:e.jsx(j,{type:"text",icon:e.jsx(Y,{}),onClick:()=>(e=>{ce(e),H(e.type),G(e.category),me.setFieldsValue({...e,date:J(e.date)}),te(!0)})(t)})}),e.jsx(T,{title:"删除",children:e.jsx(j,{type:"text",danger:!0,icon:e.jsx(P,{}),onClick:()=>(e=>{p.confirm({title:"确认删除",content:`确定要删除 ${e.studentName} 的 ${e.title} 记录吗？`,onOk:async()=>{try{(await l.delete(`/rewards/${e.id}`)).success?(m.success("删除成功"),await pe()):m.error("删除失败")}catch(s){console.error("删除奖惩记录错误:",s),m.error("删除失败")}}})})(t)})})]})}],be=v.length,we=v.filter(e=>"reward"===e.type).length,fe=v.filter(e=>"punishment"===e.type).length,Ce=v.filter(e=>"pending"===e.status).length;return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"奖惩管理"}),e.jsx("p",{className:"text-gray-600",children:"管理学生的奖励和处分记录"})]}),e.jsxs(w,{children:[e.jsx(j,{icon:e.jsx(q,{}),onClick:pe,size:"large",children:"刷新数据"}),e.jsx(j,{icon:e.jsx(_,{}),onClick:async()=>{C(!0);try{await pe(),m.success("学生信息同步完成")}catch(e){console.error("同步学生信息错误:",e),m.error("同步学生信息失败")}finally{C(!1)}},size:"large",loading:f,children:"同步学生信息"}),e.jsx(j,{type:"default",icon:e.jsx(O,{}),onClick:()=>{ce(null),je.resetFields(),ue([]),ie(!0)},size:"large",children:"提交获奖资料"}),e.jsx(j,{type:"primary",icon:e.jsx(F,{}),onClick:()=>{ce(null),me.resetFields(),H(""),G(""),te(!0)},size:"large",children:"新增记录"})]})]}),e.jsxs(i,{gutter:16,children:[e.jsx(d,{span:6,children:e.jsx(M,{children:e.jsx(D,{title:"总记录数",value:be,prefix:e.jsx(k,{})})})}),e.jsx(d,{span:6,children:e.jsx(M,{children:e.jsx(D,{title:"奖励记录",value:we,valueStyle:{color:"#3f8600"},prefix:e.jsx(B,{})})})}),e.jsx(d,{span:6,children:e.jsx(M,{children:e.jsx(D,{title:"处分记录",value:fe,valueStyle:{color:"#cf1322"},prefix:e.jsx(L,{})})})}),e.jsx(d,{span:6,children:e.jsx(M,{children:e.jsx(D,{title:"待审批",value:Ce,valueStyle:{color:"#faad14"}})})})]}),e.jsxs(M,{children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-4",children:[e.jsx(c,{placeholder:"搜索学号、姓名、班级或标题",prefix:e.jsx(z,{}),value:I,onChange:e=>S(e.target.value),style:{width:300}}),e.jsxs(o,{placeholder:"记录类型",value:N,onChange:H,allowClear:!0,style:{width:120},children:[e.jsx(X,{value:"reward",children:"奖励"}),e.jsx(X,{value:"punishment",children:"处分"})]}),e.jsxs(o,{placeholder:"奖惩分类",value:E,onChange:G,allowClear:!0,style:{width:150},children:["reward"===N&&Object.entries(ee).map(([s,l])=>e.jsx(X,{value:s,children:l.name},s)),"punishment"===N&&Object.entries(se).map(([s,l])=>e.jsx(X,{value:s,children:l.name},s)),!N&&e.jsxs(e.Fragment,{children:[Object.entries(ee).map(([s,l])=>e.jsx(X,{value:s,children:l.name},s)),Object.entries(se).map(([s,l])=>e.jsx(X,{value:s,children:l.name},s))]})]}),e.jsxs(o,{placeholder:"审批状态",value:K,onChange:Q,allowClear:!0,style:{width:120},children:[e.jsx(X,{value:"pending",children:"待审批"}),e.jsx(X,{value:"approved",children:"已通过"}),e.jsx(X,{value:"rejected",children:"已拒绝"})]})]}),e.jsx(A,{columns:ye,dataSource:ve,rowKey:"id",loading:f,pagination:{total:ve.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 条记录`},scroll:{x:1200}})]}),e.jsx(r,{title:de?"编辑记录":"新增记录",open:le,onCancel:()=>te(!1),footer:null,width:600,children:e.jsxs(n,{form:me,layout:"vertical",onFinish:async e=>{try{let s=null;if(e.studentId){const t=await l.get(`/students?student_id=${encodeURIComponent(e.studentId)}`);if(!(t.success&&t.data.length>0))return void m.error("未找到该学号对应的学生，请检查学号是否正确");s=t.data[0].id}const t={student_id:s,type:e.type,category:e.category,sub_category:e.subCategory,title:e.title,description:e.description,level:e.level,date:e.date.format("YYYY-MM-DD"),approver:e.approver,status:e.status||"pending",points:e.points||0,attachments:e.attachments||[],submitted_by:e.submittedBy||e.studentName,review_comments:e.reviewComments||""};if(de){(await l.put(`/rewards/${de.id}`,t)).success?(m.success("记录更新成功"),await pe()):m.error("更新记录失败")}else{(await l.post("/rewards",t)).success?(m.success("记录创建成功"),await pe()):m.error("创建记录失败")}te(!1),me.resetFields(),ce(null)}catch(s){console.error("保存奖惩记录错误:",s),m.error("保存失败")}},children:[e.jsxs(i,{gutter:16,children:[e.jsx(d,{span:12,children:e.jsx(n.Item,{label:"学号",name:"studentId",rules:[{required:!0,message:"请输入学号"}],children:e.jsx(c,{placeholder:"输入学号自动匹配学生信息",onChange:e=>(async e=>{await V(e,me,void 0,m)})(e.target.value)})})}),e.jsx(d,{span:12,children:e.jsx(n.Item,{label:"学生姓名",name:"studentName",rules:[{required:!0,message:"请输入学生姓名"}],children:e.jsx(c,{placeholder:"学生姓名"})})})]}),e.jsxs(i,{gutter:16,children:[e.jsx(d,{span:8,children:e.jsx(n.Item,{label:"班级",name:"class",rules:[{required:!0,message:"请输入班级"}],children:e.jsx(c,{placeholder:"自动匹配",disabled:!0})})}),e.jsx(d,{span:8,children:e.jsx(n.Item,{label:"专业",name:"major",children:e.jsx(c,{placeholder:"自动匹配",disabled:!0})})}),e.jsx(d,{span:8,children:e.jsx(n.Item,{label:"学院",name:"college",children:e.jsx(c,{placeholder:"自动匹配",disabled:!0})})})]}),e.jsxs(i,{gutter:16,children:[e.jsx(d,{span:8,children:e.jsx(n.Item,{label:"类型",name:"type",rules:[{required:!0,message:"请选择类型"}],children:e.jsxs(o,{placeholder:"选择类型",onChange:e=>{H(e),me.setFieldsValue({category:void 0,subCategory:void 0}),G("")},children:[e.jsx(X,{value:"reward",children:"奖励"}),e.jsx(X,{value:"punishment",children:"处分"})]})})}),e.jsx(d,{span:8,children:e.jsx(n.Item,{label:"级别",name:"level",rules:[{required:!0,message:"请选择级别"}],children:e.jsxs(o,{placeholder:"选择级别",children:[e.jsx(X,{value:"national",children:"国家级"}),e.jsx(X,{value:"provincial",children:"省级"}),e.jsx(X,{value:"municipal",children:"市级"}),e.jsx(X,{value:"school",children:"校级"}),e.jsx(X,{value:"college",children:"院级"}),e.jsx(X,{value:"department",children:"系级"}),e.jsx(X,{value:"class",children:"班级"})]})})}),e.jsx(d,{span:8,children:e.jsx(n.Item,{label:"积分",name:"points",rules:[{required:!0,message:"请输入积分"}],children:e.jsx(c,{type:"number",placeholder:"正数为奖励，负数为处分"})})})]}),e.jsxs(i,{gutter:16,children:[e.jsx(d,{span:12,children:e.jsx(n.Item,{label:"分类",name:"category",rules:[{required:!0,message:"请选择分类"}],children:e.jsxs(o,{placeholder:"请先选择类型",disabled:!N,onChange:e=>{G(e),me.setFieldsValue({subCategory:void 0})},children:["reward"===N&&Object.entries(ee).map(([s,l])=>e.jsx(X,{value:s,children:l.name},s)),"punishment"===N&&Object.entries(se).map(([s,l])=>e.jsx(X,{value:s,children:l.name},s))]})})}),e.jsx(d,{span:12,children:e.jsx(n.Item,{label:"具体类别",name:"subCategory",children:e.jsxs(o,{placeholder:"请先选择分类",disabled:!E,children:["reward"===N&&E&&(null==(t=ee[E])?void 0:t.subCategories.map(s=>e.jsx(X,{value:s,children:s},s))),"punishment"===N&&E&&(null==(h=se[E])?void 0:h.subCategories.map(s=>e.jsx(X,{value:s,children:s},s)))]})})})]}),e.jsx(n.Item,{label:"标题",name:"title",rules:[{required:!0,message:"请输入标题"}],children:e.jsx(c,{placeholder:"奖惩事项标题"})}),e.jsx(n.Item,{label:"详细描述",name:"description",rules:[{required:!0,message:"请输入详细描述"}],children:e.jsx(Z,{rows:3,placeholder:"详细描述奖惩事项的具体情况"})}),e.jsxs(i,{gutter:16,children:[e.jsx(d,{span:12,children:e.jsx(n.Item,{label:"日期",name:"date",rules:[{required:!0,message:"请选择日期"}],children:e.jsx(x,{style:{width:"100%"}})})}),e.jsx(d,{span:12,children:e.jsx(n.Item,{label:"审批人",name:"approver",rules:[{required:!0,message:"请输入审批人"}],children:e.jsx(c,{placeholder:"审批部门或人员"})})})]}),e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(j,{onClick:()=>te(!1),children:"取消"}),e.jsx(j,{type:"primary",htmlType:"submit",children:de?"更新":"创建"})]})]})}),e.jsx(R,{visible:ne,onCancel:()=>ie(!1),onSubmit:e=>{const s=he.map((e,s)=>({id:`file_${Date.now()}_${s}`,name:e.name,url:e.url||`/uploads/${e.name}`,type:e.type,size:e.size,uploadedAt:(new Date).toISOString()})),l=[{...e,id:Date.now().toString(),date:e.date.format("YYYY-MM-DD"),status:"pending",attachments:s,submittedBy:e.studentName,submittedAt:(new Date).toISOString().split("T")[0],createdAt:(new Date).toISOString().split("T")[0]},...v];b(l),localStorage.setItem("rewardPunishmentRecords",JSON.stringify(l)),m.success("获奖资料提交成功，等待审批"),ie(!1),ue([])},form:je,fileList:he,onFileChange:e=>{let s=[...e.fileList];s=s.slice(-5),s=s.map(e=>(e.response&&(e.url=e.response.url),e)),ue(s)},rewardCategories:ee}),e.jsx(U,{visible:ae,onCancel:()=>re(!1),record:oe,rewardCategories:ee,punishmentCategories:se}),g]})};export{le as default};
