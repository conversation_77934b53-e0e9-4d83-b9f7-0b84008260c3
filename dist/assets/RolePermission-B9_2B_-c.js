import{j as e}from"./index-Cu_U9Dm3.js";import{a0 as t,r as s,aq as i,s as r,F as a,G as l,H as d,ae as n,u as c,a as o,ao as y,O as h,ay as p,M as m,I as x,bs as u,ah as k,Q as j,i as g,T as w,af as f,aB as S,bk as b,W as A,Z as I,U as v}from"./antd-DYv0PFJq.js";import{u as C}from"./index-96zYxufZ.js";import"./vendor-D2RBMdQ0.js";const{TextArea:z}=x,F=()=>{const{modal:F}=t.useApp(),[T,O]=s.useState([]),[D,$]=s.useState([]),[q,H]=s.useState(!1),[B,W]=s.useState(!1),[E,K]=s.useState(null),[Q,_]=s.useState([]),[G]=i.useForm(),J=C("role:create"),M=C("role:edit"),R=C("role:delete");s.useEffect(()=>{U(),V()},[]);const U=async()=>{H(!0);try{O([{id:"1",name:"超级管理员",key:"admin",description:"拥有系统所有权限，可进行数据导入导出",permissions:["*"],userCount:1,isSystem:!0,createdAt:"2024-01-01 00:00:00",updatedAt:"2024-01-01 00:00:00"},{id:"2",name:"辅导员",key:"counselor",description:"管理所属班级资料和教师业务档案，可以实现信息的上传、下载、修改和编辑",permissions:["student:read","student:write","student:import","student:export","class:read","class:write","academic:read","academic:write","award:read","award:write","financial:read","financial:write","psychology:read","psychology:write","certificate:read","certificate:write","teacher:read","teacher:write"],userCount:5,isSystem:!0,createdAt:"2024-01-01 00:00:00",updatedAt:"2024-01-01 00:00:00"},{id:"3",name:"教师",key:"teacher",description:"仅具有教师业务档案功能权限--教师上传信息后同步到管理员端",permissions:["teacher:read","teacher:write","academic:read"],userCount:20,isSystem:!0,createdAt:"2024-01-01 00:00:00",updatedAt:"2024-01-01 00:00:00"},{id:"4",name:"党建专员",key:"party_secretary",description:"负责党建信息库管理--可以实现党建信息库的上传、下载、修改和编辑、同步到管理员端",permissions:["party:read","party:write","party:delete","party:develop","student:read"],userCount:3,isSystem:!0,createdAt:"2024-01-01 00:00:00",updatedAt:"2024-01-01 00:00:00"},{id:"5",name:"院长",key:"dean",description:"查看所有信息，无修改权限，拥有和管理员同样的权限，但是只有查看权限",permissions:["student:read","teacher:read","class:read","academic:read","award:read","financial:read","party:read","psychology:read","certificate:read","system:log"],userCount:2,isSystem:!0,createdAt:"2024-01-01 00:00:00",updatedAt:"2024-01-01 00:00:00"}])}catch(e){r.error("获取角色列表失败")}finally{H(!1)}},V=async()=>{try{$([])}catch(e){r.error("获取权限列表失败")}},Z=[{title:"角色名称",dataIndex:"name",key:"name",width:150,render:(t,s)=>e.jsxs(n,{children:[e.jsx(k,{style:{color:s.isSystem?"#1890ff":"#52c41a"}}),e.jsx("span",{style:{fontWeight:500},children:t}),s.isSystem&&e.jsx(j,{color:"blue",size:"small",children:"系统"})]})},{title:"角色标识",dataIndex:"key",key:"key",width:120,render:t=>e.jsx(j,{color:"geekblue",children:t})},{title:"描述",dataIndex:"description",key:"description",ellipsis:!0},{title:"权限数量",key:"permissionCount",width:100,render:(t,s)=>e.jsx(j,{color:"green",children:s.permissions.includes("*")?"全部":s.permissions.length})},{title:"用户数量",dataIndex:"userCount",key:"userCount",width:100,render:t=>e.jsxs(n,{children:[e.jsx(g,{}),t]})},{title:"更新时间",dataIndex:"updatedAt",key:"updatedAt",width:150,render:t=>e.jsx("span",{style:{fontSize:"12px",color:"#666"},children:t})},{title:"操作",key:"action",width:180,fixed:"right",render:(t,s)=>e.jsxs(n,{size:"small",children:[e.jsx(w,{title:"查看详情",children:e.jsx(o,{type:"text",size:"small",icon:e.jsx(f,{}),onClick:()=>N(s)})}),M&&e.jsx(w,{title:"编辑权限",children:e.jsx(o,{type:"text",size:"small",icon:e.jsx(S,{}),onClick:()=>L(s)})}),R&&!s.isSystem&&e.jsx(b,{title:"确定要删除这个角色吗？",onConfirm:()=>P(s.id),okText:"确定",cancelText:"取消",children:e.jsx(w,{title:"删除",children:e.jsx(o,{type:"text",size:"small",danger:!0,icon:e.jsx(A,{})})})})]})}],L=e=>{K(e),_(e.permissions),G.setFieldsValue({name:e.name,key:e.key,description:e.description}),W(!0)},N=t=>{F.info({title:`角色详情 - ${t.name}`,width:600,content:e.jsxs("div",{style:{marginTop:16},children:[e.jsxs(I,{column:1,bordered:!0,size:"small",children:[e.jsx(I.Item,{label:"角色名称",children:t.name}),e.jsx(I.Item,{label:"角色标识",children:t.key}),e.jsx(I.Item,{label:"描述",children:t.description}),e.jsx(I.Item,{label:"用户数量",children:t.userCount}),e.jsx(I.Item,{label:"创建时间",children:t.createdAt}),e.jsx(I.Item,{label:"更新时间",children:t.updatedAt})]}),e.jsx(v,{children:"权限列表"}),e.jsx("div",{style:{maxHeight:300,overflow:"auto"},children:t.permissions.includes("*")?e.jsx(j,{color:"red",style:{margin:4},children:"全部权限"}):t.permissions.map(t=>e.jsx(j,{color:"blue",style:{margin:4},children:t},t))})]})})},P=async e=>{try{O(t=>t.filter(t=>t.id!==e)),r.success("角色删除成功")}catch(t){r.error("删除失败")}};return e.jsxs("div",{children:[e.jsx(a,{style:{marginBottom:16},children:e.jsxs(l,{justify:"space-between",align:"middle",children:[e.jsx(d,{children:e.jsxs(n,{children:[e.jsx(c,{style:{fontSize:20,color:"#1890ff"}}),e.jsx("span",{style:{fontSize:16,fontWeight:500},children:"角色权限管理"})]})}),e.jsx(d,{children:J&&e.jsx(o,{type:"primary",icon:e.jsx(y,{}),onClick:()=>{K(null),_([]),G.resetFields(),W(!0)},children:"新增角色"})})]})}),e.jsx(h,{message:"系统角色说明",description:e.jsxs("div",{children:[e.jsxs("p",{children:[e.jsx("strong",{children:"超级管理员："}),"所有数据操作权限，可进行数据导入导出"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"辅导员："}),"管理所属班级资料和教师业务档案，可以实现信息的上传、下载、修改和编辑"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"教师："}),"仅具有教师业务档案功能权限--教师上传信息后同步到管理员端"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"党建专员："}),"负责党建信息库管理--可以实现党建信息库的上传、下载、修改和编辑、同步到管理员端"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"院长："}),"查看所有信息，无修改权限，拥有和管理员同样的权限，但是只有查看权限"]})]}),type:"info",showIcon:!0,style:{marginBottom:16}}),e.jsx(a,{children:e.jsx(p,{columns:Z,dataSource:T,rowKey:"id",loading:q,scroll:{x:1e3},pagination:{total:T.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>`第 ${t[0]}-${t[1]} 条/共 ${e} 条`}})}),e.jsx(m,{title:E?"编辑角色":"新增角色",open:B,onOk:async()=>{try{const e=await G.validateFields();if(E)O(t=>t.map(t=>t.id===E.id?{...t,...e,permissions:Q,updatedAt:(new Date).toISOString()}:t)),r.success("角色更新成功");else{const t={id:Date.now().toString(),...e,permissions:Q,userCount:0,isSystem:!1,createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()};O(e=>[...e,t]),r.success("角色创建成功")}W(!1),G.resetFields(),_([])}catch(e){console.error("表单验证失败:",e)}},onCancel:()=>{W(!1),G.resetFields(),_([])},width:800,destroyOnHidden:!0,children:e.jsxs(i,{form:G,layout:"vertical",children:[e.jsxs(l,{gutter:16,children:[e.jsx(d,{span:12,children:e.jsx(i.Item,{name:"name",label:"角色名称",rules:[{required:!0,message:"请输入角色名称"}],children:e.jsx(x,{placeholder:"请输入角色名称"})})}),e.jsx(d,{span:12,children:e.jsx(i.Item,{name:"key",label:"角色标识",rules:[{required:!0,message:"请输入角色标识"},{pattern:/^[a-z_]+$/,message:"只能包含小写字母和下划线"}],children:e.jsx(x,{placeholder:"请输入角色标识",disabled:null==E?void 0:E.isSystem})})})]}),e.jsx(i.Item,{name:"description",label:"角色描述",rules:[{required:!0,message:"请输入角色描述"}],children:e.jsx(z,{rows:3,placeholder:"请输入角色描述"})}),e.jsx(i.Item,{label:"权限配置",children:e.jsx("div",{style:{border:"1px solid #d9d9d9",borderRadius:6,padding:16,maxHeight:400,overflow:"auto"},children:e.jsx(u,{checkable:!0,checkedKeys:Q,onCheck:e=>{_(e)},treeData:[{title:"学生管理",key:"student",children:[{title:"查看学生信息",key:"student:read"},{title:"编辑学生信息",key:"student:write"},{title:"删除学生信息",key:"student:delete"},{title:"导入学生数据",key:"student:import"},{title:"导出学生数据",key:"student:export"}]},{title:"教师管理",key:"teacher",children:[{title:"查看教师档案",key:"teacher:read"},{title:"编辑教师档案",key:"teacher:write"},{title:"删除教师档案",key:"teacher:delete"},{title:"审核教师资料",key:"teacher:approve"}]},{title:"班级管理",key:"class",children:[{title:"查看班级信息",key:"class:read"},{title:"编辑班级信息",key:"class:write"},{title:"删除班级信息",key:"class:delete"}]},{title:"学业管理",key:"academic",children:[{title:"查看学业信息",key:"academic:read"},{title:"编辑学业信息",key:"academic:write"},{title:"成绩统计分析",key:"academic:analyze"}]},{title:"奖惩管理",key:"award",children:[{title:"查看奖惩记录",key:"award:read"},{title:"编辑奖惩记录",key:"award:write"},{title:"删除奖惩记录",key:"award:delete"},{title:"审核奖惩申请",key:"award:approve"}]},{title:"贫困补助",key:"financial",children:[{title:"查看补助信息",key:"financial:read"},{title:"编辑补助信息",key:"financial:write"},{title:"删除补助记录",key:"financial:delete"},{title:"审核补助申请",key:"financial:approve"}]},{title:"党建工作",key:"party",children:[{title:"查看党建信息",key:"party:read"},{title:"编辑党建信息",key:"party:write"},{title:"删除党建记录",key:"party:delete"},{title:"党员发展管理",key:"party:develop"}]},{title:"心理健康",key:"psychology",children:[{title:"查看心理档案",key:"psychology:read"},{title:"编辑心理档案",key:"psychology:write"},{title:"删除心理记录",key:"psychology:delete"}]},{title:"证明开具",key:"certificate",children:[{title:"查看证明记录",key:"certificate:read"},{title:"开具证明",key:"certificate:write"},{title:"审核证明申请",key:"certificate:approve"}]},{title:"系统管理",key:"system",children:[{title:"用户管理",key:"user:manage"},{title:"角色管理",key:"role:manage"},{title:"权限管理",key:"permission:manage"},{title:"系统设置",key:"system:setting"},{title:"操作日志",key:"system:log"},{title:"数据备份",key:"system:backup"}]}],defaultExpandAll:!0})})})]})})]})};export{F as default};
