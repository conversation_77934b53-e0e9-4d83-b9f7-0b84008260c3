import{j as e}from"./index-DP6eZxW9.js";import{a0 as s,aq as l,r as i,s as t,F as r,G as a,H as n,ae as c,a9 as d,a as o,bv as x,X as j,aI as h,ai as m,O as p,aM as u,aK as y,ab as g,as as v,ah as f,I as b,ad as I,bw as w,aa as P,Y as k,ay as S,ac as C,Q as A,B as H,af as q,L as B,J as z,N as Y,ag as N,bx as T}from"./antd-DYv0PFJq.js";import{s as D}from"./securityService-DjlJ9x7B.js";import"./vendor-D2RBMdQ0.js";const{Option:M}=v,{TextArea:R}=b,{TabPane:_}=m,E=()=>{const{modal:b}=s.useApp(),[E]=l.useForm(),[F,L]=i.useState(!1),[O,J]=i.useState(null),[K,V]=i.useState("config"),[W,G]=i.useState([]),[Q,U]=i.useState(null),[X,Z]=i.useState(null),[$,ee]=i.useState(!1);i.useEffect(()=>{se(),"events"===K?le():"report"===K&&ie()},[K]);const se=async()=>{L(!0);try{const e=await D.getSecurityConfig();J(e),E.setFieldsValue(e)}catch(e){t.error("获取安全配置失败")}finally{L(!1)}},le=async()=>{ee(!0);try{const{events:e}=await D.getSecurityEvents(1,50);G(e)}catch(e){t.error("获取安全事件失败")}finally{ee(!1)}},ie=async()=>{L(!0);try{const e=await D.getSecurityReport("7d");U(e)}catch(e){t.error("获取安全报告失败")}finally{L(!1)}};return e.jsxs("div",{children:[e.jsx(r,{style:{marginBottom:24},children:e.jsxs(a,{justify:"space-between",align:"middle",children:[e.jsx(n,{children:e.jsxs(c,{children:[e.jsx(d,{style:{fontSize:20,color:"#1890ff"}}),e.jsx("span",{style:{fontSize:16,fontWeight:500},children:"安全策略管理"})]})}),e.jsx(n,{children:e.jsxs(c,{children:[e.jsx(o,{icon:e.jsx(x,{}),onClick:async()=>{L(!0);try{const s=await D.testSecurityConfig();Z(s),b.info({title:"安全配置测试结果",width:600,content:e.jsxs("div",{style:{marginTop:16},children:[e.jsx("div",{style:{marginBottom:16},children:e.jsx(H,{status:s.passed?"success":"error",text:s.passed?"配置通过测试":"配置存在问题"})}),s.issues.length>0&&e.jsxs("div",{style:{marginBottom:16},children:[e.jsx("h4",{style:{color:"#ff4d4f"},children:"发现的问题："}),e.jsx("ul",{children:s.issues.map((s,l)=>e.jsx("li",{style:{color:"#ff4d4f"},children:s},l))})]}),s.recommendations.length>0&&e.jsxs("div",{children:[e.jsx("h4",{style:{color:"#faad14"},children:"建议："}),e.jsx("ul",{children:s.recommendations.map((s,l)=>e.jsx("li",{style:{color:"#faad14"},children:s},l))})]})]})})}catch(s){t.error("测试失败")}finally{L(!1)}},loading:F,children:"测试配置"}),e.jsx(o,{icon:e.jsx(j,{}),onClick:se,loading:F,children:"重置"}),e.jsx(o,{type:"primary",icon:e.jsx(h,{}),onClick:async()=>{try{const e=await E.validateFields();L(!0),await D.updateSecurityConfig(e),J(e),t.success("安全配置保存成功")}catch(e){t.error("保存失败")}finally{L(!1)}},loading:F,disabled:"config"!==K,children:"保存配置"})]})})]})}),e.jsxs(m,{activeKey:K,onChange:V,children:[e.jsxs(_,{tab:e.jsxs(c,{children:[e.jsx(d,{}),"安全配置"]}),children:[e.jsx(p,{message:"安全提醒",description:"修改安全策略可能会影响系统的正常使用，请谨慎操作。建议在非工作时间进行配置变更。",type:"warning",showIcon:!0,style:{marginBottom:24}}),e.jsx(l,{form:E,layout:"vertical",initialValues:O,children:e.jsxs(a,{gutter:24,children:[e.jsxs(n,{span:12,children:[e.jsxs(r,{title:e.jsxs(c,{children:[e.jsx(g,{}),"密码策略"]}),style:{marginBottom:24},children:[e.jsx(l.Item,{name:["passwordPolicy","minLength"],label:"最小长度",rules:[{required:!0,message:"请输入密码最小长度"}],children:e.jsx(u,{min:6,max:32,addonAfter:"位",style:{width:"100%"}})}),e.jsx(l.Item,{name:["passwordPolicy","requireUppercase"],label:"要求大写字母",valuePropName:"checked",children:e.jsx(y,{})}),e.jsx(l.Item,{name:["passwordPolicy","requireLowercase"],label:"要求小写字母",valuePropName:"checked",children:e.jsx(y,{})}),e.jsx(l.Item,{name:["passwordPolicy","requireNumbers"],label:"要求数字",valuePropName:"checked",children:e.jsx(y,{})}),e.jsx(l.Item,{name:["passwordPolicy","requireSpecialChars"],label:"要求特殊字符",valuePropName:"checked",children:e.jsx(y,{})}),e.jsx(l.Item,{name:["passwordPolicy","passwordExpireDays"],label:"密码有效期",rules:[{required:!0,message:"请输入密码有效期"}],children:e.jsx(u,{min:0,max:365,addonAfter:"天",style:{width:"100%"},placeholder:"0表示永不过期"})}),e.jsx(l.Item,{name:["passwordPolicy","preventReuse"],label:"禁止重复使用",rules:[{required:!0,message:"请输入禁止重复使用的密码数量"}],children:e.jsx(u,{min:0,max:20,addonAfter:"个历史密码",style:{width:"100%"}})})]}),e.jsxs(r,{title:e.jsxs(c,{children:[e.jsx(f,{}),"操作审计"]}),style:{marginBottom:24},children:[e.jsx(l.Item,{name:["auditPolicy","enableAudit"],label:"启用审计",valuePropName:"checked",children:e.jsx(y,{})}),e.jsx(l.Item,{name:["auditPolicy","auditLevel"],label:"审计级别",rules:[{required:!0,message:"请选择审计级别"}],children:e.jsx(v,{children:[{label:"基础",value:"basic",description:"记录登录、登出等基本操作"},{label:"详细",value:"detailed",description:"记录所有数据变更操作"},{label:"完整",value:"full",description:"记录所有操作，包括查看操作"}].map(s=>e.jsx(M,{value:s.value,children:e.jsxs("div",{children:[e.jsx("div",{children:s.label}),e.jsx("div",{style:{fontSize:"12px",color:"#666"},children:s.description})]})},s.value))})}),e.jsx(l.Item,{name:["auditPolicy","retentionDays"],label:"日志保留期",rules:[{required:!0,message:"请输入日志保留期"}],children:e.jsx(u,{min:30,max:3650,addonAfter:"天",style:{width:"100%"}})}),e.jsx(l.Item,{name:["auditPolicy","sensitiveOperations"],label:"敏感操作",children:e.jsxs(v,{mode:"multiple",placeholder:"选择需要特别审计的操作",style:{width:"100%"},children:[e.jsx(M,{value:"delete",children:"删除操作"}),e.jsx(M,{value:"export",children:"数据导出"}),e.jsx(M,{value:"import",children:"数据导入"}),e.jsx(M,{value:"user_create",children:"用户创建"}),e.jsx(M,{value:"role_modify",children:"角色修改"}),e.jsx(M,{value:"permission_change",children:"权限变更"})]})})]})]}),e.jsxs(n,{span:12,children:[e.jsxs(r,{title:e.jsxs(c,{children:[e.jsx(I,{}),"登录策略"]}),style:{marginBottom:24},children:[e.jsx(l.Item,{name:["loginPolicy","maxFailAttempts"],label:"最大失败次数",rules:[{required:!0,message:"请输入最大失败次数"}],children:e.jsx(u,{min:3,max:10,addonAfter:"次",style:{width:"100%"}})}),e.jsx(l.Item,{name:["loginPolicy","lockoutDuration"],label:"锁定时长",rules:[{required:!0,message:"请输入锁定时长"}],children:e.jsx(u,{min:5,max:1440,addonAfter:"分钟",style:{width:"100%"}})}),e.jsx(l.Item,{name:["loginPolicy","sessionTimeout"],label:"会话超时",rules:[{required:!0,message:"请输入会话超时时间"}],children:e.jsx(u,{min:30,max:480,addonAfter:"分钟",style:{width:"100%"}})}),e.jsx(l.Item,{name:["loginPolicy","allowMultipleLogin"],label:"允许多地登录",valuePropName:"checked",children:e.jsx(y,{})}),e.jsx(l.Item,{name:["loginPolicy","requireCaptcha"],label:"要求验证码",valuePropName:"checked",children:e.jsx(y,{})}),e.jsx(l.Item,{name:["loginPolicy","ipWhitelist"],label:"IP白名单",children:e.jsx(R,{rows:3,placeholder:"每行一个IP地址或IP段，如：*********** 或 ***********/24"})})]}),e.jsxs(r,{title:e.jsxs(c,{children:[e.jsx(P,{}),"访问控制"]}),style:{marginBottom:24},children:[e.jsx(l.Item,{name:["accessControl","workingHours","enabled"],label:"启用工作时间限制",valuePropName:"checked",children:e.jsx(y,{})}),e.jsxs(a,{gutter:16,children:[e.jsx(n,{span:12,children:e.jsx(l.Item,{name:["accessControl","workingHours","startTime"],label:"开始时间",children:e.jsx(w,{format:"HH:mm",style:{width:"100%"},placeholder:"选择开始时间"})})}),e.jsx(n,{span:12,children:e.jsx(l.Item,{name:["accessControl","workingHours","endTime"],label:"结束时间",children:e.jsx(w,{format:"HH:mm",style:{width:"100%"},placeholder:"选择结束时间"})})})]}),e.jsx(l.Item,{name:["accessControl","workingHours","weekdays"],label:"工作日",children:e.jsx(v,{mode:"multiple",placeholder:"选择工作日",style:{width:"100%"},children:[{label:"周一",value:1},{label:"周二",value:2},{label:"周三",value:3},{label:"周四",value:4},{label:"周五",value:5},{label:"周六",value:6},{label:"周日",value:0}].map(s=>e.jsx(M,{value:s.value,children:s.label},s.value))})}),e.jsx(l.Item,{name:["accessControl","ipRestriction","enabled"],label:"启用IP限制",valuePropName:"checked",children:e.jsx(y,{})}),e.jsx(l.Item,{name:["accessControl","ipRestriction","allowedIPs"],label:"允许的IP",children:e.jsx(R,{rows:2,placeholder:"每行一个IP地址，留空表示允许所有IP"})}),e.jsx(l.Item,{name:["accessControl","ipRestriction","deniedIPs"],label:"禁止的IP",children:e.jsx(R,{rows:2,placeholder:"每行一个IP地址"})})]})]})]})}),e.jsx(r,{title:e.jsxs(c,{children:[e.jsx(k,{}),"安全建议"]}),style:{marginTop:24},children:e.jsxs(a,{gutter:16,children:[e.jsx(n,{span:8,children:e.jsxs("div",{style:{textAlign:"center",padding:16},children:[e.jsx(g,{style:{fontSize:32,color:"#52c41a",marginBottom:8}}),e.jsx("h4",{children:"强密码策略"}),e.jsx("p",{style:{color:"#666",fontSize:"12px"},children:"建议启用所有密码复杂度要求，设置合理的密码有效期"})]})}),e.jsx(n,{span:8,children:e.jsxs("div",{style:{textAlign:"center",padding:16},children:[e.jsx(f,{style:{fontSize:32,color:"#1890ff",marginBottom:8}}),e.jsx("h4",{children:"完整审计"}),e.jsx("p",{style:{color:"#666",fontSize:"12px"},children:"启用详细级别的操作审计，确保所有重要操作都有记录"})]})}),e.jsx(n,{span:8,children:e.jsxs("div",{style:{textAlign:"center",padding:16},children:[e.jsx(d,{style:{fontSize:32,color:"#fa8c16",marginBottom:8}}),e.jsx("h4",{children:"访问控制"}),e.jsx("p",{style:{color:"#666",fontSize:"12px"},children:"根据实际情况配置工作时间和IP限制，提高系统安全性"})]})})]})})]},"config"),e.jsx(_,{tab:e.jsxs(c,{children:[e.jsx(P,{}),"安全事件",W.filter(e=>!e.resolved).length>0&&e.jsx(H,{count:W.filter(e=>!e.resolved).length})]}),children:e.jsx(r,{title:"安全事件监控",loading:$,children:e.jsx(S,{dataSource:W,rowKey:"id",pagination:{pageSize:10},columns:[{title:"事件类型",dataIndex:"type",key:"type",render:s=>{const l={login_fail:{text:"登录失败",color:"orange"},suspicious_activity:{text:"可疑活动",color:"red"},policy_violation:{text:"策略违规",color:"purple"},security_alert:{text:"安全警报",color:"red"}}[s]||{text:s,color:"default"};return e.jsx(A,{color:l.color,children:l.text})}},{title:"严重程度",dataIndex:"severity",key:"severity",render:s=>{const l={low:{text:"低",color:"green"},medium:{text:"中",color:"orange"},high:{text:"高",color:"red"},critical:{text:"严重",color:"red"}}[s];return e.jsx(A,{color:l.color,children:l.text})}},{title:"用户",dataIndex:"username",key:"username",render:e=>e||"-"},{title:"IP地址",dataIndex:"ip",key:"ip"},{title:"描述",dataIndex:"description",key:"description"},{title:"时间",dataIndex:"timestamp",key:"timestamp",render:e=>C(e).format("YYYY-MM-DD HH:mm:ss")},{title:"状态",dataIndex:"resolved",key:"resolved",render:s=>e.jsx(H,{status:s?"success":"error",text:s?"已解决":"待处理"})},{title:"操作",key:"action",render:(s,l)=>e.jsxs(c,{children:[e.jsx(o,{type:"link",size:"small",icon:e.jsx(q,{}),onClick:()=>{b.info({title:"事件详情",width:600,content:e.jsxs("div",{style:{marginTop:16},children:[e.jsxs("p",{children:[e.jsx("strong",{children:"事件ID:"})," ",l.id]}),e.jsxs("p",{children:[e.jsx("strong",{children:"类型:"})," ",l.type]}),e.jsxs("p",{children:[e.jsx("strong",{children:"严重程度:"})," ",l.severity]}),e.jsxs("p",{children:[e.jsx("strong",{children:"用户:"})," ",l.username||"-"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"IP地址:"})," ",l.ip]}),e.jsxs("p",{children:[e.jsx("strong",{children:"用户代理:"})," ",l.userAgent]}),e.jsxs("p",{children:[e.jsx("strong",{children:"描述:"})," ",l.description]}),e.jsx("p",{children:e.jsx("strong",{children:"详细信息:"})}),e.jsx("pre",{style:{background:"#f5f5f5",padding:8,borderRadius:4},children:JSON.stringify(l.details,null,2)}),e.jsxs("p",{children:[e.jsx("strong",{children:"时间:"})," ",C(l.timestamp).format("YYYY-MM-DD HH:mm:ss")]}),l.resolved&&e.jsxs(e.Fragment,{children:[e.jsxs("p",{children:[e.jsx("strong",{children:"解决人:"})," ",l.resolvedBy]}),e.jsxs("p",{children:[e.jsx("strong",{children:"解决时间:"})," ",C(l.resolvedAt).format("YYYY-MM-DD HH:mm:ss")]})]})]})})},children:"详情"}),!l.resolved&&e.jsx(o,{type:"link",size:"small",icon:e.jsx(B,{}),onClick:()=>(async e=>{try{await D.resolveSecurityEvent(e,"admin"),t.success("事件已标记为已解决"),le()}catch(s){t.error("操作失败")}})(l.id),children:"标记解决"})]})}]})})},"events"),e.jsxs(_,{tab:e.jsxs(c,{children:[e.jsx(T,{}),"安全报告"]}),children:[Q&&e.jsxs(a,{gutter:24,children:[e.jsx(n,{span:6,children:e.jsx(r,{children:e.jsx(z,{title:"总事件数",value:Q.totalEvents,prefix:e.jsx(f,{})})})}),e.jsx(n,{span:6,children:e.jsx(r,{children:e.jsx(z,{title:"严重事件",value:Q.criticalEvents,valueStyle:{color:"#cf1322"},prefix:e.jsx(Y,{})})})}),e.jsx(n,{span:6,children:e.jsx(r,{children:e.jsx(z,{title:"登录尝试",value:Q.loginAttempts,prefix:e.jsx(g,{})})})}),e.jsx(n,{span:6,children:e.jsx(r,{children:e.jsx(z,{title:"失败登录",value:Q.failedLogins,valueStyle:{color:"#cf1322"},prefix:e.jsx(P,{})})})})]}),Q&&e.jsx(r,{title:"威胁分析",style:{marginTop:24},children:e.jsx(S,{dataSource:Q.topThreats,pagination:!1,columns:[{title:"威胁类型",dataIndex:"type",key:"type"},{title:"事件数量",dataIndex:"count",key:"count"},{title:"占比",dataIndex:"percentage",key:"percentage",render:s=>e.jsx("div",{style:{width:200},children:e.jsx(N,{percent:s,size:"small"})})}]})}),Q&&e.jsx(r,{title:"安全建议",style:{marginTop:24},children:e.jsx("ul",{children:Q.recommendations.map((s,l)=>e.jsxs("li",{style:{marginBottom:8},children:[e.jsx(k,{style:{color:"#1890ff",marginRight:8}}),s]},l))})})]},"report")]})]})};export{E as default};
