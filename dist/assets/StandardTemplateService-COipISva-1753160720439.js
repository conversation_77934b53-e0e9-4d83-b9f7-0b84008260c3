var e=Object.defineProperty,t=(t,a,n)=>((t,a,n)=>a in t?e(t,a,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[a]=n)(t,"symbol"!=typeof a?a+"":a,n);import{A as a}from"./AdvancedDocumentParser-D0HhWqXv-1753160720439.js";const n=class e{constructor(){t(this,"storageKey","certificateTemplates"),t(this,"predefinedTemplatesKey","predefinedTemplates")}static getInstance(){return e.instance||(e.instance=new e),e.instance}getAllTemplates(){try{const e=this.getCustomTemplates();return[...this.getPredefinedTemplates(),...e]}catch(e){return console.error("获取模板失败:",e),[]}}getCustomTemplates(){try{const e=localStorage.getItem(this.storageKey);return e?JSON.parse(e):[]}catch(e){return console.error("获取自定义模板失败:",e),[]}}getPredefinedTemplates(){return[{id:"leave_application_ai",name:"计算机与人工智能学院学生请假申请单",type:"leave",category:"predefined",fileName:"计算机与人工智能学院学生请假申请单.docx",fileType:"docx",isDocumentTemplate:!0,variables:["studentName","studentId","class","major","gender","dormitory","contactPhone","leaveType","leaveReason","startDate","startTime","endDate","endTime","leaveDays","emergencyContact","emergencyPhone","relationship","applicationDate","advisorOpinion","advisorDate","collegeOpinion","collegeDate"],formatInfo:{hasTable:!0,hasBorder:!0,fontFamily:"SimSun",fontSize:14,lineHeight:1.6,preserveFormat:!0},content:"                    计算机与人工智能学院学生请假申请单\n\n┌─────────────────────────────────────────────────────────────────┐\n│                           申请人基本信息                           │\n├─────────────────────────────────────────────────────────────────┤\n│  姓    名：{{studentName}}                学    号：{{studentId}}  │\n│  班    级：{{class}}                      专    业：{{major}}      │\n│  性    别：{{gender}}                     宿 舍 号：{{dormitory}}  │\n│  联系电话：{{contactPhone}}                                        │\n└─────────────────────────────────────────────────────────────────┘\n\n┌─────────────────────────────────────────────────────────────────┐\n│                             请假信息                             │\n├─────────────────────────────────────────────────────────────────┤\n│  请假类型：{{leaveType}}                                          │\n│  请假原因：{{leaveReason}}                                        │\n│  请假时间：从 {{startDate}} {{startTime}} 到 {{endDate}} {{endTime}} │\n│  请假天数：{{leaveDays}} 天                                       │\n└─────────────────────────────────────────────────────────────────┘\n\n┌─────────────────────────────────────────────────────────────────┐\n│                           紧急联系人                             │\n├─────────────────────────────────────────────────────────────────┤\n│  联系人姓名：{{emergencyContact}}                                 │\n│  联系人电话：{{emergencyPhone}}                                   │\n│  与学生关系：{{relationship}}                                     │\n└─────────────────────────────────────────────────────────────────┘\n\n学生承诺：\n    本人保证请假期间注意人身安全，按时返校销假。如有意外，后果自负。\n\n学生签名：________________                    申请日期：{{applicationDate}}\n\n┌─────────────────────────────────────────────────────────────────┐\n│                             审批意见                             │\n├─────────────────────────────────────────────────────────────────┤\n│  辅导员意见：                                                     │\n│  □ 同意    □ 不同意                                              │\n│  意见：{{advisorOpinion}}                                        │\n│  辅导员签名：________________    日期：{{advisorDate}}            │\n│                                                                 │\n│  学院意见：                                                       │\n│  □ 同意    □ 不同意                                              │\n│  意见：{{collegeOpinion}}                                        │\n│  学院负责人签名：________________    日期：{{collegeDate}}        │\n└─────────────────────────────────────────────────────────────────┘\n\n备注：\n1. 请假3天以内由辅导员审批\n2. 请假3天以上需学院审批  \n3. 请假期间如有紧急情况请及时联系辅导员",metadata:{title:"学生请假申请单",description:"计算机与人工智能学院学生请假申请表格",author:"系统预定义",version:"1.0",tags:["请假","申请","学生"]},createdAt:"2024-01-01",updatedAt:"2024-01-01",usage:{count:0}},{id:"enrollment_certificate",name:"在读证明",type:"enrollment",category:"predefined",variables:["studentName","studentId","class","major","college","schoolName","currentDate"],formatInfo:{hasTable:!1,hasBorder:!1,fontFamily:"SimSun",fontSize:16,lineHeight:1.8,preserveFormat:!0},content:"                              在读证明\n\n兹证明{{studentName}}同学，学号：{{studentId}}，系我校{{college}}{{major}}专业{{class}}班学生。\n\n该生现为我校在读学生，学习情况良好，特此证明。\n\n此证明仅供相关用途使用。\n\n\n                                                    {{schoolName}}\n                                                    {{currentDate}}",metadata:{title:"在读证明",description:"学生在读状态证明文件",author:"系统预定义",version:"1.0",tags:["在读","证明","学生"]},createdAt:"2024-01-01",updatedAt:"2024-01-01",usage:{count:0}}]}getTemplateById(e){return this.getAllTemplates().find(t=>t.id===e)||null}getTemplatesByType(e){return this.getAllTemplates().filter(t=>t.type===e)}createTemplate(e){const t={id:Date.now().toString(),name:e.name||"未命名模板",type:e.type||"other",content:e.content||"",variables:e.variables||[],category:"custom",createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString(),usage:{count:0},...e},a=this.getCustomTemplates();return a.push(t),this.saveCustomTemplates(a),t}createTemplateFromDocument(e){const t={id:Date.now().toString(),name:e.metadata.title||e.fileName,type:this.inferTemplateType(e.fileName,e.originalContent),content:e.parsedContent,variables:e.variables.map(e=>e.variableName),fileName:e.fileName,fileType:e.fileType,isDocumentTemplate:!0,category:"uploaded",formatInfo:e.formatInfo,metadata:{title:e.metadata.title,description:`从文档 ${e.fileName} 创建`,author:e.metadata.author,tags:this.generateTags(e.fileName,e.originalContent)},createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString(),usage:{count:0}},a=this.getCustomTemplates();return a.push(t),this.saveCustomTemplates(a),t}saveTemplate(e){const t=this.getCustomTemplates(),a=t.findIndex(t=>t.id===e.id);return a>=0?t[a]={...e,updatedAt:(new Date).toISOString()}:t.push({...e,createdAt:e.createdAt||(new Date).toISOString(),updatedAt:(new Date).toISOString()}),localStorage.setItem(this.storageKey,JSON.stringify(t)),e}updateTemplate(e,t){const a=this.getCustomTemplates(),n=a.findIndex(t=>t.id===e);if(-1===n)return null;const r={...a[n],...t,updatedAt:(new Date).toISOString()};return a[n]=r,this.saveCustomTemplates(a),r}deleteTemplate(e){const t=this.getCustomTemplates(),a=t.findIndex(t=>t.id===e);return-1!==a&&(t.splice(a,1),this.saveCustomTemplates(t),!0)}validateTemplate(e){const t=[],a=[],n=[];if(e.name&&""!==e.name.trim()||t.push("模板名称不能为空"),e.content&&""!==e.content.trim()||t.push("模板内容不能为空"),e.type&&""!==e.type.trim()||t.push("模板类型不能为空"),e.content){/\{\{[^}]+\}\}|\{[^}]+\}|\[[^\]]+\]/.test(e.content)||a.push("模板内容中未检测到变量，可能无法动态替换内容");const t=e.content.match(/\{\{[^}]*\}\}|\{[^}]*\}|\[[^\]]*\]/g);t&&t.forEach(e=>{(e.includes("{{}}")||e.includes("{}")||e.includes("[]"))&&a.push(`发现空变量: ${e}`)})}return e.variables&&0===e.variables.length&&e.content&&n.push("建议添加变量列表以提高替换准确性"),{valid:0===t.length,errors:t,warnings:a,suggestions:n}}recordTemplateUsage(e){var t;const a=this.getTemplateById(e);if(a&&"custom"===a.category){const n={usage:{count:((null==(t=a.usage)?void 0:t.count)||0)+1,lastUsed:(new Date).toISOString()}};this.updateTemplate(e,n)}}getTemplateStatistics(){const e=this.getAllTemplates(),t={},a={};e.forEach(e=>{t[e.category]=(t[e.category]||0)+1,a[e.type]=(a[e.type]||0)+1});const n=e.filter(e=>{var t;return null==(t=e.usage)?void 0:t.count}).sort((e,t)=>{var a,n;return((null==(a=t.usage)?void 0:a.count)||0)-((null==(n=e.usage)?void 0:n.count)||0)}).slice(0,5);return{total:e.length,byCategory:t,byType:a,mostUsed:n}}saveCustomTemplates(e){try{localStorage.setItem(this.storageKey,JSON.stringify(e))}catch(t){throw console.error("保存模板失败:",t),new Error("保存模板失败，可能是存储空间不足")}}inferTemplateType(e,t){const a=e.toLowerCase(),n=t.toLowerCase();return a.includes("请假")||n.includes("请假")?"leave":a.includes("在读")||n.includes("在读")?"enrollment":a.includes("毕业")||n.includes("毕业")?"graduation":a.includes("成绩")||n.includes("成绩")?"academic":a.includes("品行")||n.includes("品行")?"conduct":a.includes("实习")||n.includes("实习")?"internship":"other"}generateTags(e,t){const a=[],n=e.toLowerCase(),r=t.toLowerCase();return Object.entries({"请假":["请假","申请"],"在读":["在读","证明"],"毕业":["毕业","证明"],"成绩":["成绩","学业"],"品行":["品行","操行"],"实习":["实习","工作"],"学生":["学生"],"申请":["申请"],"证明":["证明"]}).forEach(([e,t])=>{(n.includes(e)||r.includes(e))&&a.push(...t)}),[...new Set(a)]}searchTemplates(e){if(!e.trim())return this.getAllTemplates();const t=e.toLowerCase();return this.getAllTemplates().filter(e=>{var a,n;return e.name.toLowerCase().includes(t)||e.type.toLowerCase().includes(t)||(null==(a=e.metadata)?void 0:a.description)&&e.metadata.description.toLowerCase().includes(t)||(null==(n=e.metadata)?void 0:n.tags)&&e.metadata.tags.some(e=>e.toLowerCase().includes(t))})}exportTemplate(e){const t=this.getTemplateById(e);return t?JSON.stringify(t,null,2):null}importTemplate(e){try{const t=JSON.parse(e),a=this.validateTemplate(t);if(!a.valid)throw new Error(`模板验证失败: ${a.errors.join(", ")}`);return t.id=Date.now().toString(),t.category="custom",t.createdAt=(new Date).toISOString(),t.updatedAt=(new Date).toISOString(),this.createTemplate(t)}catch(t){throw new Error(`导入模板失败: ${t.message}`)}}};t(n,"instance");let r=n;const i=class e{constructor(){t(this,"standardMappings",{"学生姓名":{standardName:"studentName",chineseName:"学生姓名",englishName:"Student Name",category:"basic",dataType:"string",required:!0,description:"学生的真实姓名"},"姓名":{standardName:"studentName",chineseName:"学生姓名",englishName:"Student Name",category:"basic",dataType:"string",required:!0,description:"学生的真实姓名"},"学号":{standardName:"studentId",chineseName:"学号",englishName:"Student ID",category:"basic",dataType:"string",required:!0,validation:/^[A-Za-z0-9]{4,20}$/,description:"学生的唯一标识号码"},"班级":{standardName:"class",chineseName:"班级",englishName:"Class",category:"basic",dataType:"string",required:!0,description:"学生所在的班级"},"专业":{standardName:"major",chineseName:"专业",englishName:"Major",category:"basic",dataType:"string",required:!0,description:"学生所学的专业"},"学院":{standardName:"college",chineseName:"学院",englishName:"College",category:"basic",dataType:"string",required:!0,defaultValue:"计算机与人工智能学院",description:"学生所在的学院"},"性别":{standardName:"gender",chineseName:"性别",englishName:"Gender",category:"basic",dataType:"string",required:!1,description:"学生的性别"},"年级":{standardName:"grade",chineseName:"年级",englishName:"Grade",category:"basic",dataType:"string",required:!1,description:"学生所在年级"},"联系电话":{standardName:"phone",chineseName:"联系电话",englishName:"Phone",category:"contact",dataType:"string",required:!1,description:"学生联系电话"},"手机号":{standardName:"phone",chineseName:"联系电话",englishName:"Phone",category:"contact",dataType:"string",required:!1,description:"学生联系电话"},"电话":{standardName:"phone",chineseName:"联系电话",englishName:"Phone",category:"contact",dataType:"string",required:!1,description:"学生联系电话"},"学生状态":{standardName:"status",chineseName:"学生状态",englishName:"Status",category:"basic",dataType:"string",required:!1,description:"学生当前状态"},"入学时间":{standardName:"enrollmentDate",chineseName:"入学时间",englishName:"Enrollment Date",category:"academic",dataType:"date",required:!1,description:"学生入学的时间"},"入学日期":{standardName:"enrollmentDate",chineseName:"入学时间",englishName:"Enrollment Date",category:"academic",dataType:"date",required:!1,description:"学生入学的时间"},"学制":{standardName:"duration",chineseName:"学制",englishName:"Duration",category:"academic",dataType:"string",required:!1,defaultValue:"四年",description:"学制年限"},"学历层次":{standardName:"educationLevel",chineseName:"学历层次",englishName:"Education Level",category:"academic",dataType:"string",required:!1,defaultValue:"本科",description:"学历层次"},"层次":{standardName:"level",chineseName:"学历层次",englishName:"Education Level",category:"academic",dataType:"string",required:!1,defaultValue:"本科",description:"学历层次"},"民族":{standardName:"ethnicity",chineseName:"民族",englishName:"Ethnicity",category:"basic",dataType:"string",required:!1,description:"学生民族"},"籍贯":{standardName:"birthplace",chineseName:"籍贯",englishName:"Birthplace",category:"basic",dataType:"string",required:!1,description:"学生籍贯"},"挂科课程":{standardName:"failedCourses",chineseName:"挂科课程",englishName:"Failed Courses",category:"academic",dataType:"string",required:!1,description:"学生挂科的课程"},"课程学期":{standardName:"courseSemester",chineseName:"课程学期",englishName:"Course Semester",category:"academic",dataType:"string",required:!1,description:"课程所在学期"},"重修报名":{standardName:"retakeRegistered",chineseName:"重修报名",englishName:"Retake Registered",category:"academic",dataType:"string",required:!1,description:"重修报名情况"},"预计毕业时间":{standardName:"expectedGraduation",chineseName:"预计毕业时间",englishName:"Expected Graduation",category:"academic",dataType:"date",required:!1,description:"预计毕业时间"},"欠费情况":{standardName:"tuitionOwed",chineseName:"欠费情况",englishName:"Tuition Owed",category:"academic",dataType:"string",required:!1,description:"学费欠费情况"},"欠费金额":{standardName:"owedAmount",chineseName:"欠费金额",englishName:"Owed Amount",category:"academic",dataType:"string",required:!1,description:"具体欠费金额"},"辅导员联系方式":{standardName:"counselorContact",chineseName:"辅导员联系方式",englishName:"Counselor Contact",category:"contact",dataType:"string",required:!1,description:"辅导员联系方式"},"学生联系电话":{standardName:"studentPhone",chineseName:"学生联系电话",englishName:"Student Phone",category:"contact",dataType:"string",required:!1,description:"学生联系电话"},"其他情况":{standardName:"otherSituation",chineseName:"其他情况",englishName:"Other Situation",category:"academic",dataType:"string",required:!1,description:"其他特殊情况"},"当前日期":{standardName:"currentDate",chineseName:"当前日期",englishName:"Current Date",category:"system",dataType:"date",required:!1,description:"系统当前日期"},"开具日期":{standardName:"issueDate",chineseName:"开具日期",englishName:"Issue Date",category:"system",dataType:"date",required:!1,description:"证明开具日期"},"学校名称":{standardName:"schoolName",chineseName:"学校名称",englishName:"School Name",category:"system",dataType:"string",required:!1,defaultValue:"银川科技学院",description:"学校的名称"}}),t(this,"detectionPatterns",[{pattern:/\[([^\]]+)\]/g,type:"bracket",priority:10},{pattern:/\{\{([^}]+)\}\}/g,type:"double_brace",priority:9},{pattern:/\{([^}]+)\}/g,type:"brace",priority:8},{pattern:/_{3,}/g,type:"underscore",priority:5},{pattern:/\s{3,}/g,type:"space",priority:3}])}static getInstance(){return e.instance||(e.instance=new e),e.instance}detectVariables(e){console.log("开始智能变量检测，内容长度:",e.length);const t=[],a={},n=[];for(const i of this.detectionPatterns){const n=this.findMatches(e,i);a[i.type]=n.length;for(const e of n){const a=this.createVariableMapping(e,i.type);a&&!this.isDuplicate(t,a)&&t.push(a)}}const r=this.calculateOverallConfidence(t,a);return 0===t.length?n.push("未检测到变量占位符，建议添加 [变量名] 格式的占位符"):r<.7&&n.push("部分变量映射置信度较低，建议手动确认"),a.bracket>0&&n.push(`检测到 ${a.bracket} 个方括号格式变量，推荐格式`),console.log("变量检测完成:",{variableCount:t.length,confidence:r,patterns:a}),{variables:t,confidence:r,suggestions:n,patterns:a}}findMatches(e,t){const a=[];let n;for(t.pattern.lastIndex=0;null!==(n=t.pattern.exec(e));){const e=n[0],r=n[1]||e,i=n.index;if(a.push({text:e,value:r,position:i}),!t.pattern.global)break}return a}createVariableMapping(e,t){const{text:a,value:n}=e,r=n.trim();if(!r)return null;const i=this.findStandardMapping(r);return i?{original:a,...i}:{original:a,standardName:this.generateStandardName(r),chineseName:r,englishName:this.translateToEnglish(r),category:"custom",dataType:"string",required:!1,description:`自定义变量: ${r}`}}findStandardMapping(e){if(this.standardMappings[e])return this.standardMappings[e];for(const[t,a]of Object.entries(this.standardMappings))if(e.includes(t)||t.includes(e))return a;return null}generateStandardName(e){const t={"日期":"Date","时间":"Time","地址":"Address","电话":"Phone","邮箱":"Email","备注":"Remark","说明":"Description"};for(const[a,n]of Object.entries(t))if(e.includes(a))return e.replace(a,n).toLowerCase();return e.replace(/\s+/g,"").replace(/[^\w\u4e00-\u9fa5]/g,"").toLowerCase()}translateToEnglish(e){const t={"日期":"Date","时间":"Time","地址":"Address","电话":"Phone","邮箱":"Email","备注":"Remark","说明":"Description","编号":"Number","代码":"Code","状态":"Status"};for(const[a,n]of Object.entries(t))if(e.includes(a))return e.replace(a,n);return e}isDuplicate(e,t){return e.some(e=>e.standardName===t.standardName||e.original===t.original)}calculateOverallConfidence(e,t){if(0===e.length)return 0;const a=e.filter(e=>"custom"!==e.category).length/e.length,n=t.bracket/(t.bracket+t.underscore+t.space+1);return Math.min(.95,.6*a+.4*n)}getStandardVariables(){return Object.values(this.standardMappings)}validateVariable(e,t){return!(e.required&&!t)&&(!e.validation||!t||e.validation.test(t))}};t(i,"instance");let s=i;const o=class e{constructor(){t(this,"advancedParser"),t(this,"variableService"),t(this,"templateService"),t(this,"standardTemplates",[{id:"standard_enrollment_undergraduate",name:"（统招本科）计算机与人工智能学院学生在读证明模板",fileName:"（统招本科）计算机与人工智能学院学生在读证明模板.docx",filePath:"/templates/（统招本科）计算机与人工智能学院学生在读证明模板.docx",category:"enrollment",description:"统招本科学生在读证明的标准模板，包含完整的格式和变量定义",isActive:!0,createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()},{id:"standard_enrollment_college",name:"（统招专科）计算机与人工智能学院学生在读证明模板",fileName:"（统招专科）计算机与人工智能学院学生在读证明模板.doc",filePath:"/templates/（统招专科）计算机与人工智能学院学生在读证明模板.doc",category:"enrollment",description:"统招专科学生在读证明的标准模板",isActive:!0,createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()},{id:"standard_enrollment_upgrade",name:"（专升本）计算机与人工智能学院学生在读证明模板",fileName:"（专升本）计算机与人工智能学院学生在读证明模板.doc",filePath:"/templates/（专升本）计算机与人工智能学院学生在读证明模板.doc",category:"enrollment",description:"专升本学生在读证明的标准模板",isActive:!0,createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()},{id:"standard_leave_application",name:"计算机与人工智能学院学生请假申请单",fileName:"计算机与人工智能学院学生请假申请单.docx",filePath:"/templates/计算机与人工智能学院学生请假申请单.docx",category:"other",description:"学生请假申请的标准表单模板",isActive:!0,createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()},{id:"standard_student_card_replacement",name:"补办学生证申请表",fileName:"补办学生证申请表.docx",filePath:"/templates/补办学生证申请表.docx",category:"other",description:"学生证补办申请的标准表单模板",isActive:!0,createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()},{id:"standard_exam_certificate",name:"计算机与人工智能学院学生考试证明",fileName:"计算机与人工智能学院学生考试证明.pdf",filePath:"/templates/计算机与人工智能学院学生考试证明.pdf",category:"academic",description:"学生考试证明的标准模板",isActive:!0,createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()},{id:"standard_discipline_notice",name:"计算机与人工智能学院学生违纪处分告知书",fileName:"计算机与人工智能学院学生违纪处分告知书.docx",filePath:"/templates/计算机与人工智能学院学生违纪处分告知书.docx",category:"conduct",description:"学生违纪处分告知的标准文书模板",isActive:!0,createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()}]),this.advancedParser=a.getInstance(),this.variableService=s.getInstance(),this.templateService=r.getInstance()}static getInstance(){return e.instance||(e.instance=new e),e.instance}getStandardTemplates(){return this.standardTemplates.filter(e=>e.isActive)}getStandardTemplate(e){return this.standardTemplates.find(t=>t.id===e)||null}getStandardTemplatesByCategory(e){return this.standardTemplates.filter(t=>t.isActive&&t.category===e)}async integrateStandardTemplate(e){try{console.log("开始集成标准模板:",e);const t=this.getStandardTemplate(e);if(!t)return{success:!1,error:`未找到标准模板: ${e}`};const a=await this.loadTemplateFile(t.filePath),n=await this.advancedParser.analyzeDocument(a);console.log("文档分析完成:",n);const r=this.variableService.detectVariables(n.content);console.log("变量检测完成:",r);const i={id:`integrated_${e}_${Date.now()}`,name:t.name,type:this.mapCategoryToCertificateType(t.category),content:n.htmlContent||n.content,variables:r.variables.map(e=>e.standardName),isDocumentTemplate:!0,fileName:t.fileName,fileType:this.getFileType(t.fileName),originalContent:n.content,parsedContent:n.htmlContent||n.content,formatInfo:{hasTable:n.tables.length>0,hasBorder:n.tables.some(e=>e.hasBorder),fontFamily:"SimSun",fontSize:14,lineHeight:1.6},metadata:{title:t.name,description:t.description,category:t.category,source:"standard_template",integrationDate:(new Date).toISOString(),analysisScore:r.confidence,variableCount:r.variables.length},createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()};return await this.templateService.saveTemplate(i),t.analysis=n,t.variables=r.variables,t.updatedAt=(new Date).toISOString(),console.log("标准模板集成完成:",i),{success:!0,template:i,analysis:n,variables:r.variables,warnings:r.suggestions}}catch(t){return console.error("标准模板集成失败:",t),{success:!1,error:`模板集成失败: ${t.message}`}}}async integrateAllStandardTemplates(){const e=[];for(const a of this.getStandardTemplates())try{const t=await this.integrateStandardTemplate(a.id);e.push(t),await new Promise(e=>setTimeout(e,500))}catch(t){e.push({success:!1,error:`集成模板 ${a.name} 失败: ${t.message}`})}return e}async loadTemplateFile(e){try{const t=await fetch(e);if(!t.ok)throw new Error(`无法加载模板文件: ${t.status}`);const a=await t.blob(),n=e.split("/").pop()||"template";return new File([a],n,{type:this.getMimeType(n)})}catch(t){throw new Error(`模板文件加载失败: ${t.message}`)}}getMimeType(e){var t;switch(null==(t=e.split(".").pop())?void 0:t.toLowerCase()){case"docx":return"application/vnd.openxmlformats-officedocument.wordprocessingml.document";case"doc":return"application/msword";case"pdf":return"application/pdf";case"txt":return"text/plain";default:return"application/octet-stream"}}getFileType(e){var t;return(null==(t=e.split(".").pop())?void 0:t.toLowerCase())||"unknown"}mapCategoryToCertificateType(e){return{enrollment:"enrollment",academic:"academic",conduct:"conduct",other:"other"}[e]||"other"}validateTemplate(e){const t=[];return e.name||t.push("模板名称不能为空"),e.fileName||t.push("文件名不能为空"),e.filePath||t.push("文件路径不能为空"),e.description||t.push("模板描述不能为空"),{valid:0===t.length,issues:t}}getTemplateStats(){const e=this.standardTemplates.filter(e=>e.isActive),t=this.standardTemplates.filter(e=>e.analysis),a={};return e.forEach(e=>{a[e.category]=(a[e.category]||0)+1}),{total:this.standardTemplates.length,active:e.length,byCategory:a,analyzed:t.length}}};t(o,"instance");let c=o;export{s as E,c as S,r as T};
