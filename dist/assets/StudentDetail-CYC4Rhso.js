import{c as e,u as l,j as s,d as t}from"./index-Cu_U9Dm3.js";import{a0 as a,r as i,F as n,S as r,ae as d,a as c,aC as o,aB as m,A as h,g as x,Z as j,Q as u,ai as b,l as p,ah as y,aD as g,ay as I,j as v,i as f,aE as N,aF as k,aG as w,m as S,ag as _,p as z,q as C}from"./antd-DYv0PFJq.js";import{g as A}from"./studentService-0XkkoVJM.js";import"./vendor-D2RBMdQ0.js";const $=()=>{var $,R,D;const{id:T}=e(),P=l(),{message:O}=a.useApp(),[B,E]=i.useState(!0),[F,L]=i.useState(null),[W,H]=i.useState(null),[q,G]=i.useState(""),[J,<PERSON>]=i.useState(!1);i.useEffect(()=>{const e=()=>{M(window.innerWidth<=768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),i.useEffect(()=>{(async()=>{if(!T)return E(!1),void G("❌ 没有提供学生ID");console.log(`🚀 开始获取学生信息，学号: ${T}`),G(`🚀 开始获取学生信息，学号: ${T}`);try{const l=await A(T);if(console.log("📊 获取到的学生数据:",l),l){console.log("✅ 学生数据获取成功，设置到state"),console.log("🔍 [StudentDetail] 完整的学生数据:",l),console.log("🔍 [StudentDetail] 数据类型:",typeof l),console.log("🔍 [StudentDetail] 数据键:",Object.keys(l)),console.log("🔍 [StudentDetail] 基本字段:",{id:l.id,studentId:l.studentId,name:l.name,gender:l.gender,college:l.college}),console.log("🔍 [StudentDetail] 家庭信息字段:",{fatherName:l.fatherName,motherName:l.motherName,homeAddress:l.homeAddress,familyIncome:l.familyIncome}),console.log("🔍 [StudentDetail] 家庭信息字段类型:",{fatherNameType:typeof l.fatherName,motherNameType:typeof l.motherName,homeAddressType:typeof l.homeAddress}),console.log("🔍 [StudentDetail] 家庭信息字段值:",{fatherNameValue:JSON.stringify(l.fatherName),motherNameValue:JSON.stringify(l.motherName),homeAddressValue:JSON.stringify(l.homeAddress)}),console.log("🔍 [StudentDetail] 健康信息字段:",{bloodType:l.bloodType,height:l.height,weight:l.weight,healthStatus:l.healthStatus}),L(l),G(`✅ 成功获取学生信息: ${l.name||"未知姓名"}`);try{console.log("🎓 开始获取学业情况数据");const e=await t.getByStudentId(T);console.log("📊 获取到的学业情况数据:",e),H(e.data)}catch(e){console.log("⚠️ 未找到学业情况数据或获取失败:",e),H(null)}}else console.error(`❌ 未找到学号为 ${T} 的学生信息`),L(null),G(`❌ 未找到学号为 ${T} 的学生信息`),O.warning(`未找到学号为 ${T} 的学生信息`)}catch(l){console.error("💥 获取学生数据失败:",l),L(null),G(`💥 获取学生数据失败: ${l}`),O.error("获取学生信息失败")}finally{console.log("🏁 数据获取完成，设置loading为false"),E(!1)}})()},[T]);return B?s.jsx("div",{style:{padding:"24px"},children:s.jsxs(n,{style:{textAlign:"center"},children:[s.jsx(r,{size:"large"}),s.jsx("div",{style:{color:"#666",marginTop:"16px"},children:"正在加载学生信息..."}),q&&s.jsx("div",{style:{fontSize:"12px",color:"#999",marginTop:"8px"},children:q})]})}):F?s.jsxs("div",{style:{padding:"24px"},children:[q&&s.jsx(n,{style:{marginBottom:"16px"},children:s.jsxs("div",{style:{fontSize:"14px",color:"#1890ff"},children:[s.jsx("strong",{children:"调试信息："})," ",q]})}),s.jsxs("div",{className:J?"space-y-4":"flex items-center justify-between",children:[s.jsxs("div",{className:J?"space-y-2":"flex items-center space-x-4",children:[s.jsx(c,{icon:s.jsx(o,{}),onClick:()=>P("/students"),size:J?"middle":"default",children:"返回"}),s.jsxs("div",{children:[s.jsx("h1",{className:J?"text-xl font-bold text-gray-800":"text-2xl font-bold text-gray-800",children:"学生详情"}),!J&&s.jsx("p",{className:"text-gray-600",children:"查看和管理学生的详细信息"})]})]}),J?s.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"8px"},children:[s.jsx(c,{icon:s.jsx(m,{}),onClick:()=>P(`/students/${T}/edit`),size:"small",type:"primary",children:"编辑信息"}),s.jsx(c,{onClick:()=>P(`/students/${T}/family`),size:"small",children:"家庭信息"}),s.jsx(c,{onClick:()=>P(`/students/${T}/special`),size:"small",children:"特殊情况"}),s.jsx(c,{onClick:()=>P(`/students/${T}/health`),size:"small",children:"健康档案"})]}):s.jsxs(d,{children:[s.jsx(c,{icon:s.jsx(m,{}),onClick:()=>P(`/students/${T}/edit`),type:"primary",children:"编辑信息"}),s.jsx(c,{onClick:()=>P(`/students/${T}/family`),children:"家庭信息"}),s.jsx(c,{onClick:()=>P(`/students/${T}/special`),children:"特殊情况"}),s.jsx(c,{onClick:()=>P(`/students/${T}/health`),children:"健康档案"})]})]}),s.jsx(n,{style:{marginBottom:"16px"},children:s.jsxs("div",{className:J?"space-y-4":"flex items-start space-x-6",children:[s.jsx("div",{className:J?"flex justify-center":"",children:s.jsx(h,{size:J?80:120,src:F.avatar,icon:s.jsx(x,{})})}),s.jsx("div",{className:"flex-1",children:s.jsxs(j,{title:"基本信息",column:J?1:3,size:J?"small":"default",children:[s.jsx(j.Item,{label:"姓名",children:F.name}),s.jsx(j.Item,{label:"学号",children:F.studentId}),s.jsx(j.Item,{label:"性别",children:"male"===F.gender?"男":"female"===F.gender?"女":F.gender}),s.jsx(j.Item,{label:"民族",children:F.ethnicity||"-"}),s.jsx(j.Item,{label:"籍贯",children:F.birthplace||"-"}),s.jsx(j.Item,{label:"政治面貌",children:F.politicalStatus||"-"}),s.jsx(j.Item,{label:"职务",children:F.position||"-"}),s.jsx(j.Item,{label:"学院",children:F.college}),s.jsx(j.Item,{label:"层次",children:F.level}),s.jsx(j.Item,{label:"年级",children:F.grade}),s.jsx(j.Item,{label:"专业",children:F.major}),s.jsx(j.Item,{label:"班级",children:F.class}),s.jsx(j.Item,{label:"QQ号",children:F.qq||"-"}),s.jsx(j.Item,{label:"微信号",children:F.wechat||"-"}),s.jsx(j.Item,{label:"联系电话",children:F.phone}),s.jsx(j.Item,{label:"身份证号",children:F.idCard}),s.jsx(j.Item,{label:"楼栋号",children:F.dormBuilding||"-"}),s.jsx(j.Item,{label:"宿舍号",children:F.dormRoom||"-"}),s.jsx(j.Item,{label:"状态",children:s.jsx(u,{color:"active"===F.status?"success":"error",children:"active"===F.status?"正常":"异常"})})]})})]})}),s.jsx(n,{children:s.jsx(b,{defaultActiveKey:"family",size:J?"small":"default",tabPosition:"top",items:[{key:"family",label:s.jsxs("span",{children:[s.jsx(p,{}),"家庭信息"]}),children:s.jsxs("div",{children:[s.jsxs(j,{title:"家庭基本信息",column:J?1:2,className:"mb-6",size:J?"small":"default",children:[s.jsx(j.Item,{label:"户口所在地",children:F.householdLocation||"-"}),s.jsx(j.Item,{label:"学费、生活费来源",children:F.tuitionSource||"-"}),s.jsx(j.Item,{label:"现家庭住址",span:J?1:2,children:F.homeAddress||"-"}),s.jsx(j.Item,{label:"是否为单亲家庭",children:F.singleParent||"-"})]}),s.jsxs(j,{title:"父母信息",column:J?1:2,className:"mb-6",size:J?"small":"default",children:[s.jsx(j.Item,{label:"父亲姓名",children:(console.log("🎯 [渲染时] 父亲姓名值:",F.fatherName),console.log("🎯 [渲染时] 父亲姓名类型:",typeof F.fatherName),console.log("🎯 [渲染时] studentInfo对象:",F),F.fatherName||"-")}),s.jsx(j.Item,{label:"父亲联系方式",children:F.fatherPhone||"-"}),s.jsx(j.Item,{label:"父亲工作单位",span:J?1:2,children:F.fatherWork||"-"}),s.jsx(j.Item,{label:"母亲姓名",children:(console.log("🎯 [渲染时] 母亲姓名值:",F.motherName),F.motherName||"-")}),s.jsx(j.Item,{label:"母亲联系电话",children:F.motherPhone||"-"}),s.jsx(j.Item,{label:"母亲工作单位",span:J?1:2,children:F.motherWork||"-"})]}),s.jsx(j,{title:"其他家庭信息",column:J?1:2,className:"mb-6",children:s.jsx(j.Item,{label:"父母是否为残疾",span:J?1:2,children:F.parentsDisabled||"-"})}),(F.guardianName||F.familyMembers)&&s.jsxs(j,{title:"其他家庭成员",column:2,children:[s.jsx(j.Item,{label:"监护人姓名",children:F.guardianName||"-"}),s.jsx(j.Item,{label:"与本人关系",children:F.guardianRelation||"-"}),s.jsx(j.Item,{label:"家庭其他成员及联系方式",span:2,children:F.familyMembers||"-"})]})]})},{key:"special",label:s.jsxs("span",{children:[s.jsx(y,{}),"特殊情况"]}),children:s.jsxs("div",{children:[s.jsxs(j,{title:"孤残情况",column:2,className:"mb-6",children:[s.jsx(j.Item,{label:"是否为烈士子女",children:F.martyrChild||"-"}),s.jsx(j.Item,{label:"是否为孤儿",children:F.orphan||"-"}),(F.caregiverName||F.orphanDescription)&&s.jsxs(s.Fragment,{children:[s.jsx(j.Item,{label:"抚养人姓名",children:F.caregiverName||"-"}),s.jsx(j.Item,{label:"与本人关系",children:F.caregiverRelation||"-"}),s.jsx(j.Item,{label:"抚养人联系方式",children:F.caregiverPhone||"-"})]})]}),(F.caregiverName||F.orphanDescription)&&s.jsx(j,{title:"孤儿情况说明",column:1,className:"mb-6",children:s.jsx(j.Item,{label:"具体情况说明",children:F.orphanDescription||"-"})}),s.jsxs(j,{title:"残疾情况",column:2,children:[s.jsx(j.Item,{label:"是否残疾",children:F.disabled||"-"}),"是"===F.disabled&&s.jsxs(s.Fragment,{children:[s.jsx(j.Item,{label:"残疾类别",children:F.disabilityType||"-"}),s.jsx(j.Item,{label:"残疾等级",children:F.disabilityLevel||"-"})]})]}),"是"===F.disabled&&s.jsx(j,{title:"残疾情况说明",column:1,className:"mt-4",children:s.jsx(j.Item,{label:"具体情况说明",children:F.disabilityDescription||"-"})})]})},{key:"health",label:s.jsxs("span",{children:[s.jsx(g,{}),"健康信息"]}),children:s.jsxs("div",{children:[s.jsxs(j,{title:"基本健康信息",column:2,className:"mb-6",children:[s.jsx(j.Item,{label:"血型",children:F.bloodType||"-"}),s.jsx(j.Item,{label:"健康状况",children:F.healthStatus||"-"}),s.jsx(j.Item,{label:"身高",children:F.height?`${F.height}cm`:"-"}),s.jsx(j.Item,{label:"体重",children:F.weight?`${F.weight}kg`:"-"}),s.jsx(j.Item,{label:"左眼视力",children:F.visionLeft||"-"}),s.jsx(j.Item,{label:"右眼视力",children:F.visionRight||"-"})]}),s.jsxs(j,{title:"疾病与过敏史",column:1,className:"mb-6",children:[s.jsx(j.Item,{label:"慢性疾病",children:F.chronicDisease||"-"}),s.jsx(j.Item,{label:"过敏史",children:F.allergyHistory||"-"}),s.jsx(j.Item,{label:"长期用药",children:F.medication||"-"})]}),s.jsxs(j,{title:"紧急联系信息",column:2,children:[s.jsx(j.Item,{label:"紧急联系人",children:F.emergencyContact||"-"}),s.jsx(j.Item,{label:"紧急联系电话",children:F.emergencyPhone||"-"})]})]})},{key:"academic",label:s.jsxs("span",{children:[s.jsx(v,{}),"学业信息"]}),children:s.jsxs("div",{children:[s.jsxs(j,{title:"学业情况",column:2,className:"mb-6",children:[s.jsx(j.Item,{label:"挂科课程名称",children:(null==W?void 0:W.failed_courses)||F.academicFailedCourses||"-"}),s.jsx(j.Item,{label:"开课学年学期",children:(null==W?void 0:W.course_semester)||F.academicCourseSemester||"-"}),s.jsx(j.Item,{label:"是否报名重修",children:(null==W?void 0:W.retake_registered)||F.academicRetakeRegistered?s.jsx(u,{color:"是"===((null==W?void 0:W.retake_registered)||F.academicRetakeRegistered)?"blue":"default",children:(null==W?void 0:W.retake_registered)||F.academicRetakeRegistered}):"-"}),s.jsx(j.Item,{label:"预计可毕业时间",children:(null==W?void 0:W.expected_graduation)||F.academicExpectedGraduation||"-"}),s.jsx(j.Item,{label:"是否欠缴学费",children:(null==W?void 0:W.tuition_owed)||F.academicTuitionOwed?s.jsx(u,{color:"是"===((null==W?void 0:W.tuition_owed)||F.academicTuitionOwed)?"red":"green",children:(null==W?void 0:W.tuition_owed)||F.academicTuitionOwed}):"-"}),s.jsx(j.Item,{label:"欠缴金额",children:(null==W?void 0:W.owed_amount)||F.academicOwedAmount?`¥${(null==W?void 0:W.owed_amount)||F.academicOwedAmount}`:"-"}),s.jsx(j.Item,{label:"辅导员联系方式",children:(null==W?void 0:W.counselor_contact)||F.academicCounselorContact||"-"}),s.jsx(j.Item,{label:"学生联系电话",children:(null==W?void 0:W.student_contact)||F.phone||"-"})]}),s.jsxs(j,{title:"其他学业情况",column:1,className:"mb-6",children:[s.jsx(j.Item,{label:"其他情况",children:(null==W?void 0:W.other_situation)||F.academicOtherSituation||"-"}),s.jsx(j.Item,{label:"学业现状描述",children:(null==W?void 0:W.status)||F.academicStatus||"-"})]}),s.jsxs(j,{title:"基本学业情况",column:2,className:"mb-6",children:[s.jsx(j.Item,{label:"有无违纪",children:F.disciplinaryAction||"-"}),s.jsx(j.Item,{label:"是否报名辅修",children:F.minorProgram||"-"})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[s.jsx(n,{size:"small",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-blue-500",children:"3.2"}),s.jsx("div",{className:"text-gray-600",children:"平均绩点"})]})}),s.jsx(n,{size:"small",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-green-500",children:"85.5"}),s.jsx("div",{className:"text-gray-600",children:"平均分"})]})}),s.jsx(n,{size:"small",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-orange-500",children:"15/18"}),s.jsx("div",{className:"text-gray-600",children:"已修学分"})]})})]}),s.jsx(I,{columns:[{title:"课程名称",dataIndex:"course",key:"course"},{title:"学分",dataIndex:"credit",key:"credit"},{title:"成绩",dataIndex:"score",key:"score"},{title:"绩点",dataIndex:"gpa",key:"gpa"},{title:"学期",dataIndex:"semester",key:"semester"}],dataSource:[{key:"1",course:"高等数学",credit:4,score:85,gpa:3.5,semester:"2021-2022-1"},{key:"2",course:"程序设计基础",credit:3,score:92,gpa:4,semester:"2021-2022-1"},{key:"3",course:"英语",credit:2,score:78,gpa:3,semester:"2021-2022-1"}],pagination:!1,size:"small"})]})]})},{key:"religion",label:s.jsxs("span",{children:[s.jsx(f,{}),"宗教信仰"]}),children:s.jsxs("div",{children:[s.jsxs(j,{title:"宗教信仰情况",column:2,className:"mb-6",children:[s.jsx(j.Item,{label:"有无宗教信仰",children:F.religiousBelief||"-"}),s.jsx(j.Item,{label:"是否参加过宗教活动",children:F.religiousActivity||"-"})]}),s.jsx(j,{title:"家庭宗教背景",column:1,children:s.jsx(j.Item,{label:"直系亲属或近亲属是否为宗教教职人员或有宗教背景",children:F.familyReligious||"-"})})]})},{key:"financial",label:s.jsxs("span",{children:[s.jsx(N,{}),"经济状况"]}),children:s.jsxs(j,{title:"经济状况",column:2,children:[s.jsx(j.Item,{label:"是否足额缴纳本学年学杂费",children:F.tuitionPaid||"-"}),s.jsx(j.Item,{label:"是否有贷款",children:F.hasLoan||"-"}),s.jsx(j.Item,{label:"是否有助学金",children:F.hasGrant||"-"})]})},{key:"international",label:s.jsxs("span",{children:[s.jsx(k,{}),"国际教育"]}),children:s.jsxs(j,{title:"国际教育情况",column:2,children:[s.jsx(j.Item,{label:"是否有护照",children:F.hasPassport||"-"}),s.jsx(j.Item,{label:"是否有出国经历",children:F.abroadExperience||"-"}),s.jsx(j.Item,{label:"是否报名本（专）硕直通",children:F.graduateProgram||"-"}),s.jsx(j.Item,{label:"是否报名微留学",children:F.microStudyAbroad||"-"})]})},{key:"counselor",label:s.jsxs("span",{children:[s.jsx(w,{}),"辅导员信息"]}),children:s.jsxs(j,{title:"辅导员信息",column:2,children:[s.jsx(j.Item,{label:"辅导员姓名",children:F.counselorName||"-"}),s.jsx(j.Item,{label:"辅导员联系方式",children:F.counselorPhone||"-"})]})},{key:"awards",label:s.jsxs("span",{children:[s.jsx(S,{}),"奖惩记录"]}),children:s.jsx("div",{children:(null==F?void 0:F.rewardRecords)&&F.rewardRecords.length>0?s.jsx(I,{columns:[{title:"类型",dataIndex:"type",key:"type"},{title:"名称",dataIndex:"title",key:"title"},{title:"级别",dataIndex:"level",key:"level"},{title:"日期",dataIndex:"award_date",key:"award_date"},{title:"状态",dataIndex:"type",key:"status",render:e=>s.jsx(u,{color:"奖励"===e?"gold":"red",children:"奖励"===e?"获奖":"处分"})}],dataSource:F.rewardRecords.map((e,l)=>({...e,key:l})),pagination:!1,size:"small"}):s.jsx("div",{className:"text-center text-gray-500 py-8",children:"暂无奖惩记录"})})},{key:"psychology",label:s.jsxs("span",{children:[s.jsx(z,{}),"心理健康"]}),children:s.jsxs("div",{className:"space-y-4",children:[s.jsx(n,{size:"small",title:"心理健康评估",children:s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{children:[s.jsxs("div",{className:"flex justify-between mb-1",children:[s.jsx("span",{children:"心理健康指数"}),s.jsx("span",{children:"良好"})]}),s.jsx(_,{percent:85,status:"active",strokeColor:"#52c41a"})]}),s.jsxs("div",{children:[s.jsxs("div",{className:"flex justify-between mb-1",children:[s.jsx("span",{children:"压力水平"}),s.jsx("span",{children:"正常"})]}),s.jsx(_,{percent:30,status:"active",strokeColor:"#1890ff"})]})]})}),s.jsx(n,{size:"small",title:"心理健康记录",children:(null==F?void 0:F.mentalHealthRecords)&&F.mentalHealthRecords.length>0?s.jsx("div",{className:"space-y-3",children:F.mentalHealthRecords.map((e,l)=>s.jsx("div",{className:"border-l-4 border-blue-500 pl-4 py-2",children:s.jsxs("div",{className:"flex justify-between items-start",children:[s.jsxs("div",{children:[s.jsx("div",{className:"font-medium",children:e.consultation_type||"心理咨询"}),s.jsx("div",{className:"text-gray-600 text-sm mt-1",children:e.consultation_content||"咨询内容"}),s.jsx("div",{className:"text-gray-400 text-xs mt-2",children:e.consultation_date||e.created_at})]}),s.jsx(u,{color:"blue",children:e.status||"已完成"})]})},l))}):s.jsx("p",{className:"text-gray-600",children:"暂无心理健康记录"})})]})},{key:"party",label:s.jsxs("span",{children:[s.jsx(f,{}),"党建信息"]}),children:s.jsxs("div",{children:[s.jsxs(j,{title:"基本党建信息",column:2,className:"mb-6",children:[s.jsx(j.Item,{label:"政治面貌",children:F.politicalStatus||"-"}),s.jsx(j.Item,{label:"当前状态",children:(null==($=null==F?void 0:F.partyInfo)?void 0:$.status)||"群众"}),s.jsx(j.Item,{label:"入党申请时间",children:(null==(R=null==F?void 0:F.partyInfo)?void 0:R.application_date)||"-"}),s.jsx(j.Item,{label:"培养联系人",children:(null==(D=null==F?void 0:F.partyInfo)?void 0:D.mentor_name)||"-"})]}),(null==F?void 0:F.partyInfo)&&s.jsxs(s.Fragment,{children:[s.jsxs(j,{title:"详细党建信息",column:2,children:[s.jsx(j.Item,{label:"入党积极分子确定时间",children:F.partyInfo.activist_date||"-"}),s.jsx(j.Item,{label:"发展对象确定时间",children:F.partyInfo.development_date||"-"}),s.jsx(j.Item,{label:"预备党员时间",children:F.partyInfo.probationary_date||"-"}),s.jsx(j.Item,{label:"正式党员时间",children:F.partyInfo.formal_date||"-"})]}),s.jsx(j,{title:"党组织信息",column:1,className:"mt-4",children:s.jsx(j.Item,{label:"所属党支部",children:F.partyInfo.branch_name||"-"})})]})]})},{key:"other",label:s.jsxs("span",{children:[s.jsx(C,{}),"其他信息"]}),children:s.jsxs(j,{title:"其他信息",column:1,children:[s.jsx(j.Item,{label:"其它需要说明的问题",children:F.otherNotes||"-"}),s.jsx(j.Item,{label:"备注",children:F.remarks||"-"})]})}]})})]}):s.jsx("div",{style:{padding:"24px"},children:s.jsxs(n,{style:{textAlign:"center"},children:[s.jsx("div",{style:{color:"#ff4d4f",fontSize:"18px",fontWeight:600,marginBottom:"16px"},children:"学生信息不存在"}),q&&s.jsxs(n,{style:{textAlign:"left",marginBottom:"16px"},children:[s.jsx("div",{style:{fontWeight:600,marginBottom:"8px"},children:"调试信息："}),s.jsx("div",{style:{fontSize:"14px",color:"#666"},children:q}),s.jsx("div",{style:{marginTop:"8px",fontSize:"12px",color:"#999"},children:"请检查浏览器控制台获取更多详细信息"})]}),s.jsxs(d,{children:[s.jsx(c,{onClick:()=>P("/students"),children:"返回学生列表"}),s.jsx(c,{type:"primary",onClick:()=>window.location.reload(),children:"重新加载"})]})]})})};export{$ as default};
