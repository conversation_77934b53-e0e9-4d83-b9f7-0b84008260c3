import{c as e,u as s,j as l}from"./index-DXaqwR6F-1753160720439.js";import{aq as a,r,a as d,aC as i,ae as n,aI as c,F as x,ai as t,G as h,H as j,ax as m,A as o,ao as u,I as p,aJ as b,as as I,g as v,l as g,U as S,ah as y,aD as f,j as C,i as O,aE as G,aF as w,aG as k,q as N,s as R}from"./antd-lXsGnH6e-1753160720439.js";import"./vendor-D2RBMdQ0-1753160720439.js";const{Option:q}=I,{TextArea:A}=p,J=()=>{const{id:J}=e(),D=s(),[F]=a.useForm(),[P,T]=r.useState(!0),[L,E]=r.useState(!1),[Q,$]=r.useState(""),[B,U]=r.useState("basic");r.useEffect(()=>{V()},[J]);const V=()=>{T(!0),setTimeout(()=>{const e=JSON.parse(localStorage.getItem("importedStudentsData")||"{}"),s=Object.values(e).find(e=>e.studentId===J),l=JSON.parse(localStorage.getItem("academicRecordsData")||"{}"),a=Object.values(l).find(e=>e.studentId===J);if(s){const e={...s,academicFailedCourses:(null==a?void 0:a.failedCourses)||"",academicCourseSemester:(null==a?void 0:a.courseSemester)||"",academicRetakeRegistered:(null==a?void 0:a.retakeRegistered)||"",academicExpectedGraduation:(null==a?void 0:a.expectedGraduation)||"",academicTuitionOwed:(null==a?void 0:a.tuitionOwed)||"",academicOwedAmount:(null==a?void 0:a.owedAmount)||"",academicCounselorContact:(null==a?void 0:a.counselorContact)||"",academicOtherSituation:(null==a?void 0:a.otherSituation)||""};F.setFieldsValue(e),$(e.avatar||"")}else{const e={studentId:J,name:"学生姓名",gender:"男",college:"计算机学院",level:"本科",grade:"2024",major:"计算机科学与技术",class:"计科2401",phone:"",idCard:"",status:"active"};F.setFieldsValue(e)}T(!1)},500)};return P?l.jsx("div",{children:"加载中..."}):l.jsxs("div",{className:"space-y-6",children:[l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center space-x-4",children:[l.jsx(d,{icon:l.jsx(i,{}),onClick:()=>D(`/students/${J}`),children:"返回"}),l.jsxs("div",{children:[l.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"编辑学生信息"}),l.jsx("p",{className:"text-gray-600",children:"修改学生的详细信息"})]})]}),l.jsxs(n,{children:[l.jsx(d,{onClick:()=>D(`/students/${J}`),children:"取消"}),l.jsx(d,{type:"primary",icon:l.jsx(c,{}),onClick:()=>F.submit(),loading:L,children:"保存修改"})]})]}),l.jsx(x,{children:l.jsx(a,{form:F,layout:"vertical",onFinish:async e=>{E(!0);try{const s=JSON.parse(localStorage.getItem("studentsList")||"[]").map(s=>s.studentId===J?{...s,...e,avatar:Q}:s);localStorage.setItem("studentsList",JSON.stringify(s));const l=JSON.parse(localStorage.getItem("importedStudentsData")||"{}"),a=Object.keys(l).find(e=>l[e].studentId===J);a&&(l[a]={...l[a],...e,avatar:Q},localStorage.setItem("importedStudentsData",JSON.stringify(l)));const r=JSON.parse(localStorage.getItem("academicRecordsData")||"{}"),d=Object.keys(r).find(e=>r[e].studentId===J);d&&(r[d]={...r[d],failedCourses:e.academicFailedCourses,courseSemester:e.academicCourseSemester,retakeRegistered:e.academicRetakeRegistered,expectedGraduation:e.academicExpectedGraduation,tuitionOwed:e.academicTuitionOwed,owedAmount:e.academicOwedAmount,counselorContact:e.academicCounselorContact,otherSituation:e.academicOtherSituation},localStorage.setItem("academicRecordsData",JSON.stringify(r)));const i=JSON.parse(localStorage.getItem("academicRecordsList")||"[]").map(s=>s.studentId===J?{...s,failedCourses:e.academicFailedCourses,courseSemester:e.academicCourseSemester,retakeRegistered:e.academicRetakeRegistered,expectedGraduation:e.academicExpectedGraduation,tuitionOwed:e.academicTuitionOwed,owedAmount:e.academicOwedAmount,counselorContact:e.academicCounselorContact,otherSituation:e.academicOtherSituation}:s);localStorage.setItem("academicRecordsList",JSON.stringify(i)),R.success("学生信息更新成功！"),D(`/students/${J}`)}catch(s){R.error("更新失败，请重试")}finally{E(!1)}},children:l.jsx(t,{activeKey:B,onChange:U,items:[{key:"basic",label:l.jsxs("span",{children:[l.jsx(v,{}),"基本信息"]}),children:l.jsxs(h,{gutter:[24,16],children:[l.jsx(j,{span:24,className:"text-center mb-6",children:l.jsx(m,{name:"avatar",listType:"picture-card",className:"avatar-uploader",showUploadList:!1,action:"/api/upload/avatar",onChange:e=>{"done"===e.file.status&&$(e.file.response.url)},children:Q?l.jsx(o,{src:Q,size:100}):l.jsxs("div",{children:[l.jsx(u,{}),l.jsx("div",{style:{marginTop:8},children:"上传头像"})]})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"学号",name:"studentId",rules:[{required:!0,message:"请输入学号"}],children:l.jsx(p,{placeholder:"请输入学号",disabled:!0})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"姓名",name:"name",rules:[{required:!0,message:"请输入姓名"}],children:l.jsx(p,{placeholder:"请输入姓名"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"性别",name:"gender",rules:[{required:!0,message:"请选择性别"}],children:l.jsxs(b.Group,{children:[l.jsx(b,{value:"男",children:"男"}),l.jsx(b,{value:"女",children:"女"})]})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"民族",name:"ethnicity",children:l.jsx(p,{placeholder:"请输入民族"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"籍贯",name:"birthplace",children:l.jsx(p,{placeholder:"请输入籍贯"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"政治面貌",name:"politicalStatus",children:l.jsxs(I,{placeholder:"请选择政治面貌",children:[l.jsx(q,{value:"群众",children:"群众"}),l.jsx(q,{value:"共青团员",children:"共青团员"}),l.jsx(q,{value:"中共党员",children:"中共党员"}),l.jsx(q,{value:"中共预备党员",children:"中共预备党员"}),l.jsx(q,{value:"民主党派",children:"民主党派"})]})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"职务",name:"position",children:l.jsx(p,{placeholder:"请输入职务"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"学院",name:"college",rules:[{required:!0,message:"请输入学院"}],children:l.jsx(p,{placeholder:"请输入学院"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"层次",name:"level",rules:[{required:!0,message:"请选择层次"}],children:l.jsxs(I,{placeholder:"请选择层次",children:[l.jsx(q,{value:"本科",children:"本科"}),l.jsx(q,{value:"专科",children:"专科"}),l.jsx(q,{value:"研究生",children:"研究生"}),l.jsx(q,{value:"博士",children:"博士"})]})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"年级",name:"grade",rules:[{required:!0,message:"请输入年级"}],children:l.jsx(p,{placeholder:"请输入年级"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"专业",name:"major",rules:[{required:!0,message:"请输入专业"}],children:l.jsx(p,{placeholder:"请输入专业"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"班级",name:"class",rules:[{required:!0,message:"请输入班级"}],children:l.jsx(p,{placeholder:"请输入班级"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"QQ号",name:"qq",children:l.jsx(p,{placeholder:"请输入QQ号"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"微信号",name:"wechat",children:l.jsx(p,{placeholder:"请输入微信号"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"手机号码",name:"phone",rules:[{required:!0,message:"请输入手机号码"},{pattern:/^1[3-9]\d{9}$/,message:"手机号格式不正确"}],children:l.jsx(p,{placeholder:"请输入手机号码"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"身份证号码",name:"idCard",rules:[{required:!0,message:"请输入身份证号码"}],children:l.jsx(p,{placeholder:"请输入身份证号码"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"楼栋号",name:"dormBuilding",children:l.jsx(p,{placeholder:"请输入楼栋号"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"宿舍号",name:"dormRoom",children:l.jsx(p,{placeholder:"请输入宿舍号"})})})]})},{key:"family",label:l.jsxs("span",{children:[l.jsx(g,{}),"家庭信息"]}),children:l.jsxs(h,{gutter:[24,16],children:[l.jsx(j,{xs:24,md:12,children:l.jsx(a.Item,{label:"户口所在地",name:"householdLocation",children:l.jsx(p,{placeholder:"请输入户口所在地"})})}),l.jsx(j,{xs:24,children:l.jsx(a.Item,{label:"现家庭住址（详细到门牌号）",name:"homeAddress",children:l.jsx(A,{rows:2,placeholder:"请输入详细家庭住址"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"父亲姓名",name:"fatherName",children:l.jsx(p,{placeholder:"请输入父亲姓名"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"父亲联系方式",name:"fatherPhone",children:l.jsx(p,{placeholder:"请输入父亲联系方式"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"父亲工作单位",name:"fatherWork",children:l.jsx(p,{placeholder:"请输入父亲工作单位"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"母亲姓名",name:"motherName",children:l.jsx(p,{placeholder:"请输入母亲姓名"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"母亲联系电话",name:"motherPhone",children:l.jsx(p,{placeholder:"请输入母亲联系电话"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"母亲工作单位",name:"motherWork",children:l.jsx(p,{placeholder:"请输入母亲工作单位"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"父母是否为残疾",name:"parentsDisabled",children:l.jsxs(b.Group,{children:[l.jsx(b,{value:"是",children:"是"}),l.jsx(b,{value:"否",children:"否"})]})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"是否为单亲家庭",name:"singleParent",children:l.jsxs(b.Group,{children:[l.jsx(b,{value:"是",children:"是"}),l.jsx(b,{value:"否",children:"否"})]})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"监护人姓名",name:"guardianName",children:l.jsx(p,{placeholder:"请输入监护人姓名"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"与本人关系",name:"guardianRelation",children:l.jsx(p,{placeholder:"请输入与本人关系"})})}),l.jsx(j,{xs:24,children:l.jsx(a.Item,{label:"家庭其他成员及联系方式",name:"familyMembers",children:l.jsx(A,{rows:3,placeholder:"请输入家庭其他成员及联系方式"})})}),l.jsx(j,{xs:24,children:l.jsx(a.Item,{label:"学费、生活费来源",name:"tuitionSource",children:l.jsx(A,{rows:2,placeholder:"请输入学费、生活费来源"})})})]})},{key:"special",label:l.jsxs("span",{children:[l.jsx(y,{}),"特殊情况"]}),children:l.jsxs(h,{gutter:[24,16],children:[l.jsxs(j,{span:24,children:[l.jsx("h3",{children:"孤残情况"}),l.jsx(S,{})]}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"是否为烈士子女",name:"martyrChild",children:l.jsxs(b.Group,{children:[l.jsx(b,{value:"是",children:"是"}),l.jsx(b,{value:"否",children:"否"})]})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"是否为孤儿",name:"orphan",children:l.jsxs(b.Group,{children:[l.jsx(b,{value:"是",children:"是"}),l.jsx(b,{value:"否",children:"否"})]})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"抚养人姓名",name:"caregiverName",children:l.jsx(p,{placeholder:"请输入抚养人姓名"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"与本人关系",name:"caregiverRelation",children:l.jsx(p,{placeholder:"请输入与本人关系"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"抚养人联系方式",name:"caregiverPhone",children:l.jsx(p,{placeholder:"请输入抚养人联系方式"})})}),l.jsx(j,{xs:24,children:l.jsx(a.Item,{label:"具体情况说明",name:"orphanDescription",children:l.jsx(A,{rows:3,placeholder:"请输入具体情况说明"})})}),l.jsxs(j,{span:24,children:[l.jsx("h3",{children:"残疾情况"}),l.jsx(S,{})]}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"是否残疾",name:"disabled",children:l.jsxs(b.Group,{children:[l.jsx(b,{value:"是",children:"是"}),l.jsx(b,{value:"否",children:"否"})]})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"残疾类别",name:"disabilityType",children:l.jsx(p,{placeholder:"请输入残疾类别"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"残疾等级",name:"disabilityLevel",children:l.jsx(p,{placeholder:"请输入残疾等级"})})}),l.jsx(j,{xs:24,children:l.jsx(a.Item,{label:"具体情况说明",name:"disabilityDescription",children:l.jsx(A,{rows:3,placeholder:"请输入具体情况说明"})})})]})},{key:"health",label:l.jsxs("span",{children:[l.jsx(f,{}),"健康信息"]}),children:l.jsxs(h,{gutter:[24,16],children:[l.jsx(j,{xs:24,children:l.jsx(a.Item,{label:"患有基础性疾病情况",name:"healthCondition",children:l.jsx(A,{rows:4,placeholder:"请输入患有基础性疾病情况"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"是否办理过走读",name:"dayStudent",children:l.jsxs(b.Group,{children:[l.jsx(b,{value:"是",children:"是"}),l.jsx(b,{value:"否",children:"否"})]})})})]})},{key:"academic",label:l.jsxs("span",{children:[l.jsx(C,{}),"学业信息"]}),children:l.jsxs(h,{gutter:[24,16],children:[l.jsxs(j,{span:24,children:[l.jsx("h3",{children:"基本学业情况"}),l.jsx(S,{})]}),l.jsx(j,{xs:24,children:l.jsx(a.Item,{label:"学业现状描述",name:"academicStatus",children:l.jsx(A,{rows:3,placeholder:"请输入学业现状描述"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"有无违纪",name:"disciplinaryAction",children:l.jsx(p,{placeholder:"请输入违纪情况"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"是否报名辅修",name:"minorProgram",children:l.jsxs(b.Group,{children:[l.jsx(b,{value:"是",children:"是"}),l.jsx(b,{value:"否",children:"否"})]})})}),l.jsxs(j,{span:24,children:[l.jsx("h3",{children:"详细学业情况"}),l.jsx(S,{})]}),l.jsx(j,{xs:24,md:12,children:l.jsx(a.Item,{label:"挂科课程名称",name:"academicFailedCourses",children:l.jsx(p,{placeholder:"请输入挂科课程名称，多门课程用逗号分隔"})})}),l.jsx(j,{xs:24,md:12,children:l.jsx(a.Item,{label:"开课学年学期",name:"academicCourseSemester",children:l.jsx(p,{placeholder:"如：2021-2022-1"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"是否报名重修",name:"academicRetakeRegistered",children:l.jsxs(b.Group,{children:[l.jsx(b,{value:"是",children:"是"}),l.jsx(b,{value:"否",children:"否"})]})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"预计可毕业时间",name:"academicExpectedGraduation",children:l.jsx(p,{placeholder:"如：2025年6月"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"是否欠缴学费",name:"academicTuitionOwed",children:l.jsxs(b.Group,{children:[l.jsx(b,{value:"是",children:"是"}),l.jsx(b,{value:"否",children:"否"})]})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"欠缴金额",name:"academicOwedAmount",children:l.jsx(p,{placeholder:"请输入欠缴金额"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"辅导员/联系电话",name:"academicCounselorContact",children:l.jsx(p,{placeholder:"如：张老师/13700137001"})})}),l.jsx(j,{xs:24,children:l.jsx(a.Item,{label:"其他情况",name:"academicOtherSituation",children:l.jsx(A,{rows:3,placeholder:"请输入其他需要说明的学业情况"})})})]})},{key:"religion",label:l.jsxs("span",{children:[l.jsx(O,{}),"宗教信仰"]}),children:l.jsxs(h,{gutter:[24,16],children:[l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"有无宗教信仰",name:"religiousBelief",children:l.jsx(p,{placeholder:"请输入宗教信仰情况"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"是否参加过宗教活动",name:"religiousActivity",children:l.jsxs(b.Group,{children:[l.jsx(b,{value:"是",children:"是"}),l.jsx(b,{value:"否",children:"否"})]})})}),l.jsx(j,{xs:24,children:l.jsx(a.Item,{label:"直系亲属或近亲属是否为宗教教职人员或有宗教背景",name:"familyReligious",children:l.jsxs(b.Group,{children:[l.jsx(b,{value:"是",children:"是"}),l.jsx(b,{value:"否",children:"否"})]})})})]})},{key:"financial",label:l.jsxs("span",{children:[l.jsx(G,{}),"经济状况"]}),children:l.jsxs(h,{gutter:[24,16],children:[l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"是否足额缴纳本学年学杂费",name:"tuitionPaid",children:l.jsxs(b.Group,{children:[l.jsx(b,{value:"是",children:"是"}),l.jsx(b,{value:"否",children:"否"})]})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"是否有贷款",name:"hasLoan",children:l.jsxs(b.Group,{children:[l.jsx(b,{value:"是",children:"是"}),l.jsx(b,{value:"否",children:"否"})]})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"是否有助学金",name:"hasGrant",children:l.jsxs(b.Group,{children:[l.jsx(b,{value:"是",children:"是"}),l.jsx(b,{value:"否",children:"否"})]})})})]})},{key:"international",label:l.jsxs("span",{children:[l.jsx(w,{}),"国际教育"]}),children:l.jsxs(h,{gutter:[24,16],children:[l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"是否有护照",name:"hasPassport",children:l.jsxs(b.Group,{children:[l.jsx(b,{value:"是",children:"是"}),l.jsx(b,{value:"否",children:"否"})]})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"是否有出国经历",name:"abroadExperience",children:l.jsxs(b.Group,{children:[l.jsx(b,{value:"是",children:"是"}),l.jsx(b,{value:"否",children:"否"})]})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"是否报名本（专）硕直通",name:"graduateProgram",children:l.jsxs(b.Group,{children:[l.jsx(b,{value:"是",children:"是"}),l.jsx(b,{value:"否",children:"否"})]})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(a.Item,{label:"是否报名微留学",name:"microStudyAbroad",children:l.jsxs(b.Group,{children:[l.jsx(b,{value:"是",children:"是"}),l.jsx(b,{value:"否",children:"否"})]})})})]})},{key:"counselor",label:l.jsxs("span",{children:[l.jsx(k,{}),"辅导员信息"]}),children:l.jsxs(h,{gutter:[24,16],children:[l.jsx(j,{xs:24,md:12,children:l.jsx(a.Item,{label:"辅导员姓名",name:"counselorName",children:l.jsx(p,{placeholder:"请输入辅导员姓名"})})}),l.jsx(j,{xs:24,md:12,children:l.jsx(a.Item,{label:"辅导员联系方式",name:"counselorPhone",children:l.jsx(p,{placeholder:"请输入辅导员联系方式"})})})]})},{key:"other",label:l.jsxs("span",{children:[l.jsx(N,{}),"其他信息"]}),children:l.jsxs(h,{gutter:[24,16],children:[l.jsx(j,{xs:24,children:l.jsx(a.Item,{label:"其它需要说明的问题",name:"otherNotes",children:l.jsx(A,{rows:4,placeholder:"请输入其它需要说明的问题"})})}),l.jsx(j,{xs:24,children:l.jsx(a.Item,{label:"备注",name:"remarks",children:l.jsx(A,{rows:4,placeholder:"请输入备注信息"})})})]})}]})})})]})};export{J as default};
