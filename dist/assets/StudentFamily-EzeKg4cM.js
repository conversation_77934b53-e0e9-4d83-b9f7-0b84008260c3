import{c as e,u as s,j as l}from"./index-DP6eZxW9.js";import{aq as r,a0 as a,r as n,S as t,F as i,ae as d,a as c,aC as o,aB as h,Z as m,G as x,H as j,I as u,U as p,aK as g,aI as f}from"./antd-DYv0PFJq.js";import{g as y}from"./studentService-DEbeeazR.js";import"./vendor-D2RBMdQ0.js";const{TextArea:b}=u,I=()=>{const{id:I}=e(),k=s(),[S]=r.useForm(),{message:P}=a.useApp(),[N,C]=n.useState(null),[v,F]=n.useState(null),[w,W]=n.useState(!0),[$,A]=n.useState(!1),[R,M]=n.useState(!1);n.useEffect(()=>{(async()=>{if(I)try{console.log(`🔍 [StudentFamily] 获取学生数据，学号: ${I}`);const e=await y(I);if(e){console.log("✅ [StudentFamily] 学生数据获取成功:",e),C({id:e.id,name:e.name,studentId:e.studentId,class:e.class,college:e.college,major:e.major,grade:e.grade});const s={householdLocation:e.householdLocation||e.birthplace||"",currentAddress:e.homeAddress||"",fatherName:e.fatherName||"",fatherPhone:e.fatherPhone||"",fatherWork:e.fatherWork||"",motherName:e.motherName||"",motherPhone:e.motherPhone||"",motherWork:e.motherWork||"",isParentsDisabled:"是"===e.parentsDisabled,isSingleParent:"是"===e.singleParent,guardianName:e.guardianName||"",guardianRelation:e.guardianRelation||"",guardianPhone:e.guardianPhone||"",familyMembers:e.familyMembers||"",incomeSource:e.tuitionSource||"",familyIncome:e.familyIncome||""};console.log("📋 [StudentFamily] 家庭信息数据:",s),F(s),S.setFieldsValue(s)}else console.error(`❌ [StudentFamily] 未找到学号为 ${I} 的学生信息`),P.error(`未找到学号为 ${I} 的学生信息`)}catch(e){console.error("💥 [StudentFamily] 获取学生数据失败:",e),P.error("获取学生信息失败")}finally{W(!1)}else W(!1)})()},[I,S]),n.useEffect(()=>{v&&S.setFieldsValue(v)},[S,v]);return w?l.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"400px"},children:l.jsx(t,{size:"large",tip:"正在加载学生信息...",children:l.jsx("div",{style:{height:"200px"}})})}):N?l.jsxs("div",{className:"space-y-6",children:[l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center space-x-4",children:[l.jsx(c,{icon:l.jsx(o,{}),onClick:()=>k(`/students/${I}`),children:"返回"}),l.jsxs("div",{children:[l.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"家庭信息管理"}),l.jsxs("p",{className:"text-gray-600",children:["管理学生 ",N.name," 的家庭信息"]})]})]}),!$&&l.jsx(c,{type:"primary",icon:l.jsx(h,{}),onClick:()=>A(!0),children:"编辑信息"})]}),l.jsx(i,{title:"学生基本信息",style:{borderRadius:"8px"},children:l.jsxs(m,{column:3,children:[l.jsx(m.Item,{label:"姓名",children:N.name}),l.jsx(m.Item,{label:"学号",children:N.studentId}),l.jsx(m.Item,{label:"班级",children:N.class}),l.jsx(m.Item,{label:"学院",children:N.college}),l.jsx(m.Item,{label:"专业",children:N.major}),l.jsx(m.Item,{label:"年级",children:N.grade})]})}),l.jsx(i,{title:"家庭信息",style:{borderRadius:"8px"},extra:$&&l.jsxs(d,{children:[l.jsx(c,{onClick:()=>{S.resetFields(),v&&S.setFieldsValue(v),A(!1)},children:"取消"}),l.jsx(c,{type:"primary",icon:l.jsx(f,{}),loading:R,onClick:()=>S.submit(),children:"保存"})]}),children:l.jsxs(r,{form:S,layout:"vertical",onFinish:async e=>{if(I){M(!0);try{await new Promise(e=>setTimeout(e,1e3)),F(e),P.success("家庭信息更新成功！"),A(!1)}catch(s){P.error("更新失败，请重试")}finally{M(!1)}}},disabled:!$,children:[l.jsxs(x,{gutter:[24,16],children:[l.jsx(j,{span:24,children:l.jsx(r.Item,{label:"户籍所在地",name:"householdLocation",rules:[{required:!0,message:"请输入户籍所在地"}],children:l.jsx(u,{placeholder:"请输入户籍所在地"})})}),l.jsx(j,{span:24,children:l.jsx(r.Item,{label:"现居住地址",name:"currentAddress",rules:[{required:!0,message:"请输入现居住地址"}],children:l.jsx(b,{rows:2,placeholder:"请输入现居住地址"})})})]}),l.jsx(p,{orientation:"left",children:"父亲信息"}),l.jsxs(x,{gutter:[24,16],children:[l.jsx(j,{xs:24,md:8,children:l.jsx(r.Item,{label:"父亲姓名",name:"fatherName",children:l.jsx(u,{placeholder:"请输入父亲姓名"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(r.Item,{label:"父亲联系电话",name:"fatherPhone",rules:[{pattern:/^1[3-9]\d{9}$/,message:"手机号码格式不正确"}],children:l.jsx(u,{placeholder:"请输入父亲联系电话"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(r.Item,{label:"父亲工作单位",name:"fatherWork",children:l.jsx(u,{placeholder:"请输入父亲工作单位"})})})]}),l.jsx(p,{orientation:"left",children:"母亲信息"}),l.jsxs(x,{gutter:[24,16],children:[l.jsx(j,{xs:24,md:8,children:l.jsx(r.Item,{label:"母亲姓名",name:"motherName",children:l.jsx(u,{placeholder:"请输入母亲姓名"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(r.Item,{label:"母亲联系电话",name:"motherPhone",rules:[{pattern:/^1[3-9]\d{9}$/,message:"手机号码格式不正确"}],children:l.jsx(u,{placeholder:"请输入母亲联系电话"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(r.Item,{label:"母亲工作单位",name:"motherWork",children:l.jsx(u,{placeholder:"请输入母亲工作单位"})})})]}),l.jsx(p,{orientation:"left",children:"特殊情况"}),l.jsxs(x,{gutter:[24,16],children:[l.jsx(j,{xs:24,md:12,children:l.jsx(r.Item,{label:"父母是否有残疾",name:"isParentsDisabled",valuePropName:"checked",children:l.jsx(g,{checkedChildren:"是",unCheckedChildren:"否"})})}),l.jsx(j,{xs:24,md:12,children:l.jsx(r.Item,{label:"是否单亲家庭",name:"isSingleParent",valuePropName:"checked",children:l.jsx(g,{checkedChildren:"是",unCheckedChildren:"否"})})})]}),l.jsx(p,{orientation:"left",children:"监护人信息"}),l.jsxs(x,{gutter:[24,16],children:[l.jsx(j,{xs:24,md:8,children:l.jsx(r.Item,{label:"监护人姓名",name:"guardianName",children:l.jsx(u,{placeholder:"请输入监护人姓名（如非父母）"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(r.Item,{label:"与学生关系",name:"guardianRelation",children:l.jsx(u,{placeholder:"请输入与学生关系"})})}),l.jsx(j,{xs:24,md:8,children:l.jsx(r.Item,{label:"监护人联系电话",name:"guardianPhone",rules:[{pattern:/^1[3-9]\d{9}$/,message:"手机号码格式不正确"}],children:l.jsx(u,{placeholder:"请输入监护人联系电话"})})})]}),l.jsx(p,{orientation:"left",children:"其他信息"}),l.jsxs(x,{gutter:[24,16],children:[l.jsx(j,{span:24,children:l.jsx(r.Item,{label:"家庭成员情况",name:"familyMembers",children:l.jsx(b,{rows:3,placeholder:"请详细描述家庭成员情况，包括人数、年龄、职业等"})})}),l.jsx(j,{span:24,children:l.jsx(r.Item,{label:"家庭收入来源",name:"incomeSource",children:l.jsx(b,{rows:2,placeholder:"请描述家庭主要收入来源"})})})]})]})})]}):l.jsx("div",{style:{padding:"24px"},children:l.jsxs(i,{style:{textAlign:"center"},children:[l.jsx("div",{style:{color:"#ff4d4f",fontSize:"18px",fontWeight:600,marginBottom:"16px"},children:"学生信息不存在"}),l.jsxs(d,{children:[l.jsx(c,{onClick:()=>k("/students"),children:"返回学生列表"}),l.jsx(c,{type:"primary",onClick:()=>window.location.reload(),children:"重新加载"})]})]})})};export{I as default};
