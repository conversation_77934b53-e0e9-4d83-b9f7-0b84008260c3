import{c as e,u as s,e as a,f as l,g as r,j as t,h as i,i as d}from"./index-DXaqwR6F-1753160720439.js";import{aq as n,a0 as c,r as o,G as m,H as h,ax as x,A as j,ao as u,I as p,as as g,aH as v,a as b,aC as y,F as I,av as f,U as q,aI as w}from"./antd-lXsGnH6e-1753160720439.js";import"./vendor-D2RBMdQ0-1753160720439.js";const S={politicalStatuses:["群众","共青团员","入党积极分子","中共预备党员","中共党员","民主党派","无党派人士"],ethnicities:["汉族","蒙古族","回族","藏族","维吾尔族","苗族","彝族","壮族","布依族","朝鲜族","满族","侗族","瑶族","白族","土家族","哈尼族","哈萨克族","傣族","黎族","傈僳族","佤族","畲族","高山族","拉祜族","水族","东乡族","纳西族","景颇族","柯尔克孜族","土族","达斡尔族","仫佬族","羌族","布朗族","撒拉族","毛南族","仡佬族","锡伯族","阿昌族","普米族","塔吉克族","怒族","乌孜别克族","俄罗斯族","鄂温克族","德昂族","保安族","裕固族","京族","塔塔尔族","独龙族","鄂伦春族","赫哲族","门巴族","珞巴族","基诺族"]},{Option:N}=g,{TextArea:D}=p,{Step:C}=f,$=()=>{const{id:D}=e(),$=s(),F=a(),[_]=n.useForm(),{message:A}=c.useApp(),{currentStudent:Q,loading:T}=l(e=>e.student),[Y,k]=o.useState(0),[V,E]=o.useState(!1),[H,M]=o.useState(""),U=!!D&&"new"!==D,z=U?"编辑学生信息":"新增学生";o.useEffect(()=>{U&&D&&F(r(D))},[F,U,D]),o.useEffect(()=>{U&&Q&&(_.setFieldsValue({...Q,birthDate:Q.birthDate?new Date(Q.birthDate):null}),M(Q.avatar||""))},[_,U,Q]);const G=[{title:"基本信息",content:t.jsxs(m,{gutter:[24,16],children:[t.jsx(h,{span:24,className:"text-center mb-6",children:t.jsx(x,{name:"avatar",listType:"picture-card",className:"avatar-uploader",showUploadList:!1,action:"/api/upload/avatar",onChange:e=>{var s;"done"===e.file.status?(M((null==(s=e.file.response)?void 0:s.url)||""),A.success("头像上传成功")):"error"===e.file.status&&A.error("头像上传失败")},children:H?t.jsx(j,{src:H,size:100}):t.jsxs("div",{children:[t.jsx(u,{}),t.jsx("div",{style:{marginTop:8},children:"上传头像"})]})})}),t.jsx(h,{xs:24,md:12,children:t.jsx(n.Item,{label:"学号",name:"studentId",rules:[{required:!0,message:"请输入学号"},{pattern:/^\d{10}$/,message:"学号必须为10位数字"}],children:t.jsx(p,{placeholder:"请输入学号"})})}),t.jsx(h,{xs:24,md:12,children:t.jsx(n.Item,{label:"姓名",name:"name",rules:[{required:!0,message:"请输入姓名"}],children:t.jsx(p,{placeholder:"请输入姓名"})})}),t.jsx(h,{xs:24,md:12,children:t.jsx(n.Item,{label:"性别",name:"gender",rules:[{required:!0,message:"请选择性别"}],children:t.jsxs(g,{placeholder:"请选择性别",children:[t.jsx(N,{value:"male",children:"男"}),t.jsx(N,{value:"female",children:"女"})]})})}),t.jsx(h,{xs:24,md:12,children:t.jsx(n.Item,{label:"出生日期",name:"birthDate",rules:[{required:!0,message:"请选择出生日期"}],children:t.jsx(v,{placeholder:"请选择出生日期",style:{width:"100%"}})})}),t.jsx(h,{xs:24,md:12,children:t.jsx(n.Item,{label:"身份证号",name:"idCard",rules:[{required:!0,message:"请输入身份证号"},{pattern:/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,message:"身份证号格式不正确"}],children:t.jsx(p,{placeholder:"请输入身份证号"})})}),t.jsx(h,{xs:24,md:12,children:t.jsx(n.Item,{label:"民族",name:"ethnicity",rules:[{required:!0,message:"请选择民族"}],children:t.jsx(g,{placeholder:"请选择民族",showSearch:!0,children:S.ethnicities.map(e=>t.jsx(N,{value:e,children:e},e))})})}),t.jsx(h,{xs:24,md:12,children:t.jsx(n.Item,{label:"政治面貌",name:"politicalStatus",rules:[{required:!0,message:"请选择政治面貌"}],children:t.jsx(g,{placeholder:"请选择政治面貌",children:S.politicalStatuses.map(e=>t.jsx(N,{value:e,children:e},e))})})}),t.jsx(h,{xs:24,md:12,children:t.jsx(n.Item,{label:"职务",name:"position",children:t.jsx(p,{placeholder:"请输入职务（可选）"})})})]})},{title:"学业信息",content:t.jsxs(m,{gutter:[24,16],children:[t.jsx(h,{xs:24,md:12,children:t.jsx(n.Item,{label:"学院",name:"college",rules:[{required:!0,message:"请输入学院"}],children:t.jsx(p,{placeholder:"请输入学院"})})}),t.jsx(h,{xs:24,md:12,children:t.jsx(n.Item,{label:"专业",name:"major",rules:[{required:!0,message:"请输入专业"}],children:t.jsx(p,{placeholder:"请输入专业"})})}),t.jsx(h,{xs:24,md:12,children:t.jsx(n.Item,{label:"班级",name:"class",rules:[{required:!0,message:"请输入班级"}],children:t.jsx(p,{placeholder:"请输入班级"})})}),t.jsx(h,{xs:24,md:12,children:t.jsx(n.Item,{label:"年级",name:"grade",rules:[{required:!0,message:"请输入年级"}],children:t.jsxs(g,{placeholder:"请选择年级",children:[t.jsx(N,{value:"2021",children:"2021级"}),t.jsx(N,{value:"2022",children:"2022级"}),t.jsx(N,{value:"2023",children:"2023级"}),t.jsx(N,{value:"2024",children:"2024级"})]})})})]})},{title:"联系信息",content:t.jsxs(m,{gutter:[24,16],children:[t.jsx(h,{xs:24,md:12,children:t.jsx(n.Item,{label:"手机号码",name:"phone",rules:[{required:!0,message:"请输入手机号码"},{pattern:/^1[3-9]\d{9}$/,message:"手机号码格式不正确"}],children:t.jsx(p,{placeholder:"请输入手机号码"})})}),t.jsx(h,{xs:24,md:12,children:t.jsx(n.Item,{label:"邮箱",name:"email",rules:[{type:"email",message:"邮箱格式不正确"}],children:t.jsx(p,{placeholder:"请输入邮箱（可选）"})})}),t.jsx(h,{xs:24,md:12,children:t.jsx(n.Item,{label:"QQ号",name:"qq",children:t.jsx(p,{placeholder:"请输入QQ号（可选）"})})}),t.jsx(h,{xs:24,md:12,children:t.jsx(n.Item,{label:"微信号",name:"wechat",children:t.jsx(p,{placeholder:"请输入微信号（可选）"})})}),t.jsx(h,{xs:24,md:12,children:t.jsx(n.Item,{label:"宿舍楼",name:"dormitory",children:t.jsx(p,{placeholder:"请输入宿舍楼（可选）"})})}),t.jsx(h,{xs:24,md:12,children:t.jsx(n.Item,{label:"房间号",name:"room",children:t.jsx(p,{placeholder:"请输入房间号（可选）"})})})]})}];return t.jsxs("div",{className:"space-y-6",children:[t.jsx("div",{className:"flex items-center justify-between",children:t.jsxs("div",{className:"flex items-center space-x-4",children:[t.jsx(b,{icon:t.jsx(y,{}),onClick:()=>$("/students"),children:"返回"}),t.jsxs("div",{children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:z}),t.jsx("p",{className:"text-gray-600",children:U?"修改学生的基本信息":"录入新学生的基本信息"})]})]})}),t.jsxs(I,{style:{borderRadius:"8px"},children:[t.jsx(f,{current:Y,className:"mb-8",children:G.map(e=>t.jsx(C,{title:e.title},e.title))}),t.jsxs(n,{form:_,layout:"vertical",onFinish:async e=>{var s,a,l,r;E(!0);try{console.log("📝 表单原始数据:",e),console.log("📝 表单字段检查:",{studentId:e.studentId,name:e.name,gender:e.gender,college:e.college,major:e.major,class:e.class,grade:e.grade});const s=_.getFieldsValue();console.log("📋 表单实例数据:",s);const a={student_id:e.studentId,name:e.name,gender:e.gender,birth_date:e.birthDate?e.birthDate.format("YYYY-MM-DD"):"",id_card:e.idCard,ethnicity:e.ethnicity,political_status:e.politicalStatus,position:e.position||"",college:e.college,major:e.major,class:e.class,grade:e.grade,qq:e.qq||"",wechat:e.wechat||"",phone:e.phone,email:e.email||"",dormitory:e.dormitory||"",room:e.room||"",status:e.status||"active",avatar:H||""};console.log("🚀 转换后的学生数据:",a);const l=["student_id","name","gender","college","major","class","grade"].filter(e=>!a[e]);if(l.length>0)return console.error("❌ 缺少必填字段:",l),A.error(`请填写必填字段: ${l.join(", ")}`),void(l.some(e=>["student_id","name","gender"].includes(e))?k(0):l.some(e=>["college","major","class","grade"].includes(e))?k(1):k(2));U&&D?(await F(i({id:D,data:a})).unwrap(),A.success("学生信息更新成功！")):(await F(d(a)).unwrap(),A.success("学生信息创建成功！")),$("/students")}catch(t){console.error("❌ 提交失败:",t);let e=U?"更新失败，请重试":"创建失败，请重试";if((null==(a=null==(s=null==t?void 0:t.response)?void 0:s.data)?void 0:a.message)?e=t.response.data.message:(null==t?void 0:t.message)&&(e=t.message),(null==(r=null==(l=null==t?void 0:t.response)?void 0:l.data)?void 0:r.errors)&&Array.isArray(t.response.data.errors)){e=`验证失败: ${t.response.data.errors.map(e=>e.msg).join(", ")}`}A.error(e)}finally{E(!1)}},initialValues:{status:"active",gender:"male",politicalStatus:"群众",ethnicity:"汉族"},children:[t.jsx("div",{className:"min-h-96",children:G[Y].content}),t.jsx(q,{}),t.jsxs("div",{className:"flex justify-between",children:[t.jsx("div",{children:Y>0&&t.jsx(b,{onClick:()=>{k(Y-1)},children:"上一步"})}),t.jsxs("div",{className:"space-x-2",children:[Y<G.length-1&&t.jsx(b,{type:"primary",onClick:()=>{const e=([["studentId","name","gender","birthDate","idCard","ethnicity","politicalStatus","position"],["college","major","class","grade"],["phone","email","qq","wechat","dormitory","room"]][Y]||[]).filter(e=>0===Y?["studentId","name","gender","ethnicity","politicalStatus"].includes(e):1===Y?["college","major","class","grade"].includes(e):2===Y&&["phone"].includes(e));_.validateFields(e).then(()=>{k(Y+1)}).catch(()=>{A.error("请完善当前步骤的必填信息")})},children:"下一步"}),Y===G.length-1&&t.jsx(b,{type:"primary",htmlType:"submit",loading:V,icon:t.jsx(w,{}),children:U?"更新信息":"创建学生"})]})]})]})]})]})};export{$ as default};
