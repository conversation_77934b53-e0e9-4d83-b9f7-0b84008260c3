import{c as e,u as s,j as l}from"./index-DP6eZxW9.js";import{M as t,aq as r,a0 as i,r as a,S as n,F as d,ae as c,a as o,aC as h,ao as x,aB as j,Z as m,G as u,H as p,as as g,I as y,aI as f,ay as b,aH as I,Q as v,W as w}from"./antd-DYv0PFJq.js";import{g as k}from"./studentService-DEbeeazR.js";import"./vendor-D2RBMdQ0.js";const{TextArea:S}=y,{Option:C}=g,q=()=>{const[q,F]=t.useModal(),{id:H}=e(),A=s(),[D]=r.useForm(),[$]=r.useForm(),{message:B}=i.useApp(),[N,T]=a.useState(null),[M,O]=a.useState(null),[R,z]=a.useState(!0),[P,Y]=a.useState(!1),[L,Q]=a.useState(!1),[V,W]=a.useState(!1),[E,G]=a.useState([]);a.useEffect(()=>{(async()=>{if(H)try{console.log(`🔍 [StudentHealth] 获取学生数据，学号: ${H}`);const e=await k(H);if(e){console.log("✅ [StudentHealth] 学生数据获取成功:",e),T({id:e.id,name:e.name,studentId:e.studentId,class:e.class,college:e.college,major:e.major,grade:e.grade});const s={bloodType:e.bloodType||"",height:e.height||"",weight:e.weight||"",visionLeft:e.visionLeft||"",visionRight:e.visionRight||"",chronicDisease:e.chronicDisease||"",allergyHistory:e.allergyHistory||"",medication:e.medication||"",emergencyContact:e.emergencyContact||"",emergencyPhone:e.emergencyPhone||"",healthStatus:e.healthStatus||""};console.log("📋 [StudentHealth] 健康信息数据:",s),O(s),D.setFieldsValue(s)}else console.error(`❌ [StudentHealth] 未找到学号为 ${H} 的学生信息`),B.error(`未找到学号为 ${H} 的学生信息`)}catch(e){console.error("💥 [StudentHealth] 获取学生数据失败:",e),B.error("获取学生信息失败")}finally{z(!1)}else z(!1)})(),J()},[H,D]);const J=()=>{G([{id:"1",date:"2024-01-15",type:"体检",description:"年度体检",result:"健康",doctor:"李医生",hospital:"校医院"},{id:"2",date:"2024-03-20",type:"疫苗接种",description:"新冠疫苗第三针",result:"已接种",doctor:"王护士",hospital:"校医院"}])},K=[{title:"日期",dataIndex:"date",key:"date",width:120},{title:"类型",dataIndex:"type",key:"type",width:100,render:e=>l.jsx(v,{color:{"体检":"blue","疫苗接种":"green","就医":"orange","手术":"red","其他":"default"}[e]||"default",children:e})},{title:"描述",dataIndex:"description",key:"description"},{title:"结果",dataIndex:"result",key:"result",width:120},{title:"医生",dataIndex:"doctor",key:"doctor",width:100},{title:"医院",dataIndex:"hospital",key:"hospital",width:120},{title:"操作",key:"action",width:80,render:(e,s)=>l.jsx(o,{type:"link",danger:!0,icon:l.jsx(w,{}),onClick:()=>{return e=s.id,void q.confirm({title:"确认删除",content:"确定要删除这条健康记录吗？",onOk:()=>{G(E.filter(s=>s.id!==e)),B.success("删除成功")}});var e},children:"删除"})}];return R?l.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"400px"},children:l.jsx(n,{size:"large",tip:"正在加载学生信息...",children:l.jsx("div",{style:{height:"200px"}})})}):N?l.jsxs("div",{className:"space-y-6",children:[l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center space-x-4",children:[l.jsx(o,{icon:l.jsx(h,{}),onClick:()=>A(`/students/${H}`),children:"返回"}),l.jsxs("div",{children:[l.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"健康档案管理"}),l.jsxs("p",{className:"text-gray-600",children:["管理学生 ",N.name," 的健康档案信息"]})]})]}),l.jsxs(c,{children:[l.jsx(o,{type:"primary",icon:l.jsx(x,{}),onClick:()=>W(!0),children:"添加记录"}),!P&&l.jsx(o,{icon:l.jsx(j,{}),onClick:()=>Y(!0),children:"编辑档案"})]})]}),l.jsx(d,{title:"学生基本信息",style:{borderRadius:"8px"},children:l.jsxs(m,{column:3,children:[l.jsx(m.Item,{label:"姓名",children:N.name}),l.jsx(m.Item,{label:"学号",children:N.studentId}),l.jsx(m.Item,{label:"班级",children:N.class}),l.jsx(m.Item,{label:"学院",children:N.college}),l.jsx(m.Item,{label:"专业",children:N.major}),l.jsx(m.Item,{label:"年级",children:N.grade})]})}),l.jsx(d,{title:"基础健康信息",style:{borderRadius:"8px"},extra:P&&l.jsxs(c,{children:[l.jsx(o,{onClick:()=>Y(!1),children:"取消"}),l.jsx(o,{type:"primary",icon:l.jsx(f,{}),loading:L,onClick:()=>D.submit(),children:"保存"})]}),children:l.jsx(r,{form:D,layout:"vertical",onFinish:async e=>{if(H){Q(!0);try{B.success("健康档案更新成功！"),Y(!1)}catch(s){B.error("更新失败，请重试")}finally{Q(!1)}}},disabled:!P,initialValues:{bloodType:"O",height:"",weight:"",allergies:"",chronicDiseases:"",medications:"",emergencyContact:"",emergencyPhone:""},children:l.jsxs(u,{gutter:[24,16],children:[l.jsx(p,{xs:24,md:8,children:l.jsx(r.Item,{label:"血型",name:"bloodType",rules:[{required:!0,message:"请选择血型"}],children:l.jsxs(g,{placeholder:"请选择血型",children:[l.jsx(C,{value:"A",children:"A型"}),l.jsx(C,{value:"B",children:"B型"}),l.jsx(C,{value:"AB",children:"AB型"}),l.jsx(C,{value:"O",children:"O型"}),l.jsx(C,{value:"unknown",children:"未知"})]})})}),l.jsx(p,{xs:24,md:8,children:l.jsx(r.Item,{label:"身高 (cm)",name:"height",rules:[{pattern:/^\d+$/,message:"请输入正确的身高"}],children:l.jsx(y,{placeholder:"请输入身高"})})}),l.jsx(p,{xs:24,md:8,children:l.jsx(r.Item,{label:"体重 (kg)",name:"weight",rules:[{pattern:/^\d+(\.\d+)?$/,message:"请输入正确的体重"}],children:l.jsx(y,{placeholder:"请输入体重"})})}),l.jsx(p,{span:24,children:l.jsx(r.Item,{label:"过敏史",name:"allergies",children:l.jsx(S,{rows:2,placeholder:"请描述过敏情况，如无过敏史请填写'无'"})})}),l.jsx(p,{span:24,children:l.jsx(r.Item,{label:"慢性疾病史",name:"chronicDiseases",children:l.jsx(S,{rows:2,placeholder:"请描述慢性疾病情况，如无请填写'无'"})})}),l.jsx(p,{span:24,children:l.jsx(r.Item,{label:"长期用药情况",name:"medications",children:l.jsx(S,{rows:2,placeholder:"请描述长期用药情况，如无请填写'无'"})})}),l.jsx(p,{xs:24,md:12,children:l.jsx(r.Item,{label:"紧急联系人",name:"emergencyContact",rules:[{required:!0,message:"请输入紧急联系人"}],children:l.jsx(y,{placeholder:"请输入紧急联系人姓名"})})}),l.jsx(p,{xs:24,md:12,children:l.jsx(r.Item,{label:"紧急联系电话",name:"emergencyPhone",rules:[{required:!0,message:"请输入紧急联系电话"},{pattern:/^1[3-9]\d{9}$/,message:"手机号码格式不正确"}],children:l.jsx(y,{placeholder:"请输入紧急联系电话"})})})]})})}),l.jsx(d,{title:"健康记录",style:{borderRadius:"8px"},children:l.jsx(b,{columns:K,dataSource:E,rowKey:"id",pagination:{pageSize:10,showSizeChanger:!0,showQuickJumper:!0}})}),l.jsx(t,{title:"添加健康记录",open:V,onCancel:()=>{W(!1),$.resetFields()},footer:null,width:600,children:l.jsxs(r,{form:$,layout:"vertical",onFinish:async e=>{try{const s={id:Date.now().toString(),...e,date:e.date.format("YYYY-MM-DD")};G([s,...E]),W(!1),$.resetFields(),B.success("健康记录添加成功！")}catch(s){B.error("添加失败，请重试")}},children:[l.jsxs(u,{gutter:[16,16],children:[l.jsx(p,{xs:24,md:12,children:l.jsx(r.Item,{label:"日期",name:"date",rules:[{required:!0,message:"请选择日期"}],children:l.jsx(I,{style:{width:"100%"}})})}),l.jsx(p,{xs:24,md:12,children:l.jsx(r.Item,{label:"类型",name:"type",rules:[{required:!0,message:"请选择类型"}],children:l.jsxs(g,{placeholder:"请选择类型",children:[l.jsx(C,{value:"体检",children:"体检"}),l.jsx(C,{value:"疫苗接种",children:"疫苗接种"}),l.jsx(C,{value:"就医",children:"就医"}),l.jsx(C,{value:"手术",children:"手术"}),l.jsx(C,{value:"其他",children:"其他"})]})})}),l.jsx(p,{span:24,children:l.jsx(r.Item,{label:"描述",name:"description",rules:[{required:!0,message:"请输入描述"}],children:l.jsx(S,{rows:2,placeholder:"请输入详细描述"})})}),l.jsx(p,{span:24,children:l.jsx(r.Item,{label:"结果",name:"result",rules:[{required:!0,message:"请输入结果"}],children:l.jsx(y,{placeholder:"请输入检查结果或处理结果"})})}),l.jsx(p,{xs:24,md:12,children:l.jsx(r.Item,{label:"医生",name:"doctor",rules:[{required:!0,message:"请输入医生姓名"}],children:l.jsx(y,{placeholder:"请输入医生姓名"})})}),l.jsx(p,{xs:24,md:12,children:l.jsx(r.Item,{label:"医院",name:"hospital",rules:[{required:!0,message:"请输入医院名称"}],children:l.jsx(y,{placeholder:"请输入医院名称"})})})]}),l.jsxs("div",{className:"flex justify-end space-x-2 mt-4",children:[l.jsx(o,{onClick:()=>{W(!1),$.resetFields()},children:"取消"}),l.jsx(o,{type:"primary",htmlType:"submit",children:"添加"})]})]})}),F]}):l.jsx("div",{style:{padding:"24px"},children:l.jsxs(d,{style:{textAlign:"center"},children:[l.jsx("div",{style:{color:"#ff4d4f",fontSize:"18px",fontWeight:600,marginBottom:"16px"},children:"学生信息不存在"}),l.jsxs(c,{children:[l.jsx(o,{onClick:()=>A("/students"),children:"返回学生列表"}),l.jsx(o,{type:"primary",onClick:()=>window.location.reload(),children:"重新加载"})]})]})})};export{q as default};
