import{j as e,a as t,w as s,b as a,r as l,u as r}from"./index-Cu_U9Dm3.js";import{r as i,at as n,M as o,F as c,E as d,ae as u,a as h,au as m,_ as g,av as y,aw as x,L as p,ax as b,O as j,ay as k,N as S,ag as f,a0 as w,I as v,G as I,H as _,as as N,ao as q,V as C,D as O,az as J,aA as A,W as $,A as R,Q as L,af as P,aB as E}from"./antd-DYv0PFJq.js";import{u as z}from"./useMessage-DDIprusu.js";import"./vendor-D2RBMdQ0.js";const D=(e,t="学生信息")=>{try{const s=["学号","姓名","性别","专业","班级","年级","手机号码","邮箱","状态"],a=e.map(e=>[e.studentId,e.name,"male"===e.gender?"男":"女",e.major,e.class,e.grade,e.phone,e.email||"",T(e.status)]),l=[s.join(","),...a.map(e=>e.map(e=>`"${e}"`).join(","))].join("\n"),r=new Blob(["\ufeff"+l],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a"),n=URL.createObjectURL(r);return i.setAttribute("href",n),i.setAttribute("download",`${t}.csv`),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i),!0}catch(s){return console.error("导出失败:",s),!1}},T=e=>({active:"正常",warning:"预警",inactive:"异常"}[e]||e),{Step:M}=y,{Title:W,Text:B}=d,U=[{key:"index",label:"序号",required:!0},{key:"studentId",label:"学号",required:!0},{key:"name",label:"姓名",required:!0},{key:"gender",label:"性别",required:!0},{key:"ethnicity",label:"民族",required:!1},{key:"birthplace",label:"籍贯",required:!1},{key:"politicalStatus",label:"政治面貌",required:!1},{key:"position",label:"职务",required:!1},{key:"college",label:"学院",required:!0},{key:"level",label:"层次",required:!0},{key:"grade",label:"年级",required:!0},{key:"major",label:"专业",required:!0},{key:"class",label:"班级",required:!0},{key:"qq",label:"QQ号",required:!1},{key:"wechat",label:"微信号",required:!1},{key:"phone",label:"手机号码",required:!0},{key:"idCard",label:"身份证号码",required:!0},{key:"dormBuilding",label:"楼栋号",required:!1},{key:"dormRoom",label:"宿舍号",required:!1},{key:"householdLocation",label:"户口所在地",required:!1},{key:"homeAddress",label:"现家庭住址（详细到门牌号）",required:!1},{key:"fatherName",label:"父亲姓名",required:!1},{key:"fatherPhone",label:"父亲联系方式",required:!1},{key:"fatherWork",label:"父亲工作单位",required:!1},{key:"motherName",label:"母亲姓名",required:!1},{key:"motherPhone",label:"母亲联系电话",required:!1},{key:"motherWork",label:"母亲工作单位",required:!1},{key:"parentsDisabled",label:"父母是否为残疾",required:!1},{key:"singleParent",label:"是否为单亲家庭",required:!1},{key:"guardianName",label:"监护人姓名",required:!1},{key:"guardianRelation",label:"与本人关系",required:!1},{key:"familyMembers",label:"家庭其他成员及联系方式",required:!1},{key:"tuitionSource",label:"学费、生活费来源",required:!1},{key:"martyrChild",label:"是否为烈士子女",required:!1},{key:"orphan",label:"是否为孤儿",required:!1},{key:"caregiverName",label:"抚养人姓名",required:!1},{key:"caregiverRelation2",label:"与本人关系",required:!1},{key:"caregiverPhone",label:"抚养人联系方式",required:!1},{key:"orphanDescription",label:"具体情况说明",required:!1},{key:"disabled",label:"是否残疾",required:!1},{key:"disabilityType",label:"残疾类别",required:!1},{key:"disabilityLevel",label:"残疾等级",required:!1},{key:"disabilityDescription2",label:"具体情况说明",required:!1},{key:"healthCondition",label:"患有基础性疾病情况",required:!1},{key:"dayStudent",label:"是否办理过走读",required:!1},{key:"academicStatus",label:"学业现状描述",required:!1},{key:"disciplinaryAction",label:"有无违纪",required:!1},{key:"minorProgram",label:"是否报名辅修",required:!1},{key:"religiousBelief",label:"有无宗教信仰",required:!1},{key:"religiousActivity",label:"是否参加过宗教活动",required:!1},{key:"familyReligious",label:"直系亲属或近亲属是否为宗教教职人员或有宗教背景",required:!1},{key:"tuitionPaid",label:"是否足额缴纳本学年学杂费",required:!1},{key:"hasLoan",label:"是否有贷款",required:!1},{key:"hasGrant",label:"是否有助学金",required:!1},{key:"hasPassport",label:"是否有护照",required:!1},{key:"abroadExperience",label:"是否有出国经历",required:!1},{key:"graduateProgram",label:"是否报名本（专）硕直通",required:!1},{key:"microStudyAbroad",label:"是否报名微留学",required:!1},{key:"counselorName",label:"辅导员姓名",required:!1},{key:"counselorPhone",label:"辅导员联系方式",required:!1},{key:"otherNotes",label:"其它需要说明的问题",required:!1},{key:"remarks",label:"备注",required:!1}],H=({visible:r,onCancel:d,onImportSuccess:w,onExportData:v=[]})=>{const I=z(),[_,N]=i.useState(0),[q,C]=i.useState(!1),[O,J]=i.useState([]),[A,$]=i.useState([]),[R,L]=i.useState(0),[P,E]=i.useState(!1),[D,T]=i.useState({success:!1});n.useEffect(()=>{r||(N(0),J([]),$([]),L(0),E(!1),T({success:!1}))},[r]),n.useEffect(()=>{P&&D.success&&(w(O),setTimeout(()=>{d()},1500),E(!1))},[P,D,O,w,d]);const H=e=>{const t=[];e.forEach((e,s)=>{const a=[];U.forEach(t=>{t.required&&!e[t.key]&&a.push(`${t.label}不能为空`)}),e.studentId&&!/^[A-Za-z0-9]{8,12}$/.test(e.studentId)&&a.push("学号格式不正确（应为8-12位字母数字组合）"),e.phone&&!/^1[3-9]\d{9}$/.test(e.phone)&&a.push("手机号格式不正确"),e.idCard&&!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(e.idCard)&&a.push("身份证号格式不正确"),a.length>0&&t.push({row:s+1,originalRow:e.originalIndex,studentId:e.studentId,name:e.name,errors:a})}),$(t)};return e.jsx(o,{title:"学生信息导入导出",open:r,onCancel:()=>{N(0),J([]),$([]),L(0),d()},width:800,footer:null,children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs(c,{children:[e.jsx(W,{level:4,children:"选择操作"}),e.jsxs(u,{size:"large",wrap:!0,children:[e.jsx(h,{type:"primary",icon:e.jsx(m,{}),onClick:()=>{const e=[["","基本信息","","","","","","","","","","","","","","","","","","家庭信息","","","","","","","","","","","","","","孤残情况","","","","","","","","","","身体健康情况","","学业及违纪","","","宗教信仰情况","","","经济状况","","","国际教育情况","","","","辅导员信息","","其它需要说明的问题","备注"],U.map(e=>e.required?`${e.label}*`:e.label),["说明：带*号的字段为必填项","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","",""]],a=t.aoa_to_sheet(e),l=t.book_new();t.book_append_sheet(l,a,"Sheet1");const r=U.map(()=>({wch:15}));a["!cols"]=r,s(l,"学生信息导入模板.xlsx"),I.success("模板下载成功")},children:"下载完整模板"}),e.jsx(h,{icon:e.jsx(m,{}),onClick:()=>{const e=[[{key:"index",label:"序号*"},{key:"studentId",label:"学号*"},{key:"name",label:"姓名*"},{key:"gender",label:"性别*"},{key:"college",label:"学院*"},{key:"major",label:"专业*"},{key:"class",label:"班级*"},{key:"grade",label:"年级*"},{key:"phone",label:"手机号码*"},{key:"idCard",label:"身份证号码*"}].map(e=>e.label),["1","2021001001","张三","男","计算机学院","计算机科学与技术","计科2101","2021","13800138000","110101199001011234"],["2","2021001002","李四","女","计算机学院","软件工程","软工2101","2021","13800138001","110101199001011235"]],a=t.aoa_to_sheet(e),l=t.book_new();t.book_append_sheet(l,a,"Sheet1"),s(l,"学生信息导入简化模板.xlsx"),I.success("简化模板下载成功")},children:"下载简化模板（测试用）"}),e.jsx(h,{icon:e.jsx(g,{}),onClick:()=>{if(0===v.length)return void I.warning("没有可导出的数据");const e=[...[["","基本信息","","","","","","","","","","","","","","","","","","家庭信息","","","","","","","","","","","","","","孤残情况","","","","","","","","","","身体健康情况","","学业及违纪","","","宗教信仰情况","","","经济状况","","","国际教育情况","","","","辅导员信息","","其它需要说明的问题","备注"],U.map(e=>e.required?`${e.label}*`:e.label),["说明：带*号的字段为必填项","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","",""]],...v.map((e,t)=>U.map(s=>"index"===s.key?t+1:e[s.key]||""))],a=t.aoa_to_sheet(e),l=t.book_new();t.book_append_sheet(l,a,"Sheet1");const r=U.map(()=>({wch:15}));a["!cols"]=r,s(l,`学生信息导出_${(new Date).toISOString().split("T")[0]}.xlsx`),I.success("数据导出成功")},disabled:0===v.length,children:"导出学生数据"})]})]}),e.jsxs(c,{children:[e.jsx(W,{level:4,children:"导入学生信息"}),e.jsxs(y,{current:_,className:"mb-6",children:[e.jsx(M,{title:"上传文件",icon:e.jsx(x,{})}),e.jsx(M,{title:"数据解析"}),e.jsx(M,{title:"数据验证"}),e.jsx(M,{title:"导入完成",icon:e.jsx(p,{})})]}),0===_&&e.jsxs("div",{className:"text-center py-8",children:[e.jsx(b,{accept:".xlsx,.xls",beforeUpload:e=>{C(!0),N(1);const s=new FileReader;return s.onload=e=>{var s;try{const l=new Uint8Array(null==(s=e.target)?void 0:s.result),r=a(l,{type:"array"}),i=r.SheetNames[0],n=r.Sheets[i],o=t.sheet_to_json(n,{header:1});console.log("Excel原始数据:",o),console.log("Excel总行数:",o.length);let c=0;for(let e=0;e<Math.min(o.length,10);e++){const t=o[e];if(t&&t.some(e=>"string"==typeof e&&(e.includes("学号")||e.includes("姓名")||e.includes("序号")))){c=e+1,console.log("检测到标题行在第",e+1,"行，数据从第",c+1,"行开始");break}}0===c&&(c=3,console.log("未找到标题行，使用默认起始行:",c+1));const d=o.slice(c);console.log("数据行数（从第",c+1,"行开始）:",d.length),console.log("前几行数据示例:",d.slice(0,3));const u=d.map((e,t)=>{const s={originalIndex:t+c+1};return!e||e.every(e=>!e||""===e.toString().trim())?null:(U.forEach((t,a)=>{const l=e[a];s[t.key]=l?l.toString().trim():""}),s)}).filter(e=>{if(!e)return!1;const t=e.studentId||e.name;return t?e.studentId?e.name||console.log("保留的行（缺少姓名）:",e):console.log("保留的行（缺少学号）:",e):console.log("过滤掉的行（无学号和姓名）:",e),t});console.log("处理后的数据:",u),console.log("有效数据条数:",u.length),J(u),H(u),C(!1),N(2)}catch(l){I.error("文件解析失败，请检查文件格式"),C(!1),N(0)}},s.readAsArrayBuffer(e),!1},showUploadList:!1,children:e.jsx(h,{type:"primary",icon:e.jsx(x,{}),size:"large",loading:q,children:"选择Excel文件"})}),e.jsx("div",{className:"mt-4 text-gray-500",children:e.jsx(B,{children:"支持 .xlsx 和 .xls 格式，请使用标准模板"})})]}),2===_&&e.jsxs("div",{className:"space-y-4",children:[e.jsx(j,{message:`解析完成，共 ${O.length} 条数据`,description:A.length>0?`发现 ${A.length} 条数据存在问题，请检查后重新上传`:"所有数据验证通过，可以开始导入",type:A.length>0?"warning":"success",showIcon:!0}),A.length>0&&e.jsxs("div",{children:[e.jsx(W,{level:5,children:"数据错误详情："}),e.jsx(k,{dataSource:A,rowKey:"row",size:"small",pagination:{pageSize:5},columns:[{title:"行号",dataIndex:"originalRow",width:80},{title:"学号",dataIndex:"studentId",width:120},{title:"姓名",dataIndex:"name",width:100},{title:"错误信息",dataIndex:"errors",render:t=>e.jsx("div",{children:t.map((t,s)=>e.jsxs("div",{className:"text-red-500",children:[e.jsx(S,{className:"mr-1"}),t]},s))})}]})]}),e.jsx("div",{className:"text-center",children:e.jsxs(u,{children:[e.jsx(h,{onClick:()=>N(0),children:"重新上传"}),e.jsx(h,{type:"primary",onClick:async()=>{if(A.length>0)I.error("请先修复数据错误");else{N(3),L(0);try{const e=O.map(e=>({student_id:e.studentId,name:e.name,gender:e.gender,birth_date:e.birthDate,id_card:e.idCard,ethnicity:e.ethnicity,birthplace:e.birthplace,political_status:e.politicalStatus,position:e.position,college:e.college,major:e.major,class:e.class,grade:e.grade,qq:e.qq,wechat:e.wechat,phone:e.phone,email:e.email,dormitory:e.dormBuilding,room:e.dormRoom,father_name:e.fatherName,father_phone:e.fatherPhone,father_work:e.fatherWork,mother_name:e.motherName,mother_phone:e.motherPhone,mother_work:e.motherWork,guardian_name:e.guardianName,guardian_phone:e.guardianPhone,guardian_relation:e.guardianRelation,home_address:e.homeAddress,family_income:e.familyIncome,family_members:e.familyMembers,economic_status:e.economicStatus,is_poor:e.isPoor,is_disabled:e.isDisabled,is_minority:e.isMinority,is_orphan:e.isOrphan,has_disease:e.hasDisease,disease_description:e.diseaseDescription,special_notes:e.specialNotes,failed_courses:e.failedCourses,course_semester:e.courseSemester,retake_registered:e.retakeRegistered,expected_graduation:e.expectedGraduation,tuition_owed:e.tuitionOwed,owed_amount:e.owedAmount,counselor_contact:e.counselorContact,student_contact:e.studentContact,other_situation:e.otherSituation,academic_status:e.academicStatus,blood_type:e.bloodType,height:e.height,weight:e.weight,vision_left:e.visionLeft,vision_right:e.visionRight,chronic_disease:e.chronicDisease,allergy_history:e.allergyHistory,medication:e.medication,emergency_contact:e.emergencyContact,emergency_phone:e.emergencyPhone,health_status:e.healthStatus,has_passport:e.hasPassport,passport_number:e.passportNumber,abroad_experience:e.abroadExperience,abroad_countries:e.abroadCountries,graduate_program:e.graduateProgram,micro_study_abroad:e.microStudyAbroad,exchange_program:e.exchangeProgram,language_skills:e.languageSkills,religious_belief:e.religiousBelief,religious_activity:e.religiousActivity,family_religious:e.familyReligious,religious_details:e.religiousDetails,tuition_paid:e.tuitionPaid,has_loan:e.hasLoan,loan_amount:e.loanAmount,has_grant:e.hasGrant,grant_amount:e.grantAmount,tuition_source:e.tuitionSource,monthly_expense:e.monthlyExpense,part_time_job:e.partTimeJob,counselorName:e.counselorName,counselorPhone:e.counselorPhone,other_notes:e.otherNotes,remarks:e.remarks,special_skills:e.specialSkills,hobbies:e.hobbies,social_activities:e.socialActivities,volunteer_experience:e.volunteerExperience}));L(20),console.log("发送导入数据:",e),console.log("导入数据长度:",e.length);const t=await l.post("/students/import",{students:e});if(L(80),!t.success)throw new Error(t.message||"导入失败");{L(100);const{successCount:e,errorCount:s,errors:a}=t.data;if(s>0){if(I.warning(`导入完成：成功 ${e} 条，失败 ${s} 条`),a&&a.length>0){console.log("导入错误详情:",a);const e=a.slice(0,3).join("\n");I.error(`导入失败原因：\n${e}${a.length>3?"\n...":""}`)}}else I.success(`成功导入 ${e} 条学生信息`);T({success:!0,data:t.data}),E(!0)}}catch(e){console.error("导入失败:",e),I.error(e.message||"导入失败，请重试"),N(2),L(0)}}},disabled:A.length>0,children:"确认导入"})]})})]}),3===_&&e.jsxs("div",{className:"text-center py-8",children:[e.jsx(f,{type:"circle",percent:R,status:100===R?"success":"active"}),e.jsx("div",{className:"mt-4",children:e.jsx(B,{children:"正在导入数据，请稍候..."})})]})]})]})})},{Option:G}=N,{Search:K}=v,Q=()=>{const t=r(),s=z(),{modal:a}=w.useApp(),[n,o]=i.useState([]),[d,m]=i.useState(!1),[g,y]=i.useState(""),[x,p]=i.useState([]),[b,j]=i.useState(!1),[S,f]=i.useState(!1);i.useEffect(()=>{const e=()=>{f(window.innerWidth<=768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);const T=async()=>{m(!0);try{console.log("🚀 [StudentList] 开始获取学生列表...");const e=await l.get("/students");if(console.log("✅ [StudentList] API响应:",e),e.success){const t=e.data.map(e=>(console.log("🔍 [StudentList] 处理学生数据:",e),{id:e.id,studentId:e.studentId,name:e.name,gender:"male"===e.gender?"男":"女",major:e.major,class:e.class,grade:e.grade,phone:e.phone,status:e.status,avatar:e.avatar}));o(t)}else s.error("获取学生列表失败")}catch(e){console.error("获取学生列表错误:",e),s.error("获取学生列表失败")}finally{m(!1)}};i.useEffect(()=>{T()},[]);const M=[{title:"头像",dataIndex:"avatar",key:"avatar",width:80,render:(t,s)=>e.jsx(R,{src:t,icon:!t&&s.name.charAt(0)})},{title:"学号",dataIndex:"studentId",key:"studentId",width:120},{title:"姓名",dataIndex:"name",key:"name",width:100},{title:"性别",dataIndex:"gender",key:"gender",width:80},{title:"专业",dataIndex:"major",key:"major",width:150},{title:"班级",dataIndex:"class",key:"class",width:100},{title:"年级",dataIndex:"grade",key:"grade",width:80},{title:"联系电话",dataIndex:"phone",key:"phone",width:120},{title:"状态",dataIndex:"status",key:"status",width:100,render:t=>{const s={active:{color:"green",text:"正常"},warning:{color:"orange",text:"预警"},inactive:{color:"red",text:"异常"}}[t];return e.jsx(L,{color:s.color,children:s.text})}},{title:"操作",key:"action",width:150,render:(s,a)=>e.jsxs(u,{children:[e.jsx(h,{type:"link",icon:e.jsx(P,{}),onClick:()=>{console.log("🔍 [StudentList] 点击查看按钮，学生数据:",a),console.log("🔍 [StudentList] studentId:",a.studentId),console.log("🔍 [StudentList] 导航到:",`/students/${a.studentId}`),t(`/students/${a.studentId}`)},children:"查看"}),e.jsx(h,{type:"link",icon:e.jsx(E,{}),onClick:()=>t(`/students/${a.studentId}/edit`),children:"编辑"}),e.jsx(h,{type:"link",danger:!0,icon:e.jsx($,{}),onClick:()=>{console.log("删除按钮被点击，学生信息:",a),W(a)},children:"删除"})]})}],W=async e=>{a.confirm({title:"确认删除",content:`确定要删除学生 ${e.name} 吗？`,okText:"确认",cancelText:"取消",okType:"danger",onOk:async()=>{try{console.log("开始删除学生:",e.id,e.name);const t=await l.delete(`/students/${e.id}`);console.log("删除API响应:",t),t.success?(s.success(`学生 ${e.name} 已删除`),B(e.studentId),await T()):s.error(t.message||"删除失败")}catch(t){console.error("删除学生错误:",t),s.error("删除失败，请重试")}}})},B=e=>{console.log("清理学生相关的localStorage数据:",e);const t=JSON.parse(localStorage.getItem("studentsList")||"[]").filter(t=>t.studentId!==e);localStorage.setItem("studentsList",JSON.stringify(t));const s=JSON.parse(localStorage.getItem("importedStudentsData")||"{}");delete s[e],localStorage.setItem("importedStudentsData",JSON.stringify(s));const a=JSON.parse(localStorage.getItem("academicRecordsList")||"[]").filter(t=>t.studentId!==e);localStorage.setItem("academicRecordsList",JSON.stringify(a));const l=JSON.parse(localStorage.getItem("academicRecordsData")||"{}");delete l[e],localStorage.setItem("academicRecordsData",JSON.stringify(l));const r=JSON.parse(localStorage.getItem("rewardPunishmentRecords")||"[]").filter(t=>t.studentId!==e);localStorage.setItem("rewardPunishmentRecords",JSON.stringify(r));const i=JSON.parse(localStorage.getItem("financialAidApplications")||"[]").filter(t=>t.studentId!==e);localStorage.setItem("financialAidApplications",JSON.stringify(i));const n=JSON.parse(localStorage.getItem("partyWorkMembers")||"[]").filter(t=>t.studentId!==e);localStorage.setItem("partyWorkMembers",JSON.stringify(n));const o=JSON.parse(localStorage.getItem("mentalHealthRecords")||"[]").filter(t=>t.studentId!==e);localStorage.setItem("mentalHealthRecords",JSON.stringify(o)),console.log("localStorage数据清理完成")},U=e=>{console.log("批量清理学生相关的localStorage数据:",e);const t=JSON.parse(localStorage.getItem("studentsList")||"[]").filter(t=>!e.includes(t.studentId));localStorage.setItem("studentsList",JSON.stringify(t));const s=JSON.parse(localStorage.getItem("importedStudentsData")||"{}");e.forEach(e=>{delete s[e]}),localStorage.setItem("importedStudentsData",JSON.stringify(s));const a=JSON.parse(localStorage.getItem("academicRecordsList")||"[]").filter(t=>!e.includes(t.studentId));localStorage.setItem("academicRecordsList",JSON.stringify(a));const l=JSON.parse(localStorage.getItem("academicRecordsData")||"{}");e.forEach(e=>{delete l[e]}),localStorage.setItem("academicRecordsData",JSON.stringify(l));const r=JSON.parse(localStorage.getItem("rewardPunishmentRecords")||"[]").filter(t=>!e.includes(t.studentId));localStorage.setItem("rewardPunishmentRecords",JSON.stringify(r));const i=JSON.parse(localStorage.getItem("financialAidApplications")||"[]").filter(t=>!e.includes(t.studentId));localStorage.setItem("financialAidApplications",JSON.stringify(i));const n=JSON.parse(localStorage.getItem("partyWorkMembers")||"[]").filter(t=>!e.includes(t.studentId));localStorage.setItem("partyWorkMembers",JSON.stringify(n));const o=JSON.parse(localStorage.getItem("mentalHealthRecords")||"[]").filter(t=>!e.includes(t.studentId));localStorage.setItem("mentalHealthRecords",JSON.stringify(o)),console.log("批量localStorage数据清理完成")},K={selectedRowKeys:x,onChange:e=>{p(e)}};return e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"学生管理"}),e.jsx("p",{className:"text-gray-600",children:"管理学生基本信息、学业状况等"})]}),e.jsx(c,{className:"mb-6",children:e.jsxs("div",{className:""+(S?"space-y-3":"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0"),children:[e.jsxs("div",{className:""+(S?"space-y-2":"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4"),children:[e.jsx(v.Search,{placeholder:"搜索学号、姓名、专业...",allowClear:!0,style:{width:S?"100%":300},onSearch:e=>y(e),size:S?"middle":"large"}),S?e.jsxs(I,{gutter:8,children:[e.jsx(_,{span:12,children:e.jsxs(N,{placeholder:"选择年级",style:{width:"100%"},allowClear:!0,size:"middle",children:[e.jsx(G,{value:"2021",children:"2021级"}),e.jsx(G,{value:"2022",children:"2022级"}),e.jsx(G,{value:"2023",children:"2023级"}),e.jsx(G,{value:"2024",children:"2024级"})]})}),e.jsx(_,{span:12,children:e.jsxs(N,{placeholder:"选择专业",style:{width:"100%"},allowClear:!0,size:"middle",children:[e.jsx(G,{value:"cs",children:"计算机科学与技术"}),e.jsx(G,{value:"se",children:"软件工程"}),e.jsx(G,{value:"ds",children:"数据科学与大数据技术"}),e.jsx(G,{value:"ai",children:"人工智能"})]})})]}):e.jsxs(e.Fragment,{children:[e.jsxs(N,{placeholder:"选择年级",style:{width:120},allowClear:!0,size:"large",children:[e.jsx(G,{value:"2021",children:"2021级"}),e.jsx(G,{value:"2022",children:"2022级"}),e.jsx(G,{value:"2023",children:"2023级"}),e.jsx(G,{value:"2024",children:"2024级"})]}),e.jsxs(N,{placeholder:"选择专业",style:{width:180},allowClear:!0,size:"large",children:[e.jsx(G,{value:"cs",children:"计算机科学与技术"}),e.jsx(G,{value:"se",children:"软件工程"}),e.jsx(G,{value:"ds",children:"数据科学与大数据技术"}),e.jsx(G,{value:"ai",children:"人工智能"})]})]})]}),e.jsx("div",{className:S?"flex flex-col space-y-2":"",children:e.jsxs(u,{size:S?"small":"middle",wrap:!0,children:[e.jsx(h,{type:"primary",icon:e.jsx(q,{}),onClick:()=>t("/students/new"),size:S?"middle":"large",block:S,children:S?"新增":"新增学生"}),e.jsx(h,{icon:e.jsx(C,{}),onClick:()=>{j(!0)},size:S?"middle":"large",block:S,children:S?"导入":"导入导出"}),!S&&e.jsx(O,{menu:{items:[{key:"csv-all",label:"导出全部学生 (CSV)",icon:e.jsx(A,{}),onClick:()=>{D(n,"全部学生信息")?s.success(`成功导出 ${n.length} 条学生信息`):s.error("导出失败，请重试")}},...x.length>0?[{key:"csv-selected",label:`导出选中学生 (CSV) - ${x.length}条`,icon:e.jsx(A,{}),onClick:()=>{const e=n.filter(e=>x.includes(e.id));D(e,"选中学生信息")?s.success(`成功导出 ${e.length} 条学生信息`):s.error("导出失败，请重试")}}]:[],{type:"divider"},{key:"json-all",label:"导出全部学生 (JSON)",icon:e.jsx(A,{}),onClick:()=>{const e=((e,t="学生信息")=>{try{const s=JSON.stringify(e,null,2),a=new Blob([s],{type:"application/json"}),l=document.createElement("a"),r=URL.createObjectURL(a);return l.setAttribute("href",r),l.setAttribute("download",`${t}.json`),l.style.visibility="hidden",document.body.appendChild(l),l.click(),document.body.removeChild(l),!0}catch(s){return console.error("导出失败:",s),!1}})(n,"全部学生信息");e?s.success(`成功导出 ${n.length} 条学生信息`):s.error("导出失败，请重试")}}]},trigger:["click"],children:e.jsxs(h,{icon:e.jsx(A,{}),size:S?"middle":"large",children:["导出 ",e.jsx(J,{})]})}),x.length>0&&e.jsxs(h,{danger:!0,icon:e.jsx($,{}),onClick:async()=>{if(0===x.length)return void s.warning("请选择要删除的学生");const t=n.filter(e=>x.includes(e.id)),r=t.map(e=>e.name).join("、"),i=t.map(e=>e.id),o=t.map(e=>e.studentId);a.confirm({title:"批量删除确认",content:e.jsxs("div",{children:[e.jsxs("p",{children:["确定要删除以下 ",x.length," 个学生吗？"]}),e.jsxs("p",{className:"text-gray-600 mt-2",children:["学生：",r]}),e.jsx("p",{className:"text-red-600 mt-2",children:"⚠️ 这将同时删除这些学生的所有相关记录（奖惩、资助、党建、心理健康等）。"})]}),okText:"确认删除",cancelText:"取消",okType:"danger",width:500,onOk:async()=>{try{console.log("开始批量删除学生:",i);const e=await l.post("/students/batch-delete",{ids:i});console.log("批量删除API响应:",e),e.success?(U(o),p([]),s.success(`成功删除 ${x.length} 个学生及其相关记录`),await T()):s.error(e.message||"批量删除失败")}catch(e){console.error("批量删除学生错误:",e),s.error("批量删除失败，请重试")}}})},size:S?"middle":"large",block:S,children:["批量删除 (",x.length,")"]})]})})]})}),S?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("span",{className:"text-sm text-gray-600",children:["共 ",n.length," 名学生"]}),x.length>0&&e.jsxs("span",{className:"text-sm text-blue-600",children:["已选择 ",x.length," 项"]})]}),n.map(s=>(s=>e.jsx(c,{className:"mb-4",actions:[e.jsx(h,{type:"link",icon:e.jsx(P,{}),onClick:()=>t(`/students/${s.studentId}`),children:"查看"}),e.jsx(h,{type:"link",icon:e.jsx(E,{}),onClick:()=>t(`/students/${s.studentId}/edit`),children:"编辑"}),e.jsx(h,{type:"link",danger:!0,icon:e.jsx($,{}),onClick:()=>W(s),children:"删除"})],children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(R,{src:s.avatar,icon:!s.avatar&&s.name.charAt(0),size:48}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center justify-between mb-1",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 truncate",children:s.name}),e.jsx(L,{color:"active"===s.status?"green":"warning"===s.status?"orange":"red",children:"active"===s.status?"正常":"warning"===s.status?"预警":"异常"})]}),e.jsxs("div",{className:"space-y-1 text-sm text-gray-600",children:[e.jsxs("div",{children:["学号：",s.studentId]}),e.jsxs("div",{children:["专业：",s.major]}),e.jsxs("div",{children:["班级：",s.class]}),e.jsxs("div",{children:["电话：",s.phone]})]})]})]})},s.id))(s))]}):e.jsx(c,{children:e.jsx(k,{rowSelection:K,columns:M,dataSource:n,rowKey:"id",loading:d,pagination:{total:n.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>`第 ${t[0]}-${t[1]} 条/共 ${e} 条`,size:"default"},scroll:{x:1200},size:"middle"})}),e.jsx(H,{visible:b,onCancel:()=>j(!1),onImportSuccess:e=>{T(),s.success(`成功导入 ${e.length} 条学生信息`)},onExportData:n})]})};export{Q as default};
