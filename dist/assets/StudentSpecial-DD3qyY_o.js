import{c as e,u as s,j as i}from"./index-DP6eZxW9.js";import{aq as l,a0 as r,r as a,S as n,F as t,ae as d,a as c,aC as h,aB as o,Z as x,O as j,N as m,U as u,G as p,H as g,aK as y,I as b,as as v,aI as f}from"./antd-DYv0PFJq.js";import{g as I}from"./studentService-DEbeeazR.js";import"./vendor-D2RBMdQ0.js";const{TextArea:C}=b,{Option:S}=v,D=()=>{const{id:D}=e(),N=s(),[k]=l.useForm(),{message:F}=r.useApp(),[P,w]=a.useState(null),[O,M]=a.useState(null),[R,q]=a.useState(!0),[T,V]=a.useState(!1),[$,A]=a.useState(!1);a.useEffect(()=>{(async()=>{if(D)try{console.log(`🔍 [StudentSpecial] 获取学生数据，学号: ${D}`);const e=await I(D);if(e){console.log("✅ [StudentSpecial] 学生数据获取成功:",e),w({id:e.id,name:e.name,studentId:e.studentId,class:e.class,college:e.college,major:e.major,grade:e.grade});const s={isMartyrChild:"是"===e.martyrChild,isOrphan:"是"===e.orphan||"是"===e.isOrphan,caregiverName:e.caregiverName||e.guardianName||"",caregiverRelation:e.caregiverRelation||e.guardianRelation||"",caregiverPhone:e.caregiverPhone||e.guardianPhone||"",orphanDescription:e.orphanDescription||e.specialNotes||"",isDisabled:"是"===e.disabled||"是"===e.isDisabled,disabilityType:e.disabilityType||"",disabilityLevel:e.disabilityLevel||"",disabilityDescription:e.disabilityDescription||"",isPoor:"是"===e.isPoor||"是"===e.economicDifficulty,isMinority:"是"===e.isMinority,hasDisease:"是"===e.hasDisease,diseaseDescription:e.diseaseDescription||"",specialNotes:e.specialNotes||""};console.log("📋 [StudentSpecial] 特殊情况数据:",s),M(s),k.setFieldsValue(s)}else console.error(`❌ [StudentSpecial] 未找到学号为 ${D} 的学生信息`),F.error(`未找到学号为 ${D} 的学生信息`)}catch(e){console.error("💥 [StudentSpecial] 获取学生数据失败:",e),F.error("获取学生信息失败")}finally{q(!1)}else q(!1)})()},[D,k]),a.useEffect(()=>{O&&k.setFieldsValue(O)},[k,O]);if(R)return i.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"400px"},children:i.jsx(n,{size:"large",tip:"正在加载学生信息...",children:i.jsx("div",{style:{height:"200px"}})})});if(!P)return i.jsx("div",{style:{padding:"24px"},children:i.jsxs(t,{style:{textAlign:"center"},children:[i.jsx("div",{style:{color:"#ff4d4f",fontSize:"18px",fontWeight:600,marginBottom:"16px"},children:"学生信息不存在"}),i.jsxs(d,{children:[i.jsx(c,{onClick:()=>N("/students"),children:"返回学生列表"}),i.jsx(c,{type:"primary",onClick:()=>window.location.reload(),children:"重新加载"})]})]})});const L=O&&(O.isMartyrChild||O.isOrphan||O.isDisabled||O.isPoor||O.isMinority||O.hasDisease);return i.jsxs("div",{className:"space-y-6",children:[i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsxs("div",{className:"flex items-center space-x-4",children:[i.jsx(c,{icon:i.jsx(h,{}),onClick:()=>N(`/students/${D}`),children:"返回"}),i.jsxs("div",{children:[i.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"特殊情况管理"}),i.jsxs("p",{className:"text-gray-600",children:["管理学生 ",P.name," 的特殊情况信息"]})]})]}),!T&&i.jsx(c,{type:"primary",icon:i.jsx(o,{}),onClick:()=>V(!0),children:"编辑信息"})]}),i.jsx(t,{title:"学生基本信息",style:{borderRadius:"8px"},children:i.jsxs(x,{column:3,children:[i.jsx(x.Item,{label:"姓名",children:P.name}),i.jsx(x.Item,{label:"学号",children:P.studentId}),i.jsx(x.Item,{label:"班级",children:P.class}),i.jsx(x.Item,{label:"学院",children:P.college}),i.jsx(x.Item,{label:"专业",children:P.major}),i.jsx(x.Item,{label:"年级",children:P.grade})]})}),L&&i.jsx(j,{message:"该学生存在特殊情况",description:"请特别关注该学生的学习和生活状况，给予必要的帮助和支持。",type:"warning",icon:i.jsx(m,{}),showIcon:!0,style:{borderRadius:"8px"}}),i.jsx(t,{title:"特殊情况信息",style:{borderRadius:"8px"},extra:T&&i.jsxs(d,{children:[i.jsx(c,{onClick:()=>{k.resetFields(),O&&k.setFieldsValue(O),V(!1)},children:"取消"}),i.jsx(c,{type:"primary",icon:i.jsx(f,{}),loading:$,onClick:()=>k.submit(),children:"保存"})]}),children:i.jsxs(l,{form:k,layout:"vertical",onFinish:async e=>{if(D){A(!0);try{await new Promise(e=>setTimeout(e,1e3)),M(e),F.success("特殊情况信息更新成功！"),V(!1)}catch(s){F.error("更新失败，请重试")}finally{A(!1)}}},disabled:!T,children:[i.jsx(u,{orientation:"left",children:"烈士子女情况"}),i.jsx(p,{gutter:[24,16],children:i.jsx(g,{xs:24,md:12,children:i.jsx(l.Item,{label:"是否为烈士子女",name:"isMartyrChild",valuePropName:"checked",children:i.jsx(y,{checkedChildren:"是",unCheckedChildren:"否"})})})}),i.jsx(u,{orientation:"left",children:"孤儿情况"}),i.jsx(p,{gutter:[24,16],children:i.jsx(g,{xs:24,md:12,children:i.jsx(l.Item,{label:"是否为孤儿",name:"isOrphan",valuePropName:"checked",children:i.jsx(y,{checkedChildren:"是",unCheckedChildren:"否"})})})}),i.jsx(l.Item,{noStyle:!0,shouldUpdate:(e,s)=>e.isOrphan!==s.isOrphan,children:({getFieldValue:e})=>e("isOrphan")?i.jsxs(i.Fragment,{children:[i.jsxs(p,{gutter:[24,16],children:[i.jsx(g,{xs:24,md:8,children:i.jsx(l.Item,{label:"监护人姓名",name:"caregiverName",rules:[{required:!0,message:"请输入监护人姓名"}],children:i.jsx(b,{placeholder:"请输入监护人姓名"})})}),i.jsx(g,{xs:24,md:8,children:i.jsx(l.Item,{label:"与学生关系",name:"caregiverRelation",rules:[{required:!0,message:"请输入与学生关系"}],children:i.jsx(b,{placeholder:"请输入与学生关系"})})}),i.jsx(g,{xs:24,md:8,children:i.jsx(l.Item,{label:"监护人联系电话",name:"caregiverPhone",rules:[{required:!0,message:"请输入监护人联系电话"},{pattern:/^1[3-9]\d{9}$/,message:"手机号码格式不正确"}],children:i.jsx(b,{placeholder:"请输入监护人联系电话"})})})]}),i.jsx(p,{gutter:[24,16],children:i.jsx(g,{span:24,children:i.jsx(l.Item,{label:"孤儿情况说明",name:"orphanDescription",children:i.jsx(C,{rows:3,placeholder:"请详细说明孤儿情况，包括父母去世时间、原因等"})})})})]}):null}),i.jsx(u,{orientation:"left",children:"残疾情况"}),i.jsx(p,{gutter:[24,16],children:i.jsx(g,{xs:24,md:12,children:i.jsx(l.Item,{label:"是否有残疾",name:"isDisabled",valuePropName:"checked",children:i.jsx(y,{checkedChildren:"是",unCheckedChildren:"否"})})})}),i.jsx(l.Item,{noStyle:!0,shouldUpdate:(e,s)=>e.isDisabled!==s.isDisabled,children:({getFieldValue:e})=>e("isDisabled")?i.jsxs(i.Fragment,{children:[i.jsxs(p,{gutter:[24,16],children:[i.jsx(g,{xs:24,md:12,children:i.jsx(l.Item,{label:"残疾类型",name:"disabilityType",rules:[{required:!0,message:"请选择残疾类型"}],children:i.jsxs(v,{placeholder:"请选择残疾类型",children:[i.jsx(S,{value:"visual",children:"视力残疾"}),i.jsx(S,{value:"hearing",children:"听力残疾"}),i.jsx(S,{value:"speech",children:"言语残疾"}),i.jsx(S,{value:"physical",children:"肢体残疾"}),i.jsx(S,{value:"intellectual",children:"智力残疾"}),i.jsx(S,{value:"mental",children:"精神残疾"}),i.jsx(S,{value:"multiple",children:"多重残疾"}),i.jsx(S,{value:"other",children:"其他"})]})})}),i.jsx(g,{xs:24,md:12,children:i.jsx(l.Item,{label:"残疾等级",name:"disabilityLevel",rules:[{required:!0,message:"请选择残疾等级"}],children:i.jsxs(v,{placeholder:"请选择残疾等级",children:[i.jsx(S,{value:"1",children:"一级（重度）"}),i.jsx(S,{value:"2",children:"二级（重度）"}),i.jsx(S,{value:"3",children:"三级（中度）"}),i.jsx(S,{value:"4",children:"四级（轻度）"})]})})})]}),i.jsx(p,{gutter:[24,16],children:i.jsx(g,{span:24,children:i.jsx(l.Item,{label:"残疾情况详细说明",name:"disabilityDescription",children:i.jsx(C,{rows:3,placeholder:"请详细说明残疾情况，包括具体症状、对学习生活的影响等"})})})})]}):null})]})})]})};export{D as default};
