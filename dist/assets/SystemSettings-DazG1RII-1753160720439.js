import{j as e}from"./index-DXaqwR6F-1753160720439.js";import{M as s,r as l,aq as a,a as i,aI as n,F as r,ai as t,G as c,H as d,I as x,as as j,aM as h,U as m,aK as u,u as o,a9 as p,ao as v,ay as g,g as b,K as y,s as I,Q as k,aB as f,W as Y}from"./antd-lXsGnH6e-1753160720439.js";import"./vendor-D2RBMdQ0-1753160720439.js";const{Option:S}=j,{TextArea:N}=x,M=()=>{const[M,D]=s.useModal(),[F,w]=l.useState("basic"),[z,C]=l.useState(!1),[P,T]=l.useState(!1),[V,q]=l.useState(null),[A]=a.useForm(),[L]=a.useForm(),[K,O]=l.useState({siteName:"智慧学工系统",siteDescription:"现代化学生工作管理平台",logo:"",theme:"dark",language:"zh-CN",timezone:"Asia/Shanghai",dateFormat:"YYYY-MM-DD",pageSize:10,sessionTimeout:30,enableRegistration:!1,enableEmailNotification:!0,enableSMSNotification:!1,backupFrequency:"daily",maxFileSize:10,allowedFileTypes:["jpg","png","pdf","doc","docx","xls","xlsx"]}),[U,E]=l.useState([{id:"1",username:"admin",email:"<EMAIL>",role:"admin",status:"active",lastLogin:"2024-01-15 10:30:00",createdAt:"2024-01-01"},{id:"2",username:"teacher1",email:"<EMAIL>",role:"teacher",status:"active",lastLogin:"2024-01-14 16:20:00",createdAt:"2024-01-02"},{id:"3",username:"student1",email:"<EMAIL>",role:"student",status:"inactive",lastLogin:"2024-01-10 09:15:00",createdAt:"2024-01-03"}]),B=[{title:"用户名",dataIndex:"username",key:"username"},{title:"邮箱",dataIndex:"email",key:"email"},{title:"角色",dataIndex:"role",key:"role",render:s=>{const l={admin:{color:"red",text:"管理员"},teacher:{color:"blue",text:"教师"},student:{color:"green",text:"学生"}}[s];return e.jsx(k,{color:l.color,children:l.text})}},{title:"状态",dataIndex:"status",key:"status",render:s=>e.jsx(k,{color:"active"===s?"green":"red",children:"active"===s?"活跃":"禁用"})},{title:"最后登录",dataIndex:"lastLogin",key:"lastLogin"},{title:"创建时间",dataIndex:"createdAt",key:"createdAt"},{title:"操作",key:"action",render:(s,l)=>e.jsxs("div",{className:"space-x-2",children:[e.jsx(i,{type:"text",icon:e.jsx(f,{}),onClick:()=>{return q(e=l),L.setFieldsValue(e),void T(!0);var e}}),e.jsx(i,{type:"text",danger:!0,icon:e.jsx(Y,{}),onClick:()=>{return e=l.id,void M.confirm({title:"确认删除",content:"确定要删除这个用户吗？",onOk:()=>{E(U.filter(s=>s.id!==e)),I.success("用户删除成功")}});var e}})]})}];return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"系统设置"}),e.jsx(i,{type:"primary",icon:e.jsx(n,{}),onClick:()=>A.submit(),loading:z,children:"保存配置"})]}),e.jsx(r,{children:e.jsx(t,{activeKey:F,onChange:w,items:[{key:"basic",label:e.jsxs("span",{children:[e.jsx(o,{}),"基本设置"]}),children:e.jsxs(a,{form:A,layout:"vertical",onFinish:e=>{C(!0),setTimeout(()=>{O({...K,...e}),I.success("系统配置保存成功"),C(!1)},1e3)},initialValues:K,children:[e.jsxs(c,{gutter:24,children:[e.jsx(d,{span:12,children:e.jsx(a.Item,{label:"网站名称",name:"siteName",rules:[{required:!0,message:"请输入网站名称"}],children:e.jsx(x,{})})}),e.jsx(d,{span:12,children:e.jsx(a.Item,{label:"网站描述",name:"siteDescription",children:e.jsx(x,{})})})]}),e.jsxs(c,{gutter:24,children:[e.jsx(d,{span:8,children:e.jsx(a.Item,{label:"主题",name:"theme",children:e.jsxs(j,{children:[e.jsx(S,{value:"dark",children:"深色主题"}),e.jsx(S,{value:"light",children:"浅色主题"})]})})}),e.jsx(d,{span:8,children:e.jsx(a.Item,{label:"语言",name:"language",children:e.jsxs(j,{children:[e.jsx(S,{value:"zh-CN",children:"中文"}),e.jsx(S,{value:"en-US",children:"English"})]})})}),e.jsx(d,{span:8,children:e.jsx(a.Item,{label:"时区",name:"timezone",children:e.jsxs(j,{children:[e.jsx(S,{value:"Asia/Shanghai",children:"北京时间"}),e.jsx(S,{value:"UTC",children:"UTC"})]})})})]}),e.jsxs(c,{gutter:24,children:[e.jsx(d,{span:8,children:e.jsx(a.Item,{label:"日期格式",name:"dateFormat",children:e.jsxs(j,{children:[e.jsx(S,{value:"YYYY-MM-DD",children:"YYYY-MM-DD"}),e.jsx(S,{value:"MM/DD/YYYY",children:"MM/DD/YYYY"}),e.jsx(S,{value:"DD/MM/YYYY",children:"DD/MM/YYYY"})]})})}),e.jsx(d,{span:8,children:e.jsx(a.Item,{label:"每页显示条数",name:"pageSize",children:e.jsx(h,{min:5,max:100})})}),e.jsx(d,{span:8,children:e.jsx(a.Item,{label:"会话超时(分钟)",name:"sessionTimeout",children:e.jsx(h,{min:5,max:480})})})]}),e.jsx(m,{}),e.jsxs(c,{gutter:24,children:[e.jsx(d,{span:8,children:e.jsx(a.Item,{label:"允许用户注册",name:"enableRegistration",valuePropName:"checked",children:e.jsx(u,{})})}),e.jsx(d,{span:8,children:e.jsx(a.Item,{label:"邮件通知",name:"enableEmailNotification",valuePropName:"checked",children:e.jsx(u,{})})}),e.jsx(d,{span:8,children:e.jsx(a.Item,{label:"短信通知",name:"enableSMSNotification",valuePropName:"checked",children:e.jsx(u,{})})})]})]})},{key:"security",label:e.jsxs("span",{children:[e.jsx(p,{}),"安全设置"]}),children:e.jsxs("div",{className:"space-y-6",children:[e.jsx(r,{title:"密码策略",size:"small",children:e.jsxs(a,{layout:"vertical",children:[e.jsxs(c,{gutter:16,children:[e.jsx(d,{span:8,children:e.jsx(a.Item,{label:"最小长度",children:e.jsx(h,{min:6,max:20,defaultValue:8})})}),e.jsx(d,{span:8,children:e.jsx(a.Item,{label:"密码有效期(天)",children:e.jsx(h,{min:30,max:365,defaultValue:90})})}),e.jsx(d,{span:8,children:e.jsx(a.Item,{label:"登录失败锁定次数",children:e.jsx(h,{min:3,max:10,defaultValue:5})})})]}),e.jsxs(c,{gutter:16,children:[e.jsx(d,{span:8,children:e.jsx(a.Item,{label:"要求大写字母",valuePropName:"checked",children:e.jsx(u,{defaultChecked:!0})})}),e.jsx(d,{span:8,children:e.jsx(a.Item,{label:"要求小写字母",valuePropName:"checked",children:e.jsx(u,{defaultChecked:!0})})}),e.jsx(d,{span:8,children:e.jsx(a.Item,{label:"要求数字",valuePropName:"checked",children:e.jsx(u,{defaultChecked:!0})})})]})]})}),e.jsx(r,{title:"访问控制",size:"small",children:e.jsx(a,{layout:"vertical",children:e.jsxs(c,{gutter:16,children:[e.jsx(d,{span:12,children:e.jsx(a.Item,{label:"IP白名单",children:e.jsx(N,{rows:4,placeholder:"每行一个IP地址或IP段"})})}),e.jsx(d,{span:12,children:e.jsx(a.Item,{label:"IP黑名单",children:e.jsx(N,{rows:4,placeholder:"每行一个IP地址或IP段"})})})]})})})]})},{key:"users",label:e.jsxs("span",{children:[e.jsx(b,{}),"用户管理"]}),children:e.jsxs("div",{children:[e.jsx("div",{className:"mb-4",children:e.jsx(i,{type:"primary",icon:e.jsx(v,{}),onClick:()=>{q(null),L.resetFields(),T(!0)},children:"添加用户"})}),e.jsx(g,{columns:B,dataSource:U,rowKey:"id",pagination:{pageSize:10,showSizeChanger:!0,showQuickJumper:!0}})]})},{key:"data",label:e.jsxs("span",{children:[e.jsx(y,{}),"数据管理"]}),children:e.jsxs("div",{className:"space-y-6",children:[e.jsx(r,{title:"数据备份",size:"small",children:e.jsxs(a,{layout:"vertical",children:[e.jsxs(c,{gutter:16,children:[e.jsx(d,{span:8,children:e.jsx(a.Item,{label:"备份频率",name:"backupFrequency",initialValue:K.backupFrequency,children:e.jsxs(j,{children:[e.jsx(S,{value:"daily",children:"每日"}),e.jsx(S,{value:"weekly",children:"每周"}),e.jsx(S,{value:"monthly",children:"每月"})]})})}),e.jsx(d,{span:8,children:e.jsx(a.Item,{label:"保留备份数量",children:e.jsx(h,{min:1,max:30,defaultValue:7})})}),e.jsx(d,{span:8,children:e.jsx(a.Item,{label:"备份时间",children:e.jsx(x,{defaultValue:"02:00"})})})]}),e.jsx(i,{type:"primary",children:"立即备份"})]})}),e.jsx(r,{title:"文件管理",size:"small",children:e.jsx(a,{layout:"vertical",children:e.jsxs(c,{gutter:16,children:[e.jsx(d,{span:8,children:e.jsx(a.Item,{label:"最大文件大小(MB)",name:"maxFileSize",initialValue:K.maxFileSize,children:e.jsx(h,{min:1,max:100})})}),e.jsx(d,{span:16,children:e.jsx(a.Item,{label:"允许的文件类型",name:"allowedFileTypes",initialValue:K.allowedFileTypes,children:e.jsxs(j,{mode:"tags",placeholder:"输入文件扩展名",children:[e.jsx(S,{value:"jpg",children:"jpg"}),e.jsx(S,{value:"png",children:"png"}),e.jsx(S,{value:"pdf",children:"pdf"}),e.jsx(S,{value:"doc",children:"doc"}),e.jsx(S,{value:"docx",children:"docx"}),e.jsx(S,{value:"xls",children:"xls"}),e.jsx(S,{value:"xlsx",children:"xlsx"})]})})})]})})})]})}]})}),e.jsx(s,{title:V?"编辑用户":"添加用户",open:P,onCancel:()=>T(!1),footer:null,width:500,destroyOnHidden:!0,children:e.jsxs(a,{form:L,layout:"vertical",onFinish:e=>{if(V)E(U.map(s=>s.id===V.id?{...s,...e}:s)),I.success("用户信息更新成功");else{const s={...e,id:Date.now().toString(),lastLogin:"-",createdAt:(new Date).toISOString().split("T")[0]};E([...U,s]),I.success("用户创建成功")}T(!1)},children:[e.jsx(a.Item,{label:"用户名",name:"username",rules:[{required:!0,message:"请输入用户名"}],children:e.jsx(x,{})}),e.jsx(a.Item,{label:"邮箱",name:"email",rules:[{required:!0,message:"请输入邮箱"},{type:"email",message:"邮箱格式不正确"}],children:e.jsx(x,{})}),e.jsx(a.Item,{label:"角色",name:"role",rules:[{required:!0,message:"请选择角色"}],children:e.jsxs(j,{children:[e.jsx(S,{value:"admin",children:"管理员"}),e.jsx(S,{value:"teacher",children:"教师"}),e.jsx(S,{value:"student",children:"学生"})]})}),e.jsx(a.Item,{label:"状态",name:"status",rules:[{required:!0,message:"请选择状态"}],initialValue:"active",children:e.jsxs(j,{children:[e.jsx(S,{value:"active",children:"活跃"}),e.jsx(S,{value:"inactive",children:"禁用"})]})}),!V&&e.jsx(a.Item,{label:"密码",name:"password",rules:[{required:!0,message:"请输入密码"}],children:e.jsx(x.Password,{})}),e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(i,{onClick:()=>T(!1),children:"取消"}),e.jsx(i,{type:"primary",htmlType:"submit",children:V?"更新":"创建"})]})]})}),D]})};export{M as default};
