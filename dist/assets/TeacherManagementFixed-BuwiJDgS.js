import{r as e,j as s}from"./index-Cu_U9Dm3.js";import{r,aq as l,ac as a,ae as i,a as t,V as n,aA as c,X as d,ao as m,G as h,H as o,F as x,J as j,g as u,i as p,j as g,m as f,I as y,aL as v,as as b,bg as Y,ay as N,M as D,aH as C,U as I,aw as k,ax as w,a1 as M,ai as T,Q as _,aB as z,W as H,af as S,au as q}from"./antd-DYv0PFJq.js";import{u as F,a as $}from"./useMessage-DDIprusu.js";import"./vendor-D2RBMdQ0.js";const{Search:P}=y,{Option:A}=b,{TabPane:O}=T,V=()=>{const V=F(),L=$(),[K,U]=r.useState([]),[B,E]=r.useState([]),[J,Q]=r.useState([]),[W,X]=r.useState([]),[G,R]=r.useState([]),[Z,ee]=r.useState([]),[se,re]=r.useState(!1),[le,ae]=r.useState(""),[ie,te]=r.useState("all"),[ne,ce]=r.useState("all"),[de,me]=r.useState(!1),[he,oe]=r.useState(!1),[xe,je]=r.useState(null),[ue,pe]=r.useState(null),[ge,fe]=r.useState([]),[ye,ve]=r.useState(!1),[be,Ye]=r.useState(""),[Ne,De]=r.useState(""),[Ce,Ie]=r.useState(!1),[ke,we]=r.useState(!1),[Me,Te]=r.useState(!1),[_e,ze]=r.useState(!1),[He,Se]=r.useState(!1),[qe,Fe]=r.useState(null),[$e,Pe]=r.useState(null),[Ae,Oe]=r.useState(null),[Ve,Le]=r.useState(null),[Ke,Ue]=r.useState(null),[Be]=l.useForm(),[Ee]=l.useForm(),[Je]=l.useForm(),[Qe]=l.useForm(),[We]=l.useForm(),[Xe]=l.useForm();r.useEffect(()=>{Ge()},[]);const Ge=async()=>{re(!0);try{const s=await e.get("/teachers");s.success?U(s.data):V.error("获取教师列表失败")}catch(s){console.error("获取教师列表错误:",s),V.error("获取教师列表失败")}finally{re(!1)}},{testTeachers:Re,testCourses:Ze,testAchievements:es,testTeachingMaterials:ss,testTrainings:rs,testMentorshipProjects:ls}=r.useMemo(()=>({testTeachers:[{id:"teacher_001",name:"张教授",idCard:"110101198001011234",gender:"male",ethnicity:"汉族",politicalStatus:"party_member",phone:"13800138001",title:"professor",entryYear:"2010",homeAddress:"北京市海淀区中关村大街1号",emergencyContact:"李女士",emergencyPhone:"13900139001",resume:"张教授，计算机科学与技术专业博士，教授职称。2010年入职本校，主要从事人工智能、机器学习等领域的教学和科研工作。发表SCI论文50余篇，主持国家自然科学基金项目3项。",titleCertificate:"教授职称证书.pdf",degreeCertificate:"博士学位证书.pdf",graduationCertificate:"博士毕业证书.pdf",skillCertificates:["高级程序员证书.pdf","PMP项目管理证书.pdf"],otherCertificates:["优秀教师证书.pdf","科研成果奖证书.pdf"],status:"active",createTime:a().subtract(3,"year").format("YYYY-MM-DD HH:mm:ss"),updateTime:a().format("YYYY-MM-DD HH:mm:ss")},{id:"teacher_002",name:"李副教授",idCard:"110101198505152345",gender:"female",ethnicity:"汉族",politicalStatus:"party_member",phone:"13800138002",title:"associate_professor",entryYear:"2015",homeAddress:"北京市朝阳区建国路88号",emergencyContact:"王先生",emergencyPhone:"13900139002",resume:"李副教授，软件工程专业博士，副教授职称。2015年入职本校，专注于软件工程、数据库系统等领域的教学与研究。主持省部级项目5项，发表核心期刊论文30余篇。",titleCertificate:"副教授职称证书.pdf",degreeCertificate:"博士学位证书.pdf",graduationCertificate:"博士毕业证书.pdf",skillCertificates:["软件设计师证书.pdf","数据库管理员证书.pdf"],otherCertificates:["教学优秀奖证书.pdf"],status:"active",createTime:a().subtract(2,"year").format("YYYY-MM-DD HH:mm:ss"),updateTime:a().subtract(1,"month").format("YYYY-MM-DD HH:mm:ss")},{id:"teacher_003",name:"王讲师",idCard:"110101199002283456",gender:"male",ethnicity:"汉族",politicalStatus:"league_member",phone:"13800138003",title:"lecturer",entryYear:"2020",homeAddress:"北京市西城区西单大街66号",emergencyContact:"张女士",emergencyPhone:"13900139003",resume:"王讲师，网络工程专业硕士，讲师职称。2020年入职本校，主要承担计算机网络、网络安全等课程的教学工作。参与多项横向课题，具有丰富的工程实践经验。",status:"active",createTime:a().subtract(1,"year").format("YYYY-MM-DD HH:mm:ss"),updateTime:a().subtract(2,"week").format("YYYY-MM-DD HH:mm:ss")},{id:"teacher_004",name:"陈助教",idCard:"110101199508194567",gender:"female",ethnicity:"满族",politicalStatus:"masses",phone:"13800138004",title:"assistant",entryYear:"2023",homeAddress:"北京市东城区王府井大街128号",emergencyContact:"刘先生",emergencyPhone:"13900139004",resume:"陈助教，计算机应用技术专业硕士，助教职称。2023年入职本校，主要协助高年级课程的实验教学和学生指导工作。积极参与教学改革，致力于提升教学质量。",status:"active",createTime:a().subtract(6,"month").format("YYYY-MM-DD HH:mm:ss"),updateTime:a().subtract(1,"week").format("YYYY-MM-DD HH:mm:ss")},{id:"teacher_005",name:"赵高工",idCard:"110101197812125678",gender:"male",ethnicity:"汉族",politicalStatus:"party_member",phone:"13800138005",title:"senior_engineer",entryYear:"2008",homeAddress:"北京市丰台区南三环西路15号",emergencyContact:"孙女士",emergencyPhone:"13900139005",resume:"赵高工，电子信息工程专业学士，高级工程师职称。2008年入职本校，具有丰富的工程实践经验，主要负责实验室建设和设备维护工作。指导学生参加各类竞赛获奖多项。",status:"inactive",createTime:a().subtract(5,"year").format("YYYY-MM-DD HH:mm:ss"),updateTime:a().subtract(3,"month").format("YYYY-MM-DD HH:mm:ss")}],testCourses:[{id:"course_001",teacherId:"teacher_001",courseName:"人工智能基础",courseType:"theory",semester:"spring",academicYear:"2023-2024",courseHours:64,studentCount:80,status:"active"},{id:"course_002",teacherId:"teacher_001",courseName:"机器学习实践",courseType:"mixed",semester:"fall",academicYear:"2023-2024",courseHours:48,studentCount:40,status:"completed"},{id:"course_003",teacherId:"teacher_002",courseName:"软件工程",courseType:"theory",semester:"spring",academicYear:"2023-2024",courseHours:56,studentCount:60,status:"active"},{id:"course_004",teacherId:"teacher_002",courseName:"数据库系统",courseType:"mixed",semester:"fall",academicYear:"2023-2024",courseHours:64,studentCount:75,status:"active"},{id:"course_005",teacherId:"teacher_003",courseName:"计算机网络",courseType:"theory",semester:"spring",academicYear:"2023-2024",courseHours:48,studentCount:55,status:"active"}],testAchievements:[{id:"achievement_001",teacherId:"teacher_001",title:"全国高校人工智能教学竞赛一等奖",type:"competition",level:"national",points:100,awardTime:a().subtract(6,"month").format("YYYY-MM-DD")},{id:"achievement_002",teacherId:"teacher_001",title:"基于深度学习的图像识别算法研究",type:"paper",level:"national",points:80,awardTime:a().subtract(1,"year").format("YYYY-MM-DD")},{id:"achievement_003",teacherId:"teacher_002",title:"省级教学成果奖二等奖",type:"award",level:"provincial",points:100,awardTime:a().subtract(8,"month").format("YYYY-MM-DD")},{id:"achievement_004",teacherId:"teacher_003",title:"网络安全技术专利",type:"patent",level:"national",points:200,awardTime:a().subtract(4,"month").format("YYYY-MM-DD")}],testTeachingMaterials:[{id:"material_001",teacherId:"teacher_001",materialType:"calendar",materialName:"人工智能基础教学日历",courseName:"人工智能基础",semester:"spring",academicYear:"2023-2024",description:"2023-2024春季学期人工智能基础课程教学日历",fileName:"人工智能基础教学日历_2024春.pdf",fileSize:1024e3,uploadTime:a().subtract(2,"month").format("YYYY-MM-DD HH:mm:ss")},{id:"material_002",teacherId:"teacher_001",materialType:"plan",materialName:"机器学习实践教学进度计划",courseName:"机器学习实践",semester:"fall",academicYear:"2023-2024",description:"机器学习实践课程教学进度计划表",fileName:"机器学习实践教学进度计划.xlsx",fileSize:512e3,uploadTime:a().subtract(3,"month").format("YYYY-MM-DD HH:mm:ss")},{id:"material_003",teacherId:"teacher_002",materialType:"syllabus",materialName:"软件工程教学大纲",courseName:"软件工程",semester:"spring",academicYear:"2023-2024",description:"软件工程课程教学大纲，包含课程目标、内容安排、考核方式等",fileName:"软件工程教学大纲_2024.docx",fileSize:256e3,uploadTime:a().subtract(1,"month").format("YYYY-MM-DD HH:mm:ss")}],testTrainings:[{id:"training_001",teacherId:"teacher_001",trainingName:"高等学校人工智能教学能力提升培训",trainingType:"academic",organizer:"教育部高等教育司",startDate:a().subtract(3,"month").format("YYYY-MM-DD"),endDate:a().subtract(3,"month").add(5,"day").format("YYYY-MM-DD"),duration:40,location:"北京大学",trainingContent:"本次培训主要内容包括人工智能教学理念与方法、课程设计与实施、教学评价与改进等方面，旨在提升高校教师在人工智能领域的教学能力。",certificateFile:"人工智能教学培训证书.pdf",createTime:a().subtract(3,"month").format("YYYY-MM-DD HH:mm:ss")},{id:"training_002",teacherId:"teacher_002",trainingName:"软件工程教学方法研修班",trainingType:"academic",organizer:"中国计算机学会",startDate:a().subtract(6,"month").format("YYYY-MM-DD"),endDate:a().subtract(6,"month").add(3,"day").format("YYYY-MM-DD"),duration:24,location:"清华大学",trainingContent:"软件工程教学方法、案例教学、项目驱动教学等内容的深入研讨和实践。",certificateFile:"软件工程教学研修证书.pdf",createTime:a().subtract(6,"month").format("YYYY-MM-DD HH:mm:ss")}],testMentorshipProjects:[{id:"mentorship_001",projectName:"青年教师教学能力提升项目",projectType:"teaching",mentorId:"teacher_001",mentorName:"张教授",menteeId:"teacher_004",menteeName:"陈助教",startDate:a().subtract(6,"month").format("YYYY-MM-DD"),endDate:a().add(6,"month").format("YYYY-MM-DD"),objectives:["提升教学能力","课程建设","教学改革","科研指导"],currentProgress:"项目进展顺利，已完成教学观摩、课程设计指导等环节。陈助教在教学方法和课程设计方面有了显著提升，目前正在准备独立承担一门专业课程的教学工作。",progressPercentage:60,status:"active",createTime:a().subtract(6,"month").format("YYYY-MM-DD HH:mm:ss")},{id:"mentorship_002",projectName:"科研创新能力培养项目",projectType:"research",mentorId:"teacher_002",mentorName:"李副教授",menteeId:"teacher_003",menteeName:"王讲师",startDate:a().subtract(1,"year").format("YYYY-MM-DD"),endDate:a().subtract(1,"month").format("YYYY-MM-DD"),objectives:["科研方法指导","论文写作","项目申报","学术交流"],currentProgress:"项目已圆满完成。王讲师在李副教授的指导下，成功申请了一项省级科研项目，发表了2篇核心期刊论文，科研能力得到显著提升。",progressPercentage:100,status:"completed",createTime:a().subtract(1,"year").format("YYYY-MM-DD HH:mm:ss")}]}),[]);r.useEffect(()=>{0===K.length&&(U(Re),E(Ze),Q(es),X(ss),R(rs),ee(ls))},[Re,Ze,es,ss,rs,ls,K.length]);const as=K.filter(e=>{const s=e.name.toLowerCase().includes(le.toLowerCase())||e.phone.includes(le)||e.idCard.includes(le),r="all"===ie||e.status===ie,l="all"===ne||e.title===ne;return s&&r&&l}),is=e=>{je(e),Xe.setFieldsValue({...e,entryYear:e.entryYear?a(e.entryYear,"YYYY"):void 0}),me(!0)},ts=[{title:"头像",dataIndex:"avatar",key:"avatar",width:80,render:(e,r)=>s.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center",children:e?s.jsx("img",{src:e,alt:r.name,className:"w-12 h-12 rounded-full object-cover"}):s.jsx(u,{className:"text-blue-500 text-lg"})})},{title:"姓名",dataIndex:"name",key:"name",width:100,render:(e,r)=>s.jsxs("div",{children:[s.jsx("div",{className:"font-medium",children:e}),s.jsx("div",{className:"text-xs text-gray-500",children:r.id})]})},{title:"职称",dataIndex:"title",key:"title",width:100,render:e=>{const r={professor:{text:"教授",color:"red"},associate_professor:{text:"副教授",color:"orange"},lecturer:{text:"讲师",color:"blue"},assistant:{text:"助教",color:"green"},senior_engineer:{text:"高级工程师",color:"purple"},engineer:{text:"工程师",color:"cyan"}}[e]||{text:e,color:"default"};return s.jsx(_,{color:r.color,children:r.text})}},{title:"联系方式",dataIndex:"phone",key:"phone",width:120},{title:"入校年份",dataIndex:"entryYear",key:"entryYear",width:100,render:e=>`${e}年`},{title:"任课数量",key:"courseCount",width:100,render:e=>{return s.jsxs("span",{className:"text-blue-600 font-medium",children:[(r=e.id,B.filter(e=>e.teacherId===r).length),"门"]});var r}},{title:"成果积分",key:"points",width:100,render:e=>{return s.jsxs("span",{className:"text-green-600 font-medium",children:[(r=e.id,J.filter(e=>e.teacherId===r).reduce((e,s)=>e+s.points,0)),"分"]});var r}},{title:"状态",dataIndex:"status",key:"status",width:80,render:e=>s.jsx(_,{color:"active"===e?"green":"red",children:"active"===e?"在职":"离职"})},{title:"操作",key:"action",width:200,render:r=>s.jsxs(i,{size:"small",children:[s.jsx(t,{type:"link",size:"small",icon:s.jsx(S,{}),onClick:()=>(pe(r),void oe(!0)),children:"查看"}),s.jsx(t,{type:"link",size:"small",icon:s.jsx(z,{}),onClick:()=>is(r),children:"编辑"}),s.jsx(t,{type:"link",size:"small",danger:!0,icon:s.jsx(H,{}),onClick:()=>{return s=r,void L.confirm({title:"确认删除",content:`确定要删除教师 ${s.name} 的信息吗？`,onOk:async()=>{try{await e.delete(`/teachers/${s.id}`),U(K.filter(e=>e.id!==s.id)),V.success("删除成功")}catch(r){console.error("删除教师错误:",r),V.error("删除失败")}}});var s},children:"删除"})]})}];return s.jsxs("div",{className:"p-6 bg-gray-50 min-h-screen",children:[s.jsxs("div",{className:"flex items-center justify-between mb-6",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"教师业务档案"}),s.jsx("p",{className:"text-gray-600",children:"管理全校教师基础信息和教学工作档案"})]}),s.jsxs(i,{children:[s.jsx(t,{icon:s.jsx(n,{}),onClick:()=>V.info("批量导入功能开发中"),children:"批量导入"}),s.jsx(t,{icon:s.jsx(c,{}),onClick:()=>V.info("导出数据功能开发中"),children:"导出数据"}),s.jsx(t,{icon:s.jsx(d,{}),onClick:Ge,children:"刷新数据"}),s.jsx(t,{type:"primary",icon:s.jsx(m,{}),onClick:()=>{je(null),Xe.resetFields(),me(!0)},children:"新增教师"})]})]}),s.jsxs(h,{gutter:16,className:"mb-6",children:[s.jsx(o,{span:6,children:s.jsx(x,{children:s.jsx(j,{title:"教师总数",value:K.length,suffix:"人",valueStyle:{color:"#1890ff"},prefix:s.jsx(u,{})})})}),s.jsx(o,{span:6,children:s.jsx(x,{children:s.jsx(j,{title:"在职教师",value:K.filter(e=>"active"===e.status).length,suffix:"人",valueStyle:{color:"#52c41a"},prefix:s.jsx(p,{})})})}),s.jsx(o,{span:6,children:s.jsx(x,{children:s.jsx(j,{title:"开设课程",value:B.length,suffix:"门",valueStyle:{color:"#722ed1"},prefix:s.jsx(g,{})})})}),s.jsx(o,{span:6,children:s.jsx(x,{children:s.jsx(j,{title:"总积分",value:J.reduce((e,s)=>e+s.points,0),suffix:"分",valueStyle:{color:"#fa8c16"},prefix:s.jsx(f,{})})})})]}),s.jsxs(x,{className:"mb-6",children:[s.jsxs(h,{gutter:16,className:"mb-4",children:[s.jsx(o,{span:8,children:s.jsx(P,{placeholder:"搜索教师姓名、电话或身份证号",value:le,onChange:e=>ae(e.target.value),onSearch:ae,enterButton:s.jsx(v,{})})}),s.jsx(o,{span:4,children:s.jsxs(b,{placeholder:"状态筛选",value:ie,onChange:te,style:{width:"100%"},allowClear:!0,children:[s.jsx(A,{value:"all",children:"全部状态"}),s.jsx(A,{value:"active",children:"在职"}),s.jsx(A,{value:"inactive",children:"离职"})]})}),s.jsx(o,{span:4,children:s.jsxs(b,{placeholder:"职称筛选",value:ne,onChange:ce,style:{width:"100%"},allowClear:!0,children:[s.jsx(A,{value:"all",children:"全部职称"}),s.jsx(A,{value:"professor",children:"教授"}),s.jsx(A,{value:"associate_professor",children:"副教授"}),s.jsx(A,{value:"lecturer",children:"讲师"}),s.jsx(A,{value:"assistant",children:"助教"}),s.jsx(A,{value:"senior_engineer",children:"高级工程师"})]})}),s.jsx(o,{span:4,children:s.jsx(t,{icon:s.jsx(Y,{}),onClick:()=>{ae(""),te("all"),ce("all"),V.success("筛选条件已重置")},children:"重置筛选"})}),s.jsx(o,{span:4,children:s.jsx("div",{className:"text-right",children:s.jsxs("span",{className:"text-gray-600",children:["共找到 ",s.jsx("span",{className:"font-medium text-blue-600",children:as.length})," 位教师"]})})})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-gray-600",children:"快速筛选："}),s.jsxs(t,{size:"small",type:"professor"===ne?"primary":"default",onClick:()=>ce("professor"===ne?"all":"professor"),children:["教授 (",K.filter(e=>"professor"===e.title).length,")"]}),s.jsxs(t,{size:"small",type:"associate_professor"===ne?"primary":"default",onClick:()=>ce("associate_professor"===ne?"all":"associate_professor"),children:["副教授 (",K.filter(e=>"associate_professor"===e.title).length,")"]}),s.jsxs(t,{size:"small",type:"lecturer"===ne?"primary":"default",onClick:()=>ce("lecturer"===ne?"all":"lecturer"),children:["讲师 (",K.filter(e=>"lecturer"===e.title).length,")"]}),s.jsxs(t,{size:"small",type:"active"===ie?"primary":"default",onClick:()=>te("active"===ie?"all":"active"),children:["在职 (",K.filter(e=>"active"===e.status).length,")"]})]})]}),s.jsxs(x,{children:[ge.length>0&&s.jsx("div",{className:"mb-4 p-3 bg-blue-50 rounded border border-blue-200",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("span",{className:"text-blue-700",children:["已选择 ",s.jsx("strong",{children:ge.length})," 位教师"]}),s.jsxs(i,{children:[s.jsx(t,{size:"small",onClick:()=>{L.confirm({title:"批量删除确认",content:`确定要删除选中的 ${ge.length} 位教师吗？`,onOk:()=>{const e=K.filter(e=>!ge.includes(e.id));U(e),fe([]),V.success(`成功删除 ${ge.length} 位教师`)}})},danger:!0,children:"批量删除"}),s.jsx(t,{size:"small",onClick:()=>fe([]),children:"取消选择"})]})]})}),s.jsx(N,{columns:ts,dataSource:as,rowKey:"id",loading:se,rowSelection:{selectedRowKeys:ge,onChange:fe,getCheckboxProps:e=>({disabled:"inactive"===e.status})},pagination:{pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,s)=>`第 ${null==s?void 0:s[0]}-${null==s?void 0:s[1]} 条，共 ${e} 条记录`},scroll:{x:1200}})]}),s.jsx(D,{title:xe?"编辑教师信息":"新增教师",open:de,onCancel:()=>me(!1),onOk:()=>Xe.submit(),width:800,destroyOnHidden:!0,children:s.jsxs(l,{form:Xe,layout:"vertical",onFinish:async s=>{try{const r={...s,entryYear:s.entryYear?s.entryYear.format("YYYY"):""};if(xe){if((await e.put(`/teachers/${xe.id}`,r)).success){const e=K.map(e=>e.id===xe.id?{...e,...r}:e);U(e),V.success("教师信息更新成功")}}else{const s=await e.post("/teachers",r);s.success&&(U([s.data,...K]),V.success("教师信息添加成功"))}me(!1)}catch(r){console.error("保存教师信息错误:",r),V.error("操作失败")}},children:[s.jsxs(h,{gutter:16,children:[s.jsx(o,{span:8,children:s.jsx(l.Item,{name:"name",label:"姓名",rules:[{required:!0,message:"请输入姓名"}],children:s.jsx(y,{placeholder:"请输入姓名"})})}),s.jsx(o,{span:8,children:s.jsx(l.Item,{name:"idCard",label:"身份证号",rules:[{required:!0,message:"请输入身份证号"},{pattern:/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,message:"请输入正确的身份证号"}],children:s.jsx(y,{placeholder:"请输入身份证号"})})}),s.jsx(o,{span:8,children:s.jsx(l.Item,{name:"gender",label:"性别",rules:[{required:!0,message:"请选择性别"}],children:s.jsxs(b,{placeholder:"请选择性别",children:[s.jsx(A,{value:"male",children:"男"}),s.jsx(A,{value:"female",children:"女"})]})})})]}),s.jsxs(h,{gutter:16,children:[s.jsx(o,{span:8,children:s.jsx(l.Item,{name:"ethnicity",label:"民族",rules:[{required:!0,message:"请输入民族"}],children:s.jsx(y,{placeholder:"请输入民族"})})}),s.jsx(o,{span:8,children:s.jsx(l.Item,{name:"politicalStatus",label:"政治面貌",rules:[{required:!0,message:"请选择政治面貌"}],children:s.jsxs(b,{placeholder:"请选择政治面貌",children:[s.jsx(A,{value:"party_member",children:"中共党员"}),s.jsx(A,{value:"probationary_member",children:"中共预备党员"}),s.jsx(A,{value:"league_member",children:"共青团员"}),s.jsx(A,{value:"democratic_party",children:"民主党派"}),s.jsx(A,{value:"masses",children:"群众"})]})})}),s.jsx(o,{span:8,children:s.jsx(l.Item,{name:"phone",label:"联系电话",rules:[{required:!0,message:"请输入电话号码"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码"}],children:s.jsx(y,{placeholder:"请输入电话号码"})})})]}),s.jsxs(h,{gutter:16,children:[s.jsx(o,{span:8,children:s.jsx(l.Item,{name:"title",label:"职称",rules:[{required:!0,message:"请选择职称"}],children:s.jsxs(b,{placeholder:"请选择职称",children:[s.jsx(A,{value:"professor",children:"教授"}),s.jsx(A,{value:"associate_professor",children:"副教授"}),s.jsx(A,{value:"lecturer",children:"讲师"}),s.jsx(A,{value:"assistant",children:"助教"}),s.jsx(A,{value:"senior_engineer",children:"高级工程师"}),s.jsx(A,{value:"engineer",children:"工程师"})]})})}),s.jsx(o,{span:8,children:s.jsx(l.Item,{name:"entryYear",label:"入校年份",rules:[{required:!0,message:"请选择入校年份"}],children:s.jsx(C,{picker:"year",placeholder:"请选择入校年份",style:{width:"100%"}})})}),s.jsx(o,{span:8,children:s.jsx(l.Item,{name:"status",label:"状态",rules:[{required:!0,message:"请选择状态"}],children:s.jsxs(b,{placeholder:"请选择状态",children:[s.jsx(A,{value:"active",children:"在职"}),s.jsx(A,{value:"inactive",children:"离职"})]})})})]}),s.jsxs(h,{gutter:16,children:[s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"homeAddress",label:"家庭住址",rules:[{required:!0,message:"请输入家庭住址"}],children:s.jsx(y,{placeholder:"请输入家庭住址"})})}),s.jsx(o,{span:6,children:s.jsx(l.Item,{name:"emergencyContact",label:"紧急联系人",rules:[{required:!0,message:"请输入紧急联系人"}],children:s.jsx(y,{placeholder:"请输入紧急联系人"})})}),s.jsx(o,{span:6,children:s.jsx(l.Item,{name:"emergencyPhone",label:"联系人电话",rules:[{required:!0,message:"请输入联系人电话"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码"}],children:s.jsx(y,{placeholder:"请输入联系人电话"})})})]}),s.jsx(l.Item,{name:"resume",label:"个人简历",rules:[{required:!0,message:"请输入个人简历"}],children:s.jsx(y.TextArea,{rows:4,placeholder:"请输入个人简历，包括教育背景、工作经历、主要成就等"})}),s.jsx(I,{children:"证书材料"}),s.jsxs(h,{gutter:16,children:[s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"titleCertificate",label:"职称证书",children:s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(y,{placeholder:"职称证书文件名"}),s.jsx(t,{icon:s.jsx(k,{}),onClick:()=>{Ye("titleCertificate"),ve(!0)},children:"上传"})]})})}),s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"degreeCertificate",label:"学位证书",children:s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(y,{placeholder:"学位证书文件名"}),s.jsx(t,{icon:s.jsx(k,{}),onClick:()=>{Ye("degreeCertificate"),ve(!0)},children:"上传"})]})})})]}),s.jsxs(h,{gutter:16,children:[s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"graduationCertificate",label:"毕业证书",children:s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(y,{placeholder:"毕业证书文件名"}),s.jsx(t,{icon:s.jsx(k,{}),onClick:()=>{Ye("graduationCertificate"),ve(!0)},children:"上传"})]})})}),s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"skillCertificates",label:"技能证书",children:s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(b,{mode:"tags",placeholder:"技能证书文件名（可多个）",style:{flex:1}}),s.jsx(t,{icon:s.jsx(k,{}),onClick:()=>{Ye("skillCertificates"),ve(!0)},children:"上传"})]})})})]}),s.jsx(l.Item,{name:"otherCertificates",label:"其他证书",children:s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(b,{mode:"tags",placeholder:"其他证书文件名（可多个）",style:{flex:1}}),s.jsx(t,{icon:s.jsx(k,{}),onClick:()=>{Ye("otherCertificates"),ve(!0)},children:"上传"})]})})]})}),s.jsx(D,{title:"上传文件",open:ye,onCancel:()=>ve(!1),footer:null,width:600,children:s.jsxs(w.Dragger,{name:"file",multiple:be.includes("Certificates"),accept:".pdf,.doc,.docx,.jpg,.jpeg,.png",beforeUpload:()=>!1,onChange:e=>{e.fileList.length>0&&(V.success(`${e.fileList.length} 个文件准备上传`),setTimeout(()=>{ve(!1),V.success("文件上传成功！")},1e3))},children:[s.jsx("p",{className:"ant-upload-drag-icon",children:s.jsx(k,{})}),s.jsx("p",{className:"ant-upload-text",children:"点击或拖拽文件到此区域上传"}),s.jsxs("p",{className:"ant-upload-hint",children:["支持 PDF、Word、图片格式文件",be.includes("Certificates")&&"，可同时上传多个文件"]})]})}),s.jsx(M,{title:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{children:"教师详细信息"}),ue&&s.jsxs(i,{children:[s.jsx(t,{type:"primary",size:"small",icon:s.jsx(z,{}),onClick:()=>{oe(!1),is(ue)},children:"编辑信息"}),s.jsx(t,{size:"small",icon:s.jsx(q,{}),onClick:()=>V.info("导出个人档案功能开发中"),children:"导出档案"})]})]}),placement:"right",width:800,open:he,onClose:()=>oe(!1),children:ue&&s.jsx("div",{children:s.jsxs(T,{defaultActiveKey:"basic",children:[s.jsx(O,{tab:"基础信息",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center space-x-4 mb-6",children:[s.jsx("div",{className:"w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center",children:ue.avatar?s.jsx("img",{src:ue.avatar,alt:ue.name,className:"w-20 h-20 rounded-full object-cover"}):s.jsx(u,{className:"text-blue-500 text-2xl"})}),s.jsxs("div",{children:[s.jsx("h2",{className:"text-xl font-bold",children:ue.name}),s.jsx("p",{className:"text-gray-600",children:ue.id})]})]}),s.jsx(I,{}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("strong",{children:"身份证号："}),ue.idCard]}),s.jsxs("div",{children:[s.jsx("strong",{children:"性别："}),"male"===ue.gender?"男":"女"]}),s.jsxs("div",{children:[s.jsx("strong",{children:"民族："}),ue.ethnicity]}),s.jsxs("div",{children:[s.jsx("strong",{children:"政治面貌："}),"party_member"===ue.politicalStatus?"中共党员":"league_member"===ue.politicalStatus?"共青团员":"群众"]}),s.jsxs("div",{children:[s.jsx("strong",{children:"联系电话："}),ue.phone]}),s.jsxs("div",{children:[s.jsx("strong",{children:"职称："}),"professor"===ue.title?"教授":"associate_professor"===ue.title?"副教授":"lecturer"===ue.title?"讲师":"assistant"===ue.title?"助教":"高级工程师"]}),s.jsxs("div",{children:[s.jsx("strong",{children:"入校年份："}),ue.entryYear,"年"]}),s.jsxs("div",{children:[s.jsx("strong",{children:"状态："}),s.jsx(_,{color:"active"===ue.status?"green":"red",children:"active"===ue.status?"在职":"离职"})]}),s.jsxs("div",{className:"col-span-2",children:[s.jsx("strong",{children:"家庭住址："}),ue.homeAddress]}),s.jsxs("div",{children:[s.jsx("strong",{children:"紧急联系人："}),ue.emergencyContact]}),s.jsxs("div",{children:[s.jsx("strong",{children:"联系人电话："}),ue.emergencyPhone]})]}),s.jsx(I,{}),s.jsxs("div",{children:[s.jsx("strong",{children:"个人简历："}),s.jsx("div",{className:"mt-2 p-4 bg-gray-50 rounded whitespace-pre-wrap",children:ue.resume})]}),s.jsx(I,{}),s.jsxs("div",{children:[s.jsx("strong",{children:"证书材料："}),s.jsxs("div",{className:"mt-2 space-y-2",children:[ue.titleCertificate&&s.jsxs("div",{className:"flex items-center justify-between p-2 bg-blue-50 rounded",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-blue-600",children:"🏆"}),s.jsxs("span",{children:["职称证书：",ue.titleCertificate]})]}),s.jsxs(i,{children:[s.jsx(t,{size:"small",type:"link",children:"预览"}),s.jsx(t,{size:"small",type:"link",children:"下载"})]})]}),ue.degreeCertificate&&s.jsxs("div",{className:"flex items-center justify-between p-2 bg-green-50 rounded",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-green-600",children:"🎓"}),s.jsxs("span",{children:["学位证书：",ue.degreeCertificate]})]}),s.jsxs(i,{children:[s.jsx(t,{size:"small",type:"link",children:"预览"}),s.jsx(t,{size:"small",type:"link",children:"下载"})]})]}),ue.graduationCertificate&&s.jsxs("div",{className:"flex items-center justify-between p-2 bg-purple-50 rounded",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-purple-600",children:"📜"}),s.jsxs("span",{children:["毕业证书：",ue.graduationCertificate]})]}),s.jsxs(i,{children:[s.jsx(t,{size:"small",type:"link",children:"预览"}),s.jsx(t,{size:"small",type:"link",children:"下载"})]})]}),ue.skillCertificates&&ue.skillCertificates.length>0&&s.jsxs("div",{className:"p-2 bg-orange-50 rounded",children:[s.jsx("div",{className:"font-medium mb-2",children:"技能证书："}),ue.skillCertificates.map((e,r)=>s.jsxs("div",{className:"flex items-center justify-between py-1",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-orange-600",children:"⚡"}),s.jsx("span",{children:e})]}),s.jsxs(i,{children:[s.jsx(t,{size:"small",type:"link",children:"预览"}),s.jsx(t,{size:"small",type:"link",children:"下载"})]})]},r))]}),ue.otherCertificates&&ue.otherCertificates.length>0&&s.jsxs("div",{className:"p-2 bg-gray-50 rounded",children:[s.jsx("div",{className:"font-medium mb-2",children:"其他证书："}),ue.otherCertificates.map((e,r)=>s.jsxs("div",{className:"flex items-center justify-between py-1",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-gray-600",children:"📋"}),s.jsx("span",{children:e})]}),s.jsxs(i,{children:[s.jsx(t,{size:"small",type:"link",children:"预览"}),s.jsx(t,{size:"small",type:"link",children:"下载"})]})]},r))]})]})]})]})},"basic"),s.jsx(O,{tab:"任课情况",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h3",{className:"text-lg font-medium",children:"任课情况管理"}),s.jsx(t,{type:"primary",icon:s.jsx(m,{}),onClick:()=>{Fe(null),Be.resetFields(),Be.setFieldsValue({teacherId:null==ue?void 0:ue.id}),Ie(!0)},children:"新增课程"})]}),B.filter(e=>e.teacherId===ue.id).map(e=>s.jsx(x,{size:"small",children:s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("strong",{children:"课程名称："}),e.courseName]}),s.jsxs("div",{children:[s.jsx("strong",{children:"课程类型："}),"theory"===e.courseType?"理论课":"practice"===e.courseType?"实践课":"理实一体"]}),s.jsxs("div",{children:[s.jsx("strong",{children:"学期："}),"spring"===e.semester?"春季学期":"秋季学期"," ",e.academicYear]}),s.jsxs("div",{children:[s.jsx("strong",{children:"课时数："}),e.courseHours,"小时"]}),s.jsxs("div",{children:[s.jsx("strong",{children:"学生数："}),e.studentCount,"人"]}),s.jsxs("div",{children:[s.jsx("strong",{children:"状态："}),s.jsx(_,{color:"active"===e.status?"green":"blue",children:"active"===e.status?"进行中":"已完成"})]}),s.jsx("div",{className:"col-span-2",children:s.jsxs(i,{children:[s.jsx(t,{size:"small",type:"link",icon:s.jsx(z,{}),onClick:()=>(e=>{Fe(e),Be.setFieldsValue(e),Ie(!0)})(e),children:"编辑"}),s.jsx(t,{size:"small",type:"link",danger:!0,icon:s.jsx(H,{}),onClick:()=>{L.confirm({title:"确认删除",content:`确定要删除课程 ${e.courseName} 吗？`,onOk:()=>{const s=B.filter(s=>s.id!==e.id);E(s),V.success("删除成功")}})},children:"删除"})]})})]})},e.id)),0===B.filter(e=>e.teacherId===ue.id).length&&s.jsx("div",{className:"text-center py-8 text-gray-500",children:"暂无任课信息"})]})},"courses"),s.jsx(O,{tab:"教学成果",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h3",{className:"text-lg font-medium",children:"教学成果管理"}),s.jsx(t,{type:"primary",icon:s.jsx(m,{}),onClick:()=>{Pe(null),Ee.resetFields(),Ee.setFieldsValue({teacherId:null==ue?void 0:ue.id}),we(!0)},children:"新增成果"})]}),J.filter(e=>e.teacherId===ue.id).map(e=>s.jsx(x,{size:"small",children:s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{className:"col-span-2",children:[s.jsx("strong",{children:"成果标题："}),e.title]}),s.jsxs("div",{children:[s.jsx("strong",{children:"成果类型："}),"competition"===e.type?"竞赛":"paper"===e.type?"论文":"award"===e.type?"奖项":"专利"]}),s.jsxs("div",{children:[s.jsx("strong",{children:"级别："}),"national"===e.level?"国家级":"provincial"===e.level?"省级":"校级"]}),s.jsxs("div",{children:[s.jsx("strong",{children:"积分："}),s.jsxs("span",{className:"text-green-600 font-medium",children:[e.points,"分"]})]}),s.jsxs("div",{children:[s.jsx("strong",{children:"获奖时间："}),e.awardTime]}),s.jsx("div",{className:"col-span-2",children:s.jsxs(i,{children:[s.jsx(t,{size:"small",type:"link",icon:s.jsx(z,{}),onClick:()=>(e=>{Pe(e),Ee.setFieldsValue({...e,awardTime:e.awardTime?a(e.awardTime):void 0}),we(!0)})(e),children:"编辑"}),s.jsx(t,{size:"small",type:"link",danger:!0,icon:s.jsx(H,{}),onClick:()=>{L.confirm({title:"确认删除",content:`确定要删除成果 ${e.title} 吗？`,onOk:()=>{const s=J.filter(s=>s.id!==e.id);Q(s),V.success("删除成功")}})},children:"删除"})]})})]})},e.id)),0===J.filter(e=>e.teacherId===ue.id).length&&s.jsx("div",{className:"text-center py-8 text-gray-500",children:"暂无教学成果"})]})},"achievements"),s.jsx(O,{tab:"教学材料",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h3",{className:"text-lg font-medium",children:"教学材料管理"}),s.jsx(t,{type:"primary",icon:s.jsx(m,{}),onClick:()=>{Oe(null),Je.resetFields(),Je.setFieldsValue({teacherId:null==ue?void 0:ue.id}),Te(!0)},children:"新增材料"})]}),W.filter(e=>e.teacherId===ue.id).map(e=>s.jsx(x,{size:"small",children:s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{className:"col-span-2",children:[s.jsx("strong",{children:"材料名称："}),e.materialName]}),s.jsxs("div",{children:[s.jsx("strong",{children:"材料类型："}),s.jsx(_,{color:"calendar"===e.materialType?"blue":"plan"===e.materialType?"green":"syllabus"===e.materialType?"orange":"courseware"===e.materialType?"purple":"default",children:"calendar"===e.materialType?"教学日历":"plan"===e.materialType?"教学计划":"syllabus"===e.materialType?"教学大纲":"courseware"===e.materialType?"课件":"其他"})]}),s.jsxs("div",{children:[s.jsx("strong",{children:"关联课程："}),e.courseName||"无"]}),s.jsxs("div",{children:[s.jsx("strong",{children:"学期："}),"spring"===e.semester?"春季":"fall"===e.semester?"秋季":"夏季","学期 ",e.academicYear]}),s.jsxs("div",{children:[s.jsx("strong",{children:"文件名："}),e.fileName]}),s.jsxs("div",{children:[s.jsx("strong",{children:"文件大小："}),(e.fileSize/1024).toFixed(1)," KB"]}),s.jsxs("div",{children:[s.jsx("strong",{children:"上传时间："}),e.uploadTime]}),s.jsxs("div",{className:"col-span-2",children:[s.jsx("strong",{children:"描述："}),e.description||"无描述"]}),s.jsx("div",{className:"col-span-2",children:s.jsxs(i,{children:[s.jsx(t,{size:"small",type:"link",icon:s.jsx(S,{}),children:"预览"}),s.jsx(t,{size:"small",type:"link",icon:s.jsx(q,{}),children:"下载"}),s.jsx(t,{size:"small",type:"link",icon:s.jsx(z,{}),onClick:()=>(e=>{Oe(e),Je.setFieldsValue(e),Te(!0)})(e),children:"编辑"}),s.jsx(t,{size:"small",type:"link",danger:!0,icon:s.jsx(H,{}),onClick:()=>{L.confirm({title:"确认删除",content:`确定要删除材料 ${e.materialName} 吗？`,onOk:()=>{const s=W.filter(s=>s.id!==e.id);X(s),V.success("删除成功")}})},children:"删除"})]})})]})},e.id)),0===W.filter(e=>e.teacherId===ue.id).length&&s.jsx("div",{className:"text-center py-8 text-gray-500",children:"暂无教学材料"})]})},"materials"),s.jsx(O,{tab:"师资培训",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h3",{className:"text-lg font-medium",children:"师资培训记录"}),s.jsx(t,{type:"primary",icon:s.jsx(m,{}),onClick:()=>{Le(null),Qe.resetFields(),Qe.setFieldsValue({teacherId:null==ue?void 0:ue.id}),ze(!0)},children:"新增记录"})]}),G.filter(e=>e.teacherId===ue.id).map(e=>s.jsx(x,{size:"small",children:s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{className:"col-span-2",children:[s.jsx("strong",{children:"培训名称："}),e.trainingName]}),s.jsxs("div",{children:[s.jsx("strong",{children:"培训类型："}),s.jsx(_,{color:"academic"===e.trainingType?"blue":"skill"===e.trainingType?"green":"management"===e.trainingType?"orange":"default",children:"academic"===e.trainingType?"学术培训":"skill"===e.trainingType?"技能培训":"management"===e.trainingType?"管理培训":"其他"})]}),s.jsxs("div",{children:[s.jsx("strong",{children:"主办单位："}),e.organizer]}),s.jsxs("div",{children:[s.jsx("strong",{children:"培训时间："}),e.startDate," 至 ",e.endDate]}),s.jsxs("div",{children:[s.jsx("strong",{children:"培训时长："}),e.duration," 小时"]}),s.jsxs("div",{children:[s.jsx("strong",{children:"培训地点："}),e.location]}),s.jsxs("div",{children:[s.jsx("strong",{children:"证书文件："}),e.certificateFile?s.jsxs(i,{children:[s.jsx("span",{children:e.certificateFile}),s.jsx(t,{size:"small",type:"link",children:"下载"})]}):"无"]}),s.jsxs("div",{className:"col-span-2",children:[s.jsx("strong",{children:"培训内容："}),s.jsx("div",{className:"mt-1 p-2 bg-gray-50 rounded text-sm",children:e.trainingContent})]}),s.jsx("div",{className:"col-span-2",children:s.jsxs(i,{children:[s.jsx(t,{size:"small",type:"link",icon:s.jsx(z,{}),onClick:()=>(e=>{Le(e),Qe.setFieldsValue({...e,startDate:e.startDate?a(e.startDate):void 0,endDate:e.endDate?a(e.endDate):void 0}),ze(!0)})(e),children:"编辑"}),s.jsx(t,{size:"small",type:"link",danger:!0,icon:s.jsx(H,{}),onClick:()=>{L.confirm({title:"确认删除",content:`确定要删除培训记录 ${e.trainingName} 吗？`,onOk:()=>{const s=G.filter(s=>s.id!==e.id);R(s),V.success("删除成功")}})},children:"删除"})]})})]})},e.id)),0===G.filter(e=>e.teacherId===ue.id).length&&s.jsx("div",{className:"text-center py-8 text-gray-500",children:"暂无培训记录"})]})},"trainings"),s.jsx(O,{tab:"青蓝筑梦",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h3",{className:"text-lg font-medium",children:"青蓝筑梦项目"}),s.jsx(t,{type:"primary",icon:s.jsx(m,{}),onClick:()=>{Ue(null),We.resetFields(),ue&&We.setFieldsValue({mentorId:ue.id,mentorName:ue.name}),Se(!0)},children:"新增项目"})]}),Z.filter(e=>e.mentorId===ue.id||e.menteeId===ue.id).map(e=>s.jsx(x,{size:"small",children:s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsx("div",{className:"col-span-2",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("strong",{children:e.projectName}),s.jsx(_,{color:"active"===e.status?"green":"completed"===e.status?"blue":"orange",children:"active"===e.status?"进行中":"completed"===e.status?"已完成":"暂停"})]})}),s.jsxs("div",{children:[s.jsx("strong",{children:"项目类型："}),s.jsx(_,{color:"teaching"===e.projectType?"blue":"research"===e.projectType?"green":"purple",children:"teaching"===e.projectType?"教学指导":"research"===e.projectType?"科研指导":"创新指导"})]}),s.jsxs("div",{children:[s.jsx("strong",{children:"项目周期："}),e.startDate," 至 ",e.endDate]}),s.jsxs("div",{children:[s.jsx("strong",{children:"导师："}),e.mentorName]}),s.jsxs("div",{children:[s.jsx("strong",{children:"学员："}),e.menteeName]}),s.jsxs("div",{className:"col-span-2",children:[s.jsx("strong",{children:"项目目标："}),s.jsx("div",{className:"mt-1",children:e.objectives.map((e,r)=>s.jsx(_,{className:"mb-1",children:e},r))})]}),s.jsxs("div",{className:"col-span-2",children:[s.jsx("strong",{children:"当前进度："}),s.jsxs("div",{className:"mt-2",children:[s.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[s.jsx("div",{className:"flex-1 bg-gray-200 rounded-full h-2",children:s.jsx("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:`${e.progressPercentage}%`}})}),s.jsxs("span",{className:"text-sm font-medium",children:[e.progressPercentage,"%"]})]}),s.jsx("div",{className:"text-sm text-gray-600 p-2 bg-gray-50 rounded",children:e.currentProgress})]})]}),s.jsx("div",{className:"col-span-2",children:s.jsxs(i,{children:[s.jsx(t,{size:"small",type:"link",icon:s.jsx(z,{}),onClick:()=>{return Ue(s=e),We.setFieldsValue({...s,startDate:s.startDate?a(s.startDate):void 0,endDate:s.endDate?a(s.endDate):void 0}),void Se(!0);var s},children:"编辑"}),s.jsx(t,{size:"small",type:"link",icon:s.jsx(S,{}),children:"详情"}),s.jsx(t,{size:"small",type:"link",danger:!0,icon:s.jsx(H,{}),onClick:()=>{L.confirm({title:"确认删除",content:`确定要删除项目 ${e.projectName} 吗？`,onOk:()=>{const s=Z.filter(s=>s.id!==e.id);ee(s),V.success("删除成功")}})},children:"删除"})]})})]})},e.id)),0===Z.filter(e=>e.mentorId===ue.id||e.menteeId===ue.id).length&&s.jsx("div",{className:"text-center py-8 text-gray-500",children:"暂无青蓝筑梦项目"})]})},"mentorship"),s.jsx(O,{tab:"文件管理",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h3",{className:"text-lg font-medium",children:"教师相关文件"}),s.jsx(t,{type:"primary",icon:s.jsx(k,{}),onClick:()=>V.info("上传文件功能开发中"),children:"上传文件"})]}),s.jsxs("div",{className:"grid grid-cols-1 gap-4",children:[s.jsx(x,{size:"small",title:"证书文件",children:s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-blue-600",children:"📄"}),s.jsx("span",{children:"职称证书.pdf"}),s.jsx(_,{color:"green",children:"已上传"})]}),s.jsxs(i,{children:[s.jsx(t,{size:"small",type:"link",children:"预览"}),s.jsx(t,{size:"small",type:"link",children:"下载"}),s.jsx(t,{size:"small",type:"link",danger:!0,children:"删除"})]})]}),s.jsxs("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-blue-600",children:"📄"}),s.jsx("span",{children:"学位证书.pdf"}),s.jsx(_,{color:"green",children:"已上传"})]}),s.jsxs(i,{children:[s.jsx(t,{size:"small",type:"link",children:"预览"}),s.jsx(t,{size:"small",type:"link",children:"下载"}),s.jsx(t,{size:"small",type:"link",danger:!0,children:"删除"})]})]})]})}),s.jsx(x,{size:"small",title:"教学材料",children:s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-green-600",children:"📊"}),s.jsxs("span",{children:["教学大纲_",ue.name,".docx"]}),s.jsx(_,{color:"blue",children:"教学大纲"})]}),s.jsxs(i,{children:[s.jsx(t,{size:"small",type:"link",children:"预览"}),s.jsx(t,{size:"small",type:"link",children:"下载"}),s.jsx(t,{size:"small",type:"link",danger:!0,children:"删除"})]})]}),s.jsxs("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-green-600",children:"📊"}),s.jsxs("span",{children:["课件_",ue.name,".pptx"]}),s.jsx(_,{color:"orange",children:"课件"})]}),s.jsxs(i,{children:[s.jsx(t,{size:"small",type:"link",children:"预览"}),s.jsx(t,{size:"small",type:"link",children:"下载"}),s.jsx(t,{size:"small",type:"link",danger:!0,children:"删除"})]})]})]})}),s.jsx(x,{size:"small",title:"成果材料",children:s.jsxs("div",{className:"space-y-2",children:[J.filter(e=>e.teacherId===ue.id).map(e=>s.jsxs("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-orange-600",children:"🏆"}),s.jsxs("span",{children:[e.title,".pdf"]}),s.jsx(_,{color:"red",children:"competition"===e.type?"竞赛证书":"paper"===e.type?"学术论文":"award"===e.type?"获奖证书":"专利证书"})]}),s.jsxs(i,{children:[s.jsx(t,{size:"small",type:"link",children:"预览"}),s.jsx(t,{size:"small",type:"link",children:"下载"}),s.jsx(t,{size:"small",type:"link",danger:!0,children:"删除"})]})]},e.id)),0===J.filter(e=>e.teacherId===ue.id).length&&s.jsx("div",{className:"text-center py-4 text-gray-500",children:"暂无成果材料"})]})})]})]})},"files")]})})}),s.jsx(D,{title:qe?"编辑课程信息":"新增课程",open:Ce,onCancel:()=>Ie(!1),onOk:()=>Be.submit(),width:600,destroyOnHidden:!0,children:s.jsxs(l,{form:Be,layout:"vertical",onFinish:async e=>{try{const s={id:(null==qe?void 0:qe.id)||`course_${Date.now()}`,...e};if(qe){const e=B.map(e=>e.id===qe.id?s:e);E(e),V.success("课程信息更新成功")}else E([...B,s]),V.success("课程信息添加成功");Ie(!1)}catch(s){V.error("操作失败")}},children:[s.jsx(l.Item,{name:"teacherId",hidden:!0,children:s.jsx(y,{})}),s.jsxs(h,{gutter:16,children:[s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"courseName",label:"课程名称",rules:[{required:!0,message:"请输入课程名称"}],children:s.jsx(y,{placeholder:"请输入课程名称"})})}),s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"courseType",label:"课程类型",rules:[{required:!0,message:"请选择课程类型"}],children:s.jsxs(b,{placeholder:"请选择课程类型",children:[s.jsx(A,{value:"theory",children:"理论课"}),s.jsx(A,{value:"practice",children:"实践课"}),s.jsx(A,{value:"mixed",children:"理实一体"})]})})})]}),s.jsxs(h,{gutter:16,children:[s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"semester",label:"学期",rules:[{required:!0,message:"请选择学期"}],children:s.jsxs(b,{placeholder:"请选择学期",children:[s.jsx(A,{value:"spring",children:"春季学期"}),s.jsx(A,{value:"fall",children:"秋季学期"}),s.jsx(A,{value:"summer",children:"夏季学期"})]})})}),s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"academicYear",label:"学年",rules:[{required:!0,message:"请输入学年"}],children:s.jsx(y,{placeholder:"如：2023-2024"})})})]}),s.jsxs(h,{gutter:16,children:[s.jsx(o,{span:8,children:s.jsx(l.Item,{name:"courseHours",label:"课时数",rules:[{required:!0,message:"请输入课时数"}],children:s.jsx(y,{type:"number",placeholder:"课时数",addonAfter:"小时"})})}),s.jsx(o,{span:8,children:s.jsx(l.Item,{name:"studentCount",label:"学生数",rules:[{required:!0,message:"请输入学生数"}],children:s.jsx(y,{type:"number",placeholder:"学生数",addonAfter:"人"})})}),s.jsx(o,{span:8,children:s.jsx(l.Item,{name:"status",label:"状态",rules:[{required:!0,message:"请选择状态"}],children:s.jsxs(b,{placeholder:"请选择状态",children:[s.jsx(A,{value:"active",children:"进行中"}),s.jsx(A,{value:"completed",children:"已完成"}),s.jsx(A,{value:"cancelled",children:"已取消"})]})})})]})]})}),s.jsx(D,{title:$e?"编辑教学成果":"新增教学成果",open:ke,onCancel:()=>we(!1),onOk:()=>Ee.submit(),width:600,destroyOnHidden:!0,children:s.jsxs(l,{form:Ee,layout:"vertical",onFinish:async e=>{try{const s={id:(null==$e?void 0:$e.id)||`achievement_${Date.now()}`,...e,awardTime:e.awardTime?e.awardTime.format("YYYY-MM-DD"):""};if($e){const e=J.map(e=>e.id===$e.id?s:e);Q(e),V.success("成果信息更新成功")}else Q([...J,s]),V.success("成果信息添加成功");we(!1)}catch(s){V.error("操作失败")}},children:[s.jsx(l.Item,{name:"teacherId",hidden:!0,children:s.jsx(y,{})}),s.jsx(l.Item,{name:"title",label:"成果标题",rules:[{required:!0,message:"请输入成果标题"}],children:s.jsx(y,{placeholder:"请输入成果标题"})}),s.jsxs(h,{gutter:16,children:[s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"type",label:"成果类型",rules:[{required:!0,message:"请选择成果类型"}],children:s.jsxs(b,{placeholder:"请选择成果类型",children:[s.jsx(A,{value:"competition",children:"竞赛"}),s.jsx(A,{value:"award",children:"教学成果奖"}),s.jsx(A,{value:"paper",children:"论文"}),s.jsx(A,{value:"textbook",children:"教材"}),s.jsx(A,{value:"patent",children:"专利"}),s.jsx(A,{value:"software",children:"软著"}),s.jsx(A,{value:"other",children:"其他"})]})})}),s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"level",label:"级别",rules:[{required:!0,message:"请选择级别"}],children:s.jsxs(b,{placeholder:"请选择级别",children:[s.jsx(A,{value:"national",children:"国家级"}),s.jsx(A,{value:"provincial",children:"省级"}),s.jsx(A,{value:"municipal",children:"市级"}),s.jsx(A,{value:"school",children:"校级"})]})})})]}),s.jsxs(h,{gutter:16,children:[s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"points",label:"积分",rules:[{required:!0,message:"请输入积分"}],children:s.jsx(y,{type:"number",placeholder:"积分",addonAfter:"分"})})}),s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"awardTime",label:"获奖时间",rules:[{required:!0,message:"请选择获奖时间"}],children:s.jsx(C,{placeholder:"请选择获奖时间",style:{width:"100%"}})})})]})]})}),s.jsx(D,{title:Ae?"编辑教学材料":"新增教学材料",open:Me,onCancel:()=>Te(!1),onOk:()=>Je.submit(),width:600,destroyOnHidden:!0,children:s.jsxs(l,{form:Je,layout:"vertical",onFinish:async e=>{try{const s={id:(null==Ae?void 0:Ae.id)||`material_${Date.now()}`,...e,fileSize:1024e3,uploadTime:a().format("YYYY-MM-DD HH:mm:ss")};if(Ae){const e=W.map(e=>e.id===Ae.id?s:e);X(e),V.success("教学材料更新成功")}else X([...W,s]),V.success("教学材料添加成功");Te(!1)}catch(s){V.error("操作失败")}},children:[s.jsx(l.Item,{name:"teacherId",hidden:!0,children:s.jsx(y,{})}),s.jsx(l.Item,{name:"materialName",label:"材料名称",rules:[{required:!0,message:"请输入材料名称"}],children:s.jsx(y,{placeholder:"请输入材料名称"})}),s.jsxs(h,{gutter:16,children:[s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"materialType",label:"材料类型",rules:[{required:!0,message:"请选择材料类型"}],children:s.jsxs(b,{placeholder:"请选择材料类型",children:[s.jsx(A,{value:"calendar",children:"教学日历"}),s.jsx(A,{value:"plan",children:"教学计划"}),s.jsx(A,{value:"syllabus",children:"教学大纲"}),s.jsx(A,{value:"courseware",children:"课件"}),s.jsx(A,{value:"other",children:"其他"})]})})}),s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"courseName",label:"关联课程",children:s.jsx(y,{placeholder:"请输入关联课程名称"})})})]}),s.jsxs(h,{gutter:16,children:[s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"semester",label:"学期",rules:[{required:!0,message:"请选择学期"}],children:s.jsxs(b,{placeholder:"请选择学期",children:[s.jsx(A,{value:"spring",children:"春季学期"}),s.jsx(A,{value:"fall",children:"秋季学期"}),s.jsx(A,{value:"summer",children:"夏季学期"})]})})}),s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"academicYear",label:"学年",rules:[{required:!0,message:"请输入学年"}],children:s.jsx(y,{placeholder:"如：2023-2024"})})})]}),s.jsx(l.Item,{name:"description",label:"材料描述",children:s.jsx(y.TextArea,{rows:3,placeholder:"请输入材料描述"})}),s.jsx(l.Item,{name:"fileName",label:"文件上传",rules:[{required:!0,message:"请上传文件"}],children:s.jsx(w,{beforeUpload:()=>!1,maxCount:1,accept:".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx",onChange:e=>{e.fileList.length>0&&Je.setFieldsValue({fileName:e.fileList[0].name})},children:s.jsx(t,{icon:s.jsx(k,{}),children:"选择文件"})})})]})}),s.jsx(D,{title:Ve?"编辑培训记录":"新增培训记录",open:_e,onCancel:()=>ze(!1),onOk:()=>Qe.submit(),width:700,destroyOnHidden:!0,children:s.jsxs(l,{form:Qe,layout:"vertical",onFinish:async e=>{try{const s={id:(null==Ve?void 0:Ve.id)||`training_${Date.now()}`,...e,startDate:e.startDate?e.startDate.format("YYYY-MM-DD"):"",endDate:e.endDate?e.endDate.format("YYYY-MM-DD"):"",createTime:a().format("YYYY-MM-DD HH:mm:ss")};if(Ve){const e=G.map(e=>e.id===Ve.id?s:e);R(e),V.success("培训记录更新成功")}else R([...G,s]),V.success("培训记录添加成功");ze(!1)}catch(s){V.error("操作失败")}},children:[s.jsx(l.Item,{name:"teacherId",hidden:!0,children:s.jsx(y,{})}),s.jsx(l.Item,{name:"trainingName",label:"培训名称",rules:[{required:!0,message:"请输入培训名称"}],children:s.jsx(y,{placeholder:"请输入培训名称"})}),s.jsxs(h,{gutter:16,children:[s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"trainingType",label:"培训类型",rules:[{required:!0,message:"请选择培训类型"}],children:s.jsxs(b,{placeholder:"请选择培训类型",children:[s.jsx(A,{value:"academic",children:"学术培训"}),s.jsx(A,{value:"skill",children:"技能培训"}),s.jsx(A,{value:"management",children:"管理培训"}),s.jsx(A,{value:"other",children:"其他"})]})})}),s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"organizer",label:"主办单位",rules:[{required:!0,message:"请输入主办单位"}],children:s.jsx(y,{placeholder:"请输入主办单位"})})})]}),s.jsxs(h,{gutter:16,children:[s.jsx(o,{span:8,children:s.jsx(l.Item,{name:"startDate",label:"开始日期",rules:[{required:!0,message:"请选择开始日期"}],children:s.jsx(C,{placeholder:"开始日期",style:{width:"100%"}})})}),s.jsx(o,{span:8,children:s.jsx(l.Item,{name:"endDate",label:"结束日期",rules:[{required:!0,message:"请选择结束日期"}],children:s.jsx(C,{placeholder:"结束日期",style:{width:"100%"}})})}),s.jsx(o,{span:8,children:s.jsx(l.Item,{name:"duration",label:"培训时长",rules:[{required:!0,message:"请输入培训时长"}],children:s.jsx(y,{type:"number",placeholder:"培训时长",addonAfter:"小时"})})})]}),s.jsx(l.Item,{name:"location",label:"培训地点",rules:[{required:!0,message:"请输入培训地点"}],children:s.jsx(y,{placeholder:"请输入培训地点"})}),s.jsx(l.Item,{name:"trainingContent",label:"培训内容",rules:[{required:!0,message:"请输入培训内容"}],children:s.jsx(y.TextArea,{rows:4,placeholder:"请输入培训内容描述"})}),s.jsx(l.Item,{name:"certificateFile",label:"培训证书",children:s.jsx(w,{beforeUpload:()=>!1,maxCount:1,accept:".pdf,.jpg,.jpeg,.png",onChange:e=>{e.fileList.length>0&&Qe.setFieldsValue({certificateFile:e.fileList[0].name})},children:s.jsx(t,{icon:s.jsx(k,{}),children:"上传证书"})})})]})}),s.jsx(D,{title:Ke?"编辑青蓝筑梦项目":"新增青蓝筑梦项目",open:He,onCancel:()=>Se(!1),onOk:()=>We.submit(),width:700,destroyOnHidden:!0,children:s.jsxs(l,{form:We,layout:"vertical",onFinish:async e=>{try{const s={id:(null==Ke?void 0:Ke.id)||`mentorship_${Date.now()}`,...e,startDate:e.startDate?e.startDate.format("YYYY-MM-DD"):"",endDate:e.endDate?e.endDate.format("YYYY-MM-DD"):"",createTime:a().format("YYYY-MM-DD HH:mm:ss")};if(Ke){const e=Z.map(e=>e.id===Ke.id?s:e);ee(e),V.success("项目信息更新成功")}else ee([...Z,s]),V.success("项目信息添加成功");Se(!1)}catch(s){V.error("操作失败")}},children:[s.jsx(l.Item,{name:"projectName",label:"项目名称",rules:[{required:!0,message:"请输入项目名称"}],children:s.jsx(y,{placeholder:"请输入项目名称"})}),s.jsxs(h,{gutter:16,children:[s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"projectType",label:"项目类型",rules:[{required:!0,message:"请选择项目类型"}],children:s.jsxs(b,{placeholder:"请选择项目类型",children:[s.jsx(A,{value:"teaching",children:"教学指导"}),s.jsx(A,{value:"research",children:"科研指导"}),s.jsx(A,{value:"innovation",children:"创新指导"})]})})}),s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"status",label:"项目状态",rules:[{required:!0,message:"请选择项目状态"}],children:s.jsxs(b,{placeholder:"请选择项目状态",children:[s.jsx(A,{value:"active",children:"进行中"}),s.jsx(A,{value:"completed",children:"已完成"}),s.jsx(A,{value:"suspended",children:"暂停"})]})})})]}),s.jsxs(h,{gutter:16,children:[s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"mentorId",label:"导师ID",rules:[{required:!0,message:"请输入导师ID"}],children:s.jsx(y,{placeholder:"导师ID",disabled:!0})})}),s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"mentorName",label:"导师姓名",rules:[{required:!0,message:"请输入导师姓名"}],children:s.jsx(y,{placeholder:"导师姓名",disabled:!0})})})]}),s.jsxs(h,{gutter:16,children:[s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"menteeId",label:"学员ID",rules:[{required:!0,message:"请输入学员ID"}],children:s.jsx(y,{placeholder:"请输入学员ID"})})}),s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"menteeName",label:"学员姓名",rules:[{required:!0,message:"请输入学员姓名"}],children:s.jsx(y,{placeholder:"请输入学员姓名"})})})]}),s.jsxs(h,{gutter:16,children:[s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"startDate",label:"开始日期",rules:[{required:!0,message:"请选择开始日期"}],children:s.jsx(C,{placeholder:"开始日期",style:{width:"100%"}})})}),s.jsx(o,{span:12,children:s.jsx(l.Item,{name:"endDate",label:"结束日期",rules:[{required:!0,message:"请选择结束日期"}],children:s.jsx(C,{placeholder:"结束日期",style:{width:"100%"}})})})]}),s.jsx(l.Item,{name:"objectives",label:"项目目标",rules:[{required:!0,message:"请输入项目目标"}],children:s.jsx(b,{mode:"tags",placeholder:"请输入项目目标（可添加多个）",style:{width:"100%"}})}),s.jsx(l.Item,{name:"progressPercentage",label:"完成进度",rules:[{required:!0,message:"请输入完成进度"}],children:s.jsx(y,{type:"number",placeholder:"完成进度",addonAfter:"%",min:0,max:100})}),s.jsx(l.Item,{name:"currentProgress",label:"当前进展",rules:[{required:!0,message:"请输入当前进展"}],children:s.jsx(y.TextArea,{rows:4,placeholder:"请详细描述项目当前进展情况"})})]})})]})};export{V as default};
