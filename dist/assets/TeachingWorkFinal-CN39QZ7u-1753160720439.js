import{k as e,j as s}from"./index-DXaqwR6F-1753160720439.js";import{r as l,aq as n,a as r,ao as t,ai as a,F as i,S as c,ay as d,Q as x,ae as o,af as h,aB as j,M as m,G as p,H as u,U as y,q as g,bk as v,ax as f,aw as b,I as N,as as _,aH as k,ac as I,s as w}from"./antd-lXsGnH6e-1753160720439.js";import"./vendor-D2RBMdQ0-1753160720439.js";const C=()=>{const[C,z]=l.useState([]),[O,S]=l.useState(!0),[T,$]=l.useState(""),[U,Y]=l.useState(!1),[A,F]=l.useState(!1),[P,q]=l.useState(null),[L,D]=l.useState(null),[K]=n.useForm();l.useEffect(()=>{H()},[]);const H=async()=>{try{S(!0),$("");const s=await e.get("/teachers");if(s.data&&s.data.success&&s.data.data){const e=s.data.data;z(e),console.log("✅ 教师数据加载成功，数量:",e.length)}else $("API响应格式错误"),console.error("❌ API响应格式错误:",s)}catch(s){console.error("❌ 加载教师数据失败:",s),$("加载数据失败: "+((null==s?void 0:s.message)||"未知错误"))}finally{S(!1)}},W=e=>{console.log("编辑教师:",e),D(e),K.setFieldsValue({name:e.name,employeeId:e.employee_id,gender:e.gender,idCard:e.id_card,ethnicity:e.ethnicity,politicalStatus:e.political_status,phone:e.phone,title:e.title,degree:e.degree,entryYear:e.join_year?I().year(e.join_year):null,homeAddress:e.address,emergencyContact:e.emergency_contact,emergencyPhone:e.emergency_phone,resume:e.resume}),F(!0)},B=async(s,l)=>{try{if(!(null==P?void 0:P.id))return w.error("请先选择教师"),!1;const n=new FormData;n.append("file",s),n.append("module","teacher_certificate"),n.append("reference_id",P.id.toString()),n.append("certificate_type",l);const r=await e.post("/upload/single",n,{headers:{"Content-Type":"multipart/form-data"}});if(console.log("上传响应:",r),r.data&&r.data.success){const s=r.data.data.url,n={[l]:s},t=await e.put(`/teachers/${P.id}`,n);return t.data&&t.data.success?(w.success("证书上传成功"),H(),Y(!1),s):(w.error("证书信息保存失败"),!1)}return w.error("证书上传失败"),!1}catch(n){return console.error("证书上传失败:",n),w.error("证书上传失败: "+((null==n?void 0:n.message)||"未知错误")),!1}},E=async s=>{try{if(!(null==P?void 0:P.id))return void w.error("请先选择教师");const l={[s]:null},n=await e.put(`/teachers/${P.id}`,l);n.data&&n.data.success?(w.success("证书删除成功"),H(),Y(!1)):w.error("证书删除失败")}catch(l){console.error("证书删除失败:",l),w.error("证书删除失败: "+((null==l?void 0:l.message)||"未知错误"))}},G=e=>{if(e){const s=(e=>{if(!e)return"";if(e.startsWith("http")){let s=e;return s.includes(":8085/")&&(s=s.replace(":8085/",":3002/")),s.includes("***********:8085/uploads/")&&(s=s.replace("***********:8085/uploads/","***********:3002/uploads/")),s}return`http://***********:3002${e.startsWith("/")?"":"/"}${e}`})(e);window.open(s,"_blank")}else w.info("证书文件不存在")};return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"教师业务档案"}),s.jsx("p",{className:"text-gray-600 mt-1",children:"管理教师基础信息和教学工作记录"})]}),s.jsx(r,{type:"primary",icon:s.jsx(t,{}),onClick:()=>W(null),children:"新增教师"})]}),s.jsx(a,{defaultActiveKey:"basicInfo",items:[{key:"basicInfo",label:"基础信息",children:s.jsx(i,{title:"教师列表管理",children:O?s.jsxs("div",{className:"text-center py-8",children:[s.jsx(c,{size:"large"}),s.jsx("div",{className:"mt-4",children:"正在加载教师信息..."})]}):s.jsx(d,{dataSource:C,rowKey:"id",pagination:{pageSize:10},columns:[{title:"姓名",dataIndex:"name",key:"name"},{title:"工号",dataIndex:"employee_id",key:"employee_id"},{title:"性别",dataIndex:"gender",key:"gender",render:e=>"male"===e?"男":"女"},{title:"身份证号",dataIndex:"id_card",key:"id_card",render:e=>e||"未填写"},{title:"职称",dataIndex:"title",key:"title",render:e=>e||"未填写"},{title:"联系电话",dataIndex:"phone",key:"phone",render:e=>e||"未填写"},{title:"状态",dataIndex:"status",key:"status",render:e=>s.jsx(x,{color:"active"===e?"green":"red",children:"active"===e?"在职":"离职"})},{title:"操作",key:"action",width:150,fixed:"right",render:(e,l)=>s.jsxs(o,{size:"small",children:[s.jsx(r,{type:"text",icon:s.jsx(h,{}),onClick:()=>(e=>{console.log("查看教师:",e),q(e),Y(!0)})(l),children:"查看"}),s.jsx(r,{type:"text",icon:s.jsx(j,{}),onClick:()=>W(l),children:"编辑"})]})}]})})},{key:"statistics",label:"数据统计",children:s.jsx(i,{title:"教师数据统计",children:s.jsx("div",{className:"text-center py-8 text-gray-500",children:"数据统计功能开发中..."})})}]}),s.jsx(m,{title:`教师详细信息 - ${(null==P?void 0:P.name)||""}`,open:U,onCancel:()=>Y(!1),footer:null,width:1200,children:P&&s.jsx(a,{defaultActiveKey:"basicInfo",items:[{key:"basicInfo",label:"基础信息",children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-medium mb-4",children:"基础信息"}),s.jsxs(p,{gutter:16,children:[s.jsx(u,{span:8,children:s.jsxs("div",{children:[s.jsx("strong",{children:"姓名:"})," ",P.name||"未填写"]})}),s.jsx(u,{span:8,children:s.jsxs("div",{children:[s.jsx("strong",{children:"身份证号:"})," ",P.id_card||"未填写"]})}),s.jsx(u,{span:8,children:s.jsxs("div",{children:[s.jsx("strong",{children:"性别:"})," ","male"===P.gender?"男":"female"===P.gender?"女":"未填写"]})})]}),s.jsxs(p,{gutter:16,className:"mt-4",children:[s.jsx(u,{span:8,children:s.jsxs("div",{children:[s.jsx("strong",{children:"民族:"})," ",P.ethnicity||"未填写"]})}),s.jsx(u,{span:8,children:s.jsxs("div",{children:[s.jsx("strong",{children:"政治面貌:"})," ",P.political_status||"未填写"]})}),s.jsx(u,{span:8,children:s.jsxs("div",{children:[s.jsx("strong",{children:"联系电话:"})," ",P.phone||"未填写"]})})]}),s.jsxs(p,{gutter:16,className:"mt-4",children:[s.jsx(u,{span:8,children:s.jsxs("div",{children:[s.jsx("strong",{children:"职称:"})," ",P.title||"未填写"]})}),s.jsx(u,{span:8,children:s.jsxs("div",{children:[s.jsx("strong",{children:"学历:"})," ",P.degree||"未填写"]})}),s.jsx(u,{span:8,children:s.jsxs("div",{children:[s.jsx("strong",{children:"入职年份:"})," ",P.join_year||"未填写"]})})]}),s.jsxs(p,{gutter:16,className:"mt-4",children:[s.jsx(u,{span:12,children:s.jsxs("div",{children:[s.jsx("strong",{children:"家庭住址:"})," ",P.address||"未填写"]})}),s.jsx(u,{span:12,children:s.jsxs("div",{children:[s.jsx("strong",{children:"个人简历:"})," ",P.resume||"未填写"]})})]}),s.jsxs(p,{gutter:16,className:"mt-4",children:[s.jsx(u,{span:12,children:s.jsxs("div",{children:[s.jsx("strong",{children:"紧急联系人:"})," ",P.emergency_contact||"未填写"]})}),s.jsx(u,{span:12,children:s.jsxs("div",{children:[s.jsx("strong",{children:"联系人电话:"})," ",P.emergency_phone||"未填写"]})})]})]}),s.jsx(y,{}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-medium mb-4",children:"证书管理"}),s.jsxs(p,{gutter:16,children:[s.jsx(u,{span:6,children:s.jsxs("div",{className:"text-center p-4 border rounded",children:[s.jsx(g,{className:"text-2xl text-blue-500 mb-2"}),s.jsx("div",{className:"text-sm mb-2",children:"职称证书"}),P.title_cert?s.jsxs("div",{children:[s.jsx(r,{type:"link",size:"small",onClick:()=>G(P.title_cert),children:"查看"}),s.jsx(v,{title:"确定要删除这个证书吗？",onConfirm:()=>E("title_cert"),okText:"确定",cancelText:"取消",children:s.jsx(r,{type:"link",size:"small",danger:!0,children:"删除"})})]}):s.jsx(f,{accept:".pdf,.jpg,.jpeg,.png",showUploadList:!1,beforeUpload:e=>(B(e,"title_cert"),!1),children:s.jsx(r,{size:"small",icon:s.jsx(b,{}),children:"上传"})})]})}),s.jsx(u,{span:6,children:s.jsxs("div",{className:"text-center p-4 border rounded",children:[s.jsx(g,{className:"text-2xl text-green-500 mb-2"}),s.jsx("div",{className:"text-sm mb-2",children:"学位证书"}),P.degree_cert?s.jsxs("div",{children:[s.jsx(r,{type:"link",size:"small",onClick:()=>G(P.degree_cert),children:"查看"}),s.jsx(v,{title:"确定要删除这个证书吗？",onConfirm:()=>E("degree_cert"),okText:"确定",cancelText:"取消",children:s.jsx(r,{type:"link",size:"small",danger:!0,children:"删除"})})]}):s.jsx(f,{accept:".pdf,.jpg,.jpeg,.png",showUploadList:!1,beforeUpload:e=>(B(e,"degree_cert"),!1),children:s.jsx(r,{size:"small",icon:s.jsx(b,{}),children:"上传"})})]})}),s.jsx(u,{span:6,children:s.jsxs("div",{className:"text-center p-4 border rounded",children:[s.jsx(g,{className:"text-2xl text-orange-500 mb-2"}),s.jsx("div",{className:"text-sm mb-2",children:"毕业证书"}),P.graduation_cert?s.jsxs("div",{children:[s.jsx(r,{type:"link",size:"small",onClick:()=>G(P.graduation_cert),children:"查看"}),s.jsx(v,{title:"确定要删除这个证书吗？",onConfirm:()=>E("graduation_cert"),okText:"确定",cancelText:"取消",children:s.jsx(r,{type:"link",size:"small",danger:!0,children:"删除"})})]}):s.jsx(f,{accept:".pdf,.jpg,.jpeg,.png",showUploadList:!1,beforeUpload:e=>(B(e,"graduation_cert"),!1),children:s.jsx(r,{size:"small",icon:s.jsx(b,{}),children:"上传"})})]})}),s.jsx(u,{span:6,children:s.jsxs("div",{className:"text-center p-4 border rounded",children:[s.jsx(g,{className:"text-2xl text-purple-500 mb-2"}),s.jsx("div",{className:"text-sm mb-2",children:"技能证书"}),s.jsx(f,{accept:".pdf,.jpg,.jpeg,.png",showUploadList:!1,beforeUpload:e=>(B(e,"skill_certs"),!1),children:s.jsx(r,{size:"small",icon:s.jsx(b,{}),children:"上传"})}),P.skill_certs&&s.jsx("div",{className:"mt-2",children:s.jsx(r,{type:"link",size:"small",onClick:()=>G(P.skill_certs),children:"查看证书"})})]})})]})]})]})},{key:"courses",label:"任课情况",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h3",{className:"text-lg font-medium",children:"任课情况管理"}),s.jsx(r,{type:"primary",icon:s.jsx(t,{}),children:"新增课程"})]}),s.jsx("div",{className:"text-center py-8 text-gray-500",children:"任课情况功能开发中..."})]})},{key:"materials",label:"教学材料",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h3",{className:"text-lg font-medium",children:"教学材料管理"}),s.jsx(r,{type:"primary",icon:s.jsx(t,{}),children:"上传材料"})]}),s.jsx("div",{className:"text-center py-8 text-gray-500",children:"教学材料功能开发中..."})]})},{key:"achievements",label:"个人成果",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h3",{className:"text-lg font-medium",children:"个人成果管理"}),s.jsx(r,{type:"primary",icon:s.jsx(t,{}),children:"新增成果"})]}),s.jsx("div",{className:"text-center py-8 text-gray-500",children:"个人成果功能开发中..."})]})},{key:"trainings",label:"师资培训",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h3",{className:"text-lg font-medium",children:"师资培训管理"}),s.jsx(r,{type:"primary",icon:s.jsx(t,{}),children:"新增培训"})]}),s.jsx("div",{className:"text-center py-8 text-gray-500",children:"师资培训功能开发中..."})]})},{key:"mentorships",label:"青蓝筑梦",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h3",{className:"text-lg font-medium",children:"青蓝筑梦项目管理"}),s.jsx(r,{type:"primary",icon:s.jsx(t,{}),children:"新增项目"})]}),s.jsx("div",{className:"text-center py-8 text-gray-500",children:"青蓝筑梦功能开发中..."})]})}]})}),s.jsx(m,{title:L?"编辑基础信息":"新增教师",open:A,onOk:()=>K.submit(),onCancel:()=>{F(!1),D(null),K.resetFields()},width:800,okText:"保存",cancelText:"取消",children:s.jsxs(n,{form:K,layout:"vertical",onFinish:async s=>{try{const l={employee_id:s.employeeId||`T${Date.now()}`,name:s.name||"未命名教师",gender:s.gender,id_card:s.idCard||null,ethnicity:s.ethnicity,political_status:s.politicalStatus,phone:s.phone,title:s.title,degree:s.degree||"",join_year:s.entryYear?parseInt(s.entryYear.format("YYYY")):(new Date).getFullYear(),address:s.homeAddress||null,emergency_contact:s.emergencyContact||null,emergency_phone:s.emergencyPhone||null,resume:s.resume||"",status:"active"};if(null==L?void 0:L.id){await e.put(`/teachers/${L.id}`,l);w.success("教师信息更新成功")}else{await e.post("/teachers",l);w.success("教师信息创建成功")}F(!1),D(null),K.resetFields(),H()}catch(l){console.error("提交失败:",l),w.error("操作失败: "+((null==l?void 0:l.message)||"未知错误"))}},children:[s.jsxs(p,{gutter:16,children:[s.jsx(u,{span:8,children:s.jsx(n.Item,{label:"姓名",name:"name",rules:[{required:!0,message:"请输入姓名"}],children:s.jsx(N,{placeholder:"请输入姓名"})})}),s.jsx(u,{span:8,children:s.jsx(n.Item,{label:"工号",name:"employeeId",children:s.jsx(N,{placeholder:"请输入工号"})})}),s.jsx(u,{span:8,children:s.jsx(n.Item,{label:"性别",name:"gender",rules:[{required:!0,message:"请选择性别"}],children:s.jsxs(_,{placeholder:"请选择性别",children:[s.jsx(_.Option,{value:"male",children:"男"}),s.jsx(_.Option,{value:"female",children:"女"})]})})})]}),s.jsxs(p,{gutter:16,children:[s.jsx(u,{span:8,children:s.jsx(n.Item,{label:"身份证号",name:"idCard",rules:[{pattern:/^[1-9]\d{17}[0-9Xx]$/,message:"请输入正确的身份证号"}],children:s.jsx(N,{placeholder:"请输入身份证号"})})}),s.jsx(u,{span:8,children:s.jsx(n.Item,{label:"民族",name:"ethnicity",children:s.jsx(N,{placeholder:"请输入民族"})})}),s.jsx(u,{span:8,children:s.jsx(n.Item,{label:"政治面貌",name:"politicalStatus",children:s.jsxs(_,{placeholder:"请选择政治面貌",children:[s.jsx(_.Option,{value:"party_member",children:"中共党员"}),s.jsx(_.Option,{value:"league_member",children:"共青团员"}),s.jsx(_.Option,{value:"masses",children:"群众"}),s.jsx(_.Option,{value:"other",children:"其他"})]})})})]}),s.jsxs(p,{gutter:16,children:[s.jsx(u,{span:8,children:s.jsx(n.Item,{label:"联系电话",name:"phone",rules:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码"}],children:s.jsx(N,{placeholder:"请输入联系电话"})})}),s.jsx(u,{span:8,children:s.jsx(n.Item,{label:"职称",name:"title",children:s.jsxs(_,{placeholder:"请选择职称",children:[s.jsx(_.Option,{value:"professor",children:"教授"}),s.jsx(_.Option,{value:"associate_professor",children:"副教授"}),s.jsx(_.Option,{value:"lecturer",children:"讲师"}),s.jsx(_.Option,{value:"assistant",children:"助教"})]})})}),s.jsx(u,{span:8,children:s.jsx(n.Item,{label:"学历",name:"degree",children:s.jsxs(_,{placeholder:"请选择学历",children:[s.jsx(_.Option,{value:"bachelor",children:"本科"}),s.jsx(_.Option,{value:"master",children:"硕士"}),s.jsx(_.Option,{value:"doctor",children:"博士"})]})})})]}),s.jsxs(p,{gutter:16,children:[s.jsx(u,{span:8,children:s.jsx(n.Item,{label:"入职年份",name:"entryYear",children:s.jsx(k,{picker:"year",placeholder:"请选择入职年份",style:{width:"100%"}})})}),s.jsx(u,{span:16,children:s.jsx(n.Item,{label:"家庭住址",name:"homeAddress",children:s.jsx(N,{placeholder:"请输入家庭住址"})})})]}),s.jsxs(p,{gutter:16,children:[s.jsx(u,{span:12,children:s.jsx(n.Item,{label:"紧急联系人",name:"emergencyContact",children:s.jsx(N,{placeholder:"请输入紧急联系人"})})}),s.jsx(u,{span:12,children:s.jsx(n.Item,{label:"联系人电话",name:"emergencyPhone",rules:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码"}],children:s.jsx(N,{placeholder:"请输入联系人电话"})})})]}),s.jsx(n.Item,{label:"个人简历",name:"resume",children:s.jsx(N.TextArea,{rows:4,placeholder:"请输入个人简历"})})]})})]})};export{C as default};
