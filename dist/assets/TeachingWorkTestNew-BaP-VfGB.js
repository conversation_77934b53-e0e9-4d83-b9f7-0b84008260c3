import{r as e,j as s}from"./index-Cu_U9Dm3.js";import{r as t,aq as n,s as r,E as a,a as i,ao as l,ai as c,F as o,ay as d,g as x,S as h,k as p,ae as u,h as m,aO as j,G as g,H as f,J as y,i as v,ag as b,bh as _,bi as w,j as S,m as k,bj as $,M as I,U as z,af as C,ax as T,aw as D,aB as Y,bk as F,ac as R,Q as M,I as N,as as q,aM as B,aH as E,bl as L}from"./antd-DYv0PFJq.js";import{g as W,c as A}from"./vendor-D2RBMdQ0.js";import{r as H}from"./jszip.min-Bv14Fx6n.js";const O=W(H());var U,V={exports:{}};var K,X=(U||(U=1,K=V,function(){function e(e,s){return void 0===s?s={autoBom:!1}:"object"!=typeof s&&(console.warn("Deprecated: Expected third argument to be a object"),s={autoBom:!s}),s.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob(["\ufeff",e],{type:e.type}):e}function s(e,s,t){var n=new XMLHttpRequest;n.open("GET",e),n.responseType="blob",n.onload=function(){i(n.response,s,t)},n.onerror=function(){console.error("could not download file")},n.send()}function t(e){var s=new XMLHttpRequest;s.open("HEAD",e,!1);try{s.send()}catch(t){}return 200<=s.status&&299>=s.status}function n(e){try{e.dispatchEvent(new MouseEvent("click"))}catch(t){var s=document.createEvent("MouseEvents");s.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(s)}}var r="object"==typeof window&&window.window===window?window:"object"==typeof self&&self.self===self?self:"object"==typeof A&&A.global===A?A:void 0,a=r.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),i=r.saveAs||("object"!=typeof window||window!==r?function(){}:"download"in HTMLAnchorElement.prototype&&!a?function(e,a,i){var l=r.URL||r.webkitURL,c=document.createElement("a");a=a||e.name||"download",c.download=a,c.rel="noopener","string"==typeof e?(c.href=e,c.origin===location.origin?n(c):t(c.href)?s(e,a,i):n(c,c.target="_blank")):(c.href=l.createObjectURL(e),setTimeout(function(){l.revokeObjectURL(c.href)},4e4),setTimeout(function(){n(c)},0))}:"msSaveOrOpenBlob"in navigator?function(r,a,i){if(a=a||r.name||"download","string"!=typeof r)navigator.msSaveOrOpenBlob(e(r,i),a);else if(t(r))s(r,a,i);else{var l=document.createElement("a");l.href=r,l.target="_blank",setTimeout(function(){n(l)})}}:function(e,t,n,i){if((i=i||open("","_blank"))&&(i.document.title=i.document.body.innerText="downloading..."),"string"==typeof e)return s(e,t,n);var l="application/octet-stream"===e.type,c=/constructor/i.test(r.HTMLElement)||r.safari,o=/CriOS\/[\d]+/.test(navigator.userAgent);if((o||l&&c||a)&&"undefined"!=typeof FileReader){var d=new FileReader;d.onloadend=function(){var e=d.result;e=o?e:e.replace(/^data:[^;]*;/,"data:attachment/file;"),i?i.location.href=e:location=e,i=null},d.readAsDataURL(e)}else{var x=r.URL||r.webkitURL,h=x.createObjectURL(e);i?i.location=h:location.href=h,i=null,setTimeout(function(){x.revokeObjectURL(h)},4e4)}});r.saveAs=i.saveAs=i,K.exports=i}()),V.exports);const{Option:J}=q,{TextArea:P}=N,{Title:G}=a,Q=()=>{const[a,W]=t.useState(!1),[A,H]=t.useState([]),[U,V]=t.useState("basicInfo"),[K,Q]=t.useState(null),[Z,ee]=t.useState(!1),[se,te]=t.useState(!1),[ne,re]=t.useState(!1),[ae,ie]=t.useState(null),[le]=n.useForm(),[ce]=n.useForm(),[oe,de]=t.useState(!1),[xe,he]=t.useState(!1),[pe,ue]=t.useState(!1),[me,je]=t.useState(!1),[ge,fe]=t.useState(!1),[ye]=n.useForm(),[ve]=n.useForm(),[be]=n.useForm(),[_e]=n.useForm(),[we]=n.useForm(),[Se,ke]=t.useState([]),[$e,Ie]=t.useState([]),[ze,Ce]=t.useState([]),[Te,De]=t.useState([]),[Ye,Fe]=t.useState([]),[Re,Me]=t.useState(null),[Ne,qe]=t.useState(null),[Be,Ee]=t.useState(null),[Le,We]=t.useState(null),[Ae,He]=t.useState(null),[Oe,Ue]=t.useState({overview:{},titleDistribution:[],degreeDistribution:[],ageDistribution:[],joinYearTrend:[],achievementStats:{},trainingStats:{},materialStats:{},courseStats:{},mentorshipStats:{}}),[Ve,Ke]=t.useState(!1),Xe=t.useRef(null),Je=async()=>{W(!0);try{const s=await e.get("/teachers");s&&s.success?H(s.data||[]):r.error("数据格式错误")}catch(s){console.error("加载数据失败:",s),r.error("加载数据失败")}finally{W(!1)}};t.useEffect(()=>{Je(),as()},[]),t.useEffect(()=>{(null==K?void 0:K.id)&&(es(),ss(),ts(),ns(),rs())},[null==K?void 0:K.id]);const Pe=async(s,t)=>{const n=new FormData;n.append("file",s),n.append("type",t);try{console.log("开始上传文件:",s.name,"类型:",t);const a=await e.post("/upload/single",n,{headers:{"Content-Type":"multipart/form-data"}});if(console.log("上传响应:",a),a&&a.success){const s=a.data.url;console.log("文件上传成功，URL:",s);const n={...K,[`${t}_cert`]:s},i=await e.put(`/teachers/${K.id}`,n);return i&&i.success?(r.success("证书上传成功"),Q({...K,[`${t}_cert`]:s}),Je(),s):(r.error("证书信息保存失败"),!1)}return r.error("文件上传失败"),!1}catch(a){return console.error("上传失败:",a),r.error("上传失败: "+((null==a?void 0:a.message)||"未知错误")),!1}},Ge=async s=>{try{const t={...K,[`${s}_cert`]:null},n=await e.put(`/teachers/${K.id}`,t);n.data&&n.data.success?(r.success("证书删除成功"),Je()):r.error("证书删除失败")}catch(t){console.error("证书删除失败:",t),r.error("证书删除失败: "+((null==t?void 0:t.message)||"未知错误"))}},Qe=e=>{if(e){let s=e;e.startsWith("http")||(s=e),console.log("查看证书URL:",s),window.open(s,"_blank")}else r.info("证书文件不存在")},Ze=(e,s)=>{var t;if(e){let n=e;e.startsWith("http")||(n=e),console.log("预览教学材料URL:",n),console.log("文件名:",s);const a=null==(t=e.split(".").pop())?void 0:t.toLowerCase();if(["jpg","jpeg","png","gif","bmp","webp"].includes(a||""))window.open(n,"_blank");else if(["pdf"].includes(a||""))window.open(n,"_blank");else if(["doc","docx","xls","xlsx","ppt","pptx"].includes(a||"")){const e=document.createElement("a");e.href=n,e.download=s||`教学材料.${a}`,document.body.appendChild(e),e.click(),document.body.removeChild(e),r.success("文件下载已开始")}else window.open(n,"_blank")}else r.info("文件不存在")},es=async()=>{if(null==K?void 0:K.id)try{const s=await e.get(`/teacher-courses?teacher_id=${K.id}`);s&&s.success&&ke(s.data||[])}catch(s){console.error("加载课程数据失败:",s)}},ss=async()=>{if(null==K?void 0:K.id)try{const s=await e.get(`/teacher-materials?teacher_id=${K.id}`);s&&s.success&&Ie(s.data||[])}catch(s){console.error("加载教学材料数据失败:",s)}},ts=async()=>{if(null==K?void 0:K.id)try{const s=await e.get(`/teacher-achievements?teacher_id=${K.id}`);s&&s.success&&Ce(s.data||[])}catch(s){console.error("加载个人成果数据失败:",s)}},ns=async()=>{if(null==K?void 0:K.id)try{const s=await e.get(`/teacher-trainings?teacher_id=${K.id}`);s&&s.success&&De(s.data||[])}catch(s){console.error("加载师资培训数据失败:",s)}},rs=async()=>{if(null==K?void 0:K.id)try{const s=await e.get(`/teacher-mentorships?teacher_id=${K.id}`);s&&s.success&&Fe(s.data||[])}catch(s){console.error("加载青蓝筑梦项目数据失败:",s)}},as=async()=>{Ke(!0);try{const s=await e.get("/teachers");if(s&&s.success){const e=s.data||[],t={totalTeachers:e.length,activeTeachers:e.filter(e=>"active"===e.status).length,inactiveTeachers:e.filter(e=>"inactive"===e.status).length,averageAge:is(e)},n=ls(e),r=cs(e),a=os(e),i=ds(e),l=await xs(),c=await hs(),o=await ps(),d=await us(),x=await ms();Ue({overview:t,titleDistribution:n,degreeDistribution:r,ageDistribution:a,joinYearTrend:i,achievementStats:l,trainingStats:c,materialStats:o,courseStats:d,mentorshipStats:x})}}catch(s){console.error("加载数据分析失败:",s),r.error("加载数据分析失败")}finally{Ke(!1)}},is=e=>{const s=(new Date).getFullYear(),t=e.filter(e=>e.id_card&&e.id_card.length>=14).map(e=>{const t=parseInt(e.id_card.substring(6,10));return s-t}).filter(e=>e>0&&e<100);return t.length>0?Math.round(t.reduce((e,s)=>e+s,0)/t.length):0},ls=e=>{const s={professor:"教授",associate_professor:"副教授",lecturer:"讲师",assistant:"助教"},t={};return e.forEach(e=>{const n=s[e.title]||"未设置";t[n]=(t[n]||0)+1}),Object.entries(t).map(([s,t])=>({title:s,count:t,percentage:(t/e.length*100).toFixed(1)}))},cs=e=>{const s={};return e.forEach(e=>{const t=e.degree||"未设置";s[t]=(s[t]||0)+1}),Object.entries(s).map(([s,t])=>({degree:s,count:t,percentage:(t/e.length*100).toFixed(1)}))},os=e=>{const s=(new Date).getFullYear(),t={"30岁以下":0,"30-40岁":0,"40-50岁":0,"50-60岁":0,"60岁以上":0};return e.forEach(e=>{if(e.id_card&&e.id_card.length>=14){const n=parseInt(e.id_card.substring(6,10)),r=s-n;r<30?t["30岁以下"]++:r<40?t["30-40岁"]++:r<50?t["40-50岁"]++:r<60?t["50-60岁"]++:t["60岁以上"]++}}),Object.entries(t).map(([s,t])=>({range:s,count:t,percentage:(t/e.length*100).toFixed(1)}))},ds=e=>{const s={},t=(new Date).getFullYear();for(let n=t-9;n<=t;n++)s[n]=0;return e.forEach(e=>{e.join_year&&e.join_year>=t-9&&(s[e.join_year]=(s[e.join_year]||0)+1)}),Object.entries(s).map(([e,s])=>({year:parseInt(e),count:s})).sort((e,s)=>e.year-s.year)},xs=async()=>{try{const s=await e.get("/teacher-achievements/stats");if(s&&s.success)return s.data}catch(s){console.error("获取成果统计失败:",s)}return{totalAchievements:0,byType:[],byLevel:[],recentTrend:[]}},hs=async()=>{try{const s=await e.get("/teacher-trainings/stats");if(s&&s.success)return s.data}catch(s){console.error("获取培训统计失败:",s)}return{totalTrainings:0,byType:[],totalHours:0,recentTrend:[]}},ps=async()=>{try{const s=await e.get("/teacher-materials/stats");if(s&&s.success)return s.data}catch(s){console.error("获取材料统计失败:",s)}return{totalMaterials:0,byType:[],totalSize:0,recentTrend:[]}},us=async()=>{try{const s=await e.get("/teacher-courses/stats");if(s&&s.success)return s.data}catch(s){console.error("获取任课情况统计失败:",s)}return{totalCourses:0,totalCredits:0,byType:[],bySemester:[],recentTrend:[]}},ms=async()=>{try{const s=await e.get("/teacher-mentorships/stats");if(s&&s.success)return s.data}catch(s){console.error("获取青蓝筑梦项目统计失败:",s)}return{totalMentorships:0,byRole:[],byStatus:[],recentTrend:[]}},js=async(e,s)=>{try{if(!e)return null;const t=e.startsWith("http")?e:`http://localhost:3002${e}`,n=await fetch(t);if(n.ok)return await n.blob();console.warn(`下载文件失败 (${n.status}): ${s}`)}catch(t){console.warn(`下载文件失败: ${s}`,t)}return null},gs=[{title:"工号",dataIndex:"employee_id",key:"employee_id"},{title:"姓名",dataIndex:"name",key:"name"},{title:"性别",dataIndex:"gender",key:"gender",render:e=>"male"===e?"男":"female"===e?"女":"未知"},{title:"职称",dataIndex:"title",key:"title",render:e=>({professor:"教授",associate_professor:"副教授",lecturer:"讲师",assistant:"助教"}[e]||e||"未设置")},{title:"联系电话",dataIndex:"phone",key:"phone"},{title:"状态",dataIndex:"status",key:"status",render:e=>s.jsx(M,{color:"active"===e?"green":"red",children:"active"===e?"在职":"离职"})},{title:"操作",key:"action",width:400,render:(t,n)=>s.jsxs(u,{size:"small",wrap:!0,children:[s.jsx(i,{type:"primary",icon:s.jsx(C,{}),onClick:()=>(e=>{console.log("查看教师:",e),Q(e),ee(!0),setTimeout(()=>{es(),ss(),ts(),ns(),rs()},100)})(n),size:"small",children:"查看"}),s.jsx(i,{icon:s.jsx(Y,{}),onClick:()=>(e=>{ie(e),ce.setFieldsValue({...e,hire_date:e.hire_date?R(e.hire_date):null}),re(!0)})(n),size:"small",children:"编辑"}),s.jsx(i,{icon:s.jsx(L,{}),onClick:()=>(async s=>{try{r.loading("正在生成教师档案压缩包...",0);const t=await e.get(`/teachers/${s.id}/export`);if(!t||!t.success)throw new Error("获取教师档案数据失败");const n=t.data,a=new O,i=a.folder(`${s.name}_教师档案`);if(!i)throw new Error("创建文件夹失败");const l=`教师档案摘要\n==================\n\n教师姓名: ${n.teacher.name||"未填写"}\n工号: ${n.teacher.employee_id||"未填写"}\n职称: ${n.teacher.title||"未设置"}\n状态: ${"active"===n.teacher.status?"在职":"离职"}\n\n档案统计:\n- 任课情况: ${n.summary.totalCourses}门课程\n- 个人成果: ${n.summary.totalAchievements}项成果\n- 师资培训: ${n.summary.totalTrainings}次培训\n- 教学材料: ${n.summary.totalMaterials}个材料\n- 青蓝筑梦: ${n.summary.totalMentorships}个项目\n\n档案生成时间: ${(new Date).toLocaleString()}\n档案包含内容:\n- 基础信息及相关证书文件\n- 任课情况详细记录\n- 个人成果及证书文件\n- 师资培训及证书文件\n- 教学材料及相关文件\n- 青蓝筑梦项目及报告文件\n\n注意: 本档案为系统自动生成，如有疑问请联系管理员。\n`;i.file("00_档案摘要.txt",l);const c=`基础信息\n==================\n姓名: ${n.teacher.name||"未填写"}\n工号: ${n.teacher.employee_id||"未填写"}\n性别: ${"male"===n.teacher.gender?"男":"female"===n.teacher.gender?"女":"未知"}\n身份证号: ${n.teacher.id_card||"未填写"}\n民族: ${n.teacher.ethnicity||"未填写"}\n政治面貌: ${n.teacher.political_status||"未填写"}\n联系电话: ${n.teacher.phone||"未填写"}\n职称: ${n.teacher.title||"未设置"}\n学历: ${n.teacher.degree||"未设置"}\n入职年份: ${n.teacher.join_year||"未填写"}\n地址: ${n.teacher.address||"未填写"}\n紧急联系人: ${n.teacher.emergency_contact||"未填写"}\n紧急联系电话: ${n.teacher.emergency_phone||"未填写"}\n状态: ${"active"===n.teacher.status?"在职":"离职"}\n创建时间: ${n.teacher.created_at||"未知"}\n更新时间: ${n.teacher.updated_at||"未知"}\n`;i.file("01_基础信息.txt",c);const o=i.folder("01_基础信息_证书文件");if(o){const e=[{url:n.teacher.graduation_cert,name:"毕业证书"},{url:n.teacher.degree_cert,name:"学位证书"},{url:n.teacher.title_cert,name:"职称证书"},{url:n.teacher.skill_certs,name:"技能证书"},{url:n.teacher.resume,name:"简历"},{url:n.teacher.other_certs,name:"其他证书"}];for(const s of e)if(s.url){const e=await js(s.url,s.name);if(e){const t=s.url.split(".").pop()||"file";o.file(`${s.name}.${t}`,e)}}}if(n.courses&&n.courses.length>0){const e=n.courses.map((e,s)=>`课程${s+1}:\n  课程名称: ${e.course_name||"未填写"}\n  课程代码: ${e.course_code||"未填写"}\n  学分: ${e.credits||"未填写"}\n  课时: ${e.hours||"未填写"}\n  学期: ${e.semester||"未填写"}\n  学年: ${e.academic_year||"未填写"}\n  班级: ${e.class_name||"未填写"}\n  描述: ${e.description||"无"}\n  创建时间: ${e.created_at||"未知"}\n`).join("\n\n");i.file("02_任课情况.txt",e)}else i.file("02_任课情况.txt","暂无任课情况记录");if(n.achievements&&n.achievements.length>0){const e=n.achievements.map((e,s)=>`成果${s+1}:\n  成果名称: ${e.achievement_name||"未填写"}\n  成果类型: ${e.achievement_type||"未填写"}\n  获得时间: ${e.achievement_date||"未填写"}\n  级别: ${e.level||"未填写"}\n  排名: ${e.ranking||"未填写"}\n  奖金: ${e.award_amount||"未填写"}\n  描述: ${e.description||"无"}\n  证书文件: ${e.certificate_url?"已上传":"未上传"}\n  创建时间: ${e.created_at||"未知"}\n`).join("\n\n");i.file("03_个人成果.txt",e);const s=i.folder("03_个人成果_证书文件");if(s)for(let t=0;t<n.achievements.length;t++){const e=n.achievements[t];if(e.certificate_url){const n=await js(e.certificate_url,`成果${t+1}_证书`);if(n){const r=e.certificate_url.split(".").pop()||"file";s.file(`成果${t+1}_${e.achievement_name}_证书.${r}`,n)}}}}else i.file("03_个人成果.txt","暂无个人成果记录");if(n.trainings&&n.trainings.length>0){const e=n.trainings.map((e,s)=>`培训${s+1}:\n  培训名称: ${e.training_name||"未填写"}\n  培训类型: ${e.training_type||"未填写"}\n  培训机构: ${e.training_institution||"未填写"}\n  开始时间: ${e.start_date||"未填写"}\n  结束时间: ${e.end_date||"未填写"}\n  培训时长: ${e.duration_hours||"未填写"}小时\n  费用: ${e.cost||"未填写"}\n  内容: ${e.content||"无"}\n  证书文件: ${e.certificate_url?"已上传":"未上传"}\n  创建时间: ${e.created_at||"未知"}\n`).join("\n\n");i.file("04_师资培训.txt",e);const s=i.folder("04_师资培训_证书文件");if(s)for(let t=0;t<n.trainings.length;t++){const e=n.trainings[t];if(e.certificate_url){const n=await js(e.certificate_url,`培训${t+1}_证书`);if(n){const r=e.certificate_url.split(".").pop()||"file";s.file(`培训${t+1}_${e.training_name}_证书.${r}`,n)}}}}else i.file("04_师资培训.txt","暂无师资培训记录");if(n.materials&&n.materials.length>0){const e=n.materials.map((e,s)=>`材料${s+1}:\n  材料名称: ${e.material_name||"未填写"}\n  材料类型: ${e.material_type||"未填写"}\n  课程名称: ${e.course_name||"未填写"}\n  上传时间: ${e.upload_date||"未填写"}\n  文件大小: ${e.file_size?(e.file_size/1024/1024).toFixed(2)+"MB":"未知"}\n  描述: ${e.description||"无"}\n  文件: ${e.file_url?"已上传":"未上传"}\n  创建时间: ${e.created_at||"未知"}\n`).join("\n\n");i.file("05_教学材料.txt",e);const s=i.folder("05_教学材料_文件");if(s)for(let t=0;t<n.materials.length;t++){const e=n.materials[t];if(e.file_url){const n=await js(e.file_url,`材料${t+1}`);if(n){const r=e.file_url.split(".").pop()||"file";s.file(`材料${t+1}_${e.material_name}.${r}`,n)}}}}else i.file("05_教学材料.txt","暂无教学材料记录");if(n.mentorships&&n.mentorships.length>0){const e=n.mentorships.map((e,s)=>`项目${s+1}:\n  项目名称: ${e.project_name||"未填写"}\n  角色: ${e.role||"未填写"}\n  合作教师: ${e.partner_teacher||"未填写"}\n  开始时间: ${e.start_date||"未填写"}\n  结束时间: ${e.end_date||"未填写"}\n  状态: ${e.status||"未填写"}\n  目标: ${e.objectives||"无"}\n  内容: ${e.content||"无"}\n  总结报告: ${e.summary_report?"已上传":"未上传"}\n  创建时间: ${e.created_at||"未知"}\n`).join("\n\n");i.file("06_青蓝筑梦.txt",e);const s=i.folder("06_青蓝筑梦_报告文件");if(s)for(let t=0;t<n.mentorships.length;t++){const e=n.mentorships[t];if(e.summary_report){const n=await js(e.summary_report,`项目${t+1}_报告`);if(n){const r=e.summary_report.split(".").pop()||"file";s.file(`项目${t+1}_${e.project_name}_报告.${r}`,n)}}}}else i.file("06_青蓝筑梦.txt","暂无青蓝筑梦记录");i.file("99_完整数据.json",JSON.stringify(n,null,2)),r.destroy(),r.loading("正在打包文件...",0);const d=await a.generateAsync({type:"blob",compression:"DEFLATE",compressionOptions:{level:6}}),x=`${s.name||"未知教师"}_教师档案_${(new Date).toISOString().slice(0,10)}.zip`;X.saveAs(d,x),r.destroy(),r.success("教师档案压缩包导出成功！")}catch(t){console.error("导出教师档案失败:",t),r.destroy(),r.error("导出教师档案失败")}})(n),size:"small",style:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",border:"none",color:"white"},children:"导出档案"}),s.jsx(F,{title:"确定删除这位教师吗？",onConfirm:()=>(async s=>{try{const t=await e.delete(`/teachers/${s}`);t&&t.success?(r.success("删除成功"),Je()):r.error("删除失败：响应格式错误")}catch(t){console.error("删除失败:",t),r.error("删除失败: "+((null==t?void 0:t.message)||"未知错误"))}})(n.id),okText:"确定",cancelText:"取消",children:s.jsx(i,{danger:!0,size:"small",children:"删除"})})]})}];return s.jsxs("div",{className:"p-6",children:[s.jsxs("div",{className:"flex items-center justify-between mb-6",children:[s.jsx(G,{level:2,children:"教师业务档案管理"}),s.jsx(i,{type:"primary",icon:s.jsx(l,{}),onClick:()=>{le.resetFields(),te(!0)},children:"新增教师"})]}),s.jsx(c,{activeKey:U,onChange:V,items:[{key:"basicInfo",label:"基础信息",icon:s.jsx(x,{}),children:s.jsx(o,{children:s.jsx(d,{loading:a,dataSource:A,columns:gs,rowKey:"id",pagination:{total:A.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 条记录`}})})},{key:"analytics",label:"数据分析",icon:s.jsx(p,{}),children:s.jsx("div",{ref:Xe,style:{background:"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)",minHeight:"100vh",padding:"24px",borderRadius:"12px"},children:s.jsxs(h,{spinning:Ve,children:[!Ve&&0===Oe.overview.totalTeachers&&s.jsx(o,{style:{borderRadius:"16px",boxShadow:"0 8px 32px rgba(0,0,0,0.1)",border:"none"},children:s.jsxs("div",{style:{textAlign:"center",padding:"60px 0"},children:[s.jsx(p,{style:{fontSize:"64px",color:"#1890ff",marginBottom:"24px",opacity:.6}}),s.jsx("h3",{style:{color:"#666",fontSize:"18px",marginBottom:"8px"},children:"暂无数据"}),s.jsx("p",{style:{color:"#999",fontSize:"14px"},children:"请先添加教师信息以查看数据分析"})]})}),Oe.overview.totalTeachers>0&&s.jsxs(s.Fragment,{children:[s.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"32px",padding:"0 8px"},children:[s.jsxs("div",{children:[s.jsx("h2",{style:{margin:0,fontSize:"28px",fontWeight:"bold",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text"},children:"📊 数据分析中心"}),s.jsx("p",{style:{margin:"8px 0 0 0",color:"#666",fontSize:"14px"},children:"教师业务档案全方位数据洞察"})]}),s.jsxs(u,{size:"middle",children:[s.jsx(i,{icon:s.jsx(m,{}),onClick:as,loading:Ve,size:"large",style:{borderRadius:"8px",height:"40px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)"},children:"刷新数据"}),s.jsx(i,{icon:s.jsx(j,{}),onClick:()=>{const e=window.open("","_blank");if(!e)return void r.error("无法打开打印窗口，请检查浏览器弹窗设置");const s=Oe,t=`\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset="utf-8">\n        <title>教师业务档案数据分析报告</title>\n        <style>\n          body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; line-height: 1.6; }\n          .header { text-align: center; margin-bottom: 30px; }\n          .title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }\n          .date { font-size: 14px; color: #666; }\n          .section { margin-bottom: 25px; }\n          .section-title { font-size: 18px; font-weight: bold; margin-bottom: 15px; border-bottom: 2px solid #1890ff; padding-bottom: 5px; }\n          .overview-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 20px; }\n          .overview-item { text-align: center; padding: 15px; border: 1px solid #d9d9d9; border-radius: 6px; }\n          .overview-value { font-size: 24px; font-weight: bold; color: #1890ff; }\n          .overview-label { font-size: 14px; color: #666; margin-top: 5px; }\n          .distribution-item { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #f0f0f0; }\n          .stats-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; }\n          .stats-item { padding: 15px; border: 1px solid #d9d9d9; border-radius: 6px; }\n          .stats-title { font-weight: bold; margin-bottom: 10px; }\n          .stats-value { font-size: 18px; color: #1890ff; margin-bottom: 5px; }\n          @media print {\n            body { margin: 0; }\n            .no-print { display: none; }\n          }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <div class="title">教师业务档案数据分析报告</div>\n          <div class="date">生成时间: ${(new Date).toLocaleString()}</div>\n        </div>\n\n        <div class="section">\n          <div class="section-title">概览统计</div>\n          <div class="overview-grid">\n            <div class="overview-item">\n              <div class="overview-value">${s.overview.totalTeachers||0}</div>\n              <div class="overview-label">教师总数</div>\n            </div>\n            <div class="overview-item">\n              <div class="overview-value">${s.overview.activeTeachers||0}</div>\n              <div class="overview-label">在职教师</div>\n            </div>\n            <div class="overview-item">\n              <div class="overview-value">${s.overview.inactiveTeachers||0}</div>\n              <div class="overview-label">离职教师</div>\n            </div>\n            <div class="overview-item">\n              <div class="overview-value">${s.overview.averageAge||0}</div>\n              <div class="overview-label">平均年龄</div>\n            </div>\n          </div>\n        </div>\n\n        <div class="section">\n          <div class="section-title">职称分布</div>\n          ${s.titleDistribution.map(e=>`\n            <div class="distribution-item">\n              <span>${e.title}</span>\n              <span>${e.count}人 (${e.percentage}%)</span>\n            </div>\n          `).join("")}\n        </div>\n\n        <div class="section">\n          <div class="section-title">学历分布</div>\n          ${s.degreeDistribution.map(e=>`\n            <div class="distribution-item">\n              <span>${e.degree}</span>\n              <span>${e.count}人 (${e.percentage}%)</span>\n            </div>\n          `).join("")}\n        </div>\n\n        <div class="section">\n          <div class="section-title">年龄分布</div>\n          ${s.ageDistribution.map(e=>`\n            <div class="distribution-item">\n              <span>${e.range}</span>\n              <span>${e.count}人 (${e.percentage}%)</span>\n            </div>\n          `).join("")}\n        </div>\n\n        <div class="section">\n          <div class="section-title">业务数据统计</div>\n          <div class="stats-grid">\n            <div class="stats-item">\n              <div class="stats-title">任课情况统计</div>\n              <div class="stats-value">总课程数: ${s.courseStats.totalCourses||0}</div>\n              <div class="stats-value">总学分数: ${s.courseStats.totalCredits||0}</div>\n            </div>\n            <div class="stats-item">\n              <div class="stats-title">个人成果统计</div>\n              <div class="stats-value">总成果数: ${s.achievementStats.totalAchievements||0}</div>\n            </div>\n            <div class="stats-item">\n              <div class="stats-title">师资培训统计</div>\n              <div class="stats-value">总培训数: ${s.trainingStats.totalTrainings||0}</div>\n              <div class="stats-value">总培训时长: ${s.trainingStats.totalHours||0}小时</div>\n            </div>\n            <div class="stats-item">\n              <div class="stats-title">教学材料统计</div>\n              <div class="stats-value">总材料数: ${s.materialStats.totalMaterials||0}</div>\n              <div class="stats-value">总文件大小: ${((s.materialStats.totalSize||0)/1024/1024).toFixed(2)}MB</div>\n            </div>\n            <div class="stats-item">\n              <div class="stats-title">青蓝筑梦统计</div>\n              <div class="stats-value">总项目数: ${s.mentorshipStats.totalMentorships||0}</div>\n            </div>\n          </div>\n        </div>\n\n        <script>\n          window.onload = function() {\n            window.print();\n            window.onafterprint = function() {\n              window.close();\n            };\n          };\n        <\/script>\n      </body>\n      </html>\n    `;e.document.write(t),e.document.close(),r.success('正在生成PDF报告，请在打印对话框中选择"另存为PDF"')},type:"primary",size:"large",style:{borderRadius:"8px",height:"40px",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",border:"none",boxShadow:"0 4px 16px rgba(102, 126, 234, 0.4)"},children:"导出报告"})]})]}),s.jsxs(g,{gutter:[24,24],style:{marginBottom:"32px"},children:[s.jsx(f,{span:6,children:s.jsxs(o,{style:{borderRadius:"16px",border:"none",boxShadow:"0 8px 32px rgba(0,0,0,0.1)",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",color:"white",overflow:"hidden",position:"relative"},children:[s.jsx("div",{style:{position:"absolute",top:"-20px",right:"-20px",opacity:.2},children:s.jsx(x,{style:{fontSize:"80px"}})}),s.jsx(y,{title:s.jsx("span",{style:{color:"rgba(255,255,255,0.8)",fontSize:"14px"},children:"教师总数"}),value:Oe.overview.totalTeachers||0,valueStyle:{color:"white",fontSize:"32px",fontWeight:"bold"},suffix:s.jsx("span",{style:{color:"rgba(255,255,255,0.8)",fontSize:"16px"},children:"人"})})]})}),s.jsx(f,{span:6,children:s.jsxs(o,{style:{borderRadius:"16px",border:"none",boxShadow:"0 8px 32px rgba(0,0,0,0.1)",background:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",color:"white",overflow:"hidden",position:"relative"},children:[s.jsx("div",{style:{position:"absolute",top:"-20px",right:"-20px",opacity:.2},children:s.jsx(v,{style:{fontSize:"80px"}})}),s.jsx(y,{title:s.jsx("span",{style:{color:"rgba(255,255,255,0.8)",fontSize:"14px"},children:"在职教师"}),value:Oe.overview.activeTeachers||0,valueStyle:{color:"white",fontSize:"32px",fontWeight:"bold"},suffix:s.jsx("span",{style:{color:"rgba(255,255,255,0.8)",fontSize:"16px"},children:"人"})})]})}),s.jsx(f,{span:6,children:s.jsxs(o,{style:{borderRadius:"16px",border:"none",boxShadow:"0 8px 32px rgba(0,0,0,0.1)",background:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",color:"white",overflow:"hidden",position:"relative"},children:[s.jsx("div",{style:{position:"absolute",top:"-20px",right:"-20px",opacity:.2},children:s.jsx(x,{style:{fontSize:"80px"}})}),s.jsx(y,{title:s.jsx("span",{style:{color:"rgba(255,255,255,0.8)",fontSize:"14px"},children:"离职教师"}),value:Oe.overview.inactiveTeachers||0,valueStyle:{color:"white",fontSize:"32px",fontWeight:"bold"},suffix:s.jsx("span",{style:{color:"rgba(255,255,255,0.8)",fontSize:"16px"},children:"人"})})]})}),s.jsx(f,{span:6,children:s.jsxs(o,{style:{borderRadius:"16px",border:"none",boxShadow:"0 8px 32px rgba(0,0,0,0.1)",background:"linear-gradient(135deg, #fa709a 0%, #fee140 100%)",color:"white",overflow:"hidden",position:"relative"},children:[s.jsx("div",{style:{position:"absolute",top:"-20px",right:"-20px",opacity:.2},children:s.jsx(m,{style:{fontSize:"80px"}})}),s.jsx(y,{title:s.jsx("span",{style:{color:"rgba(255,255,255,0.8)",fontSize:"14px"},children:"平均年龄"}),value:Oe.overview.averageAge||0,valueStyle:{color:"white",fontSize:"32px",fontWeight:"bold"},suffix:s.jsx("span",{style:{color:"rgba(255,255,255,0.8)",fontSize:"16px"},children:"岁"})})]})})]}),s.jsxs("div",{style:{marginBottom:"32px"},children:[s.jsx("h3",{style:{fontSize:"20px",fontWeight:"bold",marginBottom:"24px",color:"#2c3e50",borderLeft:"4px solid #667eea",paddingLeft:"16px"},children:"👥 人员结构分析"}),s.jsxs(g,{gutter:[24,24],children:[s.jsx(f,{span:12,children:s.jsx(o,{title:s.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[s.jsx(_,{style:{color:"#667eea"}}),s.jsx("span",{children:"职称分布"})]}),style:{borderRadius:"16px",border:"none",boxShadow:"0 8px 32px rgba(0,0,0,0.1)",height:"100%"},styles:{body:{padding:"24px"}},children:s.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"16px"},children:Oe.titleDistribution.map((e,t)=>s.jsxs("div",{style:{padding:"16px",background:"linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%)",borderRadius:"12px",border:"1px solid #e6f0ff"},children:[s.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"8px"},children:[s.jsx("span",{style:{fontWeight:"500",color:"#2c3e50"},children:e.title}),s.jsxs("span",{style:{fontSize:"14px",color:"#667eea",fontWeight:"bold",background:"white",padding:"4px 12px",borderRadius:"20px",border:"1px solid #667eea"},children:[e.count,"人 (",e.percentage,"%)"]})]}),s.jsx(b,{percent:parseFloat(e.percentage),strokeColor:{"0%":"#667eea","100%":"#764ba2"},trailColor:"#f0f0f0",strokeWidth:8,showInfo:!1})]},t))})})}),s.jsx(f,{span:12,children:s.jsx(o,{title:s.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[s.jsx(_,{style:{color:"#f093fb"}}),s.jsx("span",{children:"学历分布"})]}),style:{borderRadius:"16px",border:"none",boxShadow:"0 8px 32px rgba(0,0,0,0.1)",height:"100%"},styles:{body:{padding:"24px"}},children:s.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"16px"},children:Oe.degreeDistribution.map((e,t)=>s.jsxs("div",{style:{padding:"16px",background:"linear-gradient(135deg, #fff8f8 0%, #ffe8f0 100%)",borderRadius:"12px",border:"1px solid #ffe0eb"},children:[s.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"8px"},children:[s.jsx("span",{style:{fontWeight:"500",color:"#2c3e50"},children:e.degree}),s.jsxs("span",{style:{fontSize:"14px",color:"#f093fb",fontWeight:"bold",background:"white",padding:"4px 12px",borderRadius:"20px",border:"1px solid #f093fb"},children:[e.count,"人 (",e.percentage,"%)"]})]}),s.jsx(b,{percent:parseFloat(e.percentage),strokeColor:{"0%":"#f093fb","100%":"#f5576c"},trailColor:"#f0f0f0",strokeWidth:8,showInfo:!1})]},t))})})})]})]}),s.jsxs("div",{style:{marginBottom:"32px"},children:[s.jsx("h3",{style:{fontSize:"20px",fontWeight:"bold",marginBottom:"24px",color:"#2c3e50",borderLeft:"4px solid #4facfe",paddingLeft:"16px"},children:"📈 趋势分析"}),s.jsxs(g,{gutter:[24,24],children:[s.jsx(f,{span:12,children:s.jsx(o,{title:s.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[s.jsx(p,{style:{color:"#4facfe"}}),s.jsx("span",{children:"年龄分布"})]}),style:{borderRadius:"16px",border:"none",boxShadow:"0 8px 32px rgba(0,0,0,0.1)",height:"100%"},styles:{body:{padding:"24px"}},children:s.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"16px"},children:Oe.ageDistribution.map((e,t)=>s.jsxs("div",{style:{padding:"16px",background:"linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)",borderRadius:"12px",border:"1px solid #bae6fd"},children:[s.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"8px"},children:[s.jsx("span",{style:{fontWeight:"500",color:"#2c3e50"},children:e.range}),s.jsxs("span",{style:{fontSize:"14px",color:"#4facfe",fontWeight:"bold",background:"white",padding:"4px 12px",borderRadius:"20px",border:"1px solid #4facfe"},children:[e.count,"人 (",e.percentage,"%)"]})]}),s.jsx(b,{percent:parseFloat(e.percentage),strokeColor:{"0%":"#4facfe","100%":"#00f2fe"},trailColor:"#f0f0f0",strokeWidth:8,showInfo:!1})]},t))})})}),s.jsx(f,{span:12,children:s.jsx(o,{title:s.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[s.jsx(w,{style:{color:"#fa709a"}}),s.jsx("span",{children:"入职年份趋势"})]}),style:{borderRadius:"16px",border:"none",boxShadow:"0 8px 32px rgba(0,0,0,0.1)",height:"100%"},styles:{body:{padding:"24px"}},children:s.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:Oe.joinYearTrend.map((e,t)=>s.jsxs("div",{style:{padding:"12px 16px",background:"linear-gradient(135deg, #fff5f5 0%, #fef2f2 100%)",borderRadius:"10px",border:"1px solid #fecaca",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[s.jsxs("span",{style:{fontWeight:"500",color:"#2c3e50"},children:[e.year,"年"]}),s.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[s.jsx(b,{percent:e.count>0?Math.min(20*e.count,100):0,strokeColor:{"0%":"#fa709a","100%":"#fee140"},trailColor:"#f0f0f0",strokeWidth:6,showInfo:!1,style:{width:120}}),s.jsxs("span",{style:{fontSize:"14px",color:"#fa709a",fontWeight:"bold",minWidth:"40px",textAlign:"right"},children:[e.count,"人"]})]})]},t))})})})]})]}),s.jsxs("div",{children:[s.jsx("h3",{style:{fontSize:"20px",fontWeight:"bold",marginBottom:"24px",color:"#2c3e50",borderLeft:"4px solid #fa709a",paddingLeft:"16px"},children:"📊 业务数据统计"}),s.jsxs(g,{gutter:[24,24],children:[s.jsx(f,{span:8,children:s.jsxs(o,{style:{borderRadius:"16px",border:"none",boxShadow:"0 8px 32px rgba(0,0,0,0.1)",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",color:"white",height:"100%"},styles:{body:{padding:"24px"}},children:[s.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"20px"},children:[s.jsx(S,{style:{fontSize:"24px",marginRight:"12px"}}),s.jsx("h4",{style:{color:"white",margin:0,fontSize:"16px"},children:"任课情况统计"})]}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:12,children:s.jsx(y,{title:s.jsx("span",{style:{color:"rgba(255,255,255,0.8)"},children:"总课程数"}),value:Oe.courseStats.totalCourses||0,valueStyle:{color:"white",fontSize:"24px"},suffix:s.jsx("span",{style:{color:"rgba(255,255,255,0.8)"},children:"门"})})}),s.jsx(f,{span:12,children:s.jsx(y,{title:s.jsx("span",{style:{color:"rgba(255,255,255,0.8)"},children:"总学分数"}),value:Oe.courseStats.totalCredits||0,valueStyle:{color:"white",fontSize:"24px"},suffix:s.jsx("span",{style:{color:"rgba(255,255,255,0.8)"},children:"分"})})})]}),(Oe.courseStats.byType||[]).length>0&&s.jsxs("div",{style:{marginTop:"20px"},children:[s.jsx("div",{style:{color:"rgba(255,255,255,0.8)",fontSize:"12px",marginBottom:"8px"},children:"课程类型分布"}),(Oe.courseStats.byType||[]).map((e,t)=>s.jsxs("div",{style:{display:"flex",justifyContent:"space-between",padding:"4px 0",borderBottom:t<Oe.courseStats.byType.length-1?"1px solid rgba(255,255,255,0.2)":"none"},children:[s.jsx("span",{style:{fontSize:"12px",color:"rgba(255,255,255,0.9)"},children:e.type}),s.jsxs("span",{style:{fontSize:"12px",fontWeight:500,color:"white"},children:[e.count,"门"]})]},t))]})]})}),s.jsx(f,{span:8,children:s.jsxs(o,{style:{borderRadius:"16px",border:"none",boxShadow:"0 8px 32px rgba(0,0,0,0.1)",background:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",color:"white",height:"100%"},styles:{body:{padding:"24px"}},children:[s.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"20px"},children:[s.jsx(k,{style:{fontSize:"24px",marginRight:"12px"}}),s.jsx("h4",{style:{color:"white",margin:0,fontSize:"16px"},children:"个人成果统计"})]}),s.jsx(y,{title:s.jsx("span",{style:{color:"rgba(255,255,255,0.8)"},children:"总成果数"}),value:Oe.achievementStats.totalAchievements||0,valueStyle:{color:"white",fontSize:"32px"},suffix:s.jsx("span",{style:{color:"rgba(255,255,255,0.8)"},children:"项"})}),(Oe.achievementStats.byType||[]).length>0&&s.jsxs("div",{style:{marginTop:"20px"},children:[s.jsx("div",{style:{color:"rgba(255,255,255,0.8)",fontSize:"12px",marginBottom:"8px"},children:"成果类型分布"}),(Oe.achievementStats.byType||[]).map((e,t)=>s.jsxs("div",{style:{display:"flex",justifyContent:"space-between",padding:"4px 0",borderBottom:t<Oe.achievementStats.byType.length-1?"1px solid rgba(255,255,255,0.2)":"none"},children:[s.jsx("span",{style:{fontSize:"12px",color:"rgba(255,255,255,0.9)"},children:e.type}),s.jsxs("span",{style:{fontSize:"12px",fontWeight:500,color:"white"},children:[e.count,"项"]})]},t))]})]})}),s.jsx(f,{span:8,children:s.jsxs(o,{style:{borderRadius:"16px",border:"none",boxShadow:"0 8px 32px rgba(0,0,0,0.1)",background:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",color:"white",height:"100%"},styles:{body:{padding:"24px"}},children:[s.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"20px"},children:[s.jsx(v,{style:{fontSize:"24px",marginRight:"12px"}}),s.jsx("h4",{style:{color:"white",margin:0,fontSize:"16px"},children:"师资培训统计"})]}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:12,children:s.jsx(y,{title:s.jsx("span",{style:{color:"rgba(255,255,255,0.8)"},children:"总培训数"}),value:Oe.trainingStats.totalTrainings||0,valueStyle:{color:"white",fontSize:"24px"},suffix:s.jsx("span",{style:{color:"rgba(255,255,255,0.8)"},children:"次"})})}),s.jsx(f,{span:12,children:s.jsx(y,{title:s.jsx("span",{style:{color:"rgba(255,255,255,0.8)"},children:"总时长"}),value:Oe.trainingStats.totalHours||0,valueStyle:{color:"white",fontSize:"24px"},suffix:s.jsx("span",{style:{color:"rgba(255,255,255,0.8)"},children:"时"})})})]}),(Oe.trainingStats.byType||[]).length>0&&s.jsxs("div",{style:{marginTop:"20px"},children:[s.jsx("div",{style:{color:"rgba(255,255,255,0.8)",fontSize:"12px",marginBottom:"8px"},children:"培训类型分布"}),(Oe.trainingStats.byType||[]).map((e,t)=>s.jsxs("div",{style:{display:"flex",justifyContent:"space-between",padding:"4px 0",borderBottom:t<Oe.trainingStats.byType.length-1?"1px solid rgba(255,255,255,0.2)":"none"},children:[s.jsx("span",{style:{fontSize:"12px",color:"rgba(255,255,255,0.9)"},children:e.type}),s.jsxs("span",{style:{fontSize:"12px",fontWeight:500,color:"white"},children:[e.count,"次"]})]},t))]})]})})]}),s.jsxs(g,{gutter:[24,24],style:{marginTop:"24px"},children:[s.jsx(f,{span:12,children:s.jsxs(o,{style:{borderRadius:"16px",border:"none",boxShadow:"0 8px 32px rgba(0,0,0,0.1)",background:"linear-gradient(135deg, #fa709a 0%, #fee140 100%)",color:"white",height:"100%"},styles:{body:{padding:"24px"}},children:[s.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"20px"},children:[s.jsx($,{style:{fontSize:"24px",marginRight:"12px"}}),s.jsx("h4",{style:{color:"white",margin:0,fontSize:"16px"},children:"教学材料统计"})]}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:12,children:s.jsx(y,{title:s.jsx("span",{style:{color:"rgba(255,255,255,0.8)"},children:"总材料数"}),value:Oe.materialStats.totalMaterials||0,valueStyle:{color:"white",fontSize:"24px"},suffix:s.jsx("span",{style:{color:"rgba(255,255,255,0.8)"},children:"个"})})}),s.jsx(f,{span:12,children:s.jsx(y,{title:s.jsx("span",{style:{color:"rgba(255,255,255,0.8)"},children:"总大小"}),value:(Oe.materialStats.totalSize||0)/1024/1024,valueStyle:{color:"white",fontSize:"24px"},suffix:s.jsx("span",{style:{color:"rgba(255,255,255,0.8)"},children:"MB"}),precision:2})})]}),(Oe.materialStats.byType||[]).length>0&&s.jsxs("div",{style:{marginTop:"20px"},children:[s.jsx("div",{style:{color:"rgba(255,255,255,0.8)",fontSize:"12px",marginBottom:"8px"},children:"材料类型分布"}),(Oe.materialStats.byType||[]).map((e,t)=>s.jsxs("div",{style:{display:"flex",justifyContent:"space-between",padding:"4px 0",borderBottom:t<Oe.materialStats.byType.length-1?"1px solid rgba(255,255,255,0.2)":"none"},children:[s.jsx("span",{style:{fontSize:"12px",color:"rgba(255,255,255,0.9)"},children:e.type}),s.jsxs("span",{style:{fontSize:"12px",fontWeight:500,color:"white"},children:[e.count,"个"]})]},t))]})]})}),s.jsx(f,{span:12,children:s.jsxs(o,{style:{borderRadius:"16px",border:"none",boxShadow:"0 8px 32px rgba(0,0,0,0.1)",background:"linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",color:"#2c3e50",height:"100%"},styles:{body:{padding:"24px"}},children:[s.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"20px"},children:[s.jsx(v,{style:{fontSize:"24px",marginRight:"12px",color:"#667eea"}}),s.jsx("h4",{style:{color:"#2c3e50",margin:0,fontSize:"16px"},children:"青蓝筑梦统计"})]}),s.jsx(y,{title:s.jsx("span",{style:{color:"#666"},children:"总项目数"}),value:Oe.mentorshipStats.totalMentorships||0,valueStyle:{color:"#2c3e50",fontSize:"32px"},suffix:s.jsx("span",{style:{color:"#666"},children:"个"})}),(Oe.mentorshipStats.byRole||[]).length>0&&s.jsxs("div",{style:{marginTop:"20px"},children:[s.jsx("div",{style:{color:"#666",fontSize:"12px",marginBottom:"8px"},children:"角色分布"}),(Oe.mentorshipStats.byRole||[]).map((e,t)=>s.jsxs("div",{style:{display:"flex",justifyContent:"space-between",padding:"4px 0",borderBottom:t<Oe.mentorshipStats.byRole.length-1?"1px solid rgba(44,62,80,0.2)":"none"},children:[s.jsx("span",{style:{fontSize:"12px",color:"#666"},children:e.role}),s.jsxs("span",{style:{fontSize:"12px",fontWeight:500,color:"#2c3e50"},children:[e.count,"个"]})]},t))]})]})})]})]})]})]})})}]}),s.jsx(I,{title:"教师详细信息",open:Z,onCancel:()=>ee(!1),footer:null,width:1200,children:K&&s.jsx(c,{defaultActiveKey:"basicInfo",items:[{key:"basicInfo",label:"基础信息",children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-medium mb-4",children:"基础信息"}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:8,children:s.jsxs("div",{children:[s.jsx("strong",{children:"姓名:"})," ",K.name||"未填写"]})}),s.jsx(f,{span:8,children:s.jsxs("div",{children:[s.jsx("strong",{children:"工号:"})," ",K.employee_id||"未填写"]})}),s.jsx(f,{span:8,children:s.jsxs("div",{children:[s.jsx("strong",{children:"性别:"})," ","male"===K.gender?"男":"female"===K.gender?"女":"未填写"]})})]}),s.jsxs(g,{gutter:16,className:"mt-4",children:[s.jsx(f,{span:8,children:s.jsxs("div",{children:[s.jsx("strong",{children:"身份证号:"})," ",K.id_card||"未填写"]})}),s.jsx(f,{span:8,children:s.jsxs("div",{children:[s.jsx("strong",{children:"民族:"})," ",K.ethnicity||"未填写"]})}),s.jsx(f,{span:8,children:s.jsxs("div",{children:[s.jsx("strong",{children:"政治面貌:"})," ",K.political_status||"未填写"]})})]}),s.jsxs(g,{gutter:16,className:"mt-4",children:[s.jsx(f,{span:8,children:s.jsxs("div",{children:[s.jsx("strong",{children:"联系电话:"})," ",K.phone||"未填写"]})}),s.jsx(f,{span:8,children:s.jsxs("div",{children:[s.jsx("strong",{children:"职称:"})," ",{professor:"教授",associate_professor:"副教授",lecturer:"讲师",assistant:"助教"}[K.title]||K.title||"未填写"]})}),s.jsx(f,{span:8,children:s.jsxs("div",{children:[s.jsx("strong",{children:"学历:"})," ",K.degree||"未填写"]})})]}),s.jsxs(g,{gutter:16,className:"mt-4",children:[s.jsx(f,{span:8,children:s.jsxs("div",{children:[s.jsx("strong",{children:"入职年份:"})," ",K.join_year||"未填写"]})}),s.jsx(f,{span:8,children:s.jsxs("div",{children:[s.jsx("strong",{children:"状态:"})," ","active"===K.status?"在职":"inactive"===K.status?"离职":"未知"]})}),s.jsx(f,{span:8,children:s.jsxs("div",{children:[s.jsx("strong",{children:"创建时间:"})," ",K.created_at?new Date(K.created_at).toLocaleDateString():"未知"]})})]}),s.jsxs(g,{gutter:16,className:"mt-4",children:[s.jsx(f,{span:12,children:s.jsxs("div",{children:[s.jsx("strong",{children:"地址:"})," ",K.address||"未填写"]})}),s.jsx(f,{span:6,children:s.jsxs("div",{children:[s.jsx("strong",{children:"紧急联系人:"})," ",K.emergency_contact||"未填写"]})}),s.jsx(f,{span:6,children:s.jsxs("div",{children:[s.jsx("strong",{children:"紧急联系电话:"})," ",K.emergency_phone||"未填写"]})})]})]}),s.jsx(z,{}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-medium mb-4",children:"证书管理"}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:8,children:s.jsxs("div",{className:"border p-4 rounded",children:[s.jsx("h4",{className:"font-medium mb-2",children:"毕业证书"}),K.graduation_cert?s.jsxs("div",{className:"space-y-2",children:[s.jsx(i,{type:"link",icon:s.jsx(C,{}),onClick:()=>Qe(K.graduation_cert),children:"查看证书"}),s.jsx(i,{type:"link",danger:!0,onClick:()=>Ge("graduation"),children:"删除"})]}):s.jsx(T,{customRequest:async({file:e,onSuccess:s,onError:t})=>{try{const n=await Pe(e,"graduation");n?null==s||s(n):null==t||t(new Error("上传失败"))}catch(n){null==t||t(n)}},showUploadList:!1,children:s.jsx(i,{icon:s.jsx(D,{}),children:"上传毕业证书"})})]})}),s.jsx(f,{span:8,children:s.jsxs("div",{className:"border p-4 rounded",children:[s.jsx("h4",{className:"font-medium mb-2",children:"学位证书"}),K.degree_cert?s.jsxs("div",{className:"space-y-2",children:[s.jsx(i,{type:"link",icon:s.jsx(C,{}),onClick:()=>Qe(K.degree_cert),children:"查看证书"}),s.jsx(i,{type:"link",danger:!0,onClick:()=>Ge("degree"),children:"删除"})]}):s.jsx(T,{customRequest:async({file:e,onSuccess:s,onError:t})=>{try{const n=await Pe(e,"degree");n?null==s||s(n):null==t||t(new Error("上传失败"))}catch(n){null==t||t(n)}},showUploadList:!1,children:s.jsx(i,{icon:s.jsx(D,{}),children:"上传学位证书"})})]})}),s.jsx(f,{span:8,children:s.jsxs("div",{className:"border p-4 rounded",children:[s.jsx("h4",{className:"font-medium mb-2",children:"职称证书"}),K.title_cert?s.jsxs("div",{className:"space-y-2",children:[s.jsx(i,{type:"link",icon:s.jsx(C,{}),onClick:()=>Qe(K.title_cert),children:"查看证书"}),s.jsx(i,{type:"link",danger:!0,onClick:()=>Ge("title"),children:"删除"})]}):s.jsx(T,{customRequest:async({file:e,onSuccess:s,onError:t})=>{try{const n=await Pe(e,"title");n?null==s||s(n):null==t||t(new Error("上传失败"))}catch(n){null==t||t(n)}},showUploadList:!1,children:s.jsx(i,{icon:s.jsx(D,{}),children:"上传职称证书"})})]})})]})]})]})},{key:"courses",label:"任课情况",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h3",{className:"text-lg font-medium",children:"任课情况管理"}),s.jsx(i,{type:"primary",icon:s.jsx(l,{}),onClick:()=>{Me(null),ye.resetFields(),de(!0)},children:"新增课程"})]}),s.jsx(d,{dataSource:Se,rowKey:"id",pagination:{pageSize:5},columns:[{title:"课程名称",dataIndex:"course_name",key:"course_name"},{title:"课程代码",dataIndex:"course_code",key:"course_code"},{title:"学期",dataIndex:"semester",key:"semester"},{title:"学年",dataIndex:"academic_year",key:"academic_year"},{title:"学分",dataIndex:"credits",key:"credits"},{title:"课时",dataIndex:"hours",key:"hours"},{title:"班级",dataIndex:"class_name",key:"class_name"},{title:"操作",key:"action",width:150,render:(t,n)=>s.jsxs(u,{size:"small",children:[s.jsx(i,{type:"text",icon:s.jsx(Y,{}),onClick:()=>(e=>{Me(e),ye.setFieldsValue(e),de(!0)})(n),children:"编辑"}),s.jsx(F,{title:"确定删除这门课程吗？",onConfirm:()=>(async s=>{try{const t=await e.delete(`/teacher-courses/${s}`);t&&t.success?(r.success("课程删除成功"),es()):r.error("课程删除失败：响应格式错误")}catch(t){console.error("课程删除失败:",t),r.error("课程删除失败: "+((null==t?void 0:t.message)||"未知错误"))}})(n.id),okText:"确定",cancelText:"取消",children:s.jsx(i,{type:"text",danger:!0,children:"删除"})})]})}]})]})},{key:"materials",label:"教学材料",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h3",{className:"text-lg font-medium",children:"教学材料管理"}),s.jsx(i,{type:"primary",icon:s.jsx(l,{}),onClick:()=>{qe(null),ve.resetFields(),he(!0)},children:"上传材料"})]}),s.jsx(d,{dataSource:$e,rowKey:"id",pagination:{pageSize:5},columns:[{title:"材料名称",dataIndex:"material_name",key:"material_name"},{title:"材料类型",dataIndex:"material_type",key:"material_type",render:e=>({courseware:"课件",textbook:"教材",reference:"参考资料",assignment:"作业",exam:"考试资料",other:"其他"}[e]||e)},{title:"课程名称",dataIndex:"course_name",key:"course_name"},{title:"上传时间",dataIndex:"upload_date",key:"upload_date",render:e=>e?R(e).format("YYYY-MM-DD"):"-"},{title:"文件大小",dataIndex:"file_size",key:"file_size",render:e=>e?`${(e/1024/1024).toFixed(2)} MB`:"-"},{title:"操作",key:"action",width:200,render:(t,n)=>s.jsxs(u,{size:"small",children:[s.jsx(i,{type:"text",icon:s.jsx(C,{}),onClick:()=>Ze(n.file_url,n.material_name),children:"预览"}),s.jsx(i,{type:"text",icon:s.jsx(Y,{}),onClick:()=>(e=>{qe(e);const s={...e,upload_date:e.upload_date?R(e.upload_date):null};ve.setFieldsValue(s),he(!0)})(n),children:"编辑"}),s.jsx(F,{title:"确定删除这个材料吗？",onConfirm:()=>(async s=>{try{const t=await e.delete(`/teacher-materials/${s}`);t&&t.success?(r.success("教学材料删除成功"),ss()):r.error("教学材料删除失败：响应格式错误")}catch(t){console.error("教学材料删除失败:",t),r.error("教学材料删除失败: "+((null==t?void 0:t.message)||"未知错误"))}})(n.id),okText:"确定",cancelText:"取消",children:s.jsx(i,{type:"text",danger:!0,children:"删除"})})]})}]})]})},{key:"achievements",label:"个人成果",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h3",{className:"text-lg font-medium",children:"个人成果管理"}),s.jsx(i,{type:"primary",icon:s.jsx(l,{}),onClick:()=>{Ee(null),be.resetFields(),ue(!0)},children:"新增成果"})]}),s.jsx(d,{dataSource:ze,rowKey:"id",pagination:{pageSize:5},columns:[{title:"成果名称",dataIndex:"achievement_name",key:"achievement_name"},{title:"成果类型",dataIndex:"achievement_type",key:"achievement_type",render:e=>({paper:"论文",patent:"专利",project:"项目",award:"获奖",book:"著作",software:"软件著作权",other:"其他"}[e]||e)},{title:"获得时间",dataIndex:"achievement_date",key:"achievement_date",render:e=>e?R(e).format("YYYY-MM-DD"):"-"},{title:"级别",dataIndex:"level",key:"level",render:e=>({national:"国家级",provincial:"省级",municipal:"市级",school:"校级",other:"其他"}[e]||e)},{title:"排名",dataIndex:"ranking",key:"ranking"},{title:"操作",key:"action",width:200,render:(t,n)=>s.jsxs(u,{size:"small",children:[s.jsx(i,{type:"text",icon:s.jsx(C,{}),onClick:()=>Qe(n.certificate_url),children:"查看证明"}),s.jsx(i,{type:"text",icon:s.jsx(Y,{}),onClick:()=>(e=>{Ee(e);const s={...e,achievement_date:e.achievement_date?R(e.achievement_date):null};be.setFieldsValue(s),ue(!0)})(n),children:"编辑"}),s.jsx(F,{title:"确定删除这个成果吗？",onConfirm:()=>(async s=>{try{const t=await e.delete(`/teacher-achievements/${s}`);t&&t.success?(r.success("个人成果删除成功"),ts()):r.error("个人成果删除失败：响应格式错误")}catch(t){console.error("个人成果删除失败:",t),r.error("个人成果删除失败: "+((null==t?void 0:t.message)||"未知错误"))}})(n.id),okText:"确定",cancelText:"取消",children:s.jsx(i,{type:"text",danger:!0,children:"删除"})})]})}]})]})},{key:"trainings",label:"师资培训",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h3",{className:"text-lg font-medium",children:"师资培训管理"}),s.jsx(i,{type:"primary",icon:s.jsx(l,{}),onClick:()=>{We(null),_e.resetFields(),je(!0)},children:"新增培训"})]}),s.jsx(d,{dataSource:Te,rowKey:"id",pagination:{pageSize:5},columns:[{title:"培训名称",dataIndex:"training_name",key:"training_name"},{title:"培训类型",dataIndex:"training_type",key:"training_type",render:e=>({academic:"学术培训",skill:"技能培训",management:"管理培训",teaching:"教学培训",research:"科研培训",other:"其他"}[e]||e)},{title:"培训机构",dataIndex:"training_institution",key:"training_institution"},{title:"开始时间",dataIndex:"start_date",key:"start_date",render:e=>e?R(e).format("YYYY-MM-DD"):"-"},{title:"结束时间",dataIndex:"end_date",key:"end_date",render:e=>e?R(e).format("YYYY-MM-DD"):"-"},{title:"培训时长",dataIndex:"duration_hours",key:"duration_hours",render:e=>e?`${e}小时`:"-"},{title:"操作",key:"action",width:200,render:(t,n)=>s.jsxs(u,{size:"small",children:[s.jsx(i,{type:"text",icon:s.jsx(C,{}),onClick:()=>Qe(n.certificate_url),children:"查看证书"}),s.jsx(i,{type:"text",icon:s.jsx(Y,{}),onClick:()=>(e=>{We(e);const s={...e,training_date:e.training_date?R(e.training_date):null,start_date:e.start_date?R(e.start_date):null,end_date:e.end_date?R(e.end_date):null};_e.setFieldsValue(s),je(!0)})(n),children:"编辑"}),s.jsx(F,{title:"确定删除这个培训记录吗？",onConfirm:()=>(async s=>{try{const t=await e.delete(`/teacher-trainings/${s}`);t&&t.success?(r.success("师资培训删除成功"),ns()):r.error("师资培训删除失败：响应格式错误")}catch(t){console.error("师资培训删除失败:",t),r.error("师资培训删除失败: "+((null==t?void 0:t.message)||"未知错误"))}})(n.id),okText:"确定",cancelText:"取消",children:s.jsx(i,{type:"text",danger:!0,children:"删除"})})]})}]})]})},{key:"mentorships",label:"青蓝筑梦",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h3",{className:"text-lg font-medium",children:"青蓝筑梦项目管理"}),s.jsx(i,{type:"primary",icon:s.jsx(l,{}),onClick:()=>{He(null),we.resetFields(),fe(!0)},children:"新增项目"})]}),s.jsx(d,{dataSource:Ye,rowKey:"id",pagination:{pageSize:5},columns:[{title:"项目名称",dataIndex:"project_name",key:"project_name"},{title:"角色",dataIndex:"role",key:"role",render:e=>({mentor:"导师",mentee:"学员",coordinator:"协调员",observer:"观察员"}[e]||e)},{title:"合作教师",dataIndex:"partner_teacher",key:"partner_teacher"},{title:"开始时间",dataIndex:"start_date",key:"start_date",render:e=>e?R(e).format("YYYY-MM-DD"):"-"},{title:"结束时间",dataIndex:"end_date",key:"end_date",render:e=>e?R(e).format("YYYY-MM-DD"):"-"},{title:"状态",dataIndex:"status",key:"status",render:e=>{const t={active:{text:"进行中",color:"processing"},completed:{text:"已完成",color:"success"},paused:{text:"暂停",color:"warning"},cancelled:{text:"已取消",color:"error"}}[e]||{text:e,color:"default"};return s.jsx(M,{color:t.color,children:t.text})}},{title:"操作",key:"action",width:200,render:(t,n)=>s.jsxs(u,{size:"small",children:[s.jsx(i,{type:"text",icon:s.jsx(C,{}),onClick:()=>Qe(n.summary_report),children:"查看报告"}),s.jsx(i,{type:"text",icon:s.jsx(Y,{}),onClick:()=>(e=>{He(e);const s={...e,start_date:e.start_date?R(e.start_date):null,end_date:e.end_date?R(e.end_date):null};we.setFieldsValue(s),fe(!0)})(n),children:"编辑"}),s.jsx(F,{title:"确定删除这个项目吗？",onConfirm:()=>(async s=>{try{const t=await e.delete(`/teacher-mentorships/${s}`);t&&t.success?(r.success("青蓝筑梦项目删除成功"),rs()):r.error("青蓝筑梦项目删除失败：响应格式错误")}catch(t){console.error("青蓝筑梦项目删除失败:",t),r.error("青蓝筑梦项目删除失败: "+((null==t?void 0:t.message)||"未知错误"))}})(n.id),okText:"确定",cancelText:"取消",children:s.jsx(i,{type:"text",danger:!0,children:"删除"})})]})}]})]})}]})}),s.jsx(I,{title:Re?"编辑课程":"新增课程",open:oe,onCancel:()=>de(!1),onOk:()=>ye.submit(),width:600,children:s.jsxs(n,{form:ye,layout:"vertical",onFinish:async s=>{try{const t={...s,teacher_id:null==K?void 0:K.id,id:null==Re?void 0:Re.id};if(Re){const s=await e.put(`/teacher-courses/${Re.id}`,t);s&&s.success?(r.success("课程更新成功"),de(!1),es()):r.error("课程更新失败：响应格式错误")}else{const s=await e.post("/teacher-courses",t);s&&s.success?(r.success("课程添加成功"),de(!1),es()):r.error("课程添加失败：响应格式错误")}}catch(t){console.error("课程操作失败:",t),r.error("课程操作失败: "+((null==t?void 0:t.message)||"未知错误"))}},children:[s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"课程名称",name:"course_name",rules:[{required:!0,message:"请输入课程名称"}],children:s.jsx(N,{placeholder:"请输入课程名称"})})}),s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"课程代码",name:"course_code",rules:[{required:!0,message:"请输入课程代码"}],children:s.jsx(N,{placeholder:"请输入课程代码"})})})]}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"学期",name:"semester",rules:[{required:!0,message:"请选择学期"}],children:s.jsxs(q,{placeholder:"请选择学期",children:[s.jsx(J,{value:"spring",children:"春季学期"}),s.jsx(J,{value:"fall",children:"秋季学期"}),s.jsx(J,{value:"summer",children:"夏季学期"})]})})}),s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"学年",name:"academic_year",rules:[{required:!0,message:"请输入学年"}],children:s.jsx(N,{placeholder:"如：2023-2024"})})})]}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:8,children:s.jsx(n.Item,{label:"学分",name:"credits",rules:[{required:!0,message:"请输入学分"}],children:s.jsx(B,{min:0,max:10,step:.5,placeholder:"学分",style:{width:"100%"}})})}),s.jsx(f,{span:8,children:s.jsx(n.Item,{label:"课时",name:"hours",rules:[{required:!0,message:"请输入课时"}],children:s.jsx(B,{min:0,placeholder:"课时",style:{width:"100%"}})})}),s.jsx(f,{span:8,children:s.jsx(n.Item,{label:"班级",name:"class_name",children:s.jsx(N,{placeholder:"班级名称"})})})]}),s.jsx(n.Item,{label:"课程描述",name:"description",children:s.jsx(P,{rows:3,placeholder:"课程描述"})})]})}),s.jsx(I,{title:Ne?"编辑教学材料":"上传教学材料",open:xe,onCancel:()=>he(!1),onOk:()=>ve.submit(),width:600,children:s.jsxs(n,{form:ve,layout:"vertical",onFinish:async s=>{try{const t={...s,teacher_id:null==K?void 0:K.id,id:null==Ne?void 0:Ne.id};if(t.upload_date&&(t.upload_date=t.upload_date.format("YYYY-MM-DD HH:mm:ss")),Ne){const s=await e.put(`/teacher-materials/${Ne.id}`,t);s&&s.success?(r.success("教学材料更新成功"),he(!1),ss()):r.error("教学材料更新失败：响应格式错误")}else{const s=await e.post("/teacher-materials",t);s&&s.success?(r.success("教学材料添加成功"),he(!1),ss()):r.error("教学材料添加失败：响应格式错误")}}catch(t){console.error("教学材料操作失败:",t),r.error("教学材料操作失败: "+((null==t?void 0:t.message)||"未知错误"))}},children:[s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"材料名称",name:"material_name",rules:[{required:!0,message:"请输入材料名称"}],children:s.jsx(N,{placeholder:"请输入材料名称"})})}),s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"材料类型",name:"material_type",rules:[{required:!0,message:"请选择材料类型"}],children:s.jsxs(q,{placeholder:"请选择材料类型",children:[s.jsx(J,{value:"courseware",children:"课件"}),s.jsx(J,{value:"textbook",children:"教材"}),s.jsx(J,{value:"reference",children:"参考资料"}),s.jsx(J,{value:"assignment",children:"作业"}),s.jsx(J,{value:"exam",children:"考试资料"}),s.jsx(J,{value:"other",children:"其他"})]})})})]}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"课程名称",name:"course_name",children:s.jsx(N,{placeholder:"相关课程名称"})})}),s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"上传时间",name:"upload_date",rules:[{required:!0,message:"请选择上传时间"}],children:s.jsx(E,{style:{width:"100%"}})})})]}),s.jsx(n.Item,{label:"材料描述",name:"description",children:s.jsx(P,{rows:3,placeholder:"材料描述"})}),s.jsx(n.Item,{name:"file_size",hidden:!0,children:s.jsx(N,{})}),s.jsx(n.Item,{label:"文件上传",name:"file_url",children:s.jsxs("div",{className:"space-y-2",children:[(null==Ne?void 0:Ne.file_url)&&s.jsxs("div",{className:"flex items-center space-x-2 p-2 bg-gray-50 rounded",children:[s.jsx("span",{className:"text-sm text-gray-600",children:"当前文件:"}),s.jsx(i,{type:"link",size:"small",icon:s.jsx(C,{}),onClick:()=>Ze(Ne.file_url,Ne.material_name),children:Ne.material_name||"查看文件"}),Ne.file_size&&s.jsxs("span",{className:"text-xs text-gray-500",children:["(",(Ne.file_size/1024/1024).toFixed(2)," MB)"]})]}),s.jsx(T,{customRequest:async({file:s,onSuccess:t,onError:n})=>{try{const a=new FormData;a.append("file",s);const i=await e.post("/teacher-materials/upload",a,{headers:{"Content-Type":"multipart/form-data"}});if(i&&i.success){const e=i.data;ve.setFieldsValue({file_url:e.url,file_size:e.file_size}),r.success("文件上传成功"),null==t||t(e)}else r.error("文件上传失败"),null==n||n(new Error("上传失败"))}catch(a){console.error("文件上传错误:",a),r.error("文件上传失败: "+((null==a?void 0:a.message)||"未知错误")),null==n||n(a)}},showUploadList:!1,children:s.jsx(i,{icon:s.jsx(D,{}),children:Ne?"重新上传文件":"上传文件"})})]})})]})}),s.jsx(I,{title:Be?"编辑个人成果":"新增个人成果",open:pe,onCancel:()=>ue(!1),onOk:()=>be.submit(),width:600,children:s.jsxs(n,{form:be,layout:"vertical",onFinish:async s=>{try{const t={...s,teacher_id:null==K?void 0:K.id,id:null==Be?void 0:Be.id};if(t.achievement_date&&(t.achievement_date=t.achievement_date.format("YYYY-MM-DD HH:mm:ss")),Be){const s=await e.put(`/teacher-achievements/${Be.id}`,t);s&&s.success?(r.success("个人成果更新成功"),ue(!1),ts()):r.error("个人成果更新失败：响应格式错误")}else{const s=await e.post("/teacher-achievements",t);s&&s.success?(r.success("个人成果添加成功"),ue(!1),ts()):r.error("个人成果添加失败：响应格式错误")}}catch(t){console.error("个人成果操作失败:",t),r.error("个人成果操作失败: "+((null==t?void 0:t.message)||"未知错误"))}},children:[s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"成果名称",name:"achievement_name",rules:[{required:!0,message:"请输入成果名称"}],children:s.jsx(N,{placeholder:"请输入成果名称"})})}),s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"成果类型",name:"achievement_type",rules:[{required:!0,message:"请选择成果类型"}],children:s.jsxs(q,{placeholder:"请选择成果类型",children:[s.jsx(J,{value:"paper",children:"论文"}),s.jsx(J,{value:"patent",children:"专利"}),s.jsx(J,{value:"project",children:"项目"}),s.jsx(J,{value:"award",children:"获奖"}),s.jsx(J,{value:"book",children:"著作"}),s.jsx(J,{value:"software",children:"软件著作权"}),s.jsx(J,{value:"other",children:"其他"})]})})})]}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"获得时间",name:"achievement_date",rules:[{required:!0,message:"请选择获得时间"}],children:s.jsx(E,{style:{width:"100%"}})})}),s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"级别",name:"level",rules:[{required:!0,message:"请选择级别"}],children:s.jsxs(q,{placeholder:"请选择级别",children:[s.jsx(J,{value:"national",children:"国家级"}),s.jsx(J,{value:"provincial",children:"省级"}),s.jsx(J,{value:"municipal",children:"市级"}),s.jsx(J,{value:"school",children:"校级"}),s.jsx(J,{value:"other",children:"其他"})]})})})]}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"排名",name:"ranking",children:s.jsx(N,{placeholder:"如：第一作者、第二完成人等"})})}),s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"获奖金额",name:"award_amount",children:s.jsx(B,{min:0,placeholder:"获奖金额",style:{width:"100%"}})})})]}),s.jsx(n.Item,{label:"成果描述",name:"description",children:s.jsx(P,{rows:3,placeholder:"成果描述"})}),s.jsx(n.Item,{label:"证明材料",name:"certificate_url",children:s.jsxs("div",{className:"space-y-2",children:[(null==Be?void 0:Be.certificate_url)&&s.jsxs("div",{className:"flex items-center space-x-2 p-2 bg-gray-50 rounded",children:[s.jsx("span",{className:"text-sm text-gray-600",children:"当前文件:"}),s.jsx(i,{type:"link",size:"small",icon:s.jsx(C,{}),onClick:()=>Qe(Be.certificate_url),children:Be.achievement_name||"查看证明"})]}),s.jsx(T,{customRequest:async({file:s,onSuccess:t,onError:n})=>{try{const a=new FormData;a.append("file",s);const i=await e.post("/teacher-achievements/upload",a,{headers:{"Content-Type":"multipart/form-data"}});if(i&&i.success){const e=i.data;be.setFieldsValue({certificate_url:e.url}),r.success("证明材料上传成功"),null==t||t(e)}else r.error("证明材料上传失败"),null==n||n(new Error("上传失败"))}catch(a){console.error("文件上传错误:",a),r.error("证明材料上传失败: "+((null==a?void 0:a.message)||"未知错误")),null==n||n(a)}},showUploadList:!1,children:s.jsx(i,{icon:s.jsx(D,{}),children:Be?"重新上传证明":"上传证明材料"})})]})})]})}),s.jsx(I,{title:Le?"编辑师资培训":"新增师资培训",open:me,onCancel:()=>je(!1),onOk:()=>_e.submit(),width:600,children:s.jsxs(n,{form:_e,layout:"vertical",onFinish:async s=>{try{const t={...s,teacher_id:null==K?void 0:K.id,id:null==Le?void 0:Le.id};if(t.training_date&&(t.training_date=t.training_date.format("YYYY-MM-DD HH:mm:ss")),t.start_date&&(t.start_date=t.start_date.format("YYYY-MM-DD HH:mm:ss")),t.end_date&&(t.end_date=t.end_date.format("YYYY-MM-DD HH:mm:ss")),Le){const s=await e.put(`/teacher-trainings/${Le.id}`,t);s&&s.success?(r.success("师资培训更新成功"),je(!1),ns()):r.error("师资培训更新失败：响应格式错误")}else{const s=await e.post("/teacher-trainings",t);s&&s.success?(r.success("师资培训添加成功"),je(!1),ns()):r.error("师资培训添加失败：响应格式错误")}}catch(t){console.error("师资培训操作失败:",t),r.error("师资培训操作失败: "+((null==t?void 0:t.message)||"未知错误"))}},children:[s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"培训名称",name:"training_name",rules:[{required:!0,message:"请输入培训名称"}],children:s.jsx(N,{placeholder:"请输入培训名称"})})}),s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"培训类型",name:"training_type",rules:[{required:!0,message:"请选择培训类型"}],children:s.jsxs(q,{placeholder:"请选择培训类型",children:[s.jsx(J,{value:"academic",children:"学术培训"}),s.jsx(J,{value:"skill",children:"技能培训"}),s.jsx(J,{value:"management",children:"管理培训"}),s.jsx(J,{value:"teaching",children:"教学培训"}),s.jsx(J,{value:"research",children:"科研培训"}),s.jsx(J,{value:"other",children:"其他"})]})})})]}),s.jsx(g,{gutter:16,children:s.jsx(f,{span:24,children:s.jsx(n.Item,{label:"培训机构",name:"training_institution",rules:[{required:!0,message:"请输入培训机构"}],children:s.jsx(N,{placeholder:"请输入培训机构"})})})}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"开始时间",name:"start_date",rules:[{required:!0,message:"请选择开始时间"}],children:s.jsx(E,{style:{width:"100%"}})})}),s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"结束时间",name:"end_date",rules:[{required:!0,message:"请选择结束时间"}],children:s.jsx(E,{style:{width:"100%"}})})})]}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"培训时长(小时)",name:"duration_hours",rules:[{required:!0,message:"请输入培训时长"}],children:s.jsx(B,{min:0,placeholder:"培训时长",style:{width:"100%"}})})}),s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"培训费用",name:"cost",children:s.jsx(B,{min:0,placeholder:"培训费用",style:{width:"100%"}})})})]}),s.jsx(n.Item,{label:"培训内容",name:"content",children:s.jsx(P,{rows:3,placeholder:"培训内容描述"})}),s.jsx(n.Item,{label:"培训证书",name:"certificate_url",children:s.jsxs("div",{className:"space-y-2",children:[(null==Le?void 0:Le.certificate_url)&&s.jsxs("div",{className:"flex items-center space-x-2 p-2 bg-gray-50 rounded",children:[s.jsx("span",{className:"text-sm text-gray-600",children:"当前证书:"}),s.jsx(i,{type:"link",size:"small",icon:s.jsx(C,{}),onClick:()=>Qe(Le.certificate_url),children:Le.training_name||"查看证书"})]}),s.jsx(T,{customRequest:async({file:s,onSuccess:t,onError:n})=>{try{const a=new FormData;a.append("file",s);const i=await e.post("/teacher-trainings/upload",a,{headers:{"Content-Type":"multipart/form-data"}});if(i&&i.success){const e=i.data;_e.setFieldsValue({certificate_url:e.url}),r.success("培训证书上传成功"),null==t||t(e)}else r.error("培训证书上传失败"),null==n||n(new Error("上传失败"))}catch(a){console.error("文件上传错误:",a),r.error("培训证书上传失败: "+((null==a?void 0:a.message)||"未知错误")),null==n||n(a)}},showUploadList:!1,children:s.jsx(i,{icon:s.jsx(D,{}),children:Le?"重新上传证书":"上传培训证书"})})]})})]})}),s.jsx(I,{title:Ae?"编辑青蓝筑梦项目":"新增青蓝筑梦项目",open:ge,onCancel:()=>fe(!1),onOk:()=>we.submit(),width:600,children:s.jsxs(n,{form:we,layout:"vertical",onFinish:async s=>{try{const t={...s,teacher_id:null==K?void 0:K.id,id:null==Ae?void 0:Ae.id};if(t.start_date&&(t.start_date=t.start_date.format("YYYY-MM-DD HH:mm:ss")),t.end_date&&(t.end_date=t.end_date.format("YYYY-MM-DD HH:mm:ss")),Ae){const s=await e.put(`/teacher-mentorships/${Ae.id}`,t);s&&s.success?(r.success("青蓝筑梦项目更新成功"),fe(!1),rs()):r.error("青蓝筑梦项目更新失败：响应格式错误")}else{const s=await e.post("/teacher-mentorships",t);s&&s.success?(r.success("青蓝筑梦项目添加成功"),fe(!1),rs()):r.error("青蓝筑梦项目添加失败：响应格式错误")}}catch(t){console.error("青蓝筑梦项目操作失败:",t),r.error("青蓝筑梦项目操作失败: "+((null==t?void 0:t.message)||"未知错误"))}},children:[s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"项目名称",name:"project_name",rules:[{required:!0,message:"请输入项目名称"}],children:s.jsx(N,{placeholder:"请输入项目名称"})})}),s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"角色",name:"role",rules:[{required:!0,message:"请选择角色"}],children:s.jsxs(q,{placeholder:"请选择角色",children:[s.jsx(J,{value:"mentor",children:"导师"}),s.jsx(J,{value:"mentee",children:"学员"}),s.jsx(J,{value:"coordinator",children:"协调员"}),s.jsx(J,{value:"observer",children:"观察员"})]})})})]}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"合作教师",name:"partner_teacher",children:s.jsx(N,{placeholder:"请输入合作教师姓名"})})}),s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"项目状态",name:"status",rules:[{required:!0,message:"请选择项目状态"}],children:s.jsxs(q,{placeholder:"请选择项目状态",children:[s.jsx(J,{value:"active",children:"进行中"}),s.jsx(J,{value:"completed",children:"已完成"}),s.jsx(J,{value:"paused",children:"暂停"}),s.jsx(J,{value:"cancelled",children:"已取消"})]})})})]}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"开始时间",name:"start_date",rules:[{required:!0,message:"请选择开始时间"}],children:s.jsx(E,{style:{width:"100%"}})})}),s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"结束时间",name:"end_date",children:s.jsx(E,{style:{width:"100%"}})})})]}),s.jsx(n.Item,{label:"项目目标",name:"objectives",children:s.jsx(P,{rows:3,placeholder:"项目目标描述"})}),s.jsx(n.Item,{label:"项目内容",name:"content",children:s.jsx(P,{rows:3,placeholder:"项目内容描述"})}),s.jsx(n.Item,{label:"项目总结报告",name:"summary_report",children:s.jsxs("div",{className:"space-y-2",children:[(null==Ae?void 0:Ae.summary_report)&&s.jsxs("div",{className:"flex items-center space-x-2 p-2 bg-gray-50 rounded",children:[s.jsx("span",{className:"text-sm text-gray-600",children:"当前报告:"}),s.jsx(i,{type:"link",size:"small",icon:s.jsx(C,{}),onClick:()=>Qe(Ae.summary_report),children:Ae.project_name||"查看报告"})]}),s.jsx(T,{customRequest:async({file:s,onSuccess:t,onError:n})=>{try{const a=new FormData;a.append("file",s);const i=await e.post("/teacher-mentorships/upload",a,{headers:{"Content-Type":"multipart/form-data"}});if(i&&i.success){const e=i.data;we.setFieldsValue({summary_report:e.url}),r.success("项目报告上传成功"),null==t||t(e)}else r.error("项目报告上传失败"),null==n||n(new Error("上传失败"))}catch(a){console.error("文件上传错误:",a),r.error("项目报告上传失败: "+((null==a?void 0:a.message)||"未知错误")),null==n||n(a)}},showUploadList:!1,children:s.jsx(i,{icon:s.jsx(D,{}),children:Ae?"重新上传报告":"上传项目总结报告"})})]})})]})}),s.jsx(I,{title:"新增教师",open:se,onCancel:()=>te(!1),onOk:()=>le.submit(),width:800,children:s.jsxs(n,{form:le,layout:"vertical",onFinish:async s=>{try{const t=await e.post("/teachers",s);t.data&&t.data.success&&(r.success("添加成功"),te(!1),Je())}catch(t){console.error("添加失败:",t),r.error("添加失败: "+((null==t?void 0:t.message)||"未知错误"))}},children:[s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:8,children:s.jsx(n.Item,{label:"姓名",name:"name",rules:[{required:!0,message:"请输入教师姓名"}],children:s.jsx(N,{placeholder:"请输入教师姓名"})})}),s.jsx(f,{span:8,children:s.jsx(n.Item,{label:"工号",name:"employee_id",rules:[{required:!0,message:"请输入工号"}],children:s.jsx(N,{placeholder:"请输入工号"})})}),s.jsx(f,{span:8,children:s.jsx(n.Item,{label:"性别",name:"gender",rules:[{required:!0,message:"请选择性别"}],children:s.jsxs(q,{placeholder:"请选择性别",children:[s.jsx(J,{value:"male",children:"男"}),s.jsx(J,{value:"female",children:"女"})]})})})]}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"身份证号",name:"id_card",rules:[{required:!0,message:"请输入身份证号"},{pattern:/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,message:"请输入正确的身份证号"}],children:s.jsx(N,{placeholder:"请输入身份证号"})})}),s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"联系电话",name:"phone",rules:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号"}],children:s.jsx(N,{placeholder:"请输入联系电话"})})})]}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:8,children:s.jsx(n.Item,{label:"民族",name:"ethnicity",children:s.jsx(N,{placeholder:"请输入民族"})})}),s.jsx(f,{span:8,children:s.jsx(n.Item,{label:"政治面貌",name:"political_status",children:s.jsxs(q,{placeholder:"请选择政治面貌",children:[s.jsx(J,{value:"党员",children:"中共党员"}),s.jsx(J,{value:"预备党员",children:"中共预备党员"}),s.jsx(J,{value:"团员",children:"共青团员"}),s.jsx(J,{value:"群众",children:"群众"}),s.jsx(J,{value:"民主党派",children:"民主党派"})]})})}),s.jsx(f,{span:8,children:s.jsx(n.Item,{label:"入职年份",name:"join_year",children:s.jsx(B,{style:{width:"100%"},placeholder:"请输入入职年份",min:1900,max:(new Date).getFullYear()})})})]}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:8,children:s.jsx(n.Item,{label:"职称",name:"title",children:s.jsxs(q,{placeholder:"请选择职称",children:[s.jsx(J,{value:"professor",children:"教授"}),s.jsx(J,{value:"associate_professor",children:"副教授"}),s.jsx(J,{value:"lecturer",children:"讲师"}),s.jsx(J,{value:"assistant",children:"助教"})]})})}),s.jsx(f,{span:8,children:s.jsx(n.Item,{label:"学历",name:"degree",children:s.jsxs(q,{placeholder:"请选择学历",children:[s.jsx(J,{value:"博士",children:"博士"}),s.jsx(J,{value:"硕士",children:"硕士"}),s.jsx(J,{value:"本科",children:"本科"}),s.jsx(J,{value:"专科",children:"专科"})]})})}),s.jsx(f,{span:8,children:s.jsx(n.Item,{label:"状态",name:"status",initialValue:"active",children:s.jsxs(q,{placeholder:"请选择状态",children:[s.jsx(J,{value:"active",children:"在职"}),s.jsx(J,{value:"inactive",children:"离职"})]})})})]}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"邮箱",name:"email",rules:[{type:"email",message:"请输入正确的邮箱地址"}],children:s.jsx(N,{placeholder:"请输入邮箱地址"})})}),s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"入职时间",name:"hire_date",children:s.jsx(E,{style:{width:"100%"},placeholder:"请选择入职时间"})})})]}),s.jsx(n.Item,{label:"地址",name:"address",children:s.jsx(N,{placeholder:"请输入地址"})}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"紧急联系人",name:"emergency_contact",children:s.jsx(N,{placeholder:"请输入紧急联系人姓名"})})}),s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"紧急联系电话",name:"emergency_phone",rules:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号"}],children:s.jsx(N,{placeholder:"请输入紧急联系人电话"})})})]}),s.jsx(n.Item,{label:"备注",name:"notes",children:s.jsx(P,{rows:3,placeholder:"备注信息"})})]})}),s.jsx(I,{title:"编辑教师信息",open:ne,onCancel:()=>re(!1),onOk:()=>ce.submit(),width:800,children:s.jsxs(n,{form:ce,layout:"vertical",onFinish:async s=>{try{const t=await e.put(`/teachers/${ae.id}`,s);t&&t.success?(r.success("更新成功"),re(!1),K&&K.id===ae.id&&Q({...K,...s}),Je()):r.error("更新失败：响应格式错误")}catch(t){console.error("更新失败:",t),r.error("更新失败: "+((null==t?void 0:t.message)||"未知错误"))}},children:[s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:8,children:s.jsx(n.Item,{label:"姓名",name:"name",rules:[{required:!0,message:"请输入教师姓名"}],children:s.jsx(N,{placeholder:"请输入教师姓名"})})}),s.jsx(f,{span:8,children:s.jsx(n.Item,{label:"工号",name:"employee_id",rules:[{required:!0,message:"请输入工号"}],children:s.jsx(N,{placeholder:"请输入工号"})})}),s.jsx(f,{span:8,children:s.jsx(n.Item,{label:"性别",name:"gender",rules:[{required:!0,message:"请选择性别"}],children:s.jsxs(q,{placeholder:"请选择性别",children:[s.jsx(J,{value:"male",children:"男"}),s.jsx(J,{value:"female",children:"女"})]})})})]}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"身份证号",name:"id_card",rules:[{required:!0,message:"请输入身份证号"},{pattern:/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,message:"请输入正确的身份证号"}],children:s.jsx(N,{placeholder:"请输入身份证号"})})}),s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"联系电话",name:"phone",rules:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号"}],children:s.jsx(N,{placeholder:"请输入联系电话"})})})]}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:8,children:s.jsx(n.Item,{label:"民族",name:"ethnicity",children:s.jsx(N,{placeholder:"请输入民族"})})}),s.jsx(f,{span:8,children:s.jsx(n.Item,{label:"政治面貌",name:"political_status",children:s.jsxs(q,{placeholder:"请选择政治面貌",children:[s.jsx(J,{value:"党员",children:"中共党员"}),s.jsx(J,{value:"预备党员",children:"中共预备党员"}),s.jsx(J,{value:"团员",children:"共青团员"}),s.jsx(J,{value:"群众",children:"群众"}),s.jsx(J,{value:"民主党派",children:"民主党派"})]})})}),s.jsx(f,{span:8,children:s.jsx(n.Item,{label:"入职年份",name:"join_year",children:s.jsx(B,{style:{width:"100%"},placeholder:"请输入入职年份",min:1900,max:(new Date).getFullYear()})})})]}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:8,children:s.jsx(n.Item,{label:"职称",name:"title",children:s.jsxs(q,{placeholder:"请选择职称",children:[s.jsx(J,{value:"professor",children:"教授"}),s.jsx(J,{value:"associate_professor",children:"副教授"}),s.jsx(J,{value:"lecturer",children:"讲师"}),s.jsx(J,{value:"assistant",children:"助教"})]})})}),s.jsx(f,{span:8,children:s.jsx(n.Item,{label:"学历",name:"degree",children:s.jsxs(q,{placeholder:"请选择学历",children:[s.jsx(J,{value:"博士",children:"博士"}),s.jsx(J,{value:"硕士",children:"硕士"}),s.jsx(J,{value:"本科",children:"本科"}),s.jsx(J,{value:"专科",children:"专科"})]})})}),s.jsx(f,{span:8,children:s.jsx(n.Item,{label:"状态",name:"status",children:s.jsxs(q,{placeholder:"请选择状态",children:[s.jsx(J,{value:"active",children:"在职"}),s.jsx(J,{value:"inactive",children:"离职"})]})})})]}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"邮箱",name:"email",rules:[{type:"email",message:"请输入正确的邮箱地址"}],children:s.jsx(N,{placeholder:"请输入邮箱地址"})})}),s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"入职时间",name:"hire_date",children:s.jsx(E,{style:{width:"100%"},placeholder:"请选择入职时间"})})})]}),s.jsx(n.Item,{label:"地址",name:"address",children:s.jsx(N,{placeholder:"请输入地址"})}),s.jsxs(g,{gutter:16,children:[s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"紧急联系人",name:"emergency_contact",children:s.jsx(N,{placeholder:"请输入紧急联系人姓名"})})}),s.jsx(f,{span:12,children:s.jsx(n.Item,{label:"紧急联系电话",name:"emergency_phone",rules:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号"}],children:s.jsx(N,{placeholder:"请输入紧急联系人电话"})})})]}),s.jsx(n.Item,{label:"备注",name:"notes",children:s.jsx(P,{rows:3,placeholder:"备注信息"})})]})})]})};export{Q as default};
