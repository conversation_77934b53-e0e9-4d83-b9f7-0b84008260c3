import{j as e}from"./index-DXaqwR6F-1753160720439.js";import{r as s,F as r,q as n,E as i,ae as l,a as t,k as a,S as o,O as d,ag as c,Q as x,L as h,aa as j}from"./antd-lXsGnH6e-1753160720439.js";import{T as p}from"./templateAnalyzer-CiUuG2Cz-1753160720439.js";import"./vendor-D2RBMdQ0-1753160720439.js";import"./AdvancedDocumentParser-D0HhWqXv-1753160720439.js";import"./jszip.min-DISjQb3B-1753160720439.js";const{Title:m,Paragraph:g,Text:y}=i,v=()=>{var i,v,u,f,b,S,B;const[z,w]=s.useState(!1),[C,T]=s.useState(null),[k]=s.useState(()=>new p),W="/templates/（统招本科）计算机与人工智能学院学生在读证明模板.docx",A=e=>e>=80?"#52c41a":e>=60?"#faad14":"#ff4d4f",R=e=>e>=80?"success":e>=60?"normal":"exception";return e.jsx("div",{style:{padding:"24px",maxWidth:"1200px",margin:"0 auto"},children:e.jsxs(r,{children:[e.jsxs("div",{style:{textAlign:"center",marginBottom:"24px"},children:[e.jsx(n,{style:{fontSize:"48px",color:"#1890ff",marginBottom:"16px"}}),e.jsx(m,{level:2,children:"Word模板分析工具"}),e.jsx(g,{children:"分析指定的Word模板文件，评估格式保持能力和变量识别效果"})]}),e.jsx(r,{size:"small",style:{marginBottom:"24px",backgroundColor:"#f6ffed"},children:e.jsxs(l,{children:[e.jsx(n,{}),e.jsx(y,{strong:!0,children:"目标模板:"}),e.jsx(y,{code:!0,children:W})]})}),e.jsx("div",{style:{textAlign:"center",marginBottom:"24px"},children:e.jsx(t,{type:"primary",size:"large",icon:e.jsx(a,{}),onClick:async()=>{w(!0);try{console.log("开始分析模板:",W);const e=await k.analyzeTemplate(W);T(e),console.log("分析完成:",e)}catch(e){console.error("分析失败:",e),T({success:!1,error:e.message,recommendations:["分析过程中发生错误，请检查模板文件"],formatScore:0,variableScore:0,overallScore:0})}finally{w(!1)}},loading:z,children:z?"正在分析...":"开始分析模板"})}),z&&e.jsx(r,{children:e.jsxs("div",{style:{textAlign:"center",padding:"40px"},children:[e.jsx(o,{size:"large"}),e.jsx("div",{style:{marginTop:"16px"},children:e.jsx(y,{children:"正在解析Word文档格式和变量信息..."})})]})}),C&&!z&&e.jsx("div",{children:C.success?e.jsxs("div",{children:[e.jsx(r,{title:"📊 评分概览",style:{marginBottom:"24px"},children:e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"16px"},children:[e.jsxs("div",{children:[e.jsx(y,{strong:!0,children:"格式质量"}),e.jsx(c,{percent:C.formatScore,strokeColor:A(C.formatScore),status:R(C.formatScore)})]}),e.jsxs("div",{children:[e.jsx(y,{strong:!0,children:"变量质量"}),e.jsx(c,{percent:C.variableScore,strokeColor:A(C.variableScore),status:R(C.variableScore)})]}),e.jsxs("div",{children:[e.jsx(y,{strong:!0,children:"综合评分"}),e.jsx(c,{percent:C.overallScore,strokeColor:A(C.overallScore),status:R(C.overallScore)})]})]})}),e.jsx(r,{title:"📄 文档基本信息",style:{marginBottom:"24px"},children:e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(150px, 1fr))",gap:"16px"},children:[e.jsxs("div",{children:[e.jsx(y,{type:"secondary",children:"内容长度"}),e.jsxs("div",{children:[e.jsx(y,{strong:!0,children:(null==(i=C.analysis)?void 0:i.content.length)||0})," 字符"]})]}),e.jsxs("div",{children:[e.jsx(y,{type:"secondary",children:"样式数量"}),e.jsxs("div",{children:[e.jsx(y,{strong:!0,children:(null==(v=C.analysis)?void 0:v.styles.length)||0})," 个"]})]}),e.jsxs("div",{children:[e.jsx(y,{type:"secondary",children:"表格数量"}),e.jsxs("div",{children:[e.jsx(y,{strong:!0,children:(null==(u=C.analysis)?void 0:u.tables.length)||0})," 个"]})]}),e.jsxs("div",{children:[e.jsx(y,{type:"secondary",children:"变量数量"}),e.jsxs("div",{children:[e.jsx(y,{strong:!0,children:(null==(f=C.analysis)?void 0:f.variables.length)||0})," 个"]})]})]})}),(null==(b=C.analysis)?void 0:b.tables)&&C.analysis.tables.length>0&&e.jsx(r,{title:"🎨 表格结构分析",style:{marginBottom:"24px"},children:C.analysis.tables.map((s,n)=>e.jsx(r,{size:"small",style:{marginBottom:"8px"},children:e.jsxs(l,{children:[e.jsxs(x,{color:"blue",children:["表格 ",n+1]}),e.jsxs(y,{children:["行数: ",s.rows]}),e.jsxs(y,{children:["列数: ",s.columns]}),e.jsx(x,{color:s.hasBorder?"green":"orange",children:s.hasBorder?"有边框":"无边框"})]})},n))}),(null==(S=C.analysis)?void 0:S.variables)&&C.analysis.variables.length>0&&e.jsx(r,{title:"🔤 变量识别分析",style:{marginBottom:"24px"},children:C.analysis.variables.map((s,n)=>e.jsxs(r,{size:"small",style:{marginBottom:"8px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs(l,{children:[e.jsx(x,{color:"orange",children:s.text}),e.jsx(y,{children:"→"}),e.jsx(x,{color:"blue",children:s.suggestedMapping}),e.jsxs(x,{color:s.confidence>.8?"green":s.confidence>.5?"orange":"red",children:[(100*s.confidence).toFixed(1),"%"]})]})}),e.jsxs("div",{style:{marginTop:"8px",fontSize:"12px",color:"#666"},children:["上下文: ",s.context.trim()]})]},n))}),e.jsx(r,{title:"💡 分析建议",style:{marginBottom:"24px"},children:C.recommendations.map((s,r)=>e.jsx("div",{style:{marginBottom:"8px"},children:s.startsWith("✅")?e.jsxs(y,{type:"success",children:[e.jsx(h,{style:{marginRight:"8px"}}),s.substring(2)]}):s.startsWith("⚠️")?e.jsxs(y,{type:"warning",children:[e.jsx(j,{style:{marginRight:"8px"}}),s.substring(2)]}):e.jsx(y,{children:s})},r))}),(null==(B=C.analysis)?void 0:B.content)&&e.jsx(r,{title:"📋 内容预览",children:e.jsxs("div",{style:{backgroundColor:"#f5f5f5",padding:"16px",borderRadius:"4px",fontFamily:"monospace",fontSize:"12px",maxHeight:"300px",overflowY:"auto",whiteSpace:"pre-wrap"},children:[C.analysis.content.substring(0,1e3),C.analysis.content.length>1e3&&"\n\n... (内容已截断)"]})})]}):e.jsx(d,{message:"分析失败",description:C.error,type:"error",showIcon:!0,style:{marginBottom:"24px"}})})]})})};export{v as default};
