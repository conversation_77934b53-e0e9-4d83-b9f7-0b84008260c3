import{j as e}from"./index-BbdVriMF-1753162495593.js";import{M as s,F as t,Z as l,ae as r,g as a,bn as i,aU as n,Q as o,i as c,B as d,ac as x,ad as h,L as m,ah as j,am as u,A as p,r as y,ab as g,ag as f,P as v,bo as k,O as w,Y as b,aq as S,s as z,G as C,H as I,J as Y,I as B,as as L,a as H,ao as N,ay as D,aK as M,T as q,af as F,aB as $,bp as P,bq as T,br as U,bk as A,W as O,aH as W}from"./antd-w1cSjLgF-1753162495593.js";import{u as R}from"./index-CM1m3_az-1753162495593.js";import{u as V}from"./userService-kUY0fGJp-1753162495593.js";import{s as E}from"./securityService-DjlJ9x7B-1753162495593.js";import"./vendor-D2RBMdQ0-1753162495593.js";const J=({visible:y,user:g,onClose:f})=>{if(!g)return null;const v={active:{text:"正常",color:"success"},inactive:{text:"禁用",color:"default"},locked:{text:"锁定",color:"error"}},k=[{key:"admin",name:"超级管理员",color:"red"},{key:"counselor",name:"辅导员",color:"blue"},{key:"teacher",name:"教师",color:"green"},{key:"party_secretary",name:"党建专员",color:"purple"},{key:"dean",name:"院长",color:"orange"}].find(e=>e.key===g.role);return e.jsx(s,{title:e.jsxs(r,{children:[e.jsx(p,{size:40,src:g.avatar,icon:e.jsx(a,{}),style:{backgroundColor:"#1890ff"}}),e.jsxs("div",{children:[e.jsx("div",{style:{fontSize:16,fontWeight:500},children:g.realName}),e.jsxs("div",{style:{fontSize:12,color:"#666"},children:["@",g.username]})]})]}),open:y,onCancel:f,footer:null,width:800,destroyOnHidden:!0,children:e.jsxs("div",{style:{marginTop:16},children:[e.jsx(t,{title:"基本信息",size:"small",style:{marginBottom:16},children:e.jsxs(l,{column:2,size:"small",children:[e.jsx(l.Item,{label:"用户名",children:e.jsxs(r,{children:[e.jsx(a,{}),g.username]})}),e.jsx(l.Item,{label:"真实姓名",children:g.realName}),e.jsx(l.Item,{label:"邮箱",children:e.jsxs(r,{children:[e.jsx(i,{}),g.email]})}),e.jsx(l.Item,{label:"手机号",children:e.jsxs(r,{children:[e.jsx(n,{}),g.phone]})}),e.jsx(l.Item,{label:"角色",children:e.jsx(o,{color:null==k?void 0:k.color,icon:e.jsx(c,{}),children:(null==k?void 0:k.name)||g.role})}),e.jsx(l.Item,{label:"部门",children:g.department}),e.jsx(l.Item,{label:"状态",children:e.jsx(d,{status:v[g.status].color,text:v[g.status].text})}),e.jsx(l.Item,{label:"权限数量",children:e.jsx(o,{color:"blue",children:g.permissions.includes("*")?"全部权限":`${g.permissions.length}个权限`})})]})}),e.jsx(t,{title:"时间信息",size:"small",style:{marginBottom:16},children:e.jsxs(l,{column:2,size:"small",children:[e.jsx(l.Item,{label:"创建时间",children:e.jsxs(r,{children:[e.jsx(h,{}),x(g.createdAt).format("YYYY-MM-DD HH:mm:ss")]})}),e.jsx(l.Item,{label:"更新时间",children:e.jsxs(r,{children:[e.jsx(h,{}),x(g.updatedAt).format("YYYY-MM-DD HH:mm:ss")]})}),e.jsx(l.Item,{label:"最后登录",children:e.jsxs(r,{children:[e.jsx(m,{style:{color:"#52c41a"}}),g.lastLogin?x(g.lastLogin).format("YYYY-MM-DD HH:mm:ss"):"从未登录"]})}),e.jsxs(l.Item,{label:"账户年龄",children:[x().diff(x(g.createdAt),"day")," 天"]})]})}),e.jsx(t,{title:"权限详情",size:"small",style:{marginBottom:16},children:e.jsx("div",{style:{maxHeight:200,overflow:"auto"},children:g.permissions.includes("*")?e.jsxs(o,{color:"red",style:{margin:4},children:[e.jsx(j,{})," 全部权限"]}):g.permissions.map(s=>e.jsx(o,{color:"blue",style:{margin:4},children:s},s))})}),e.jsx(t,{title:"最近活动",size:"small",children:e.jsx(u,{size:"small",items:[{time:"2024-01-15 10:30:00",action:"登录系统",description:"从 ************* 登录",type:"login"},{time:"2024-01-15 09:15:00",action:"修改个人信息",description:"更新了联系方式",type:"update"},{time:"2024-01-14 16:45:00",action:"查看学生信息",description:"查看了班级学生列表",type:"view"},{time:"2024-01-14 14:20:00",action:"导出数据",description:"导出了学生成绩报表",type:"export"}].map(s=>({dot:"login"===s.type?e.jsx(m,{style:{color:"#52c41a"}}):"update"===s.type?e.jsx(h,{style:{color:"#1890ff"}}):e.jsx(a,{style:{color:"#666"}}),children:e.jsxs("div",{children:[e.jsx("div",{style:{fontWeight:500},children:s.action}),e.jsx("div",{style:{fontSize:12,color:"#666",marginBottom:4},children:s.description}),e.jsx("div",{style:{fontSize:12,color:"#999"},children:x(s.time).format("YYYY-MM-DD HH:mm:ss")})]})}))})})]})})},_=({password:s,style:t,showDetails:l=!0})=>{const[a,i]=y.useState({score:0,feedback:[],passed:!1});y.useEffect(()=>{if(s){const e=E.validatePasswordStrength(s);i(e)}else i({score:0,feedback:[],passed:!1})},[s]);const n=0===(c=a.score)?{text:"无",color:"#d9d9d9",percent:0}:c<=1?{text:"很弱",color:"#ff4d4f",percent:20}:c<=2?{text:"弱",color:"#fa8c16",percent:40}:c<=3?{text:"中等",color:"#faad14",percent:60}:c<=4?{text:"强",color:"#52c41a",percent:80}:{text:"很强",color:"#389e0d",percent:100};var c;return e.jsxs("div",{style:t,children:[e.jsxs("div",{style:{marginBottom:16},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:8},children:[e.jsxs("span",{style:{fontSize:14,fontWeight:500},children:[e.jsx(g,{style:{marginRight:4}}),"密码强度"]}),e.jsx(o,{color:n.color,children:n.text})]}),e.jsx(f,{percent:n.percent,strokeColor:n.color,showInfo:!1,size:"small"})]}),l&&s&&e.jsxs("div",{style:{marginBottom:16},children:[e.jsx("div",{style:{fontSize:14,fontWeight:500,marginBottom:8},children:"密码要求:"}),e.jsx(v,{size:"small",dataSource:[{key:"length",text:"至少8位字符",check:e=>e.length>=8},{key:"uppercase",text:"包含大写字母",check:e=>/[A-Z]/.test(e)},{key:"lowercase",text:"包含小写字母",check:e=>/[a-z]/.test(e)},{key:"numbers",text:"包含数字",check:e=>/\d/.test(e)},{key:"special",text:"包含特殊字符",check:e=>/[!@#$%^&*(),.?":{}|<>]/.test(e)}],renderItem:t=>{const l=t.check(s);return e.jsx(v.Item,{style:{padding:"4px 0",border:"none"},children:e.jsxs(r,{children:[l?e.jsx(m,{style:{color:"#52c41a"}}):e.jsx(k,{style:{color:"#ff4d4f"}}),e.jsx("span",{style:{color:l?"#52c41a":"#ff4d4f",fontSize:12},children:t.text})]})})}})]}),a.feedback.length>0&&e.jsx(w,{message:"密码建议",description:e.jsx("ul",{style:{margin:0,paddingLeft:16},children:a.feedback.map((s,t)=>e.jsx("li",{style:{fontSize:12},children:s},t))}),type:"warning",icon:e.jsx(b,{}),showIcon:!0,style:{marginBottom:16}}),s&&e.jsx("div",{style:{textAlign:"center"},children:a.passed?e.jsxs(o,{color:"success",style:{fontSize:12},children:[e.jsx(m,{style:{marginRight:4}}),"密码符合安全要求"]}):e.jsxs(o,{color:"error",style:{fontSize:12},children:[e.jsx(k,{style:{marginRight:4}}),"密码不符合安全要求"]})}),!s&&l&&e.jsx(w,{message:"密码安全提示",description:e.jsxs("div",{style:{fontSize:12},children:[e.jsx("p",{children:"为了保护您的账户安全，请设置一个强密码："}),e.jsxs("ul",{style:{margin:0,paddingLeft:16},children:[e.jsx("li",{children:"使用至少8个字符"}),e.jsx("li",{children:"包含大小写字母、数字和特殊字符"}),e.jsx("li",{children:"避免使用常见密码或个人信息"}),e.jsx("li",{children:"定期更换密码"})]})]}),type:"info",showIcon:!0,style:{fontSize:12}})]})},{Option:K}=L,{Search:Q}=B,{RangePicker:Z}=W,G=()=>{const[l,i]=s.useModal(),[n,x]=y.useState([]),[h,m]=y.useState(!1),[j,u]=y.useState(!1),[f,v]=y.useState(null),[k]=S.useForm(),[w,b]=y.useState(""),[W,E]=y.useState(""),[Z,G]=y.useState(""),[X,ee]=y.useState(!1),[se,te]=y.useState(null),[le,re]=y.useState(""),ae=R("user:create"),ie=R("user:edit"),ne=R("user:delete"),oe=R("user:logs"),ce=[{key:"admin",name:"超级管理员",color:"red"},{key:"counselor",name:"辅导员",color:"blue"},{key:"teacher",name:"教师",color:"green"},{key:"party_secretary",name:"党建专员",color:"purple"},{key:"dean",name:"院长",color:"orange"}],de={active:{text:"正常",color:"success"},inactive:{text:"禁用",color:"default"},locked:{text:"锁定",color:"error"}};y.useEffect(()=>{xe()},[]);const xe=async()=>{m(!0);try{const e=await V.getUsers();x(e)}catch(e){z.error("获取用户列表失败"),console.error("获取用户列表失败:",e)}finally{m(!1)}},he=[{title:"用户信息",key:"userInfo",width:200,render:(s,t)=>e.jsxs(r,{children:[e.jsx(p,{size:40,src:t.avatar,icon:e.jsx(a,{}),style:{backgroundColor:"#1890ff"}}),e.jsxs("div",{children:[e.jsx("div",{style:{fontWeight:500},children:t.realName}),e.jsxs("div",{style:{fontSize:"12px",color:"#666"},children:["@",t.username]})]})]})},{title:"角色",dataIndex:"role",key:"role",width:120,render:s=>{const t=ce.find(e=>e.key===s);return e.jsx(o,{color:null==t?void 0:t.color,children:(null==t?void 0:t.name)||s})}},{title:"部门",dataIndex:"department",key:"department",width:120},{title:"联系方式",key:"contact",width:180,render:(s,t)=>e.jsxs("div",{children:[e.jsx("div",{style:{fontSize:"12px"},children:t.email}),e.jsx("div",{style:{fontSize:"12px",color:"#666"},children:t.phone})]})},{title:"状态",dataIndex:"status",key:"status",width:100,render:s=>{const t=de[s];return e.jsx(d,{status:t.color,text:t.text})}},{title:"最后登录",dataIndex:"lastLogin",key:"lastLogin",width:150,render:s=>e.jsx("div",{style:{fontSize:"12px"},children:s})},{title:"操作",key:"action",width:200,fixed:"right",render:(s,t)=>e.jsxs(r,{size:"small",children:[e.jsx(q,{title:"查看详情",children:e.jsx(H,{type:"text",size:"small",icon:e.jsx(F,{}),onClick:()=>je(t)})}),ie&&e.jsx(q,{title:"编辑",children:e.jsx(H,{type:"text",size:"small",icon:e.jsx($,{}),onClick:()=>me(t)})}),e.jsx(q,{title:"locked"===t.status?"解锁":"锁定",children:e.jsx(H,{type:"text",size:"small",icon:"locked"===t.status?e.jsx(P,{}):e.jsx(g,{}),onClick:()=>ue(t)})}),oe&&e.jsx(q,{title:"登录日志",children:e.jsx(H,{type:"text",size:"small",icon:e.jsx(T,{}),onClick:()=>ye(t)})}),ie&&e.jsx(q,{title:"重置密码",children:e.jsx(H,{type:"text",size:"small",icon:e.jsx(U,{}),onClick:()=>ge(t)})}),ne&&"admin"!==t.role&&e.jsx(A,{title:"确定要删除这个用户吗？",onConfirm:()=>pe(t.id),okText:"确定",cancelText:"取消",children:e.jsx(q,{title:"删除",children:e.jsx(H,{type:"text",size:"small",danger:!0,icon:e.jsx(O,{})})})})]})}],me=e=>{v(e),k.setFieldsValue({...e,password:void 0}),u(!0)},je=e=>{te(e),ee(!0)},ue=async e=>{try{const s="locked"===e.status?"active":"locked",t=await V.toggleUserStatus(e.id,s);x(s=>s.map(s=>s.id===e.id?t:s)),z.success("用户已"+("locked"===s?"锁定":"解锁"))}catch(s){z.error(s.message||"操作失败")}},pe=async e=>{try{await V.deleteUser(e),x(s=>s.filter(s=>s.id!==e)),z.success("用户删除成功")}catch(s){z.error(s.message||"删除失败")}},ye=e=>{window.open(`/system/login-logs?user=${e.username}`,"_blank")},ge=e=>{l.confirm({title:"重置密码",content:`确定要重置用户 "${e.realName}" 的密码吗？重置后密码将变为 "123456"`,onOk:async()=>{try{await V.resetUserPassword(e.id,"123456"),z.success("密码重置成功，新密码为：123456")}catch(s){z.error(s.message||"密码重置失败")}}})},fe=n.filter(e=>{const s=!w||e.realName.toLowerCase().includes(w.toLowerCase())||e.username.toLowerCase().includes(w.toLowerCase())||e.email.toLowerCase().includes(w.toLowerCase()),t=!W||e.role===W,l=!Z||e.status===Z;return s&&t&&l}),ve={total:n.length,active:n.filter(e=>"active"===e.status).length,inactive:n.filter(e=>"inactive"===e.status).length,locked:n.filter(e=>"locked"===e.status).length};return e.jsxs("div",{children:[e.jsxs(C,{gutter:16,style:{marginBottom:24},children:[e.jsx(I,{span:6,children:e.jsx(t,{children:e.jsx(Y,{title:"总用户数",value:ve.total,prefix:e.jsx(c,{}),valueStyle:{color:"#1890ff"}})})}),e.jsx(I,{span:6,children:e.jsx(t,{children:e.jsx(Y,{title:"正常用户",value:ve.active,prefix:e.jsx(a,{}),valueStyle:{color:"#52c41a"}})})}),e.jsx(I,{span:6,children:e.jsx(t,{children:e.jsx(Y,{title:"禁用用户",value:ve.inactive,prefix:e.jsx(a,{}),valueStyle:{color:"#faad14"}})})}),e.jsx(I,{span:6,children:e.jsx(t,{children:e.jsx(Y,{title:"锁定用户",value:ve.locked,prefix:e.jsx(g,{}),valueStyle:{color:"#f5222d"}})})})]}),e.jsx(t,{style:{marginBottom:16},children:e.jsxs(C,{gutter:16,align:"middle",children:[e.jsx(I,{flex:"auto",children:e.jsxs(r,{children:[e.jsx(Q,{placeholder:"搜索用户名、姓名或邮箱",allowClear:!0,style:{width:300},onSearch:b,onChange:e=>b(e.target.value)}),e.jsx(L,{placeholder:"选择角色",allowClear:!0,style:{width:120},onChange:E,children:ce.map(s=>e.jsx(K,{value:s.key,children:s.name},s.key))}),e.jsxs(L,{placeholder:"选择状态",allowClear:!0,style:{width:120},onChange:G,children:[e.jsx(K,{value:"active",children:"正常"}),e.jsx(K,{value:"inactive",children:"禁用"}),e.jsx(K,{value:"locked",children:"锁定"})]})]})}),e.jsx(I,{children:ae&&e.jsx(H,{type:"primary",icon:e.jsx(N,{}),onClick:()=>{v(null),k.resetFields(),u(!0)},children:"新增用户"})})]})}),e.jsx(t,{children:e.jsx(D,{columns:he,dataSource:fe,rowKey:"id",loading:h,scroll:{x:1200},pagination:{total:fe.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,s)=>`第 ${s[0]}-${s[1]} 条/共 ${e} 条`}})}),e.jsx(s,{title:f?"编辑用户":"新增用户",open:j,onOk:async()=>{try{const e=await k.validateFields();if(f){const s={realName:e.realName,email:e.email,phone:e.phone,role:e.role,department:e.department,status:e.status||"active"};e.password&&(s.password=e.password);const t=await V.updateUser(f.id,s);x(e=>e.map(e=>e.id===f.id?t:e)),z.success("用户更新成功")}else{const s={username:e.username,realName:e.realName,email:e.email,phone:e.phone,role:e.role,department:e.department,password:e.password,status:e.status||"active"},t=await V.createUser(s);x(e=>[...e,t]),z.success("用户创建成功")}u(!1),k.resetFields()}catch(e){z.error(e.message||"操作失败"),console.error("用户操作失败:",e)}},onCancel:()=>{u(!1),k.resetFields()},width:600,destroyOnHidden:!0,children:e.jsxs(S,{form:k,layout:"vertical",initialValues:{status:"active"},children:[e.jsxs(C,{gutter:16,children:[e.jsx(I,{span:12,children:e.jsx(S.Item,{name:"username",label:"用户名",rules:[{required:!0,message:"请输入用户名"},{min:3,message:"用户名至少3个字符"}],children:e.jsx(B,{placeholder:"请输入用户名"})})}),e.jsx(I,{span:12,children:e.jsx(S.Item,{name:"realName",label:"真实姓名",rules:[{required:!0,message:"请输入真实姓名"}],children:e.jsx(B,{placeholder:"请输入真实姓名"})})})]}),e.jsxs(C,{gutter:16,children:[e.jsx(I,{span:12,children:e.jsx(S.Item,{name:"email",label:"邮箱",rules:[{required:!0,message:"请输入邮箱"},{type:"email",message:"请输入有效的邮箱地址"}],children:e.jsx(B,{placeholder:"请输入邮箱"})})}),e.jsx(I,{span:12,children:e.jsx(S.Item,{name:"phone",label:"手机号",rules:[{required:!0,message:"请输入手机号"},{pattern:/^1[3-9]\d{9}$/,message:"请输入有效的手机号"}],children:e.jsx(B,{placeholder:"请输入手机号"})})})]}),e.jsxs(C,{gutter:16,children:[e.jsx(I,{span:12,children:e.jsx(S.Item,{name:"role",label:"角色",rules:[{required:!0,message:"请选择角色"}],children:e.jsx(L,{placeholder:"请选择角色",children:ce.map(s=>e.jsx(K,{value:s.key,children:s.name},s.key))})})}),e.jsx(I,{span:12,children:e.jsx(S.Item,{name:"department",label:"部门",rules:[{required:!0,message:"请输入部门"}],children:e.jsx(B,{placeholder:"请输入部门"})})})]}),!f&&e.jsxs(e.Fragment,{children:[e.jsx(S.Item,{name:"password",label:"密码",rules:[{required:!0,message:"请输入密码"},{min:6,message:"密码至少6个字符"}],children:e.jsx(B.Password,{placeholder:"请输入密码",onChange:e=>re(e.target.value)})}),le&&e.jsx("div",{style:{marginTop:-16,marginBottom:16},children:e.jsx(_,{password:le,showDetails:!0})})]}),e.jsx(S.Item,{name:"status",label:"状态",valuePropName:"checked",getValueFromEvent:e=>e?"active":"inactive",getValueProps:e=>({checked:"active"===e}),children:e.jsx(M,{checkedChildren:"启用",unCheckedChildren:"禁用"})})]})}),e.jsx(J,{visible:X,user:se,onClose:()=>{ee(!1),te(null)}}),i]})};export{G as default};
