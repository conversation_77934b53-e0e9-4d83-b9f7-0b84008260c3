import{j as s}from"./index-Cu_U9Dm3.js";import{r as e,F as t,ae as a,a as l,Z as i,Q as c,s as r}from"./antd-DYv0PFJq.js";import{u as n}from"./userService-kUY0fGJp.js";import"./vendor-D2RBMdQ0.js";const d=()=>{const[d,o]=e.useState([]),[h,m]=e.useState(!1);return s.jsxs("div",{style:{padding:24},children:[s.jsx(t,{title:"用户服务测试",style:{marginBottom:24},children:s.jsxs(a,{children:[s.jsx(l,{type:"primary",onClick:async()=>{m(!0);const s=[];try{console.log("测试1: 获取用户列表");const t=await n.getUsers();s.push({test:"获取用户列表",success:!0,data:`获取到 ${t.length} 个用户`,details:t.map(s=>`${s.realName}(${s.username})`).join(", ")}),console.log("测试2: 创建新用户");try{const e=await n.createUser({username:"test001",realName:"测试用户",email:"<EMAIL>",phone:"13800138999",role:"teacher",department:"测试部门",password:"123456",status:"active"});s.push({test:"创建新用户",success:!0,data:`成功创建用户: ${e.realName}`,details:`ID: ${e.id}, 用户名: ${e.username}`})}catch(e){s.push({test:"创建新用户",success:!1,data:e.message,details:"创建用户失败"})}console.log("测试3: 登录验证");try{const e=await n.validateLogin("admin","123456");e?s.push({test:"登录验证 (admin/123456)",success:!0,data:`登录成功: ${e.realName}`,details:`角色: ${e.role}, 权限数: ${e.permissions.length}`}):s.push({test:"登录验证 (admin/123456)",success:!1,data:"登录失败",details:"用户名或密码错误"})}catch(e){s.push({test:"登录验证 (admin/123456)",success:!1,data:e.message,details:"登录验证异常"})}console.log("测试4: 错误密码登录");try{const e=await n.validateLogin("admin","wrongpassword");s.push({test:"错误密码登录测试",success:!e,data:e?"应该登录失败但成功了":"正确拒绝了错误密码",details:"安全验证正常"})}catch(e){s.push({test:"错误密码登录测试",success:!0,data:"正确抛出异常",details:e.message})}console.log("测试5: 获取用户统计");const a=await n.getUserStats();s.push({test:"获取用户统计",success:!0,data:`总用户: ${a.total}, 活跃: ${a.active}`,details:`角色分布: ${JSON.stringify(a.roleStats)}`})}catch(e){s.push({test:"测试执行",success:!1,data:e.message,details:"测试执行过程中发生错误"})}o(s),m(!1),r.success("测试完成")},loading:h,children:"运行测试"}),s.jsx(l,{onClick:()=>{o([])},children:"清空结果"})]})}),d.length>0&&s.jsx(t,{title:"测试结果",children:d.map((e,l)=>s.jsx(t,{size:"small",style:{marginBottom:16},title:s.jsxs(a,{children:[s.jsx("span",{children:e.test}),s.jsx(c,{color:e.success?"success":"error",children:e.success?"成功":"失败"})]}),children:s.jsxs(i,{column:1,size:"small",children:[s.jsx(i.Item,{label:"结果",children:e.data}),s.jsx(i.Item,{label:"详情",children:e.details})]})},l))}),s.jsx(t,{title:"测试说明",style:{marginTop:24},children:s.jsxs("div",{children:[s.jsx("h4",{children:"测试内容："}),s.jsxs("ul",{children:[s.jsx("li",{children:"1. 获取用户列表 - 验证用户数据加载"}),s.jsx("li",{children:"2. 创建新用户 - 验证用户创建功能"}),s.jsx("li",{children:"3. 正确密码登录 - 验证登录验证功能"}),s.jsx("li",{children:"4. 错误密码登录 - 验证安全性"}),s.jsx("li",{children:"5. 获取用户统计 - 验证统计功能"})]}),s.jsx("h4",{children:"默认测试账户："}),s.jsxs("ul",{children:[s.jsxs("li",{children:[s.jsx("strong",{children:"管理员："})," admin / 123456"]}),s.jsxs("li",{children:[s.jsx("strong",{children:"辅导员："})," counselor001 / 123456"]}),s.jsxs("li",{children:[s.jsx("strong",{children:"教师："})," teacher001 / 123456"]}),s.jsxs("li",{children:[s.jsx("strong",{children:"党建专员："})," party001 / 123456"]}),s.jsxs("li",{children:[s.jsx("strong",{children:"院长："})," dean001 / 123456"]})]})]})})]})};export{d as default};
