import{r}from"./antd-lXsGnH6e-1753160720439.js";const e=e=>{const t=(()=>{try{const r=localStorage.getItem("user");return r?JSON.parse(r):null}catch{return null}})(),s=(()=>{try{const r=localStorage.getItem("permissions");return r?JSON.parse(r):[]}catch{return[]}})();return r.useCallback(()=>!!t&&(!("admin"!==t.role&&!s.includes("*"))||(Array.isArray(e)?e.some(r=>s.includes(r)):s.includes(e))),[t,s,e])()};export{e as u};
