var t,e,s,i,n,a,r,o,l,h,c,d,u,p,g,f,m,v,w,b,A,_,y,x,S,k,M,E,C,T,R,P,I,L,D,N,F,W,O,B,$,H,z,G,j,U,V,q,X,Y,K,Q,J,Z,tt,et,st,it,nt,at,rt,ot,lt,ht,ct,dt,ut,pt,gt,ft,mt,vt,wt,bt,At,_t,yt,xt,St,kt,Mt,Et,Ct,Tt,Rt,Pt,It,Lt,Dt,Nt,Ft,Wt,Ot,Bt,$t,Ht,zt,Gt,jt,Ut,Vt,qt,Xt,Yt,Kt,Qt,Jt,Zt,te,ee,se,ie,ne,ae,re,oe,le,he,ce,de,ue,pe,ge,fe,me,ve,we,be,Ae,_e,ye,xe,<PERSON>,ke,<PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,Pe,<PERSON>e,Le,De,Ne,Fe,We,Oe,Be,$e,He,ze,Ge,je,Ue,Ve,qe,Xe,Ye,Ke,Qe,Je,Ze,ts,es,ss,is,ns,as,rs,os,ls,hs,cs,ds,us,ps,gs,fs,ms,vs,ws,bs,As,_s,ys,xs,Ss,ks,Ms,Es,Cs,Ts,Rs,Ps,Is,Ls,Ds,Ns,Fs,Ws,Os,Bs,$s,Hs,zs,Gs,js,Us,Vs,qs,Xs,Ys,Ks,Qs,Js,Zs,ti,ei,si,ii,ni,ai,ri,oi,li,hi,ci,di,ui,pi,gi,fi,mi,vi,wi,bi,Ai,_i,yi,xi,Si,ki,Mi,Ei,Ci,Ti,Ri,Pi,Ii,Li,Di,Ni,Fi,Wi,Oi,Bi,$i,Hi,zi,Gi,ji,Ui,Vi,qi,Xi,Yi,Ki,Qi,Ji,Zi,tn,en,sn,nn,an,rn,on,ln,hn,cn,dn,un,pn,gn,fn,mn,vn,wn,bn,An,_n,yn,xn,Sn,kn,Mn,En,Cn,Tn,Rn,Pn,In,Ln,Dn,Nn,Fn,Wn,On,Bn,$n,Hn,zn,Gn,jn,Un,Vn,qn,Xn,Yn,Kn,Qn,Jn,Zn,ta,ea,sa,ia,na,aa,ra,oa,la,ha,ca,da,ua,pa,ga,fa,ma,va,wa,ba,Aa,_a,ya,xa,Sa,ka,Ma,Ea,Ca,Ta,Ra,Pa,Ia,La,Da,Na,Fa,Wa,Oa,Ba,$a,Ha,za,Ga,ja,Ua,Va,qa,Xa,Ya,Ka,Qa,Ja,Za,tr,er,sr,ir,nr,ar,rr,or,lr,hr,cr,dr,ur,pr,gr,fr,mr,vr,wr,br,Ar,_r,yr,xr,Sr,kr,Mr,Er,Cr,Tr,Rr,Pr,Ir,Lr,Dr,Nr,Fr,Wr,Or,Br,$r,Hr,zr,Gr,jr,Ur,Vr,qr,Xr,Yr,Kr,Qr,Jr,Zr,to,eo,so,io,no,ao,ro,oo,lo,ho,co,uo,po,go,fo,mo,vo,wo,bo,Ao,_o,yo,xo,So,ko,Mo,Eo,Co,To,Ro,Po,Io,Lo,Do,No,Fo,Wo,Oo,Bo,$o,Ho,zo,Go,jo,Uo,Vo,qo,Xo,Yo,Ko,Qo,Jo,Zo,tl,el,sl,il,nl,al,rl,ol,ll,hl,cl,dl,ul,pl,gl,fl,ml,vl,wl,bl,Al,_l,yl,xl,Sl,kl,Ml,El,Cl,Tl,Rl,Pl,Il,Ll,Dl,Nl,Fl,Wl,Ol,Bl,$l,Hl,zl,Gl,jl,Ul,Vl,ql,Xl,Yl,Kl,Ql,Jl,Zl,th,eh,sh,ih,nh,ah,rh,oh,lh,hh,ch,dh,uh,ph,gh,fh,mh,vh,wh,bh,Ah,_h,yh,xh,Sh,kh,Mh,Eh,Ch,Th,Rh,Ph,Ih,Lh,Dh,Nh,Fh,Wh,Oh,Bh,$h,Hh,zh,Gh,jh,Uh,Vh,qh,Xh,Yh,Kh,Qh,Jh,Zh,tc,ec,sc,ic=Object.defineProperty,nc=t=>{throw TypeError(t)},ac=(t,e,s)=>((t,e,s)=>e in t?ic(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s)(t,"symbol"!=typeof e?e+"":e,s),rc=(t,e,s)=>e.has(t)||nc("Cannot "+s),oc=(t,e,s)=>(rc(t,e,"read from private field"),s?s.call(t):e.get(t)),lc=(t,e,s)=>e.has(t)?nc("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,s),hc=(t,e,s,i)=>(rc(t,e,"write to private field"),i?i.call(t,s):e.set(t,s),s),cc=(t,e,s)=>(rc(t,e,"access private method"),s),dc=(t,e,s,i)=>({set _(i){hc(t,e,i,s)},get _(){return oc(t,e,i)}});const uc=!("object"!=typeof process||process+""!="[object process]"||process.versions.nw||process.versions.electron&&process.type&&"browser"!==process.type),pc=[.001,0,0,.001,0,0],gc=1.35,fc=1,mc=2,vc=4,wc=16,bc=32,Ac=64,_c=128,yc=256,xc={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3},Sc="pdfjs_internal_editor_",kc={DISABLE:-1,NONE:0,FREETEXT:3,HIGHLIGHT:9,STAMP:13,INK:15,SIGNATURE:101},Mc={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23,HIGHLIGHT_COLOR:31,HIGHLIGHT_DEFAULT_COLOR:32,HIGHLIGHT_THICKNESS:33,HIGHLIGHT_FREE:34,HIGHLIGHT_SHOW_ALL:35,DRAW_STEP:41},Ec={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048},Cc=0,Tc=1,Rc=2,Pc=3,Ic=3,Lc=4,Dc={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3},Nc={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26},Fc=1,Wc=2,Oc=3,Bc=4,$c=5,Hc={ERRORS:0,WARNINGS:1,INFOS:5},zc={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91,setStrokeTransparent:92,setFillTransparent:93,rawFillPath:94},Gc=0,jc=1,Uc=2,Vc=3,qc={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let Xc=Hc.WARNINGS;function Yc(){return Xc}function Kc(t){Xc>=Hc.INFOS&&console.log(`Info: ${t}`)}function Qc(t){Xc>=Hc.WARNINGS&&console.log(`Warning: ${t}`)}function Jc(t){throw new Error(t)}function Zc(t,e){t||Jc(e)}function td(t,e=null,s=null){if(!t)return null;if(s&&"string"==typeof t){if(s.addDefaultProtocol&&t.startsWith("www.")){const e=t.match(/\./g);(null==e?void 0:e.length)>=2&&(t=`http://${t}`)}if(s.tryConvertEncoding)try{t=decodeURIComponent(escape(t))}catch{}}const i=e?URL.parse(t,e):URL.parse(t);return function(t){switch(null==t?void 0:t.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}(i)?i:null}function ed(t,e,s=!1){const i=URL.parse(t);return i?(i.hash=e,i.href):s&&td(t,"http://example.com")?t.split("#",1)[0]+""+(e?`#${e}`:""):""}function sd(t,e,s,i=!1){return Object.defineProperty(t,e,{value:s,enumerable:!i,configurable:!0,writable:!1}),s}const id=function(){function t(t,e){this.message=t,this.name=e}return t.prototype=new Error,t.constructor=t,t}();class nd extends id{constructor(t,e){super(t,"PasswordException"),this.code=e}}class ad extends id{constructor(t,e){super(t,"UnknownErrorException"),this.details=e}}class rd extends id{constructor(t){super(t,"InvalidPDFException")}}class od extends id{constructor(t,e,s){super(t,"ResponseException"),this.status=e,this.missing=s}}class ld extends id{constructor(t){super(t,"FormatError")}}class hd extends id{constructor(t){super(t,"AbortException")}}function cd(t){"object"==typeof t&&void 0!==(null==t?void 0:t.length)||Jc("Invalid argument for bytesToString");const e=t.length,s=8192;if(e<s)return String.fromCharCode.apply(null,t);const i=[];for(let n=0;n<e;n+=s){const a=Math.min(n+s,e),r=t.subarray(n,a);i.push(String.fromCharCode.apply(null,r))}return i.join("")}function dd(t){"string"!=typeof t&&Jc("Invalid argument for stringToBytes");const e=t.length,s=new Uint8Array(e);for(let i=0;i<e;++i)s[i]=255&t.charCodeAt(i);return s}class ud{static get isLittleEndian(){return sd(this,"isLittleEndian",function(){const t=new Uint8Array(4);return t[0]=1,1===new Uint32Array(t.buffer,0,1)[0]}())}static get isEvalSupported(){return sd(this,"isEvalSupported",function(){try{return new Function(""),!0}catch{return!1}}())}static get isOffscreenCanvasSupported(){return sd(this,"isOffscreenCanvasSupported","undefined"!=typeof OffscreenCanvas)}static get isImageDecoderSupported(){return sd(this,"isImageDecoderSupported","undefined"!=typeof ImageDecoder)}static get platform(){const{platform:t,userAgent:e}=navigator;return sd(this,"platform",{isAndroid:e.includes("Android"),isLinux:t.includes("Linux"),isMac:t.includes("Mac"),isWindows:t.includes("Win"),isFirefox:e.includes("Firefox")})}static get isCSSRoundSupported(){var t,e;return sd(this,"isCSSRoundSupported",null==(e=null==(t=globalThis.CSS)?void 0:t.supports)?void 0:e.call(t,"width: round(1.5px, 1px)"))}}const pd=Array.from(Array(256).keys(),t=>t.toString(16).padStart(2,"0"));class gd{static makeHexColor(t,e,s){return`#${pd[t]}${pd[e]}${pd[s]}`}static scaleMinMax(t,e){let s;t[0]?(t[0]<0&&(s=e[0],e[0]=e[2],e[2]=s),e[0]*=t[0],e[2]*=t[0],t[3]<0&&(s=e[1],e[1]=e[3],e[3]=s),e[1]*=t[3],e[3]*=t[3]):(s=e[0],e[0]=e[1],e[1]=s,s=e[2],e[2]=e[3],e[3]=s,t[1]<0&&(s=e[1],e[1]=e[3],e[3]=s),e[1]*=t[1],e[3]*=t[1],t[2]<0&&(s=e[0],e[0]=e[2],e[2]=s),e[0]*=t[2],e[2]*=t[2]),e[0]+=t[4],e[1]+=t[5],e[2]+=t[4],e[3]+=t[5]}static transform(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]}static applyTransform(t,e,s=0){const i=t[s],n=t[s+1];t[s]=i*e[0]+n*e[2]+e[4],t[s+1]=i*e[1]+n*e[3]+e[5]}static applyTransformToBezier(t,e,s=0){const i=e[0],n=e[1],a=e[2],r=e[3],o=e[4],l=e[5];for(let h=0;h<6;h+=2){const e=t[s+h],c=t[s+h+1];t[s+h]=e*i+c*a+o,t[s+h+1]=e*n+c*r+l}}static applyInverseTransform(t,e){const s=t[0],i=t[1],n=e[0]*e[3]-e[1]*e[2];t[0]=(s*e[3]-i*e[2]+e[2]*e[5]-e[4]*e[3])/n,t[1]=(-s*e[1]+i*e[0]+e[4]*e[1]-e[5]*e[0])/n}static axialAlignedBoundingBox(t,e,s){const i=e[0],n=e[1],a=e[2],r=e[3],o=e[4],l=e[5],h=t[0],c=t[1],d=t[2],u=t[3];let p=i*h+o,g=p,f=i*d+o,m=f,v=r*c+l,w=v,b=r*u+l,A=b;if(0!==n||0!==a){const t=n*h,e=n*d,s=a*c,i=a*u;p+=s,m+=s,f+=i,g+=i,v+=t,A+=t,b+=e,w+=e}s[0]=Math.min(s[0],p,f,g,m),s[1]=Math.min(s[1],v,b,w,A),s[2]=Math.max(s[2],p,f,g,m),s[3]=Math.max(s[3],v,b,w,A)}static inverseTransform(t){const e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[4]*t[3])/e,(t[4]*t[1]-t[5]*t[0])/e]}static singularValueDecompose2dScale(t,e){const s=t[0],i=t[1],n=t[2],a=t[3],r=s**2+i**2,o=s*n+i*a,l=n**2+a**2,h=(r+l)/2,c=Math.sqrt(h**2-(r*l-o**2));e[0]=Math.sqrt(h+c||1),e[1]=Math.sqrt(h-c||1)}static normalizeRect(t){const e=t.slice(0);return t[0]>t[2]&&(e[0]=t[2],e[2]=t[0]),t[1]>t[3]&&(e[1]=t[3],e[3]=t[1]),e}static intersect(t,e){const s=Math.max(Math.min(t[0],t[2]),Math.min(e[0],e[2])),i=Math.min(Math.max(t[0],t[2]),Math.max(e[0],e[2]));if(s>i)return null;const n=Math.max(Math.min(t[1],t[3]),Math.min(e[1],e[3])),a=Math.min(Math.max(t[1],t[3]),Math.max(e[1],e[3]));return n>a?null:[s,n,i,a]}static pointBoundingBox(t,e,s){s[0]=Math.min(s[0],t),s[1]=Math.min(s[1],e),s[2]=Math.max(s[2],t),s[3]=Math.max(s[3],e)}static rectBoundingBox(t,e,s,i,n){n[0]=Math.min(n[0],t,s),n[1]=Math.min(n[1],e,i),n[2]=Math.max(n[2],t,s),n[3]=Math.max(n[3],e,i)}static bezierBoundingBox(e,i,n,a,r,o,l,h,c){c[0]=Math.min(c[0],e,l),c[1]=Math.min(c[1],i,h),c[2]=Math.max(c[2],e,l),c[3]=Math.max(c[3],i,h),cc(this,t,s).call(this,e,n,r,l,i,a,o,h,3*(3*(n-r)-e+l),6*(e-2*n+r),3*(n-e),c),cc(this,t,s).call(this,e,n,r,l,i,a,o,h,3*(3*(a-o)-i+h),6*(i-2*a+o),3*(a-i),c)}}t=new WeakSet,e=function(t,e,s,i,n,a,r,o,l,h){if(l<=0||l>=1)return;const c=1-l,d=l*l,u=d*l,p=c*(c*(c*t+3*l*e)+3*d*s)+u*i,g=c*(c*(c*n+3*l*a)+3*d*r)+u*o;h[0]=Math.min(h[0],p),h[1]=Math.min(h[1],g),h[2]=Math.max(h[2],p),h[3]=Math.max(h[3],g)},s=function(s,i,n,a,r,o,l,h,c,d,u,p){if(Math.abs(c)<1e-12)return void(Math.abs(d)>=1e-12&&cc(this,t,e).call(this,s,i,n,a,r,o,l,h,-u/d,p));const g=d**2-4*u*c;if(g<0)return;const f=Math.sqrt(g),m=2*c;cc(this,t,e).call(this,s,i,n,a,r,o,l,h,(-d+f)/m,p),cc(this,t,e).call(this,s,i,n,a,r,o,l,h,(-d-f)/m,p)},lc(gd,t);let fd=null,md=null;function vd(t){return fd||(fd=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu,md=new Map([["ﬅ","ſt"]])),t.replaceAll(fd,(t,e,s)=>e?e.normalize("NFKC"):md.get(s))}function wd(){if("function"==typeof crypto.randomUUID)return crypto.randomUUID();const t=new Uint8Array(32);return crypto.getRandomValues(t),cd(t)}const bd="pdfjs_internal_id_";function Ad(t,e,s){return Math.min(Math.max(t,e),s)}function _d(t){return Uint8Array.prototype.toBase64?t.toBase64():btoa(cd(t))}"function"!=typeof Promise.try&&(Promise.try=function(t,...e){return new Promise(s=>{s(t(...e))})}),"function"!=typeof Math.sumPrecise&&(Math.sumPrecise=function(t){return t.reduce((t,e)=>t+e,0)});const yd="http://www.w3.org/2000/svg",xd=class{};ac(xd,"CSS",96),ac(xd,"PDF",72),ac(xd,"PDF_TO_CSS_UNITS",xd.CSS/xd.PDF);let Sd=xd;async function kd(t,e="text"){if(Ld(t,document.baseURI)){const s=await fetch(t);if(!s.ok)throw new Error(s.statusText);switch(e){case"arraybuffer":return s.arrayBuffer();case"blob":return s.blob();case"json":return s.json()}return s.text()}return new Promise((s,i)=>{const n=new XMLHttpRequest;n.open("GET",t,!0),n.responseType=e,n.onreadystatechange=()=>{if(n.readyState===XMLHttpRequest.DONE)if(200!==n.status&&0!==n.status)i(new Error(n.statusText));else{switch(e){case"arraybuffer":case"blob":case"json":return void s(n.response)}s(n.responseText)}},n.send(null)})}class Md{constructor({viewBox:t,userUnit:e,scale:s,rotation:i,offsetX:n=0,offsetY:a=0,dontFlip:r=!1}){this.viewBox=t,this.userUnit=e,this.scale=s,this.rotation=i,this.offsetX=n,this.offsetY=a,s*=e;const o=(t[2]+t[0])/2,l=(t[3]+t[1])/2;let h,c,d,u,p,g,f,m;switch((i%=360)<0&&(i+=360),i){case 180:h=-1,c=0,d=0,u=1;break;case 90:h=0,c=1,d=1,u=0;break;case 270:h=0,c=-1,d=-1,u=0;break;case 0:h=1,c=0,d=0,u=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}r&&(d=-d,u=-u),0===h?(p=Math.abs(l-t[1])*s+n,g=Math.abs(o-t[0])*s+a,f=(t[3]-t[1])*s,m=(t[2]-t[0])*s):(p=Math.abs(o-t[0])*s+n,g=Math.abs(l-t[1])*s+a,f=(t[2]-t[0])*s,m=(t[3]-t[1])*s),this.transform=[h*s,c*s,d*s,u*s,p-h*s*o-d*s*l,g-c*s*o-u*s*l],this.width=f,this.height=m}get rawDims(){const t=this.viewBox;return sd(this,"rawDims",{pageWidth:t[2]-t[0],pageHeight:t[3]-t[1],pageX:t[0],pageY:t[1]})}clone({scale:t=this.scale,rotation:e=this.rotation,offsetX:s=this.offsetX,offsetY:i=this.offsetY,dontFlip:n=!1}={}){return new Md({viewBox:this.viewBox.slice(),userUnit:this.userUnit,scale:t,rotation:e,offsetX:s,offsetY:i,dontFlip:n})}convertToViewportPoint(t,e){const s=[t,e];return gd.applyTransform(s,this.transform),s}convertToViewportRectangle(t){const e=[t[0],t[1]];gd.applyTransform(e,this.transform);const s=[t[2],t[3]];return gd.applyTransform(s,this.transform),[e[0],e[1],s[0],s[1]]}convertToPdfPoint(t,e){const s=[t,e];return gd.applyInverseTransform(s,this.transform),s}}class Ed extends id{constructor(t,e=0){super(t,"RenderingCancelledException"),this.extraDelay=e}}function Cd(t){const e=t.length;let s=0;for(;s<e&&""===t[s].trim();)s++;return"data:"===t.substring(s,s+5).toLowerCase()}function Td(t){return"string"==typeof t&&/\.pdf$/i.test(t)}function Rd(t){return[t]=t.split(/[#?]/,1),t.substring(t.lastIndexOf("/")+1)}function Pd(t,e="document.pdf"){if("string"!=typeof t)return e;if(Cd(t))return Qc('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.'),e;const s=(t=>{try{return new URL(t)}catch{try{return new URL(decodeURIComponent(t))}catch{try{return new URL(t,"https://foo.bar")}catch{try{return new URL(decodeURIComponent(t),"https://foo.bar")}catch{return null}}}}})(t);if(!s)return e;const i=t=>{try{let e=decodeURIComponent(t);return e.includes("/")?(e=e.split("/").at(-1),e.test(/^\.pdf$/i)?e:t):e}catch{return t}},n=/\.pdf$/i,a=s.pathname.split("/").at(-1);if(n.test(a))return i(a);if(s.searchParams.size>0){const t=Array.from(s.searchParams.values()).reverse();for(const s of t)if(n.test(s))return i(s);const e=Array.from(s.searchParams.keys()).reverse();for(const s of e)if(n.test(s))return i(s)}if(s.hash){const t=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i.exec(s.hash);if(t)return i(t[0])}return e}class Id{constructor(){ac(this,"started",Object.create(null)),ac(this,"times",[])}time(t){t in this.started&&Qc(`Timer is already running for ${t}`),this.started[t]=Date.now()}timeEnd(t){t in this.started||Qc(`Timer has not been started for ${t}`),this.times.push({name:t,start:this.started[t],end:Date.now()}),delete this.started[t]}toString(){const t=[];let e=0;for(const{name:s}of this.times)e=Math.max(s.length,e);for(const{name:s,start:i,end:n}of this.times)t.push(`${s.padEnd(e)} ${n-i}ms\n`);return t.join("")}}function Ld(t,e){const s=e?URL.parse(t,e):URL.parse(t);return"http:"===(null==s?void 0:s.protocol)||"https:"===(null==s?void 0:s.protocol)}function Dd(t){t.preventDefault()}function Nd(t){t.preventDefault(),t.stopPropagation()}class Fd{static toDateObject(t){if(!t||"string"!=typeof t)return null;oc(this,i)||hc(this,i,new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?"));const e=oc(this,i).exec(t);if(!e)return null;const s=parseInt(e[1],10);let n=parseInt(e[2],10);n=n>=1&&n<=12?n-1:0;let a=parseInt(e[3],10);a=a>=1&&a<=31?a:1;let r=parseInt(e[4],10);r=r>=0&&r<=23?r:0;let o=parseInt(e[5],10);o=o>=0&&o<=59?o:0;let l=parseInt(e[6],10);l=l>=0&&l<=59?l:0;const h=e[7]||"Z";let c=parseInt(e[8],10);c=c>=0&&c<=23?c:0;let d=parseInt(e[9],10)||0;return d=d>=0&&d<=59?d:0,"-"===h?(r+=c,o+=d):"+"===h&&(r-=c,o-=d),new Date(Date.UTC(s,n,a,r,o,l))}}function Wd(t,{scale:e=1,rotation:s=0}){const{width:i,height:n}=t.attributes.style,a=[0,0,parseInt(i),parseInt(n)];return new Md({viewBox:a,userUnit:1,scale:e,rotation:s})}function Od(t){if(t.startsWith("#")){const e=parseInt(t.slice(1),16);return[(16711680&e)>>16,(65280&e)>>8,255&e]}return t.startsWith("rgb(")?t.slice(4,-1).split(",").map(t=>parseInt(t)):t.startsWith("rgba(")?t.slice(5,-1).split(",").map(t=>parseInt(t)).slice(0,3):(Qc(`Not a valid color format: "${t}"`),[0,0,0])}function Bd(t){const{a:e,b:s,c:i,d:n,e:a,f:r}=t.getTransform();return[e,s,i,n,a,r]}function $d(t){const{a:e,b:s,c:i,d:n,e:a,f:r}=t.getTransform().invertSelf();return[e,s,i,n,a,r]}function Hd(t,e,s=!1,i=!0){if(e instanceof Md){const{pageWidth:i,pageHeight:n}=e.rawDims,{style:a}=t,r=ud.isCSSRoundSupported,o=`var(--total-scale-factor) * ${i}px`,l=`var(--total-scale-factor) * ${n}px`,h=r?`round(down, ${o}, var(--scale-round-x))`:`calc(${o})`,c=r?`round(down, ${l}, var(--scale-round-y))`:`calc(${l})`;s&&e.rotation%180!=0?(a.width=c,a.height=h):(a.width=h,a.height=c)}i&&t.setAttribute("data-main-rotation",e.rotation)}i=new WeakMap,lc(Fd,i);class zd{constructor(){const{pixelRatio:t}=zd;this.sx=t,this.sy=t}get scaled(){return 1!==this.sx||1!==this.sy}get symmetric(){return this.sx===this.sy}limitCanvas(t,e,s,i,n=-1){let a=1/0,r=1/0,o=1/0;(s=zd.capPixels(s,n))>0&&(a=Math.sqrt(s/(t*e))),-1!==i&&(r=i/t,o=i/e);const l=Math.min(a,r,o);return(this.sx>l||this.sy>l)&&(this.sx=l,this.sy=l,!0)}static get pixelRatio(){return globalThis.devicePixelRatio||1}static capPixels(t,e){if(e>=0){const s=Math.ceil(window.screen.availWidth*window.screen.availHeight*this.pixelRatio**2*(1+e/100));return t>0?Math.min(t,s):s}return t}}const Gd=["image/apng","image/avif","image/bmp","image/gif","image/jpeg","image/png","image/svg+xml","image/webp","image/x-icon"],jd=class t{constructor(e){lc(this,p),lc(this,n,null),lc(this,a,null),lc(this,r),lc(this,o,null),lc(this,l,null),lc(this,h,null),hc(this,r,e),oc(t,c)||hc(t,c,Object.freeze({freetext:"pdfjs-editor-remove-freetext-button",highlight:"pdfjs-editor-remove-highlight-button",ink:"pdfjs-editor-remove-ink-button",stamp:"pdfjs-editor-remove-stamp-button",signature:"pdfjs-editor-remove-signature-button"}))}render(){const e=hc(this,n,document.createElement("div"));e.classList.add("editToolbar","hidden"),e.setAttribute("role","toolbar");const s=oc(this,r)._uiManager._signal;e.addEventListener("contextmenu",Dd,{signal:s}),e.addEventListener("pointerdown",cc(t,d,u),{signal:s});const i=hc(this,o,document.createElement("div"));i.className="buttons",e.append(i);const a=oc(this,r).toolbarPosition;if(a){const{style:t}=e,s="ltr"===oc(this,r)._uiManager.direction?1-a[0]:a[0];t.insetInlineEnd=100*s+"%",t.top=`calc(${100*a[1]}% + var(--editor-toolbar-vert-offset))`}return e}get div(){return oc(this,n)}hide(){var t;oc(this,n).classList.add("hidden"),null==(t=oc(this,a))||t.hideDropdown()}show(){var t;oc(this,n).classList.remove("hidden"),null==(t=oc(this,l))||t.shown()}addDeleteButton(){const{editorType:e,_uiManager:s}=oc(this,r),i=document.createElement("button");i.className="delete",i.tabIndex=0,i.setAttribute("data-l10n-id",oc(t,c)[e]),cc(this,p,m).call(this,i),i.addEventListener("click",t=>{s.delete()},{signal:s._signal}),oc(this,o).append(i)}async addAltText(t){const e=await t.render();cc(this,p,m).call(this,e),oc(this,o).append(e,oc(this,p,v)),hc(this,l,t)}addColorPicker(t){hc(this,a,t);const e=t.renderButton();cc(this,p,m).call(this,e),oc(this,o).append(e,oc(this,p,v))}async addEditSignatureButton(t){const e=hc(this,h,await t.renderEditButton(oc(this,r)));cc(this,p,m).call(this,e),oc(this,o).append(e,oc(this,p,v))}async addButton(t,e){switch(t){case"colorPicker":this.addColorPicker(e);break;case"altText":await this.addAltText(e);break;case"editSignature":await this.addEditSignatureButton(e);break;case"delete":this.addDeleteButton()}}updateEditSignatureButton(t){oc(this,h)&&(oc(this,h).title=t)}remove(){var t;oc(this,n).remove(),null==(t=oc(this,a))||t.destroy(),hc(this,a,null)}};n=new WeakMap,a=new WeakMap,r=new WeakMap,o=new WeakMap,l=new WeakMap,h=new WeakMap,c=new WeakMap,d=new WeakSet,u=function(t){t.stopPropagation()},p=new WeakSet,g=function(t){oc(this,r)._focusEventsAllowed=!1,Nd(t)},f=function(t){oc(this,r)._focusEventsAllowed=!0,Nd(t)},m=function(t){const e=oc(this,r)._uiManager._signal;t.addEventListener("focusin",cc(this,p,g).bind(this),{capture:!0,signal:e}),t.addEventListener("focusout",cc(this,p,f).bind(this),{capture:!0,signal:e}),t.addEventListener("contextmenu",Dd,{signal:e})},v=function(){const t=document.createElement("div");return t.className="divider",t},lc(jd,d),lc(jd,c,null);let Ud=jd;class Vd{constructor(t){lc(this,_),lc(this,w,null),lc(this,b,null),lc(this,A),hc(this,A,t)}show(t,e,s){const[i,n]=cc(this,_,x).call(this,e,s),{style:a}=oc(this,b)||hc(this,b,cc(this,_,y).call(this));t.append(oc(this,b)),a.insetInlineEnd=100*i+"%",a.top=`calc(${100*n}% + var(--editor-toolbar-vert-offset))`}hide(){oc(this,b).remove()}}function qd(t,e,s){for(const i of s)e.addEventListener(i,t[i].bind(t))}w=new WeakMap,b=new WeakMap,A=new WeakMap,_=new WeakSet,y=function(){const t=hc(this,b,document.createElement("div"));t.className="editToolbar",t.setAttribute("role","toolbar"),t.addEventListener("contextmenu",Dd,{signal:oc(this,A)._signal});const e=hc(this,w,document.createElement("div"));return e.className="buttons",t.append(e),cc(this,_,S).call(this),t},x=function(t,e){let s=0,i=0;for(const n of t){const t=n.y+n.height;if(t<s)continue;const a=n.x+(e?n.width:0);t>s?(i=a,s=t):e?a>i&&(i=a):a<i&&(i=a)}return[e?1-i:i,s]},S=function(){const t=document.createElement("button");t.className="highlightButton",t.tabIndex=0,t.setAttribute("data-l10n-id","pdfjs-highlight-floating-button1");const e=document.createElement("span");t.append(e),e.className="visuallyHidden",e.setAttribute("data-l10n-id","pdfjs-highlight-floating-button-label");const s=oc(this,A)._signal;t.addEventListener("contextmenu",Dd,{signal:s}),t.addEventListener("click",()=>{oc(this,A).highlightSelection("floating_button")},{signal:s}),oc(this,w).append(t)};class Xd{constructor(){lc(this,k,0)}get id(){return`${Sc}${dc(this,k)._++}`}}k=new WeakMap;const Yd=class{constructor(){lc(this,T),lc(this,M,wd()),lc(this,E,0),lc(this,C,null)}static get _isSVGFittingCanvas(){const t=new OffscreenCanvas(1,3).getContext("2d",{willReadFrequently:!0}),e=new Image;e.src='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>';return sd(this,"_isSVGFittingCanvas",e.decode().then(()=>(t.drawImage(e,0,0,1,1,0,0,1,3),0===new Uint32Array(t.getImageData(0,0,1,1).data.buffer)[0])))}async getFromFile(t){const{lastModified:e,name:s,size:i,type:n}=t;return cc(this,T,R).call(this,`${e}_${s}_${i}_${n}`,t)}async getFromUrl(t){return cc(this,T,R).call(this,t,t)}async getFromBlob(t,e){const s=await e;return cc(this,T,R).call(this,t,s)}async getFromId(t){oc(this,C)||hc(this,C,new Map);const e=oc(this,C).get(t);if(!e)return null;if(e.bitmap)return e.refCounter+=1,e;if(e.file)return this.getFromFile(e.file);if(e.blobPromise){const{blobPromise:t}=e;return delete e.blobPromise,this.getFromBlob(e.id,t)}return this.getFromUrl(e.url)}getFromCanvas(t,e){oc(this,C)||hc(this,C,new Map);let s=oc(this,C).get(t);if(null==s?void 0:s.bitmap)return s.refCounter+=1,s;const i=new OffscreenCanvas(e.width,e.height);return i.getContext("2d").drawImage(e,0,0),s={bitmap:i.transferToImageBitmap(),id:`image_${oc(this,M)}_${dc(this,E)._++}`,refCounter:1,isSvg:!1},oc(this,C).set(t,s),oc(this,C).set(s.id,s),s}getSvgUrl(t){const e=oc(this,C).get(t);return(null==e?void 0:e.isSvg)?e.svgUrl:null}deleteId(t){var e;oc(this,C)||hc(this,C,new Map);const s=oc(this,C).get(t);if(!s)return;if(s.refCounter-=1,0!==s.refCounter)return;const{bitmap:i}=s;if(!s.url&&!s.file){const t=new OffscreenCanvas(i.width,i.height);t.getContext("bitmaprenderer").transferFromImageBitmap(i),s.blobPromise=t.convertToBlob()}null==(e=i.close)||e.call(i),s.bitmap=null}isValidId(t){return t.startsWith(`image_${oc(this,M)}_`)}};M=new WeakMap,E=new WeakMap,C=new WeakMap,T=new WeakSet,R=async function(t,e){oc(this,C)||hc(this,C,new Map);let s=oc(this,C).get(t);if(null===s)return null;if(null==s?void 0:s.bitmap)return s.refCounter+=1,s;try{let t;if(s||(s={bitmap:null,id:`image_${oc(this,M)}_${dc(this,E)._++}`,refCounter:0,isSvg:!1}),"string"==typeof e?(s.url=e,t=await kd(e,"blob")):e instanceof File?t=s.file=e:e instanceof Blob&&(t=e),"image/svg+xml"===t.type){const e=Yd._isSVGFittingCanvas,i=new FileReader,n=new Image,a=new Promise((t,a)=>{n.onload=()=>{s.bitmap=n,s.isSvg=!0,t()},i.onload=async()=>{const t=s.svgUrl=i.result;n.src=await e?`${t}#svgView(preserveAspectRatio(none))`:t},n.onerror=i.onerror=a});i.readAsDataURL(t),await a}else s.bitmap=await createImageBitmap(t);s.refCounter=1}catch(i){Qc(i),s=null}return oc(this,C).set(t,s),s&&oc(this,C).set(s.id,s),s};let Kd=Yd;class Qd{constructor(t=128){lc(this,P,[]),lc(this,I,!1),lc(this,L),lc(this,D,-1),hc(this,L,t)}add({cmd:t,undo:e,post:s,mustExec:i,type:n=NaN,overwriteIfSameType:a=!1,keepUndo:r=!1}){if(i&&t(),oc(this,I))return;const o={cmd:t,undo:e,post:s,type:n};if(-1===oc(this,D))return oc(this,P).length>0&&(oc(this,P).length=0),hc(this,D,0),void oc(this,P).push(o);if(a&&oc(this,P)[oc(this,D)].type===n)return r&&(o.undo=oc(this,P)[oc(this,D)].undo),void(oc(this,P)[oc(this,D)]=o);const l=oc(this,D)+1;l===oc(this,L)?oc(this,P).splice(0,1):(hc(this,D,l),l<oc(this,P).length&&oc(this,P).splice(l)),oc(this,P).push(o)}undo(){if(-1===oc(this,D))return;hc(this,I,!0);const{undo:t,post:e}=oc(this,P)[oc(this,D)];t(),null==e||e(),hc(this,I,!1),hc(this,D,oc(this,D)-1)}redo(){if(oc(this,D)<oc(this,P).length-1){hc(this,D,oc(this,D)+1),hc(this,I,!0);const{cmd:t,post:e}=oc(this,P)[oc(this,D)];t(),null==e||e(),hc(this,I,!1)}}hasSomethingToUndo(){return-1!==oc(this,D)}hasSomethingToRedo(){return oc(this,D)<oc(this,P).length-1}cleanType(t){if(-1!==oc(this,D)){for(let e=oc(this,D);e>=0;e--)if(oc(this,P)[e].type!==t)return oc(this,P).splice(e+1,oc(this,D)-e),void hc(this,D,e);oc(this,P).length=0,hc(this,D,-1)}}destroy(){hc(this,P,null)}}P=new WeakMap,I=new WeakMap,L=new WeakMap,D=new WeakMap;class Jd{constructor(t){lc(this,N),this.buffer=[],this.callbacks=new Map,this.allKeys=new Set;const{isMac:e}=ud.platform;for(const[s,i,n={}]of t)for(const t of s){const s=t.startsWith("mac+");e&&s?(this.callbacks.set(t.slice(4),{callback:i,options:n}),this.allKeys.add(t.split("+").at(-1))):e||s||(this.callbacks.set(t,{callback:i,options:n}),this.allKeys.add(t.split("+").at(-1)))}}exec(t,e){if(!this.allKeys.has(e.key))return;const s=this.callbacks.get(cc(this,N,F).call(this,e));if(!s)return;const{callback:i,options:{bubbles:n=!1,args:a=[],checker:r=null}}=s;r&&!r(t,e)||(i.bind(t,...a,e)(),n||Nd(e))}}N=new WeakSet,F=function(t){t.altKey&&this.buffer.push("alt"),t.ctrlKey&&this.buffer.push("ctrl"),t.metaKey&&this.buffer.push("meta"),t.shiftKey&&this.buffer.push("shift"),this.buffer.push(t.key);const e=this.buffer.join("+");return this.buffer.length=0,e};const Zd=class t{get _colors(){const t=new Map([["CanvasText",null],["Canvas",null]]);return function(t){const e=document.createElement("span");e.style.visibility="hidden",e.style.colorScheme="only light",document.body.append(e);for(const s of t.keys()){e.style.color=s;const i=window.getComputedStyle(e).color;t.set(s,Od(i))}e.remove()}(t),sd(this,"_colors",t)}convert(e){const s=Od(e);if(!window.matchMedia("(forced-colors: active)").matches)return s;for(const[i,n]of this._colors)if(n.every((t,e)=>t===s[e]))return t._colorsMapping.get(i);return s}getHexCode(t){const e=this._colors.get(t);return e?gd.makeHexColor(...e):t}};ac(Zd,"_colorsMapping",new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]));let tu=Zd;const eu=class t{constructor(t,e,s,i,n,a,r,o,l,h,c,d,u,p,g){lc(this,Ct),lc(this,W,new AbortController),lc(this,O,null),lc(this,B,new Map),lc(this,$,new Map),lc(this,H,null),lc(this,z,null),lc(this,G,null),lc(this,j,new Qd),lc(this,U,null),lc(this,V,null),lc(this,q,0),lc(this,X,new Set),lc(this,Y,null),lc(this,K,null),lc(this,Q,new Set),ac(this,"_editorUndoBar",null),lc(this,J,!1),lc(this,Z,!1),lc(this,tt,!1),lc(this,et,null),lc(this,st,null),lc(this,it,null),lc(this,nt,null),lc(this,at,!1),lc(this,rt,null),lc(this,ot,new Xd),lc(this,lt,!1),lc(this,ht,!1),lc(this,ct,null),lc(this,dt,null),lc(this,ut,null),lc(this,pt,null),lc(this,gt,null),lc(this,ft,kc.NONE),lc(this,mt,new Set),lc(this,vt,null),lc(this,wt,null),lc(this,bt,null),lc(this,At,null),lc(this,_t,{isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1,hasSelectedText:!1}),lc(this,yt,[0,0]),lc(this,xt,null),lc(this,St,null),lc(this,kt,null),lc(this,Mt,null),lc(this,Et,null);const f=this._signal=oc(this,W).signal;hc(this,St,t),hc(this,kt,e),hc(this,Mt,s),hc(this,H,i),hc(this,wt,n),this._eventBus=a,a._on("editingaction",this.onEditingAction.bind(this),{signal:f}),a._on("pagechanging",this.onPageChanging.bind(this),{signal:f}),a._on("scalechanging",this.onScaleChanging.bind(this),{signal:f}),a._on("rotationchanging",this.onRotationChanging.bind(this),{signal:f}),a._on("setpreference",this.onSetPreference.bind(this),{signal:f}),a._on("switchannotationeditorparams",t=>this.updateParams(t.type,t.value),{signal:f}),cc(this,Ct,Dt).call(this),cc(this,Ct,Ht).call(this),cc(this,Ct,Wt).call(this),hc(this,z,r.annotationStorage),hc(this,et,r.filterFactory),hc(this,bt,o),hc(this,nt,l||null),hc(this,J,h),hc(this,Z,c),hc(this,tt,d),hc(this,gt,u||null),this.viewParameters={realScale:Sd.PDF_TO_CSS_UNITS,rotation:0},this.isShiftKeyDown=!1,this._editorUndoBar=p||null,this._supportsPinchToZoom=!1!==g}static get _keyboardManager(){const e=t.prototype,s=t=>oc(t,St).contains(document.activeElement)&&"BUTTON"!==document.activeElement.tagName&&t.hasSomethingToControl(),i=(t,{target:e})=>{if(e instanceof HTMLInputElement){const{type:t}=e;return"text"!==t&&"number"!==t}return!0},n=this.TRANSLATE_SMALL,a=this.TRANSLATE_BIG;return sd(this,"_keyboardManager",new Jd([[["ctrl+a","mac+meta+a"],e.selectAll,{checker:i}],[["ctrl+z","mac+meta+z"],e.undo,{checker:i}],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],e.redo,{checker:i}],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],e.delete,{checker:i}],[["Enter","mac+Enter"],e.addNewEditorFromKeyboard,{checker:(t,{target:e})=>!(e instanceof HTMLButtonElement)&&oc(t,St).contains(e)&&!t.isEnterHandled}],[[" ","mac+ "],e.addNewEditorFromKeyboard,{checker:(t,{target:e})=>!(e instanceof HTMLButtonElement)&&oc(t,St).contains(document.activeElement)}],[["Escape","mac+Escape"],e.unselectAll],[["ArrowLeft","mac+ArrowLeft"],e.translateSelectedEditors,{args:[-n,0],checker:s}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],e.translateSelectedEditors,{args:[-a,0],checker:s}],[["ArrowRight","mac+ArrowRight"],e.translateSelectedEditors,{args:[n,0],checker:s}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],e.translateSelectedEditors,{args:[a,0],checker:s}],[["ArrowUp","mac+ArrowUp"],e.translateSelectedEditors,{args:[0,-n],checker:s}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],e.translateSelectedEditors,{args:[0,-a],checker:s}],[["ArrowDown","mac+ArrowDown"],e.translateSelectedEditors,{args:[0,n],checker:s}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],e.translateSelectedEditors,{args:[0,a],checker:s}]]))}destroy(){var t,e,s,i,n,a,r,o;null==(t=oc(this,Et))||t.resolve(),hc(this,Et,null),null==(e=oc(this,W))||e.abort(),hc(this,W,null),this._signal=null;for(const l of oc(this,$).values())l.destroy();oc(this,$).clear(),oc(this,B).clear(),oc(this,Q).clear(),null==(s=oc(this,pt))||s.clear(),hc(this,O,null),oc(this,mt).clear(),oc(this,j).destroy(),null==(i=oc(this,H))||i.destroy(),null==(n=oc(this,wt))||n.destroy(),null==(a=oc(this,rt))||a.hide(),hc(this,rt,null),null==(r=oc(this,ut))||r.destroy(),hc(this,ut,null),oc(this,st)&&(clearTimeout(oc(this,st)),hc(this,st,null)),oc(this,xt)&&(clearTimeout(oc(this,xt)),hc(this,xt,null)),null==(o=this._editorUndoBar)||o.destroy()}combinedSignal(t){return AbortSignal.any([this._signal,t.signal])}get mlManager(){return oc(this,gt)}get useNewAltTextFlow(){return oc(this,Z)}get useNewAltTextWhenAddingImage(){return oc(this,tt)}get hcmFilter(){return sd(this,"hcmFilter",oc(this,bt)?oc(this,et).addHCMFilter(oc(this,bt).foreground,oc(this,bt).background):"none")}get direction(){return sd(this,"direction",getComputedStyle(oc(this,St)).direction)}get highlightColors(){return sd(this,"highlightColors",oc(this,nt)?new Map(oc(this,nt).split(",").map(t=>((t=t.split("=").map(t=>t.trim()))[1]=t[1].toUpperCase(),t))):null)}get highlightColorNames(){return sd(this,"highlightColorNames",this.highlightColors?new Map(Array.from(this.highlightColors,t=>t.reverse())):null)}setCurrentDrawingSession(t){t?(this.unselectAll(),this.disableUserSelect(!0)):this.disableUserSelect(!1),hc(this,V,t)}setMainHighlightColorPicker(t){hc(this,ut,t)}editAltText(t,e=!1){var s;null==(s=oc(this,H))||s.editAltText(this,t,e)}getSignature(t){var e;null==(e=oc(this,wt))||e.getSignature({uiManager:this,editor:t})}get signatureManager(){return oc(this,wt)}switchToMode(t,e){this._eventBus.on("annotationeditormodechanged",e,{once:!0,signal:this._signal}),this._eventBus.dispatch("showannotationeditorui",{source:this,mode:t})}setPreference(t,e){this._eventBus.dispatch("setpreference",{source:this,name:t,value:e})}onSetPreference({name:t,value:e}){if("enableNewAltTextWhenAddingImage"===t)hc(this,tt,e)}onPageChanging({pageNumber:t}){hc(this,q,t-1)}focusMainContainer(){oc(this,St).focus()}findParent(t,e){for(const s of oc(this,$).values()){const{x:i,y:n,width:a,height:r}=s.div.getBoundingClientRect();if(t>=i&&t<=i+a&&e>=n&&e<=n+r)return s}return null}disableUserSelect(t=!1){oc(this,kt).classList.toggle("noUserSelect",t)}addShouldRescale(t){oc(this,Q).add(t)}removeShouldRescale(t){oc(this,Q).delete(t)}onScaleChanging({scale:t}){var e;this.commitOrRemove(),this.viewParameters.realScale=t*Sd.PDF_TO_CSS_UNITS;for(const s of oc(this,Q))s.onScaleChanging();null==(e=oc(this,V))||e.onScaleChanging()}onRotationChanging({pagesRotation:t}){this.commitOrRemove(),this.viewParameters.rotation=t}highlightSelection(t=""){const e=document.getSelection();if(!e||e.isCollapsed)return;const{anchorNode:s,anchorOffset:i,focusNode:n,focusOffset:a}=e,r=e.toString(),o=cc(this,Ct,Tt).call(this,e).closest(".textLayer"),l=this.getSelectionBoxes(o);if(!l)return;e.empty();const h=cc(this,Ct,Rt).call(this,o),c=oc(this,ft)===kc.NONE,d=()=>{null==h||h.createAndAddNewEditor({x:0,y:0},!1,{methodOfCreation:t,boxes:l,anchorNode:s,anchorOffset:i,focusNode:n,focusOffset:a,text:r}),c&&this.showAllEditors("highlight",!0,!0)};c?this.switchToMode(kc.HIGHLIGHT,d):d()}addToAnnotationStorage(t){t.isEmpty()||!oc(this,z)||oc(this,z).has(t.id)||oc(this,z).setValue(t.id,t)}a11yAlert(t,e=null){const s=oc(this,Mt);s&&(s.setAttribute("data-l10n-id",t),e?s.setAttribute("data-l10n-args",JSON.stringify(e)):s.removeAttribute("data-l10n-args"))}blur(){if(this.isShiftKeyDown=!1,oc(this,at)&&(hc(this,at,!1),cc(this,Ct,Lt).call(this,"main_toolbar")),!this.hasSelection)return;const{activeElement:t}=document;for(const e of oc(this,mt))if(e.div.contains(t)){hc(this,dt,[e,t]),e._focusEventsAllowed=!1;break}}focus(){if(!oc(this,dt))return;const[t,e]=oc(this,dt);hc(this,dt,null),e.addEventListener("focusin",()=>{t._focusEventsAllowed=!0},{once:!0,signal:this._signal}),e.focus()}addEditListeners(){cc(this,Ct,Wt).call(this),cc(this,Ct,Bt).call(this)}removeEditListeners(){cc(this,Ct,Ot).call(this),cc(this,Ct,$t).call(this)}dragOver(t){for(const{type:e}of t.dataTransfer.items)for(const s of oc(this,K))if(s.isHandlingMimeForPasting(e))return t.dataTransfer.dropEffect="copy",void t.preventDefault()}drop(t){for(const e of t.dataTransfer.items)for(const s of oc(this,K))if(s.isHandlingMimeForPasting(e.type))return s.paste(e,this.currentLayer),void t.preventDefault()}copy(t){var e;if(t.preventDefault(),null==(e=oc(this,O))||e.commitOrRemove(),!this.hasSelection)return;const s=[];for(const i of oc(this,mt)){const t=i.serialize(!0);t&&s.push(t)}0!==s.length&&t.clipboardData.setData("application/pdfjs",JSON.stringify(s))}cut(t){this.copy(t),this.delete()}async paste(t){t.preventDefault();const{clipboardData:e}=t;for(const a of e.items)for(const t of oc(this,K))if(t.isHandlingMimeForPasting(a.type))return void t.paste(a,this.currentLayer);let s=e.getData("application/pdfjs");if(!s)return;try{s=JSON.parse(s)}catch(n){return void Qc(`paste: "${n.message}".`)}if(!Array.isArray(s))return;this.unselectAll();const i=this.currentLayer;try{const t=[];for(const a of s){const e=await i.deserialize(a);if(!e)return;t.push(e)}const e=()=>{for(const e of t)cc(this,Ct,Vt).call(this,e);cc(this,Ct,Yt).call(this,t)},n=()=>{for(const e of t)e.remove()};this.addCommands({cmd:e,undo:n,mustExec:!0})}catch(n){Qc(`paste: "${n.message}".`)}}keydown(e){this.isShiftKeyDown||"Shift"!==e.key||(this.isShiftKeyDown=!0),oc(this,ft)===kc.NONE||this.isEditorHandlingKeyboard||t._keyboardManager.exec(this,e)}keyup(t){this.isShiftKeyDown&&"Shift"===t.key&&(this.isShiftKeyDown=!1,oc(this,at)&&(hc(this,at,!1),cc(this,Ct,Lt).call(this,"main_toolbar")))}onEditingAction({name:t}){switch(t){case"undo":case"redo":case"delete":case"selectAll":this[t]();break;case"highlightSelection":this.highlightSelection("context_menu")}}setEditingState(t){t?(cc(this,Ct,Nt).call(this),cc(this,Ct,Bt).call(this),cc(this,Ct,zt).call(this,{isEditing:oc(this,ft)!==kc.NONE,isEmpty:cc(this,Ct,Xt).call(this),hasSomethingToUndo:oc(this,j).hasSomethingToUndo(),hasSomethingToRedo:oc(this,j).hasSomethingToRedo(),hasSelectedEditor:!1})):(cc(this,Ct,Ft).call(this),cc(this,Ct,$t).call(this),cc(this,Ct,zt).call(this,{isEditing:!1}),this.disableUserSelect(!1))}registerEditorTypes(t){if(!oc(this,K)){hc(this,K,t);for(const t of oc(this,K))cc(this,Ct,Gt).call(this,t.defaultPropertiesToUpdate)}}getId(){return oc(this,ot).id}get currentLayer(){return oc(this,$).get(oc(this,q))}getLayer(t){return oc(this,$).get(t)}get currentPageIndex(){return oc(this,q)}addLayer(t){oc(this,$).set(t.pageIndex,t),oc(this,lt)?t.enable():t.disable()}removeLayer(t){oc(this,$).delete(t.pageIndex)}async updateMode(t,e=null,s=!1,i=!1){var n,a,r;if(oc(this,ft)!==t&&(!oc(this,Et)||(await oc(this,Et).promise,oc(this,Et)))){if(hc(this,Et,Promise.withResolvers()),null==(n=oc(this,V))||n.commitOrRemove(),hc(this,ft,t),t===kc.NONE)return this.setEditingState(!1),cc(this,Ct,Ut).call(this),null==(a=this._editorUndoBar)||a.hide(),void oc(this,Et).resolve();t===kc.SIGNATURE&&await(null==(r=oc(this,wt))?void 0:r.loadSignatures()),this.setEditingState(!0),await cc(this,Ct,jt).call(this),this.unselectAll();for(const e of oc(this,$).values())e.updateMode(t);if(!e)return s&&this.addNewEditorFromKeyboard(),void oc(this,Et).resolve();for(const t of oc(this,B).values())t.annotationElementId===e||t.id===e?(this.setSelected(t),i&&t.enterInEditMode()):t.unselect();oc(this,Et).resolve()}}addNewEditorFromKeyboard(){this.currentLayer.canCreateNewEmptyEditor()&&this.currentLayer.addNewEditor()}updateToolbar(t){t.mode!==oc(this,ft)&&this._eventBus.dispatch("switchannotationeditormode",{source:this,...t})}updateParams(t,e){var s;if(oc(this,K)){switch(t){case Mc.CREATE:return void this.currentLayer.addNewEditor(e);case Mc.HIGHLIGHT_DEFAULT_COLOR:null==(s=oc(this,ut))||s.updateColor(e);break;case Mc.HIGHLIGHT_SHOW_ALL:this._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:{type:"highlight",action:"toggle_visibility"}}}),(oc(this,At)||hc(this,At,new Map)).set(t,e),this.showAllEditors("highlight",e)}for(const s of oc(this,mt))s.updateParams(t,e);for(const s of oc(this,K))s.updateDefaultParams(t,e)}}showAllEditors(t,e,s=!1){var i;for(const n of oc(this,B).values())n.editorType===t&&n.show(e);((null==(i=oc(this,At))?void 0:i.get(Mc.HIGHLIGHT_SHOW_ALL))??!0)!==e&&cc(this,Ct,Gt).call(this,[[Mc.HIGHLIGHT_SHOW_ALL,e]])}enableWaiting(t=!1){if(oc(this,ht)!==t){hc(this,ht,t);for(const e of oc(this,$).values())t?e.disableClick():e.enableClick(),e.div.classList.toggle("waiting",t)}}getEditors(t){const e=[];for(const s of oc(this,B).values())s.pageIndex===t&&e.push(s);return e}getEditor(t){return oc(this,B).get(t)}addEditor(t){oc(this,B).set(t.id,t)}removeEditor(t){var e,s;t.div.contains(document.activeElement)&&(oc(this,st)&&clearTimeout(oc(this,st)),hc(this,st,setTimeout(()=>{this.focusMainContainer(),hc(this,st,null)},0))),oc(this,B).delete(t.id),t.annotationElementId&&(null==(e=oc(this,pt))||e.delete(t.annotationElementId)),this.unselect(t),t.annotationElementId&&oc(this,X).has(t.annotationElementId)||null==(s=oc(this,z))||s.remove(t.id)}addDeletedAnnotationElement(t){oc(this,X).add(t.annotationElementId),this.addChangedExistingAnnotation(t),t.deleted=!0}isDeletedAnnotationElement(t){return oc(this,X).has(t)}removeDeletedAnnotationElement(t){oc(this,X).delete(t.annotationElementId),this.removeChangedExistingAnnotation(t),t.deleted=!1}setActiveEditor(t){oc(this,O)!==t&&(hc(this,O,t),t&&cc(this,Ct,Gt).call(this,t.propertiesToUpdate))}updateUI(t){oc(this,Ct,qt)===t&&cc(this,Ct,Gt).call(this,t.propertiesToUpdate)}updateUIForDefaultProperties(t){cc(this,Ct,Gt).call(this,t.defaultPropertiesToUpdate)}toggleSelected(t){if(oc(this,mt).has(t))return oc(this,mt).delete(t),t.unselect(),void cc(this,Ct,zt).call(this,{hasSelectedEditor:this.hasSelection});oc(this,mt).add(t),t.select(),cc(this,Ct,Gt).call(this,t.propertiesToUpdate),cc(this,Ct,zt).call(this,{hasSelectedEditor:!0})}setSelected(t){var e;this.updateToolbar({mode:t.mode,editId:t.id}),null==(e=oc(this,V))||e.commitOrRemove();for(const s of oc(this,mt))s!==t&&s.unselect();oc(this,mt).clear(),oc(this,mt).add(t),t.select(),cc(this,Ct,Gt).call(this,t.propertiesToUpdate),cc(this,Ct,zt).call(this,{hasSelectedEditor:!0})}isSelected(t){return oc(this,mt).has(t)}get firstSelectedEditor(){return oc(this,mt).values().next().value}unselect(t){t.unselect(),oc(this,mt).delete(t),cc(this,Ct,zt).call(this,{hasSelectedEditor:this.hasSelection})}get hasSelection(){return 0!==oc(this,mt).size}get isEnterHandled(){return 1===oc(this,mt).size&&this.firstSelectedEditor.isEnterHandled}undo(){var t;oc(this,j).undo(),cc(this,Ct,zt).call(this,{hasSomethingToUndo:oc(this,j).hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:cc(this,Ct,Xt).call(this)}),null==(t=this._editorUndoBar)||t.hide()}redo(){oc(this,j).redo(),cc(this,Ct,zt).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:oc(this,j).hasSomethingToRedo(),isEmpty:cc(this,Ct,Xt).call(this)})}addCommands(t){oc(this,j).add(t),cc(this,Ct,zt).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:cc(this,Ct,Xt).call(this)})}cleanUndoStack(t){oc(this,j).cleanType(t)}delete(){var t;this.commitOrRemove();const e=null==(t=this.currentLayer)?void 0:t.endDrawingSession(!0);if(!this.hasSelection&&!e)return;const s=e?[e]:[...oc(this,mt)],i=()=>{for(const t of s)cc(this,Ct,Vt).call(this,t)};this.addCommands({cmd:()=>{var t;null==(t=this._editorUndoBar)||t.show(i,1===s.length?s[0].editorType:s.length);for(const e of s)e.remove()},undo:i,mustExec:!0})}commitOrRemove(){var t;null==(t=oc(this,O))||t.commitOrRemove()}hasSomethingToControl(){return oc(this,O)||this.hasSelection}selectAll(){for(const t of oc(this,mt))t.commit();cc(this,Ct,Yt).call(this,oc(this,B).values())}unselectAll(){var t;if((!oc(this,O)||(oc(this,O).commitOrRemove(),oc(this,ft)===kc.NONE))&&!(null==(t=oc(this,V))?void 0:t.commitOrRemove())&&this.hasSelection){for(const t of oc(this,mt))t.unselect();oc(this,mt).clear(),cc(this,Ct,zt).call(this,{hasSelectedEditor:!1})}}translateSelectedEditors(t,e,s=!1){if(s||this.commitOrRemove(),!this.hasSelection)return;oc(this,yt)[0]+=t,oc(this,yt)[1]+=e;const[i,n]=oc(this,yt),a=[...oc(this,mt)];oc(this,xt)&&clearTimeout(oc(this,xt)),hc(this,xt,setTimeout(()=>{hc(this,xt,null),oc(this,yt)[0]=oc(this,yt)[1]=0,this.addCommands({cmd:()=>{for(const t of a)oc(this,B).has(t.id)&&(t.translateInPage(i,n),t.translationDone())},undo:()=>{for(const t of a)oc(this,B).has(t.id)&&(t.translateInPage(-i,-n),t.translationDone())},mustExec:!1})},1e3));for(const r of a)r.translateInPage(t,e),r.translationDone()}setUpDragSession(){if(this.hasSelection){this.disableUserSelect(!0),hc(this,Y,new Map);for(const t of oc(this,mt))oc(this,Y).set(t,{savedX:t.x,savedY:t.y,savedPageIndex:t.pageIndex,newX:0,newY:0,newPageIndex:-1})}}endDragSession(){if(!oc(this,Y))return!1;this.disableUserSelect(!1);const t=oc(this,Y);hc(this,Y,null);let e=!1;for(const[{x:i,y:n,pageIndex:a},r]of t)r.newX=i,r.newY=n,r.newPageIndex=a,e||(e=i!==r.savedX||n!==r.savedY||a!==r.savedPageIndex);if(!e)return!1;const s=(t,e,s,i)=>{if(oc(this,B).has(t.id)){const n=oc(this,$).get(i);n?t._setParentAndPosition(n,e,s):(t.pageIndex=i,t.x=e,t.y=s)}};return this.addCommands({cmd:()=>{for(const[e,{newX:i,newY:n,newPageIndex:a}]of t)s(e,i,n,a)},undo:()=>{for(const[e,{savedX:i,savedY:n,savedPageIndex:a}]of t)s(e,i,n,a)},mustExec:!0}),!0}dragSelectedEditors(t,e){if(oc(this,Y))for(const s of oc(this,Y).keys())s.drag(t,e)}rebuild(t){if(null===t.parent){const e=this.getLayer(t.pageIndex);e?(e.changeParent(t),e.addOrRebuild(t)):(this.addEditor(t),this.addToAnnotationStorage(t),t.rebuild())}else t.parent.addOrRebuild(t)}get isEditorHandlingKeyboard(){var t;return(null==(t=this.getActive())?void 0:t.shouldGetKeyboardEvents())||1===oc(this,mt).size&&this.firstSelectedEditor.shouldGetKeyboardEvents()}isActive(t){return oc(this,O)===t}getActive(){return oc(this,O)}getMode(){return oc(this,ft)}get imageManager(){return sd(this,"imageManager",new Kd)}getSelectionBoxes(t){if(!t)return null;const e=document.getSelection();for(let l=0,h=e.rangeCount;l<h;l++)if(!t.contains(e.getRangeAt(l).commonAncestorContainer))return null;const{x:s,y:i,width:n,height:a}=t.getBoundingClientRect();let r;switch(t.getAttribute("data-main-rotation")){case"90":r=(t,e,r,o)=>({x:(e-i)/a,y:1-(t+r-s)/n,width:o/a,height:r/n});break;case"180":r=(t,e,r,o)=>({x:1-(t+r-s)/n,y:1-(e+o-i)/a,width:r/n,height:o/a});break;case"270":r=(t,e,r,o)=>({x:1-(e+o-i)/a,y:(t-s)/n,width:o/a,height:r/n});break;default:r=(t,e,r,o)=>({x:(t-s)/n,y:(e-i)/a,width:r/n,height:o/a})}const o=[];for(let l=0,h=e.rangeCount;l<h;l++){const t=e.getRangeAt(l);if(!t.collapsed)for(const{x:e,y:s,width:i,height:n}of t.getClientRects())0!==i&&0!==n&&o.push(r(e,s,i,n))}return 0===o.length?null:o}addChangedExistingAnnotation({annotationElementId:t,id:e}){(oc(this,G)||hc(this,G,new Map)).set(t,e)}removeChangedExistingAnnotation({annotationElementId:t}){var e;null==(e=oc(this,G))||e.delete(t)}renderAnnotationElement(t){var e;const s=null==(e=oc(this,G))?void 0:e.get(t.data.id);if(!s)return;const i=oc(this,z).getRawValue(s);i&&(oc(this,ft)!==kc.NONE||i.hasBeenModified)&&i.renderAnnotationElement(t)}setMissingCanvas(t,e,s){var i;const n=null==(i=oc(this,pt))?void 0:i.get(t);n&&(n.setCanvas(e,s),oc(this,pt).delete(t))}addMissingCanvas(t,e){(oc(this,pt)||hc(this,pt,new Map)).set(t,e)}};W=new WeakMap,O=new WeakMap,B=new WeakMap,$=new WeakMap,H=new WeakMap,z=new WeakMap,G=new WeakMap,j=new WeakMap,U=new WeakMap,V=new WeakMap,q=new WeakMap,X=new WeakMap,Y=new WeakMap,K=new WeakMap,Q=new WeakMap,J=new WeakMap,Z=new WeakMap,tt=new WeakMap,et=new WeakMap,st=new WeakMap,it=new WeakMap,nt=new WeakMap,at=new WeakMap,rt=new WeakMap,ot=new WeakMap,lt=new WeakMap,ht=new WeakMap,ct=new WeakMap,dt=new WeakMap,ut=new WeakMap,pt=new WeakMap,gt=new WeakMap,ft=new WeakMap,mt=new WeakMap,vt=new WeakMap,wt=new WeakMap,bt=new WeakMap,At=new WeakMap,_t=new WeakMap,yt=new WeakMap,xt=new WeakMap,St=new WeakMap,kt=new WeakMap,Mt=new WeakMap,Et=new WeakMap,Ct=new WeakSet,Tt=function({anchorNode:t}){return t.nodeType===Node.TEXT_NODE?t.parentElement:t},Rt=function(t){const{currentLayer:e}=this;if(e.hasTextLayer(t))return e;for(const s of oc(this,$).values())if(s.hasTextLayer(t))return s;return null},Pt=function(){const t=document.getSelection();if(!t||t.isCollapsed)return;const e=cc(this,Ct,Tt).call(this,t).closest(".textLayer"),s=this.getSelectionBoxes(e);s&&(oc(this,rt)||hc(this,rt,new Vd(this)),oc(this,rt).show(e,s,"ltr"===this.direction))},It=function(){var t,e,s;const i=document.getSelection();if(!i||i.isCollapsed)return void(oc(this,vt)&&(null==(t=oc(this,rt))||t.hide(),hc(this,vt,null),cc(this,Ct,zt).call(this,{hasSelectedText:!1})));const{anchorNode:n}=i;if(n===oc(this,vt))return;const a=cc(this,Ct,Tt).call(this,i).closest(".textLayer");if(a){if(null==(s=oc(this,rt))||s.hide(),hc(this,vt,n),cc(this,Ct,zt).call(this,{hasSelectedText:!0}),(oc(this,ft)===kc.HIGHLIGHT||oc(this,ft)===kc.NONE)&&(oc(this,ft)===kc.HIGHLIGHT&&this.showAllEditors("highlight",!0,!0),hc(this,at,this.isShiftKeyDown),!this.isShiftKeyDown)){const t=oc(this,ft)===kc.HIGHLIGHT?cc(this,Ct,Rt).call(this,a):null;null==t||t.toggleDrawing();const e=new AbortController,s=this.combinedSignal(e),i=s=>{"pointerup"===s.type&&0!==s.button||(e.abort(),null==t||t.toggleDrawing(!0),"pointerup"===s.type&&cc(this,Ct,Lt).call(this,"main_toolbar"))};window.addEventListener("pointerup",i,{signal:s}),window.addEventListener("blur",i,{signal:s})}}else oc(this,vt)&&(null==(e=oc(this,rt))||e.hide(),hc(this,vt,null),cc(this,Ct,zt).call(this,{hasSelectedText:!1}))},Lt=function(t=""){oc(this,ft)===kc.HIGHLIGHT?this.highlightSelection(t):oc(this,J)&&cc(this,Ct,Pt).call(this)},Dt=function(){document.addEventListener("selectionchange",cc(this,Ct,It).bind(this),{signal:this._signal})},Nt=function(){if(oc(this,it))return;hc(this,it,new AbortController);const t=this.combinedSignal(oc(this,it));window.addEventListener("focus",this.focus.bind(this),{signal:t}),window.addEventListener("blur",this.blur.bind(this),{signal:t})},Ft=function(){var t;null==(t=oc(this,it))||t.abort(),hc(this,it,null)},Wt=function(){if(oc(this,ct))return;hc(this,ct,new AbortController);const t=this.combinedSignal(oc(this,ct));window.addEventListener("keydown",this.keydown.bind(this),{signal:t}),window.addEventListener("keyup",this.keyup.bind(this),{signal:t})},Ot=function(){var t;null==(t=oc(this,ct))||t.abort(),hc(this,ct,null)},Bt=function(){if(oc(this,U))return;hc(this,U,new AbortController);const t=this.combinedSignal(oc(this,U));document.addEventListener("copy",this.copy.bind(this),{signal:t}),document.addEventListener("cut",this.cut.bind(this),{signal:t}),document.addEventListener("paste",this.paste.bind(this),{signal:t})},$t=function(){var t;null==(t=oc(this,U))||t.abort(),hc(this,U,null)},Ht=function(){const t=this._signal;document.addEventListener("dragover",this.dragOver.bind(this),{signal:t}),document.addEventListener("drop",this.drop.bind(this),{signal:t})},zt=function(t){Object.entries(t).some(([t,e])=>oc(this,_t)[t]!==e)&&(this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(oc(this,_t),t)}),oc(this,ft)===kc.HIGHLIGHT&&!1===t.hasSelectedEditor&&cc(this,Ct,Gt).call(this,[[Mc.HIGHLIGHT_FREE,!0]]))},Gt=function(t){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:t})},jt=async function(){if(!oc(this,lt)){hc(this,lt,!0);const t=[];for(const e of oc(this,$).values())t.push(e.enable());await Promise.all(t);for(const e of oc(this,B).values())e.enable()}},Ut=function(){if(this.unselectAll(),oc(this,lt)){hc(this,lt,!1);for(const t of oc(this,$).values())t.disable();for(const t of oc(this,B).values())t.disable()}},Vt=function(t){const e=oc(this,$).get(t.pageIndex);e?e.addOrRebuild(t):(this.addEditor(t),this.addToAnnotationStorage(t))},qt=function(){let t=null;for(t of oc(this,mt));return t},Xt=function(){if(0===oc(this,B).size)return!0;if(1===oc(this,B).size)for(const t of oc(this,B).values())return t.isEmpty();return!1},Yt=function(t){for(const e of oc(this,mt))e.unselect();oc(this,mt).clear();for(const e of t)e.isEmpty()||(oc(this,mt).add(e),e.select());cc(this,Ct,zt).call(this,{hasSelectedEditor:this.hasSelection})},ac(eu,"TRANSLATE_SMALL",1),ac(eu,"TRANSLATE_BIG",10);let su=eu;const iu=class t{constructor(e){lc(this,he),lc(this,Kt,null),lc(this,Qt,!1),lc(this,Jt,null),lc(this,Zt,null),lc(this,te,null),lc(this,ee,null),lc(this,se,!1),lc(this,ie,null),lc(this,ne,null),lc(this,ae,null),lc(this,re,null),lc(this,oe,!1),hc(this,ne,e),hc(this,oe,e._uiManager.useNewAltTextFlow),oc(t,le)||hc(t,le,Object.freeze({added:"pdfjs-editor-new-alt-text-added-button","added-label":"pdfjs-editor-new-alt-text-added-button-label",missing:"pdfjs-editor-new-alt-text-missing-button","missing-label":"pdfjs-editor-new-alt-text-missing-button-label",review:"pdfjs-editor-new-alt-text-to-review-button","review-label":"pdfjs-editor-new-alt-text-to-review-button-label"}))}static initialize(e){t._l10n??(t._l10n=e)}async render(){const e=hc(this,Jt,document.createElement("button"));e.className="altText",e.tabIndex="0";const s=hc(this,Zt,document.createElement("span"));e.append(s),oc(this,oe)?(e.classList.add("new"),e.setAttribute("data-l10n-id",oc(t,le).missing),s.setAttribute("data-l10n-id",oc(t,le)["missing-label"])):(e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button"),s.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button-label"));const i=oc(this,ne)._uiManager._signal;e.addEventListener("contextmenu",Dd,{signal:i}),e.addEventListener("pointerdown",t=>t.stopPropagation(),{signal:i});const n=t=>{t.preventDefault(),oc(this,ne)._uiManager.editAltText(oc(this,ne)),oc(this,oe)&&oc(this,ne)._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_clicked",data:{label:oc(this,he,ce)}})};return e.addEventListener("click",n,{capture:!0,signal:i}),e.addEventListener("keydown",t=>{t.target===e&&"Enter"===t.key&&(hc(this,se,!0),n(t))},{signal:i}),await cc(this,he,de).call(this),e}finish(){oc(this,Jt)&&(oc(this,Jt).focus({focusVisible:oc(this,se)}),hc(this,se,!1))}isEmpty(){return oc(this,oe)?null===oc(this,Kt):!oc(this,Kt)&&!oc(this,Qt)}hasData(){return oc(this,oe)?null!==oc(this,Kt)||!!oc(this,ae):this.isEmpty()}get guessedText(){return oc(this,ae)}async setGuessedText(e){null===oc(this,Kt)&&(hc(this,ae,e),hc(this,re,await t._l10n.get("pdfjs-editor-new-alt-text-generated-alt-text-with-disclaimer",{generatedAltText:e})),cc(this,he,de).call(this))}toggleAltTextBadge(t=!1){var e;if(!oc(this,oe)||oc(this,Kt))return null==(e=oc(this,ie))||e.remove(),void hc(this,ie,null);if(!oc(this,ie)){const t=hc(this,ie,document.createElement("div"));t.className="noAltTextBadge",oc(this,ne).div.append(t)}oc(this,ie).classList.toggle("hidden",!t)}serialize(t){let e=oc(this,Kt);return t||oc(this,ae)!==e||(e=oc(this,re)),{altText:e,decorative:oc(this,Qt),guessedText:oc(this,ae),textWithDisclaimer:oc(this,re)}}get data(){return{altText:oc(this,Kt),decorative:oc(this,Qt)}}set data({altText:t,decorative:e,guessedText:s,textWithDisclaimer:i,cancel:n=!1}){s&&(hc(this,ae,s),hc(this,re,i)),oc(this,Kt)===t&&oc(this,Qt)===e||(n||(hc(this,Kt,t),hc(this,Qt,e)),cc(this,he,de).call(this))}toggle(t=!1){oc(this,Jt)&&(!t&&oc(this,ee)&&(clearTimeout(oc(this,ee)),hc(this,ee,null)),oc(this,Jt).disabled=!t)}shown(){oc(this,ne)._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_displayed",data:{label:oc(this,he,ce)}})}destroy(){var t,e;null==(t=oc(this,Jt))||t.remove(),hc(this,Jt,null),hc(this,Zt,null),hc(this,te,null),null==(e=oc(this,ie))||e.remove(),hc(this,ie,null)}};Kt=new WeakMap,Qt=new WeakMap,Jt=new WeakMap,Zt=new WeakMap,te=new WeakMap,ee=new WeakMap,se=new WeakMap,ie=new WeakMap,ne=new WeakMap,ae=new WeakMap,re=new WeakMap,oe=new WeakMap,le=new WeakMap,he=new WeakSet,ce=function(){return(oc(this,Kt)?"added":null===oc(this,Kt)&&this.guessedText&&"review")||"missing"},de=async function(){var t,e,s;const i=oc(this,Jt);if(!i)return;if(oc(this,oe)){if(i.classList.toggle("done",!!oc(this,Kt)),i.setAttribute("data-l10n-id",oc(iu,le)[oc(this,he,ce)]),null==(t=oc(this,Zt))||t.setAttribute("data-l10n-id",oc(iu,le)[`${oc(this,he,ce)}-label`]),!oc(this,Kt))return void(null==(e=oc(this,te))||e.remove())}else{if(!oc(this,Kt)&&!oc(this,Qt))return i.classList.remove("done"),void(null==(s=oc(this,te))||s.remove());i.classList.add("done"),i.setAttribute("data-l10n-id","pdfjs-editor-alt-text-edit-button")}let n=oc(this,te);if(!n){hc(this,te,n=document.createElement("span")),n.className="tooltip",n.setAttribute("role","tooltip"),n.id=`alt-text-tooltip-${oc(this,ne).id}`;const t=100,e=oc(this,ne)._uiManager._signal;e.addEventListener("abort",()=>{clearTimeout(oc(this,ee)),hc(this,ee,null)},{once:!0}),i.addEventListener("mouseenter",()=>{hc(this,ee,setTimeout(()=>{hc(this,ee,null),oc(this,te).classList.add("show"),oc(this,ne)._reportTelemetry({action:"alt_text_tooltip"})},t))},{signal:e}),i.addEventListener("mouseleave",()=>{var t;oc(this,ee)&&(clearTimeout(oc(this,ee)),hc(this,ee,null)),null==(t=oc(this,te))||t.classList.remove("show")},{signal:e})}oc(this,Qt)?n.setAttribute("data-l10n-id","pdfjs-editor-alt-text-decorative-tooltip"):(n.removeAttribute("data-l10n-id"),n.textContent=oc(this,Kt)),n.parentNode||i.append(n);const a=oc(this,ne).getElementForAltText();null==a||a.setAttribute("aria-describedby",n.id)},lc(iu,le,null),ac(iu,"_l10n",null);let nu=iu;const au=class{constructor({container:t,isPinchingDisabled:e=null,isPinchingStopped:s=null,onPinchStart:i=null,onPinching:n=null,onPinchEnd:a=null,signal:r}){lc(this,Se),lc(this,ue),lc(this,pe,!1),lc(this,ge,null),lc(this,fe),lc(this,me),lc(this,ve),lc(this,we),lc(this,be,null),lc(this,Ae),lc(this,_e,null),lc(this,ye),lc(this,xe,null),hc(this,ue,t),hc(this,ge,s),hc(this,fe,e),hc(this,me,i),hc(this,ve,n),hc(this,we,a),hc(this,ye,new AbortController),hc(this,Ae,AbortSignal.any([r,oc(this,ye).signal])),t.addEventListener("touchstart",cc(this,Se,ke).bind(this),{passive:!1,signal:oc(this,Ae)})}get MIN_TOUCH_DISTANCE_TO_PINCH(){return 35/zd.pixelRatio}destroy(){var t,e;null==(t=oc(this,ye))||t.abort(),hc(this,ye,null),null==(e=oc(this,be))||e.abort(),hc(this,be,null)}};ue=new WeakMap,pe=new WeakMap,ge=new WeakMap,fe=new WeakMap,me=new WeakMap,ve=new WeakMap,we=new WeakMap,be=new WeakMap,Ae=new WeakMap,_e=new WeakMap,ye=new WeakMap,xe=new WeakMap,Se=new WeakSet,ke=function(t){var e,s,i;if(null==(e=oc(this,fe))?void 0:e.call(this))return;if(1===t.touches.length){if(oc(this,be))return;const t=hc(this,be,new AbortController),e=AbortSignal.any([oc(this,Ae),t.signal]),s=oc(this,ue),i={capture:!0,signal:e,passive:!1},n=t=>{var e;"touch"===t.pointerType&&(null==(e=oc(this,be))||e.abort(),hc(this,be,null))};return s.addEventListener("pointerdown",t=>{"touch"===t.pointerType&&(Nd(t),n(t))},i),s.addEventListener("pointerup",n,i),void s.addEventListener("pointercancel",n,i)}if(!oc(this,xe)){hc(this,xe,new AbortController);const t=AbortSignal.any([oc(this,Ae),oc(this,xe).signal]),e=oc(this,ue),i={signal:t,capture:!1,passive:!1};e.addEventListener("touchmove",cc(this,Se,Me).bind(this),i);const n=cc(this,Se,Ee).bind(this);e.addEventListener("touchend",n,i),e.addEventListener("touchcancel",n,i),i.capture=!0,e.addEventListener("pointerdown",Nd,i),e.addEventListener("pointermove",Nd,i),e.addEventListener("pointercancel",Nd,i),e.addEventListener("pointerup",Nd,i),null==(s=oc(this,me))||s.call(this)}if(Nd(t),2!==t.touches.length||(null==(i=oc(this,ge))?void 0:i.call(this)))return void hc(this,_e,null);let[n,a]=t.touches;n.identifier>a.identifier&&([n,a]=[a,n]),hc(this,_e,{touch0X:n.screenX,touch0Y:n.screenY,touch1X:a.screenX,touch1Y:a.screenY})},Me=function(t){var e;if(!oc(this,_e)||2!==t.touches.length)return;Nd(t);let[s,i]=t.touches;s.identifier>i.identifier&&([s,i]=[i,s]);const{screenX:n,screenY:a}=s,{screenX:r,screenY:o}=i,l=oc(this,_e),{touch0X:h,touch0Y:c,touch1X:d,touch1Y:u}=l,p=d-h,g=u-c,f=r-n,m=o-a,v=Math.hypot(f,m)||1,w=Math.hypot(p,g)||1;if(!oc(this,pe)&&Math.abs(w-v)<=au.MIN_TOUCH_DISTANCE_TO_PINCH)return;if(l.touch0X=n,l.touch0Y=a,l.touch1X=r,l.touch1Y=o,!oc(this,pe))return void hc(this,pe,!0);const b=[(n+r)/2,(a+o)/2];null==(e=oc(this,ve))||e.call(this,b,w,v)},Ee=function(t){var e;t.touches.length>=2||(oc(this,xe)&&(oc(this,xe).abort(),hc(this,xe,null),null==(e=oc(this,we))||e.call(this)),oc(this,_e)&&(Nd(t),hc(this,_e,null),hc(this,pe,!1)))};let ru=au;const ou=class t{constructor(e){lc(this,Je),lc(this,Ce,null),lc(this,Te,null),lc(this,Re,null),lc(this,Pe,!1),lc(this,Ie,null),lc(this,Le,""),lc(this,De,!1),lc(this,Ne,null),lc(this,Fe,null),lc(this,We,null),lc(this,Oe,null),lc(this,Be,""),lc(this,$e,!1),lc(this,He,null),lc(this,ze,!1),lc(this,Ge,!1),lc(this,je,!1),lc(this,Ue,null),lc(this,Ve,0),lc(this,qe,0),lc(this,Xe,null),lc(this,Ye,null),ac(this,"isSelected",!1),ac(this,"_isCopy",!1),ac(this,"_editToolbar",null),ac(this,"_initialOptions",Object.create(null)),ac(this,"_initialData",null),ac(this,"_isVisible",!0),ac(this,"_uiManager",null),ac(this,"_focusEventsAllowed",!0),lc(this,Ke,!1),lc(this,Qe,t._zIndex++),this.parent=e.parent,this.id=e.id,this.width=this.height=null,this.pageIndex=e.parent.pageIndex,this.name=e.name,this.div=null,this._uiManager=e.uiManager,this.annotationElementId=null,this._willKeepAspectRatio=!1,this._initialOptions.isCentered=e.isCentered,this._structTreeParentId=null,this.annotationElementId=e.annotationElementId||null;const{rotation:s,rawDims:{pageWidth:i,pageHeight:n,pageX:a,pageY:r}}=this.parent.viewport;this.rotation=s,this.pageRotation=(360+s-this._uiManager.viewParameters.rotation)%360,this.pageDimensions=[i,n],this.pageTranslation=[a,r];const[o,l]=this.parentDimensions;this.x=e.x/o,this.y=e.y/l,this.isAttachedToDOM=!1,this.deleted=!1}static get _resizerKeyboardManager(){const e=t.prototype._resizeWithKeyboard,s=su.TRANSLATE_SMALL,i=su.TRANSLATE_BIG;return sd(this,"_resizerKeyboardManager",new Jd([[["ArrowLeft","mac+ArrowLeft"],e,{args:[-s,0]}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],e,{args:[-i,0]}],[["ArrowRight","mac+ArrowRight"],e,{args:[s,0]}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],e,{args:[i,0]}],[["ArrowUp","mac+ArrowUp"],e,{args:[0,-s]}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],e,{args:[0,-i]}],[["ArrowDown","mac+ArrowDown"],e,{args:[0,s]}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],e,{args:[0,i]}],[["Escape","mac+Escape"],t.prototype._stopResizingWithKeyboard]]))}get editorType(){return Object.getPrototypeOf(this).constructor._type}get mode(){return Object.getPrototypeOf(this).constructor._editorType}static get isDrawer(){return!1}static get _defaultLineColor(){return sd(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(t){const e=new hu({id:t.parent.getNextId(),parent:t.parent,uiManager:t._uiManager});e.annotationElementId=t.annotationElementId,e.deleted=!0,e._uiManager.addToAnnotationStorage(e)}static initialize(e,s){if(t._l10n??(t._l10n=e),t._l10nResizer||(t._l10nResizer=Object.freeze({topLeft:"pdfjs-editor-resizer-top-left",topMiddle:"pdfjs-editor-resizer-top-middle",topRight:"pdfjs-editor-resizer-top-right",middleRight:"pdfjs-editor-resizer-middle-right",bottomRight:"pdfjs-editor-resizer-bottom-right",bottomMiddle:"pdfjs-editor-resizer-bottom-middle",bottomLeft:"pdfjs-editor-resizer-bottom-left",middleLeft:"pdfjs-editor-resizer-middle-left"})),-1!==t._borderLineWidth)return;const i=getComputedStyle(document.documentElement);t._borderLineWidth=parseFloat(i.getPropertyValue("--outline-width"))||0}static updateDefaultParams(t,e){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(t){return!1}static paste(t,e){Jc("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return oc(this,Ke)}set _isDraggable(t){var e;hc(this,Ke,t),null==(e=this.div)||e.classList.toggle("draggable",t)}get isEnterHandled(){return!0}center(){const[t,e]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*e/(2*t),this.y+=this.width*t/(2*e);break;case 180:this.x+=this.width/2,this.y+=this.height/2;break;case 270:this.x+=this.height*e/(2*t),this.y-=this.width*t/(2*e);break;default:this.x-=this.width/2,this.y-=this.height/2}this.fixAndSetPosition()}addCommands(t){this._uiManager.addCommands(t)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=oc(this,Qe)}setParent(t){null!==t?(this.pageIndex=t.pageIndex,this.pageDimensions=t.pageDimensions):cc(this,Je,ws).call(this),this.parent=t}focusin(t){this._focusEventsAllowed&&(oc(this,$e)?hc(this,$e,!1):this.parent.setSelected(this))}focusout(t){var e;if(!this._focusEventsAllowed)return;if(!this.isAttachedToDOM)return;const s=t.relatedTarget;(null==s?void 0:s.closest(`#${this.id}`))||(t.preventDefault(),(null==(e=this.parent)?void 0:e.isMultipleSelection)||this.commitOrRemove())}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.isInEditMode()&&this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(t,e,s,i){const[n,a]=this.parentDimensions;[s,i]=this.screenToPageTranslation(s,i),this.x=(t+s)/n,this.y=(e+i)/a,this.fixAndSetPosition()}_moveAfterPaste(t,e){const[s,i]=this.parentDimensions;this.setAt(t*s,e*i,this.width*s,this.height*i),this._onTranslated()}translate(t,e){cc(this,Je,Ze).call(this,this.parentDimensions,t,e)}translateInPage(t,e){oc(this,He)||hc(this,He,[this.x,this.y,this.width,this.height]),cc(this,Je,Ze).call(this,this.pageDimensions,t,e),this.div.scrollIntoView({block:"nearest"})}translationDone(){this._onTranslated(this.x,this.y)}drag(t,e){oc(this,He)||hc(this,He,[this.x,this.y,this.width,this.height]);const{div:s,parentDimensions:[i,n]}=this;if(this.x+=t/i,this.y+=e/n,this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){const{x:t,y:e}=this.div.getBoundingClientRect();this.parent.findNewParent(this,t,e)&&(this.x-=Math.floor(this.x),this.y-=Math.floor(this.y))}let{x:a,y:r}=this;const[o,l]=this.getBaseTranslation();a+=o,r+=l;const{style:h}=s;h.left=`${(100*a).toFixed(2)}%`,h.top=`${(100*r).toFixed(2)}%`,this._onTranslating(a,r),s.scrollIntoView({block:"nearest"})}_onTranslating(t,e){}_onTranslated(t,e){}get _hasBeenMoved(){return!!oc(this,He)&&(oc(this,He)[0]!==this.x||oc(this,He)[1]!==this.y)}get _hasBeenResized(){return!!oc(this,He)&&(oc(this,He)[2]!==this.width||oc(this,He)[3]!==this.height)}getBaseTranslation(){const[e,s]=this.parentDimensions,{_borderLineWidth:i}=t,n=i/e,a=i/s;switch(this.rotation){case 90:return[-n,a];case 180:return[n,a];case 270:return[n,-a];default:return[-n,-a]}}get _mustFixPosition(){return!0}fixAndSetPosition(t=this.rotation){const{div:{style:e},pageDimensions:[s,i]}=this;let{x:n,y:a,width:r,height:o}=this;if(r*=s,o*=i,n*=s,a*=i,this._mustFixPosition)switch(t){case 0:n=Ad(n,0,s-r),a=Ad(a,0,i-o);break;case 90:n=Ad(n,0,s-o),a=Ad(a,r,i);break;case 180:n=Ad(n,r,s),a=Ad(a,o,i);break;case 270:n=Ad(n,o,s),a=Ad(a,0,i-r)}this.x=n/=s,this.y=a/=i;const[l,h]=this.getBaseTranslation();n+=l,a+=h,e.left=`${(100*n).toFixed(2)}%`,e.top=`${(100*a).toFixed(2)}%`,this.moveInDOM()}screenToPageTranslation(e,s){var i;return cc(i=t,ts,es).call(i,e,s,this.parentRotation)}pageTranslationToScreen(e,s){var i;return cc(i=t,ts,es).call(i,e,s,360-this.parentRotation)}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){const{parentScale:t,pageDimensions:[e,s]}=this;return[e*t,s*t]}setDims(t,e){const[s,i]=this.parentDimensions,{style:n}=this.div;n.width=`${(100*t/s).toFixed(2)}%`,oc(this,De)||(n.height=`${(100*e/i).toFixed(2)}%`)}fixDims(){const{style:t}=this.div,{height:e,width:s}=t,i=s.endsWith("%"),n=!oc(this,De)&&e.endsWith("%");if(i&&n)return;const[a,r]=this.parentDimensions;i||(t.width=`${(100*parseFloat(s)/a).toFixed(2)}%`),oc(this,De)||n||(t.height=`${(100*parseFloat(e)/r).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}_onResized(){}static _round(t){return Math.round(1e4*t)/1e4}_onResizing(){}altTextFinish(){var t;null==(t=oc(this,Re))||t.finish()}get toolbarButtons(){return null}async addEditToolbar(){if(this._editToolbar||oc(this,Ge))return this._editToolbar;this._editToolbar=new Ud(this),this.div.append(this._editToolbar.render());const{toolbarButtons:t}=this;if(t)for(const[e,s]of t)await this._editToolbar.addButton(e,s);return this._editToolbar.addButton("delete"),this._editToolbar}removeEditToolbar(){var t;this._editToolbar&&(this._editToolbar.remove(),this._editToolbar=null,null==(t=oc(this,Re))||t.destroy())}addContainer(t){var e;const s=null==(e=this._editToolbar)?void 0:e.div;s?s.before(t):this.div.append(t)}getClientDimensions(){return this.div.getBoundingClientRect()}createAltText(){return oc(this,Re)||(nu.initialize(t._l10n),hc(this,Re,new nu(this)),oc(this,Ce)&&(oc(this,Re).data=oc(this,Ce),hc(this,Ce,null))),oc(this,Re)}get altTextData(){var t;return null==(t=oc(this,Re))?void 0:t.data}set altTextData(t){oc(this,Re)&&(oc(this,Re).data=t)}get guessedAltText(){var t;return null==(t=oc(this,Re))?void 0:t.guessedText}async setGuessedAltText(t){var e;await(null==(e=oc(this,Re))?void 0:e.setGuessedText(t))}serializeAltText(t){var e;return null==(e=oc(this,Re))?void 0:e.serialize(t)}hasAltText(){return!!oc(this,Re)&&!oc(this,Re).isEmpty()}hasAltTextData(){var t;return(null==(t=oc(this,Re))?void 0:t.hasData())??!1}render(){var t;const e=this.div=document.createElement("div");e.setAttribute("data-editor-rotation",(360-this.rotation)%360),e.className=this.name,e.setAttribute("id",this.id),e.tabIndex=oc(this,Pe)?-1:0,e.setAttribute("role","application"),this.defaultL10nId&&e.setAttribute("data-l10n-id",this.defaultL10nId),this._isVisible||e.classList.add("hidden"),this.setInForeground(),cc(this,Je,ps).call(this);const[s,i]=this.parentDimensions;this.parentRotation%180!=0&&(e.style.maxWidth=`${(100*i/s).toFixed(2)}%`,e.style.maxHeight=`${(100*s/i).toFixed(2)}%`);const[n,a]=this.getInitialTranslation();return this.translate(n,a),qd(this,e,["keydown","pointerdown","dblclick"]),this.isResizable&&this._uiManager._supportsPinchToZoom&&(oc(this,Ye)||hc(this,Ye,new ru({container:e,isPinchingDisabled:()=>!this.isSelected,onPinchStart:cc(this,Je,ls).bind(this),onPinching:cc(this,Je,hs).bind(this),onPinchEnd:cc(this,Je,cs).bind(this),signal:this._uiManager._signal}))),null==(t=this._uiManager._editorUndoBar)||t.hide(),e}pointerdown(t){const{isMac:e}=ud.platform;0!==t.button||t.ctrlKey&&e?t.preventDefault():(hc(this,$e,!0),this._isDraggable?cc(this,Je,us).call(this,t):cc(this,Je,ds).call(this,t))}_onStartDragging(){}_onStopDragging(){}moveInDOM(){oc(this,Ue)&&clearTimeout(oc(this,Ue)),hc(this,Ue,setTimeout(()=>{var t;hc(this,Ue,null),null==(t=this.parent)||t.moveEditorInDOM(this)},0))}_setParentAndPosition(t,e,s){t.changeParent(this),this.x=e,this.y=s,this.fixAndSetPosition(),this._onTranslated()}getRect(t,e,s=this.rotation){const i=this.parentScale,[n,a]=this.pageDimensions,[r,o]=this.pageTranslation,l=t/i,h=e/i,c=this.x*n,d=this.y*a,u=this.width*n,p=this.height*a;switch(s){case 0:return[c+l+r,a-d-h-p+o,c+l+u+r,a-d-h+o];case 90:return[c+h+r,a-d+l+o,c+h+p+r,a-d+l+u+o];case 180:return[c-l-u+r,a-d+h+o,c-l+r,a-d+h+p+o];case 270:return[c-h-p+r,a-d-l-u+o,c-h+r,a-d-l+o];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(t,e){const[s,i,n,a]=t,r=n-s,o=a-i;switch(this.rotation){case 0:return[s,e-a,r,o];case 90:return[s,e-i,o,r];case 180:return[n,e-i,r,o];case 270:return[n,e-a,o,r];default:throw new Error("Invalid rotation")}}onceAdded(t){}isEmpty(){return!1}enableEditMode(){return!this.isInEditMode()&&(this.parent.setEditingState(!1),hc(this,Ge,!0),!0)}disableEditMode(){return!!this.isInEditMode()&&(this.parent.setEditingState(!0),hc(this,Ge,!1),!0)}isInEditMode(){return oc(this,Ge)}shouldGetKeyboardEvents(){return oc(this,je)}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}get isOnScreen(){const{top:t,left:e,bottom:s,right:i}=this.getClientDimensions(),{innerHeight:n,innerWidth:a}=window;return e<a&&i>0&&t<n&&s>0}rebuild(){cc(this,Je,ps).call(this)}rotate(t){}resize(){}serializeDeleted(){var t;return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex,popupRef:(null==(t=this._initialData)?void 0:t.popupRef)||""}}serialize(t=!1,e=null){Jc("An editor must be serializable")}static async deserialize(t,e,s){const i=new this.prototype.constructor({parent:e,id:e.getNextId(),uiManager:s,annotationElementId:t.annotationElementId});i.rotation=t.rotation,hc(i,Ce,t.accessibilityData),i._isCopy=t.isCopy||!1;const[n,a]=i.pageDimensions,[r,o,l,h]=i.getRectInCurrentCoords(t.rect,a);return i.x=r/n,i.y=o/a,i.width=l/n,i.height=h/a,i}get hasBeenModified(){return!!this.annotationElementId&&(this.deleted||null!==this.serialize())}remove(){var t,e;if(null==(t=oc(this,Oe))||t.abort(),hc(this,Oe,null),this.isEmpty()||this.commit(),this.parent?this.parent.remove(this):this._uiManager.removeEditor(this),oc(this,Ue)&&(clearTimeout(oc(this,Ue)),hc(this,Ue,null)),cc(this,Je,ws).call(this),this.removeEditToolbar(),oc(this,Xe)){for(const t of oc(this,Xe).values())clearTimeout(t);hc(this,Xe,null)}this.parent=null,null==(e=oc(this,Ye))||e.destroy(),hc(this,Ye,null)}get isResizable(){return!1}makeResizable(){this.isResizable&&(cc(this,Je,is).call(this),oc(this,Ne).classList.remove("hidden"))}get toolbarPosition(){return null}keydown(e){if(!this.isResizable||e.target!==this.div||"Enter"!==e.key)return;this._uiManager.setSelected(this),hc(this,We,{savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height});const s=oc(this,Ne).children;if(!oc(this,Te)){hc(this,Te,Array.from(s));const e=cc(this,Je,gs).bind(this),i=cc(this,Je,fs).bind(this),n=this._uiManager._signal;for(const s of oc(this,Te)){const a=s.getAttribute("data-resizer-name");s.setAttribute("role","spinbutton"),s.addEventListener("keydown",e,{signal:n}),s.addEventListener("blur",i,{signal:n}),s.addEventListener("focus",cc(this,Je,ms).bind(this,a),{signal:n}),s.setAttribute("data-l10n-id",t._l10nResizer[a])}}const i=oc(this,Te)[0];let n=0;for(const t of s){if(t===i)break;n++}const a=(360-this.rotation+this.parentRotation)%360/90*(oc(this,Te).length/4);if(a!==n){if(a<n)for(let t=0;t<n-a;t++)oc(this,Ne).append(oc(this,Ne).firstChild);else if(a>n)for(let t=0;t<a-n;t++)oc(this,Ne).firstChild.before(oc(this,Ne).lastChild);let e=0;for(const i of s){const s=oc(this,Te)[e++].getAttribute("data-resizer-name");i.setAttribute("data-l10n-id",t._l10nResizer[s])}}cc(this,Je,vs).call(this,0),hc(this,je,!0),oc(this,Ne).firstChild.focus({focusVisible:!0}),e.preventDefault(),e.stopImmediatePropagation()}_resizeWithKeyboard(t,e){oc(this,je)&&cc(this,Je,os).call(this,oc(this,Be),{deltaX:t,deltaY:e,fromKeyboard:!0})}_stopResizingWithKeyboard(){cc(this,Je,ws).call(this),this.div.focus()}select(){var t,e,s;this.isSelected&&this._editToolbar||(this.isSelected=!0,this.makeResizable(),null==(t=this.div)||t.classList.add("selectedEditor"),this._editToolbar?(null==(e=this._editToolbar)||e.show(),null==(s=oc(this,Re))||s.toggleAltTextBadge(!1)):this.addEditToolbar().then(()=>{var t,e;(null==(t=this.div)?void 0:t.classList.contains("selectedEditor"))&&(null==(e=this._editToolbar)||e.show())}))}unselect(){var t,e,s,i,n;this.isSelected&&(this.isSelected=!1,null==(t=oc(this,Ne))||t.classList.add("hidden"),null==(e=this.div)||e.classList.remove("selectedEditor"),(null==(s=this.div)?void 0:s.contains(document.activeElement))&&this._uiManager.currentLayer.div.focus({preventScroll:!0}),null==(i=this._editToolbar)||i.hide(),null==(n=oc(this,Re))||n.toggleAltTextBadge(!0))}updateParams(t,e){}disableEditing(){}enableEditing(){}get canChangeContent(){return!1}enterInEditMode(){this.canChangeContent&&(this.enableEditMode(),this.div.focus())}dblclick(t){this.enterInEditMode(),this.parent.updateToolbar({mode:this.constructor._editorType,editId:this.id})}getElementForAltText(){return this.div}get contentDiv(){return this.div}get isEditing(){return oc(this,ze)}set isEditing(t){hc(this,ze,t),this.parent&&(t?(this.parent.setSelected(this),this.parent.setActiveEditor(this)):this.parent.setActiveEditor(null))}setAspectRatio(t,e){hc(this,De,!0);const s=t/e,{style:i}=this.div;i.aspectRatio=s,i.height="auto"}static get MIN_SIZE(){return 16}static canCreateNewEmptyEditor(){return!0}get telemetryInitialData(){return{action:"added"}}get telemetryFinalData(){return null}_reportTelemetry(e,s=!1){if(s){oc(this,Xe)||hc(this,Xe,new Map);const{action:s}=e;let i=oc(this,Xe).get(s);return i&&clearTimeout(i),i=setTimeout(()=>{this._reportTelemetry(e),oc(this,Xe).delete(s),0===oc(this,Xe).size&&hc(this,Xe,null)},t._telemetryTimeout),void oc(this,Xe).set(s,i)}e.type||(e.type=this.editorType),this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:e}})}show(t=this._isVisible){this.div.classList.toggle("hidden",!t),this._isVisible=t}enable(){this.div&&(this.div.tabIndex=0),hc(this,Pe,!1)}disable(){this.div&&(this.div.tabIndex=-1),hc(this,Pe,!0)}renderAnnotationElement(t){let e=t.container.querySelector(".annotationContent");if(e){if("CANVAS"===e.nodeName){const t=e;e=document.createElement("div"),e.classList.add("annotationContent",this.editorType),t.before(e)}}else e=document.createElement("div"),e.classList.add("annotationContent",this.editorType),t.container.prepend(e);return e}resetAnnotationElement(t){const{firstChild:e}=t.container;"DIV"===(null==e?void 0:e.nodeName)&&e.classList.contains("annotationContent")&&e.remove()}};Ce=new WeakMap,Te=new WeakMap,Re=new WeakMap,Pe=new WeakMap,Ie=new WeakMap,Le=new WeakMap,De=new WeakMap,Ne=new WeakMap,Fe=new WeakMap,We=new WeakMap,Oe=new WeakMap,Be=new WeakMap,$e=new WeakMap,He=new WeakMap,ze=new WeakMap,Ge=new WeakMap,je=new WeakMap,Ue=new WeakMap,Ve=new WeakMap,qe=new WeakMap,Xe=new WeakMap,Ye=new WeakMap,Ke=new WeakMap,Qe=new WeakMap,Je=new WeakSet,Ze=function([t,e],s,i){[s,i]=this.screenToPageTranslation(s,i),this.x+=s/t,this.y+=i/e,this._onTranslating(this.x,this.y),this.fixAndSetPosition()},ts=new WeakSet,es=function(t,e,s){switch(s){case 90:return[e,-t];case 180:return[-t,-e];case 270:return[-e,t];default:return[t,e]}},ss=function(t){switch(t){case 90:{const[t,e]=this.pageDimensions;return[0,-t/e,e/t,0]}case 180:return[-1,0,0,-1];case 270:{const[t,e]=this.pageDimensions;return[0,t/e,-e/t,0]}default:return[1,0,0,1]}},is=function(){if(oc(this,Ne))return;hc(this,Ne,document.createElement("div")),oc(this,Ne).classList.add("resizers");const t=this._willKeepAspectRatio?["topLeft","topRight","bottomRight","bottomLeft"]:["topLeft","topMiddle","topRight","middleRight","bottomRight","bottomMiddle","bottomLeft","middleLeft"],e=this._uiManager._signal;for(const s of t){const t=document.createElement("div");oc(this,Ne).append(t),t.classList.add("resizer",s),t.setAttribute("data-resizer-name",s),t.addEventListener("pointerdown",cc(this,Je,ns).bind(this,s),{signal:e}),t.addEventListener("contextmenu",Dd,{signal:e}),t.tabIndex=-1}this.div.prepend(oc(this,Ne))},ns=function(t,e){var s;e.preventDefault();const{isMac:i}=ud.platform;if(0!==e.button||e.ctrlKey&&i)return;null==(s=oc(this,Re))||s.toggle(!1);const n=this._isDraggable;this._isDraggable=!1,hc(this,Fe,[e.screenX,e.screenY]);const a=new AbortController,r=this._uiManager.combinedSignal(a);this.parent.togglePointerEvents(!1),window.addEventListener("pointermove",cc(this,Je,os).bind(this,t),{passive:!0,capture:!0,signal:r}),window.addEventListener("touchmove",Nd,{passive:!1,signal:r}),window.addEventListener("contextmenu",Dd,{signal:r}),hc(this,We,{savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height});const o=this.parent.div.style.cursor,l=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(e.target).cursor;const h=()=>{var t;a.abort(),this.parent.togglePointerEvents(!0),null==(t=oc(this,Re))||t.toggle(!0),this._isDraggable=n,this.parent.div.style.cursor=o,this.div.style.cursor=l,cc(this,Je,rs).call(this)};window.addEventListener("pointerup",h,{signal:r}),window.addEventListener("blur",h,{signal:r})},as=function(t,e,s,i){this.width=s,this.height=i,this.x=t,this.y=e;const[n,a]=this.parentDimensions;this.setDims(n*s,a*i),this.fixAndSetPosition(),this._onResized()},rs=function(){if(!oc(this,We))return;const{savedX:t,savedY:e,savedWidth:s,savedHeight:i}=oc(this,We);hc(this,We,null);const n=this.x,a=this.y,r=this.width,o=this.height;n===t&&a===e&&r===s&&o===i||this.addCommands({cmd:cc(this,Je,as).bind(this,n,a,r,o),undo:cc(this,Je,as).bind(this,t,e,s,i),mustExec:!0})},os=function(t,e){const[s,i]=this.parentDimensions,n=this.x,a=this.y,r=this.width,o=this.height,l=ou.MIN_SIZE/s,h=ou.MIN_SIZE/i,c=cc(this,Je,ss).call(this,this.rotation),d=(t,e)=>[c[0]*t+c[2]*e,c[1]*t+c[3]*e],u=cc(this,Je,ss).call(this,360-this.rotation);let p,g,f=!1,m=!1;switch(t){case"topLeft":f=!0,p=(t,e)=>[0,0],g=(t,e)=>[t,e];break;case"topMiddle":p=(t,e)=>[t/2,0],g=(t,e)=>[t/2,e];break;case"topRight":f=!0,p=(t,e)=>[t,0],g=(t,e)=>[0,e];break;case"middleRight":m=!0,p=(t,e)=>[t,e/2],g=(t,e)=>[0,e/2];break;case"bottomRight":f=!0,p=(t,e)=>[t,e],g=(t,e)=>[0,0];break;case"bottomMiddle":p=(t,e)=>[t/2,e],g=(t,e)=>[t/2,0];break;case"bottomLeft":f=!0,p=(t,e)=>[0,e],g=(t,e)=>[t,0];break;case"middleLeft":m=!0,p=(t,e)=>[0,e/2],g=(t,e)=>[t,e/2]}const v=p(r,o),w=g(r,o);let b=d(...w);const A=ou._round(n+b[0]),_=ou._round(a+b[1]);let y,x,S=1,k=1;if(e.fromKeyboard)({deltaX:y,deltaY:x}=e);else{const{screenX:t,screenY:s}=e,[i,n]=oc(this,Fe);[y,x]=this.screenToPageTranslation(t-i,s-n),oc(this,Fe)[0]=t,oc(this,Fe)[1]=s}var M,E;if([y,x]=(M=y/s,E=x/i,[u[0]*M+u[2]*E,u[1]*M+u[3]*E]),f){const t=Math.hypot(r,o);S=k=Math.max(Math.min(Math.hypot(w[0]-v[0]-y,w[1]-v[1]-x)/t,1/r,1/o),l/r,h/o)}else m?S=Ad(Math.abs(w[0]-v[0]-y),l,1)/r:k=Ad(Math.abs(w[1]-v[1]-x),h,1)/o;const C=ou._round(r*S),T=ou._round(o*k);b=d(...g(C,T));const R=A-b[0],P=_-b[1];oc(this,He)||hc(this,He,[this.x,this.y,this.width,this.height]),this.width=C,this.height=T,this.x=R,this.y=P,this.setDims(s*C,i*T),this.fixAndSetPosition(),this._onResizing()},ls=function(){var t;hc(this,We,{savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height}),null==(t=oc(this,Re))||t.toggle(!1),this.parent.togglePointerEvents(!1)},hs=function(t,e,s){let i=s/e*.7+1-.7;if(1===i)return;const n=cc(this,Je,ss).call(this,this.rotation),a=(t,e)=>[n[0]*t+n[2]*e,n[1]*t+n[3]*e],[r,o]=this.parentDimensions,l=this.x,h=this.y,c=this.width,d=this.height,u=ou.MIN_SIZE/r,p=ou.MIN_SIZE/o;i=Math.max(Math.min(i,1/c,1/d),u/c,p/d);const g=ou._round(c*i),f=ou._round(d*i);if(g===c&&f===d)return;oc(this,He)||hc(this,He,[l,h,c,d]);const m=a(c/2,d/2),v=ou._round(l+m[0]),w=ou._round(h+m[1]),b=a(g/2,f/2);this.x=v-b[0],this.y=w-b[1],this.width=g,this.height=f,this.setDims(r*g,o*f),this.fixAndSetPosition(),this._onResizing()},cs=function(){var t;null==(t=oc(this,Re))||t.toggle(!0),this.parent.togglePointerEvents(!0),cc(this,Je,rs).call(this)},ds=function(t){const{isMac:e}=ud.platform;t.ctrlKey&&!e||t.shiftKey||t.metaKey&&e?this.parent.toggleSelected(this):this.parent.setSelected(this)},us=function(t){const{isSelected:e}=this;this._uiManager.setUpDragSession();let s=!1;const i=new AbortController,n=this._uiManager.combinedSignal(i),a={capture:!0,passive:!1,signal:n},r=t=>{i.abort(),hc(this,Ie,null),hc(this,$e,!1),this._uiManager.endDragSession()||cc(this,Je,ds).call(this,t),s&&this._onStopDragging()};e&&(hc(this,Ve,t.clientX),hc(this,qe,t.clientY),hc(this,Ie,t.pointerId),hc(this,Le,t.pointerType),window.addEventListener("pointermove",t=>{s||(s=!0,this._onStartDragging());const{clientX:e,clientY:i,pointerId:n}=t;if(n!==oc(this,Ie))return void Nd(t);const[a,r]=this.screenToPageTranslation(e-oc(this,Ve),i-oc(this,qe));hc(this,Ve,e),hc(this,qe,i),this._uiManager.dragSelectedEditors(a,r)},a),window.addEventListener("touchmove",Nd,a),window.addEventListener("pointerdown",t=>{t.pointerType===oc(this,Le)&&(oc(this,Ye)||t.isPrimary)&&r(t),Nd(t)},a));const o=t=>{oc(this,Ie)&&oc(this,Ie)!==t.pointerId?Nd(t):r(t)};window.addEventListener("pointerup",o,{signal:n}),window.addEventListener("blur",o,{signal:n})},ps=function(){if(oc(this,Oe)||!this.div)return;hc(this,Oe,new AbortController);const t=this._uiManager.combinedSignal(oc(this,Oe));this.div.addEventListener("focusin",this.focusin.bind(this),{signal:t}),this.div.addEventListener("focusout",this.focusout.bind(this),{signal:t})},gs=function(t){ou._resizerKeyboardManager.exec(this,t)},fs=function(t){var e;oc(this,je)&&(null==(e=t.relatedTarget)?void 0:e.parentNode)!==oc(this,Ne)&&cc(this,Je,ws).call(this)},ms=function(t){hc(this,Be,oc(this,je)?t:"")},vs=function(t){if(oc(this,Te))for(const e of oc(this,Te))e.tabIndex=t},ws=function(){hc(this,je,!1),cc(this,Je,vs).call(this,-1),cc(this,Je,rs).call(this)},lc(ou,ts),ac(ou,"_l10n",null),ac(ou,"_l10nResizer",null),ac(ou,"_borderLineWidth",-1),ac(ou,"_colorManager",new tu),ac(ou,"_zIndex",1),ac(ou,"_telemetryTimeout",1e3);let lu=ou;class hu extends lu{constructor(t){super(t),this.annotationElementId=t.annotationElementId,this.deleted=!0}serialize(){return this.serializeDeleted()}}const cu=3285377520,du=4294901760,uu=65535;class pu{constructor(t){this.h1=t?4294967295&t:cu,this.h2=t?4294967295&t:cu}update(t){let e,s;if("string"==typeof t){e=new Uint8Array(2*t.length),s=0;for(let i=0,n=t.length;i<n;i++){const n=t.charCodeAt(i);n<=255?e[s++]=n:(e[s++]=n>>>8,e[s++]=255&n)}}else{if(!ArrayBuffer.isView(t))throw new Error("Invalid data format, must be a string or TypedArray.");e=t.slice(),s=e.byteLength}const i=s>>2,n=s-4*i,a=new Uint32Array(e.buffer,0,i);let r=0,o=0,l=this.h1,h=this.h2;const c=3432918353,d=461845907,u=11601,p=13715;for(let g=0;g<i;g++)1&g?(r=a[g],r=r*c&du|r*u&uu,r=r<<15|r>>>17,r=r*d&du|r*p&uu,l^=r,l=l<<13|l>>>19,l=5*l+3864292196):(o=a[g],o=o*c&du|o*u&uu,o=o<<15|o>>>17,o=o*d&du|o*p&uu,h^=o,h=h<<13|h>>>19,h=5*h+3864292196);switch(r=0,n){case 3:r^=e[4*i+2]<<16;case 2:r^=e[4*i+1]<<8;case 1:r^=e[4*i],r=r*c&du|r*u&uu,r=r<<15|r>>>17,r=r*d&du|r*p&uu,1&i?l^=r:h^=r}this.h1=l,this.h2=h}hexdigest(){let t=this.h1,e=this.h2;return t^=e>>>1,t=3981806797*t&du|36045*t&uu,e=4283543511*e&du|(2950163797*(e<<16|t>>>16)&du)>>>16,t^=e>>>1,t=444984403*t&du|60499*t&uu,e=3301882366*e&du|(3120437893*(e<<16|t>>>16)&du)>>>16,t^=e>>>1,(t>>>0).toString(16).padStart(8,"0")+(e>>>0).toString(16).padStart(8,"0")}}const gu=Object.freeze({map:null,hash:"",transfer:void 0});class fu{constructor(){lc(this,ys),lc(this,bs,!1),lc(this,As,null),lc(this,_s,new Map),this.onSetModified=null,this.onResetModified=null,this.onAnnotationEditor=null}getValue(t,e){const s=oc(this,_s).get(t);return void 0===s?e:Object.assign(e,s)}getRawValue(t){return oc(this,_s).get(t)}remove(t){if(oc(this,_s).delete(t),0===oc(this,_s).size&&this.resetModified(),"function"==typeof this.onAnnotationEditor){for(const t of oc(this,_s).values())if(t instanceof lu)return;this.onAnnotationEditor(null)}}setValue(t,e){const s=oc(this,_s).get(t);let i=!1;if(void 0!==s)for(const[n,a]of Object.entries(e))s[n]!==a&&(i=!0,s[n]=a);else i=!0,oc(this,_s).set(t,e);i&&cc(this,ys,xs).call(this),e instanceof lu&&"function"==typeof this.onAnnotationEditor&&this.onAnnotationEditor(e.constructor._type)}has(t){return oc(this,_s).has(t)}get size(){return oc(this,_s).size}resetModified(){oc(this,bs)&&(hc(this,bs,!1),"function"==typeof this.onResetModified&&this.onResetModified())}get print(){return new mu(this)}get serializable(){if(0===oc(this,_s).size)return gu;const t=new Map,e=new pu,s=[],i=Object.create(null);let n=!1;for(const[a,r]of oc(this,_s)){const s=r instanceof lu?r.serialize(!1,i):r;s&&(t.set(a,s),e.update(`${a}:${JSON.stringify(s)}`),n||(n=!!s.bitmap))}if(n)for(const a of t.values())a.bitmap&&s.push(a.bitmap);return t.size>0?{map:t,hash:e.hexdigest(),transfer:s}:gu}get editorStats(){let t=null;const e=new Map;for(const s of oc(this,_s).values()){if(!(s instanceof lu))continue;const i=s.telemetryFinalData;if(!i)continue;const{type:n}=i;e.has(n)||e.set(n,Object.getPrototypeOf(s).constructor),t||(t=Object.create(null));const a=t[n]||(t[n]=new Map);for(const[t,e]of Object.entries(i)){if("type"===t)continue;let s=a.get(t);s||(s=new Map,a.set(t,s));const i=s.get(e)??0;s.set(e,i+1)}}for(const[s,i]of e)t[s]=i.computeTelemetryFinalData(t[s]);return t}resetModifiedIds(){hc(this,As,null)}get modifiedIds(){if(oc(this,As))return oc(this,As);const t=[];for(const e of oc(this,_s).values())e instanceof lu&&e.annotationElementId&&e.serialize()&&t.push(e.annotationElementId);return hc(this,As,{ids:new Set(t),hash:t.join(",")})}[Symbol.iterator](){return oc(this,_s).entries()}}bs=new WeakMap,As=new WeakMap,_s=new WeakMap,ys=new WeakSet,xs=function(){oc(this,bs)||(hc(this,bs,!0),"function"==typeof this.onSetModified&&this.onSetModified())};class mu extends fu{constructor(t){super(),lc(this,Ss);const{map:e,hash:s,transfer:i}=t.serializable,n=structuredClone(e,i?{transfer:i}:null);hc(this,Ss,{map:n,hash:s,transfer:i})}get print(){Jc("Should not call PrintAnnotationStorage.print")}get serializable(){return oc(this,Ss)}get modifiedIds(){return sd(this,"modifiedIds",{ids:new Set,hash:""})}}Ss=new WeakMap;class vu{constructor({ownerDocument:t=globalThis.document,styleElement:e=null}){lc(this,ks,new Set),this._document=t,this.nativeFontFaces=new Set,this.styleElement=null,this.loadingRequests=[],this.loadTestFontId=0}addNativeFontFace(t){this.nativeFontFaces.add(t),this._document.fonts.add(t)}removeNativeFontFace(t){this.nativeFontFaces.delete(t),this._document.fonts.delete(t)}insertRule(t){this.styleElement||(this.styleElement=this._document.createElement("style"),this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement));const e=this.styleElement.sheet;e.insertRule(t,e.cssRules.length)}clear(){for(const t of this.nativeFontFaces)this._document.fonts.delete(t);this.nativeFontFaces.clear(),oc(this,ks).clear(),this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}async loadSystemFont({systemFontInfo:t,disableFontFace:e,_inspectFont:s}){if(t&&!oc(this,ks).has(t.loadedName)){if(Zc(!e,"loadSystemFont shouldn't be called when `disableFontFace` is set."),this.isFontLoadingAPISupported){const{loadedName:e,src:i,style:n}=t,a=new FontFace(e,i,n);this.addNativeFontFace(a);try{await a.load(),oc(this,ks).add(e),null==s||s(t)}catch{Qc(`Cannot load system font: ${t.baseFontName}, installing it could help to improve PDF rendering.`),this.removeNativeFontFace(a)}return}Jc("Not implemented: loadSystemFont without the Font Loading API.")}}async bind(t){if(t.attached||t.missingFile&&!t.systemFontInfo)return;if(t.attached=!0,t.systemFontInfo)return void(await this.loadSystemFont(t));if(this.isFontLoadingAPISupported){const e=t.createNativeFontFace();if(e){this.addNativeFontFace(e);try{await e.loaded}catch(s){throw Qc(`Failed to load font '${e.family}': '${s}'.`),t.disableFontFace=!0,s}}return}const e=t.createFontFaceRule();if(e){if(this.insertRule(e),this.isSyncFontLoadingSupported)return;await new Promise(e=>{const s=this._queueLoadingCallback(e);this._prepareFontLoadEvent(t,s)})}}get isFontLoadingAPISupported(){var t;return sd(this,"isFontLoadingAPISupported",!!(null==(t=this._document)?void 0:t.fonts))}get isSyncFontLoadingSupported(){return sd(this,"isSyncFontLoadingSupported",uc||ud.platform.isFirefox)}_queueLoadingCallback(t){const{loadingRequests:e}=this,s={done:!1,complete:function(){for(Zc(!s.done,"completeRequest() cannot be called twice."),s.done=!0;e.length>0&&e[0].done;){const t=e.shift();setTimeout(t.callback,0)}},callback:t};return e.push(s),s}get _loadTestFont(){return sd(this,"_loadTestFont",atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA=="))}_prepareFontLoadEvent(t,e){function s(t,e){return t.charCodeAt(e)<<24|t.charCodeAt(e+1)<<16|t.charCodeAt(e+2)<<8|255&t.charCodeAt(e+3)}function i(t,e,s,i){return t.substring(0,e)+i+t.substring(e+s)}let n,a;const r=this._document.createElement("canvas");r.width=1,r.height=1;const o=r.getContext("2d");let l=0;const h=`lt${Date.now()}${this.loadTestFontId++}`;let c=this._loadTestFont;c=i(c,976,h.length,h);const d=1482184792;let u=s(c,16);for(n=0,a=h.length-3;n<a;n+=4)u=u-d+s(h,n)|0;var p;n<h.length&&(u=u-d+s(h+"XXX",n)|0),c=i(c,16,4,(p=u,String.fromCharCode(p>>24&255,p>>16&255,p>>8&255,255&p)));const g=`@font-face {font-family:"${h}";src:${`url(data:font/opentype;base64,${btoa(c)});`}}`;this.insertRule(g);const f=this._document.createElement("div");f.style.visibility="hidden",f.style.width=f.style.height="10px",f.style.position="absolute",f.style.top=f.style.left="0px";for(const m of[t.loadedName,h]){const t=this._document.createElement("span");t.textContent="Hi",t.style.fontFamily=m,f.append(t)}this._document.body.append(f),function t(e,s){if(++l>30)return Qc("Load test font never loaded."),void s();o.font="30px "+e,o.fillText(".",0,20),o.getImageData(0,0,1,1).data[3]>0?s():setTimeout(t.bind(null,e,s))}(h,()=>{f.remove(),e.complete()})}}ks=new WeakMap;class wu{constructor(t,e=null){this.compiledGlyphs=Object.create(null);for(const s in t)this[s]=t[s];this._inspectFont=e}createNativeFontFace(){var t;if(!this.data||this.disableFontFace)return null;let e;if(this.cssFontInfo){const t={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(t.style=`oblique ${this.cssFontInfo.italicAngle}deg`),e=new FontFace(this.cssFontInfo.fontFamily,this.data,t)}else e=new FontFace(this.loadedName,this.data,{});return null==(t=this._inspectFont)||t.call(this,this),e}createFontFaceRule(){var t;if(!this.data||this.disableFontFace)return null;const e=`url(data:${this.mimetype};base64,${_d(this.data)});`;let s;if(this.cssFontInfo){let t=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(t+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`),s=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${t}src:${e}}`}else s=`@font-face {font-family:"${this.loadedName}";src:${e}}`;return null==(t=this._inspectFont)||t.call(this,this,e),s}getPathGenerator(t,e){if(void 0!==this.compiledGlyphs[e])return this.compiledGlyphs[e];const s=this.loadedName+"_path_"+e;let i;try{i=t.get(s)}catch(a){Qc(`getPathGenerator - ignoring character: "${a}".`)}const n=new Path2D(i||"");return this.fontExtraProperties||t.delete(s),this.compiledGlyphs[e]=n}}function bu(t){if("string"!=typeof t)return null;if(t.endsWith("/"))return t;throw new Error(`Invalid factory url: "${t}" must include trailing slash.`)}const Au=t=>"object"==typeof t&&Number.isInteger(null==t?void 0:t.num)&&t.num>=0&&Number.isInteger(null==t?void 0:t.gen)&&t.gen>=0,_u=function(t,e,s){if(!Array.isArray(s)||s.length<2)return!1;const[i,n,...a]=s;if(!t(i)&&!Number.isInteger(i))return!1;if(!e(n))return!1;const r=a.length;let o=!0;switch(n.name){case"XYZ":if(r<2||r>3)return!1;break;case"Fit":case"FitB":return 0===r;case"FitH":case"FitBH":case"FitV":case"FitBV":if(r>1)return!1;break;case"FitR":if(4!==r)return!1;o=!1;break;default:return!1}for(const l of a)if(!("number"==typeof l||o&&null===l))return!1;return!0}.bind(null,Au,t=>"object"==typeof t&&"string"==typeof(null==t?void 0:t.name));class yu{constructor(){lc(this,Ms,new Map),lc(this,Es,Promise.resolve())}postMessage(t,e){const s={data:structuredClone(t,e?{transfer:e}:null)};oc(this,Es).then(()=>{for(const[t]of oc(this,Ms))t.call(this,s)})}addEventListener(t,e,s=null){let i=null;if((null==s?void 0:s.signal)instanceof AbortSignal){const{signal:n}=s;if(n.aborted)return void Qc("LoopbackPort - cannot use an `aborted` signal.");const a=()=>this.removeEventListener(t,e);i=()=>n.removeEventListener("abort",a),n.addEventListener("abort",a)}oc(this,Ms).set(e,i)}removeEventListener(t,e){const s=oc(this,Ms).get(e);null==s||s(),oc(this,Ms).delete(e)}terminate(){for(const[,t]of oc(this,Ms))null==t||t();oc(this,Ms).clear()}}Ms=new WeakMap,Es=new WeakMap;const xu=1,Su=2,ku=1,Mu=2,Eu=3,Cu=4,Tu=5,Ru=6,Pu=7,Iu=8;function Lu(){}function Du(t){if(t instanceof hd||t instanceof rd||t instanceof nd||t instanceof od||t instanceof ad)return t;switch(t instanceof Error||"object"==typeof t&&null!==t||Jc('wrapReason: Expected "reason" to be a (possibly cloned) Error.'),t.name){case"AbortException":return new hd(t.message);case"InvalidPDFException":return new rd(t.message);case"PasswordException":return new nd(t.message,t.code);case"ResponseException":return new od(t.message,t.status,t.missing);case"UnknownErrorException":return new ad(t.message,t.details)}return new ad(t.message,t.toString())}class Nu{constructor(t,e,s){lc(this,Ts),lc(this,Cs,new AbortController),this.sourceName=t,this.targetName=e,this.comObj=s,this.callbackId=1,this.streamId=1,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null),this.callbackCapabilities=Object.create(null),this.actionHandler=Object.create(null),s.addEventListener("message",cc(this,Ts,Rs).bind(this),{signal:oc(this,Cs).signal})}on(t,e){const s=this.actionHandler;if(s[t])throw new Error(`There is already an actionName called "${t}"`);s[t]=e}send(t,e,s){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,data:e},s)}sendWithPromise(t,e,s){const i=this.callbackId++,n=Promise.withResolvers();this.callbackCapabilities[i]=n;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,callbackId:i,data:e},s)}catch(a){n.reject(a)}return n.promise}sendWithStream(t,e,s,i){const n=this.streamId++,a=this.sourceName,r=this.targetName,o=this.comObj;return new ReadableStream({start:s=>{const l=Promise.withResolvers();return this.streamControllers[n]={controller:s,startCall:l,pullCall:null,cancelCall:null,isClosed:!1},o.postMessage({sourceName:a,targetName:r,action:t,streamId:n,data:e,desiredSize:s.desiredSize},i),l.promise},pull:t=>{const e=Promise.withResolvers();return this.streamControllers[n].pullCall=e,o.postMessage({sourceName:a,targetName:r,stream:Ru,streamId:n,desiredSize:t.desiredSize}),e.promise},cancel:t=>{Zc(t instanceof Error,"cancel must have a valid reason");const e=Promise.withResolvers();return this.streamControllers[n].cancelCall=e,this.streamControllers[n].isClosed=!0,o.postMessage({sourceName:a,targetName:r,stream:ku,streamId:n,reason:Du(t)}),e.promise}},s)}destroy(){var t;null==(t=oc(this,Cs))||t.abort(),hc(this,Cs,null)}}Cs=new WeakMap,Ts=new WeakSet,Rs=function({data:t}){if(t.targetName!==this.sourceName)return;if(t.stream)return void cc(this,Ts,Is).call(this,t);if(t.callback){const e=t.callbackId,s=this.callbackCapabilities[e];if(!s)throw new Error(`Cannot resolve callback ${e}`);if(delete this.callbackCapabilities[e],t.callback===xu)s.resolve(t.data);else{if(t.callback!==Su)throw new Error("Unexpected callback case");s.reject(Du(t.reason))}return}const e=this.actionHandler[t.action];if(!e)throw new Error(`Unknown action from worker: ${t.action}`);if(t.callbackId){const s=this.sourceName,i=t.sourceName,n=this.comObj;return void Promise.try(e,t.data).then(function(e){n.postMessage({sourceName:s,targetName:i,callback:xu,callbackId:t.callbackId,data:e})},function(e){n.postMessage({sourceName:s,targetName:i,callback:Su,callbackId:t.callbackId,reason:Du(e)})})}t.streamId?cc(this,Ts,Ps).call(this,t):e(t.data)},Ps=function(t){const e=t.streamId,s=this.sourceName,i=t.sourceName,n=this.comObj,a=this,r=this.actionHandler[t.action],o={enqueue(t,a=1,r){if(this.isCancelled)return;const o=this.desiredSize;this.desiredSize-=a,o>0&&this.desiredSize<=0&&(this.sinkCapability=Promise.withResolvers(),this.ready=this.sinkCapability.promise),n.postMessage({sourceName:s,targetName:i,stream:Cu,streamId:e,chunk:t},r)},close(){this.isCancelled||(this.isCancelled=!0,n.postMessage({sourceName:s,targetName:i,stream:Eu,streamId:e}),delete a.streamSinks[e])},error(t){Zc(t instanceof Error,"error must have a valid reason"),this.isCancelled||(this.isCancelled=!0,n.postMessage({sourceName:s,targetName:i,stream:Tu,streamId:e,reason:Du(t)}))},sinkCapability:Promise.withResolvers(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:t.desiredSize,ready:null};o.sinkCapability.resolve(),o.ready=o.sinkCapability.promise,this.streamSinks[e]=o,Promise.try(r,t.data,o).then(function(){n.postMessage({sourceName:s,targetName:i,stream:Iu,streamId:e,success:!0})},function(t){n.postMessage({sourceName:s,targetName:i,stream:Iu,streamId:e,reason:Du(t)})})},Is=function(t){const e=t.streamId,s=this.sourceName,i=t.sourceName,n=this.comObj,a=this.streamControllers[e],r=this.streamSinks[e];switch(t.stream){case Iu:t.success?a.startCall.resolve():a.startCall.reject(Du(t.reason));break;case Pu:t.success?a.pullCall.resolve():a.pullCall.reject(Du(t.reason));break;case Ru:if(!r){n.postMessage({sourceName:s,targetName:i,stream:Pu,streamId:e,success:!0});break}r.desiredSize<=0&&t.desiredSize>0&&r.sinkCapability.resolve(),r.desiredSize=t.desiredSize,Promise.try(r.onPull||Lu).then(function(){n.postMessage({sourceName:s,targetName:i,stream:Pu,streamId:e,success:!0})},function(t){n.postMessage({sourceName:s,targetName:i,stream:Pu,streamId:e,reason:Du(t)})});break;case Cu:if(Zc(a,"enqueue should have stream controller"),a.isClosed)break;a.controller.enqueue(t.chunk);break;case Eu:if(Zc(a,"close should have stream controller"),a.isClosed)break;a.isClosed=!0,a.controller.close(),cc(this,Ts,Ls).call(this,a,e);break;case Tu:Zc(a,"error should have stream controller"),a.controller.error(Du(t.reason)),cc(this,Ts,Ls).call(this,a,e);break;case Mu:t.success?a.cancelCall.resolve():a.cancelCall.reject(Du(t.reason)),cc(this,Ts,Ls).call(this,a,e);break;case ku:if(!r)break;const o=Du(t.reason);Promise.try(r.onCancel||Lu,o).then(function(){n.postMessage({sourceName:s,targetName:i,stream:Mu,streamId:e,success:!0})},function(t){n.postMessage({sourceName:s,targetName:i,stream:Mu,streamId:e,reason:Du(t)})}),r.sinkCapability.reject(o),r.isCancelled=!0,delete this.streamSinks[e];break;default:throw new Error("Unexpected stream case")}},Ls=async function(t,e){var s,i,n;await Promise.allSettled([null==(s=t.startCall)?void 0:s.promise,null==(i=t.pullCall)?void 0:i.promise,null==(n=t.cancelCall)?void 0:n.promise]),delete this.streamControllers[e]};class Fu{constructor({enableHWA:t=!1}){lc(this,Ds,!1),hc(this,Ds,t)}create(t,e){if(t<=0||e<=0)throw new Error("Invalid canvas size");const s=this._createCanvas(t,e);return{canvas:s,context:s.getContext("2d",{willReadFrequently:!oc(this,Ds)})}}reset(t,e,s){if(!t.canvas)throw new Error("Canvas is not specified");if(e<=0||s<=0)throw new Error("Invalid canvas size");t.canvas.width=e,t.canvas.height=s}destroy(t){if(!t.canvas)throw new Error("Canvas is not specified");t.canvas.width=0,t.canvas.height=0,t.canvas=null,t.context=null}_createCanvas(t,e){Jc("Abstract method `_createCanvas` called.")}}Ds=new WeakMap;class Wu extends Fu{constructor({ownerDocument:t=globalThis.document,enableHWA:e=!1}){super({enableHWA:e}),this._document=t}_createCanvas(t,e){const s=this._document.createElement("canvas");return s.width=t,s.height=e,s}}class Ou{constructor({baseUrl:t=null,isCompressed:e=!0}){this.baseUrl=t,this.isCompressed=e}async fetch({name:t}){if(!this.baseUrl)throw new Error("Ensure that the `cMapUrl` and `cMapPacked` API parameters are provided.");if(!t)throw new Error("CMap name must be specified.");const e=this.baseUrl+t+(this.isCompressed?".bcmap":"");return this._fetch(e).then(t=>({cMapData:t,isCompressed:this.isCompressed})).catch(t=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${e}`)})}async _fetch(t){Jc("Abstract method `_fetch` called.")}}class Bu extends Ou{async _fetch(t){const e=await kd(t,this.isCompressed?"arraybuffer":"text");return e instanceof ArrayBuffer?new Uint8Array(e):dd(e)}}class $u{addFilter(t){return"none"}addHCMFilter(t,e){return"none"}addAlphaFilter(t){return"none"}addLuminosityFilter(t){return"none"}addHighlightHCMFilter(t,e,s,i,n){return"none"}destroy(t=!1){}}class Hu extends $u{constructor({docId:t,ownerDocument:e=globalThis.document}){super(),lc(this,zs),lc(this,Ns),lc(this,Fs),lc(this,Ws),lc(this,Os),lc(this,Bs),lc(this,$s),lc(this,Hs,0),hc(this,Os,t),hc(this,Bs,e)}addFilter(t){if(!t)return"none";let e=oc(this,zs,Gs).get(t);if(e)return e;const[s,i,n]=cc(this,zs,Vs).call(this,t),a=1===t.length?s:`${s}${i}${n}`;if(e=oc(this,zs,Gs).get(a),e)return oc(this,zs,Gs).set(t,e),e;const r=`g_${oc(this,Os)}_transfer_map_${dc(this,Hs)._++}`,o=cc(this,zs,qs).call(this,r);oc(this,zs,Gs).set(t,o),oc(this,zs,Gs).set(a,o);const l=cc(this,zs,Ks).call(this,r);return cc(this,zs,Js).call(this,s,i,n,l),o}addHCMFilter(t,e){var s;const i=`${t}-${e}`,n="base";let a=oc(this,zs,js).get(n);if((null==a?void 0:a.key)===i)return a.url;if(a?(null==(s=a.filter)||s.remove(),a.key=i,a.url="none",a.filter=null):(a={key:i,url:"none",filter:null},oc(this,zs,js).set(n,a)),!t||!e)return a.url;const r=cc(this,zs,ti).call(this,t);t=gd.makeHexColor(...r);const o=cc(this,zs,ti).call(this,e);if(e=gd.makeHexColor(...o),oc(this,zs,Us).style.color="","#000000"===t&&"#ffffff"===e||t===e)return a.url;const l=new Array(256);for(let p=0;p<=255;p++){const t=p/255;l[p]=t<=.03928?t/12.92:((t+.055)/1.055)**2.4}const h=l.join(","),c=`g_${oc(this,Os)}_hcm_filter`,d=a.filter=cc(this,zs,Ks).call(this,c);cc(this,zs,Js).call(this,h,h,h,d),cc(this,zs,Ys).call(this,d);const u=(t,e)=>{const s=r[t]/255,i=o[t]/255,n=new Array(e+1);for(let a=0;a<=e;a++)n[a]=s+a/e*(i-s);return n.join(",")};return cc(this,zs,Js).call(this,u(0,5),u(1,5),u(2,5),d),a.url=cc(this,zs,qs).call(this,c),a.url}addAlphaFilter(t){let e=oc(this,zs,Gs).get(t);if(e)return e;const[s]=cc(this,zs,Vs).call(this,[t]),i=`alpha_${s}`;if(e=oc(this,zs,Gs).get(i),e)return oc(this,zs,Gs).set(t,e),e;const n=`g_${oc(this,Os)}_alpha_map_${dc(this,Hs)._++}`,a=cc(this,zs,qs).call(this,n);oc(this,zs,Gs).set(t,a),oc(this,zs,Gs).set(i,a);const r=cc(this,zs,Ks).call(this,n);return cc(this,zs,Zs).call(this,s,r),a}addLuminosityFilter(t){let e,s,i=oc(this,zs,Gs).get(t||"luminosity");if(i)return i;if(t?([e]=cc(this,zs,Vs).call(this,[t]),s=`luminosity_${e}`):s="luminosity",i=oc(this,zs,Gs).get(s),i)return oc(this,zs,Gs).set(t,i),i;const n=`g_${oc(this,Os)}_luminosity_map_${dc(this,Hs)._++}`,a=cc(this,zs,qs).call(this,n);oc(this,zs,Gs).set(t,a),oc(this,zs,Gs).set(s,a);const r=cc(this,zs,Ks).call(this,n);return cc(this,zs,Xs).call(this,r),t&&cc(this,zs,Zs).call(this,e,r),a}addHighlightHCMFilter(t,e,s,i,n){var a;const r=`${e}-${s}-${i}-${n}`;let o=oc(this,zs,js).get(t);if((null==o?void 0:o.key)===r)return o.url;if(o?(null==(a=o.filter)||a.remove(),o.key=r,o.url="none",o.filter=null):(o={key:r,url:"none",filter:null},oc(this,zs,js).set(t,o)),!e||!s)return o.url;const[l,h]=[e,s].map(cc(this,zs,ti).bind(this));let c=Math.round(.2126*l[0]+.7152*l[1]+.0722*l[2]),d=Math.round(.2126*h[0]+.7152*h[1]+.0722*h[2]),[u,p]=[i,n].map(cc(this,zs,ti).bind(this));d<c&&([c,d,u,p]=[d,c,p,u]),oc(this,zs,Us).style.color="";const g=(t,e,s)=>{const i=new Array(256),n=(d-c)/s,a=t/255,r=(e-t)/(255*s);let o=0;for(let l=0;l<=s;l++){const t=Math.round(c+l*n),e=a+l*r;for(let s=o;s<=t;s++)i[s]=e;o=t+1}for(let l=o;l<256;l++)i[l]=i[o-1];return i.join(",")},f=`g_${oc(this,Os)}_hcm_${t}_filter`,m=o.filter=cc(this,zs,Ks).call(this,f);return cc(this,zs,Ys).call(this,m),cc(this,zs,Js).call(this,g(u[0],p[0],5),g(u[1],p[1],5),g(u[2],p[2],5),m),o.url=cc(this,zs,qs).call(this,f),o.url}destroy(t=!1){var e,s,i,n;t&&(null==(e=oc(this,$s))?void 0:e.size)||(null==(s=oc(this,Ws))||s.parentNode.parentNode.remove(),hc(this,Ws,null),null==(i=oc(this,Fs))||i.clear(),hc(this,Fs,null),null==(n=oc(this,$s))||n.clear(),hc(this,$s,null),hc(this,Hs,0))}}Ns=new WeakMap,Fs=new WeakMap,Ws=new WeakMap,Os=new WeakMap,Bs=new WeakMap,$s=new WeakMap,Hs=new WeakMap,zs=new WeakSet,Gs=function(){return oc(this,Fs)||hc(this,Fs,new Map)},js=function(){return oc(this,$s)||hc(this,$s,new Map)},Us=function(){if(!oc(this,Ws)){const t=oc(this,Bs).createElement("div"),{style:e}=t;e.visibility="hidden",e.contain="strict",e.width=e.height=0,e.position="absolute",e.top=e.left=0,e.zIndex=-1;const s=oc(this,Bs).createElementNS(yd,"svg");s.setAttribute("width",0),s.setAttribute("height",0),hc(this,Ws,oc(this,Bs).createElementNS(yd,"defs")),t.append(s),s.append(oc(this,Ws)),oc(this,Bs).body.append(t)}return oc(this,Ws)},Vs=function(t){if(1===t.length){const e=t[0],s=new Array(256);for(let t=0;t<256;t++)s[t]=e[t]/255;const i=s.join(",");return[i,i,i]}const[e,s,i]=t,n=new Array(256),a=new Array(256),r=new Array(256);for(let o=0;o<256;o++)n[o]=e[o]/255,a[o]=s[o]/255,r[o]=i[o]/255;return[n.join(","),a.join(","),r.join(",")]},qs=function(t){if(void 0===oc(this,Ns)){hc(this,Ns,"");const t=oc(this,Bs).URL;t!==oc(this,Bs).baseURI&&(Cd(t)?Qc('#createUrl: ignore "data:"-URL for performance reasons.'):hc(this,Ns,ed(t,"")))}return`url(${oc(this,Ns)}#${t})`},Xs=function(t){const e=oc(this,Bs).createElementNS(yd,"feColorMatrix");e.setAttribute("type","matrix"),e.setAttribute("values","0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0.59 0.11 0 0"),t.append(e)},Ys=function(t){const e=oc(this,Bs).createElementNS(yd,"feColorMatrix");e.setAttribute("type","matrix"),e.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0"),t.append(e)},Ks=function(t){const e=oc(this,Bs).createElementNS(yd,"filter");return e.setAttribute("color-interpolation-filters","sRGB"),e.setAttribute("id",t),oc(this,zs,Us).append(e),e},Qs=function(t,e,s){const i=oc(this,Bs).createElementNS(yd,e);i.setAttribute("type","discrete"),i.setAttribute("tableValues",s),t.append(i)},Js=function(t,e,s,i){const n=oc(this,Bs).createElementNS(yd,"feComponentTransfer");i.append(n),cc(this,zs,Qs).call(this,n,"feFuncR",t),cc(this,zs,Qs).call(this,n,"feFuncG",e),cc(this,zs,Qs).call(this,n,"feFuncB",s)},Zs=function(t,e){const s=oc(this,Bs).createElementNS(yd,"feComponentTransfer");e.append(s),cc(this,zs,Qs).call(this,s,"feFuncA",t)},ti=function(t){return oc(this,zs,Us).style.color=t,Od(getComputedStyle(oc(this,zs,Us)).getPropertyValue("color"))};class zu{constructor({baseUrl:t=null}){this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error("Ensure that the `standardFontDataUrl` API parameter is provided.");if(!t)throw new Error("Font filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetch(e).catch(t=>{throw new Error(`Unable to load font data at: ${e}`)})}async _fetch(t){Jc("Abstract method `_fetch` called.")}}class Gu extends zu{async _fetch(t){const e=await kd(t,"arraybuffer");return new Uint8Array(e)}}class ju{constructor({baseUrl:t=null}){this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error("Ensure that the `wasmUrl` API parameter is provided.");if(!t)throw new Error("Wasm filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetch(e).catch(t=>{throw new Error(`Unable to load wasm data at: ${e}`)})}async _fetch(t){Jc("Abstract method `_fetch` called.")}}class Uu extends ju{async _fetch(t){const e=await kd(t,"arraybuffer");return new Uint8Array(e)}}async function Vu(t){const e=process.getBuiltinModule("fs"),s=await e.promises.readFile(t);return new Uint8Array(s)}uc&&Qc("Please use the `legacy` build in Node.js environments.");class qu extends $u{}class Xu extends Fu{_createCanvas(t,e){return process.getBuiltinModule("module").createRequire(import.meta.url)("@napi-rs/canvas").createCanvas(t,e)}}class Yu extends Ou{async _fetch(t){return Vu(t)}}class Ku extends zu{async _fetch(t){return Vu(t)}}class Qu extends ju{async _fetch(t){return Vu(t)}}const Ju="Fill",Zu="Stroke",tp="Shading";function ep(t,e){if(!e)return;const s=e[2]-e[0],i=e[3]-e[1],n=new Path2D;n.rect(e[0],e[1],s,i),t.clip(n)}class sp{isModifyingCurrentTransform(){return!1}getPattern(){Jc("Abstract method `getPattern` called.")}}class ip extends sp{constructor(t){super(),this._type=t[1],this._bbox=t[2],this._colorStops=t[3],this._p0=t[4],this._p1=t[5],this._r0=t[6],this._r1=t[7],this.matrix=null}_createGradient(t){let e;"axial"===this._type?e=t.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):"radial"===this._type&&(e=t.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const s of this._colorStops)e.addColorStop(s[0],s[1]);return e}getPattern(t,e,s,i){let n;if(i===Zu||i===Ju){const a=e.current.getClippedPathBoundingBox(i,Bd(t))||[0,0,0,0],r=Math.ceil(a[2]-a[0])||1,o=Math.ceil(a[3]-a[1])||1,l=e.cachedCanvases.getCanvas("pattern",r,o),h=l.context;h.clearRect(0,0,h.canvas.width,h.canvas.height),h.beginPath(),h.rect(0,0,h.canvas.width,h.canvas.height),h.translate(-a[0],-a[1]),s=gd.transform(s,[1,0,0,1,a[0],a[1]]),h.transform(...e.baseTransform),this.matrix&&h.transform(...this.matrix),ep(h,this._bbox),h.fillStyle=this._createGradient(h),h.fill(),n=t.createPattern(l.canvas,"no-repeat");const c=new DOMMatrix(s);n.setTransform(c)}else ep(t,this._bbox),n=this._createGradient(t);return n}}function np(t,e,s,i,n,a,r,o){const l=e.coords,h=e.colors,c=t.data,d=4*t.width;let u;l[s+1]>l[i+1]&&(u=s,s=i,i=u,u=a,a=r,r=u),l[i+1]>l[n+1]&&(u=i,i=n,n=u,u=r,r=o,o=u),l[s+1]>l[i+1]&&(u=s,s=i,i=u,u=a,a=r,r=u);const p=(l[s]+e.offsetX)*e.scaleX,g=(l[s+1]+e.offsetY)*e.scaleY,f=(l[i]+e.offsetX)*e.scaleX,m=(l[i+1]+e.offsetY)*e.scaleY,v=(l[n]+e.offsetX)*e.scaleX,w=(l[n+1]+e.offsetY)*e.scaleY;if(g>=w)return;const b=h[a],A=h[a+1],_=h[a+2],y=h[r],x=h[r+1],S=h[r+2],k=h[o],M=h[o+1],E=h[o+2],C=Math.round(g),T=Math.round(w);let R,P,I,L,D,N,F,W;for(let O=C;O<=T;O++){if(O<m){const t=O<g?0:(g-O)/(g-m);R=p-(p-f)*t,P=b-(b-y)*t,I=A-(A-x)*t,L=_-(_-S)*t}else{let t;t=O>w?1:m===w?0:(m-O)/(m-w),R=f-(f-v)*t,P=y-(y-k)*t,I=x-(x-M)*t,L=S-(S-E)*t}let t;t=O<g?0:O>w?1:(g-O)/(g-w),D=p-(p-v)*t,N=b-(b-k)*t,F=A-(A-M)*t,W=_-(_-E)*t;const e=Math.round(Math.min(R,D)),s=Math.round(Math.max(R,D));let i=d*O+4*e;for(let n=e;n<=s;n++)t=(R-n)/(R-D),t<0?t=0:t>1&&(t=1),c[i++]=P-(P-N)*t|0,c[i++]=I-(I-F)*t|0,c[i++]=L-(L-W)*t|0,c[i++]=255}}function ap(t,e,s){const i=e.coords,n=e.colors;let a,r;switch(e.type){case"lattice":const o=e.verticesPerRow,l=Math.floor(i.length/o)-1,h=o-1;for(a=0;a<l;a++){let e=a*o;for(let a=0;a<h;a++,e++)np(t,s,i[e],i[e+1],i[e+o],n[e],n[e+1],n[e+o]),np(t,s,i[e+o+1],i[e+1],i[e+o],n[e+o+1],n[e+1],n[e+o])}break;case"triangles":for(a=0,r=i.length;a<r;a+=3)np(t,s,i[a],i[a+1],i[a+2],n[a],n[a+1],n[a+2]);break;default:throw new Error("illegal figure")}}class rp extends sp{constructor(t){super(),this._coords=t[2],this._colors=t[3],this._figures=t[4],this._bounds=t[5],this._bbox=t[6],this._background=t[7],this.matrix=null}_createMeshCanvas(t,e,s){const i=Math.floor(this._bounds[0]),n=Math.floor(this._bounds[1]),a=Math.ceil(this._bounds[2])-i,r=Math.ceil(this._bounds[3])-n,o=Math.min(Math.ceil(Math.abs(a*t[0]*1.1)),3e3),l=Math.min(Math.ceil(Math.abs(r*t[1]*1.1)),3e3),h=a/o,c=r/l,d={coords:this._coords,colors:this._colors,offsetX:-i,offsetY:-n,scaleX:1/h,scaleY:1/c},u=o+4,p=l+4,g=s.getCanvas("mesh",u,p),f=g.context,m=f.createImageData(o,l);if(e){const t=m.data;for(let s=0,i=t.length;s<i;s+=4)t[s]=e[0],t[s+1]=e[1],t[s+2]=e[2],t[s+3]=255}for(const v of this._figures)ap(m,v,d);f.putImageData(m,2,2);return{canvas:g.canvas,offsetX:i-2*h,offsetY:n-2*c,scaleX:h,scaleY:c}}isModifyingCurrentTransform(){return!0}getPattern(t,e,s,i){ep(t,this._bbox);const n=new Float32Array(2);if(i===tp)gd.singularValueDecompose2dScale(Bd(t),n);else if(this.matrix){gd.singularValueDecompose2dScale(this.matrix,n);const[t,s]=n;gd.singularValueDecompose2dScale(e.baseTransform,n),n[0]*=t,n[1]*=s}else gd.singularValueDecompose2dScale(e.baseTransform,n);const a=this._createMeshCanvas(n,i===tp?null:this._background,e.cachedCanvases);return i!==tp&&(t.setTransform(...e.baseTransform),this.matrix&&t.transform(...this.matrix)),t.translate(a.offsetX,a.offsetY),t.scale(a.scaleX,a.scaleY),t.createPattern(a.canvas,"no-repeat")}}class op extends sp{getPattern(){return"hotpink"}}const lp=1,hp=2,cp=class t{constructor(t,e,s,i){this.color=t[1],this.operatorList=t[2],this.matrix=t[3],this.bbox=t[4],this.xstep=t[5],this.ystep=t[6],this.paintType=t[7],this.tilingType=t[8],this.ctx=e,this.canvasGraphicsFactory=s,this.baseTransform=i}createPatternCanvas(t){const{bbox:e,operatorList:s,paintType:i,tilingType:n,color:a,canvasGraphicsFactory:r}=this;let{xstep:o,ystep:l}=this;o=Math.abs(o),l=Math.abs(l),Kc("TilingType: "+n);const h=e[0],c=e[1],d=e[2],u=e[3],p=d-h,g=u-c,f=new Float32Array(2);gd.singularValueDecompose2dScale(this.matrix,f);const[m,v]=f;gd.singularValueDecompose2dScale(this.baseTransform,f);const w=m*f[0],b=v*f[1];let A=p,_=g,y=!1,x=!1;const S=Math.ceil(o*w),k=Math.ceil(l*b);S>=Math.ceil(p*w)?A=o:y=!0,k>=Math.ceil(g*b)?_=l:x=!0;const M=this.getSizeAndScale(A,this.ctx.canvas.width,w),E=this.getSizeAndScale(_,this.ctx.canvas.height,b),C=t.cachedCanvases.getCanvas("pattern",M.size,E.size),T=C.context,R=r.createCanvasGraphics(T);if(R.groupLevel=t.groupLevel,this.setFillAndStrokeStyleToContext(R,i,a),T.translate(-M.scale*h,-E.scale*c),R.transform(M.scale,0,0,E.scale,0,0),T.save(),this.clipBbox(R,h,c,d,u),R.baseTransform=Bd(R.ctx),R.executeOperatorList(s),R.endDrawing(),T.restore(),y||x){const e=C.canvas;y&&(A=o),x&&(_=l);const s=this.getSizeAndScale(A,this.ctx.canvas.width,w),i=this.getSizeAndScale(_,this.ctx.canvas.height,b),n=s.size,a=i.size,r=t.cachedCanvases.getCanvas("pattern-workaround",n,a),d=r.context,u=y?Math.floor(p/o):0,f=x?Math.floor(g/l):0;for(let t=0;t<=u;t++)for(let s=0;s<=f;s++)d.drawImage(e,n*t,a*s,n,a,0,0,n,a);return{canvas:r.canvas,scaleX:s.scale,scaleY:i.scale,offsetX:h,offsetY:c}}return{canvas:C.canvas,scaleX:M.scale,scaleY:E.scale,offsetX:h,offsetY:c}}getSizeAndScale(e,s,i){const n=Math.max(t.MAX_PATTERN_SIZE,s);let a=Math.ceil(e*i);return a>=n?a=n:i=a/e,{scale:i,size:a}}clipBbox(t,e,s,i,n){const a=i-e,r=n-s;t.ctx.rect(e,s,a,r),gd.axialAlignedBoundingBox([e,s,i,n],Bd(t.ctx),t.current.minMax),t.clip(),t.endPath()}setFillAndStrokeStyleToContext(t,e,s){const i=t.ctx,n=t.current;switch(e){case lp:const{fillStyle:t,strokeStyle:a}=this.ctx;i.fillStyle=n.fillColor=t,i.strokeStyle=n.strokeColor=a;break;case hp:i.fillStyle=i.strokeStyle=s,n.fillColor=n.strokeColor=s;break;default:throw new ld(`Unsupported paint type: ${e}`)}}isModifyingCurrentTransform(){return!1}getPattern(t,e,s,i){let n=s;i!==tp&&(n=gd.transform(n,e.baseTransform),this.matrix&&(n=gd.transform(n,this.matrix)));const a=this.createPatternCanvas(e);let r=new DOMMatrix(n);r=r.translate(a.offsetX,a.offsetY),r=r.scale(1/a.scaleX,1/a.scaleY);const o=t.createPattern(a.canvas,"repeat");return o.setTransform(r),o}};ac(cp,"MAX_PATTERN_SIZE",3e3);let dp=cp;function up({src:t,srcPos:e=0,dest:s,width:i,height:n,nonBlackColor:a=4294967295,inverseDecode:r=!1}){const o=ud.isLittleEndian?4278190080:255,[l,h]=r?[a,o]:[o,a],c=i>>3,d=7&i,u=t.length;s=new Uint32Array(s.buffer);let p=0;for(let g=0;g<n;g++){for(const n=e+c;e<n;e++){const i=e<u?t[e]:255;s[p++]=128&i?h:l,s[p++]=64&i?h:l,s[p++]=32&i?h:l,s[p++]=16&i?h:l,s[p++]=8&i?h:l,s[p++]=4&i?h:l,s[p++]=2&i?h:l,s[p++]=1&i?h:l}if(0===d)continue;const i=e<u?t[e++]:255;for(let t=0;t<d;t++)s[p++]=i&1<<7-t?h:l}return{srcPos:e,destPos:p}}const pp=16,gp=new DOMMatrix,fp=new Float32Array(2),mp=new Float32Array([1/0,1/0,-1/0,-1/0]);class vp{constructor(t){this.canvasFactory=t,this.cache=Object.create(null)}getCanvas(t,e,s){let i;return void 0!==this.cache[t]?(i=this.cache[t],this.canvasFactory.reset(i,e,s)):(i=this.canvasFactory.create(e,s),this.cache[t]=i),i}delete(t){delete this.cache[t]}clear(){for(const t in this.cache){const e=this.cache[t];this.canvasFactory.destroy(e),delete this.cache[t]}}}function wp(t,e,s,i,n,a,r,o,l,h){const[c,d,u,p,g,f]=Bd(t);if(0===d&&0===u){const m=r*c+g,v=Math.round(m),w=o*p+f,b=Math.round(w),A=(r+l)*c+g,_=Math.abs(Math.round(A)-v)||1,y=(o+h)*p+f,x=Math.abs(Math.round(y)-b)||1;return t.setTransform(Math.sign(c),0,0,Math.sign(p),v,b),t.drawImage(e,s,i,n,a,0,0,_,x),t.setTransform(c,d,u,p,g,f),[_,x]}if(0===c&&0===p){const m=o*u+g,v=Math.round(m),w=r*d+f,b=Math.round(w),A=(o+h)*u+g,_=Math.abs(Math.round(A)-v)||1,y=(r+l)*d+f,x=Math.abs(Math.round(y)-b)||1;return t.setTransform(0,Math.sign(d),Math.sign(u),0,v,b),t.drawImage(e,s,i,n,a,0,0,x,_),t.setTransform(c,d,u,p,g,f),[x,_]}t.drawImage(e,s,i,n,a,r,o,l,h);return[Math.hypot(c,d)*l,Math.hypot(u,p)*h]}class bp{constructor(t,e){ac(this,"alphaIsShape",!1),ac(this,"fontSize",0),ac(this,"fontSizeScale",1),ac(this,"textMatrix",null),ac(this,"textMatrixScale",1),ac(this,"fontMatrix",pc),ac(this,"leading",0),ac(this,"x",0),ac(this,"y",0),ac(this,"lineX",0),ac(this,"lineY",0),ac(this,"charSpacing",0),ac(this,"wordSpacing",0),ac(this,"textHScale",1),ac(this,"textRenderingMode",Cc),ac(this,"textRise",0),ac(this,"fillColor","#000000"),ac(this,"strokeColor","#000000"),ac(this,"patternFill",!1),ac(this,"patternStroke",!1),ac(this,"fillAlpha",1),ac(this,"strokeAlpha",1),ac(this,"lineWidth",1),ac(this,"activeSMask",null),ac(this,"transferMaps","none"),this.clipBox=new Float32Array([0,0,t,e]),this.minMax=mp.slice()}clone(){const t=Object.create(this);return t.clipBox=this.clipBox.slice(),t.minMax=this.minMax.slice(),t}getPathBoundingBox(t=Ju,e=null){const s=this.minMax.slice();if(t===Zu){e||Jc("Stroke bounding box must include transform."),gd.singularValueDecompose2dScale(e,fp);const t=fp[0]*this.lineWidth/2,i=fp[1]*this.lineWidth/2;s[0]-=t,s[1]-=i,s[2]+=t,s[3]+=i}return s}updateClipFromPath(){const t=gd.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(t||[0,0,0,0])}isEmptyClip(){return this.minMax[0]===1/0}startNewPathAndClipBox(t){this.clipBox.set(t,0),this.minMax.set(mp,0)}getClippedPathBoundingBox(t=Ju,e=null){return gd.intersect(this.clipBox,this.getPathBoundingBox(t,e))}}function Ap(t,e){if(e instanceof ImageData)return void t.putImageData(e,0,0);const s=e.height,i=e.width,n=s%pp,a=(s-n)/pp,r=0===n?a:a+1,o=t.createImageData(i,pp);let l,h=0;const c=e.data,d=o.data;let u,p,g,f;if(e.kind===Dc.GRAYSCALE_1BPP){const e=c.byteLength,s=new Uint32Array(d.buffer,0,d.byteLength>>2),f=s.length,m=i+7>>3,v=4294967295,w=ud.isLittleEndian?4278190080:255;for(u=0;u<r;u++){for(g=u<a?pp:n,l=0,p=0;p<g;p++){const t=e-h;let n=0;const a=t>m?i:8*t-7,r=-8&a;let o=0,d=0;for(;n<r;n+=8)d=c[h++],s[l++]=128&d?v:w,s[l++]=64&d?v:w,s[l++]=32&d?v:w,s[l++]=16&d?v:w,s[l++]=8&d?v:w,s[l++]=4&d?v:w,s[l++]=2&d?v:w,s[l++]=1&d?v:w;for(;n<a;n++)0===o&&(d=c[h++],o=128),s[l++]=d&o?v:w,o>>=1}for(;l<f;)s[l++]=0;t.putImageData(o,0,u*pp)}}else if(e.kind===Dc.RGBA_32BPP){for(p=0,f=i*pp*4,u=0;u<a;u++)d.set(c.subarray(h,h+f)),h+=f,t.putImageData(o,0,p),p+=pp;u<r&&(f=i*n*4,d.set(c.subarray(h,h+f)),t.putImageData(o,0,p))}else{if(e.kind!==Dc.RGB_24BPP)throw new Error(`bad image kind: ${e.kind}`);for(g=pp,f=i*g,u=0;u<r;u++){for(u>=a&&(g=n,f=i*g),l=0,p=f;p--;)d[l++]=c[h++],d[l++]=c[h++],d[l++]=c[h++],d[l++]=255;t.putImageData(o,0,u*pp)}}}function _p(t,e){if(e.bitmap)return void t.drawImage(e.bitmap,0,0);const s=e.height,i=e.width,n=s%pp,a=(s-n)/pp,r=0===n?a:a+1,o=t.createImageData(i,pp);let l=0;const h=e.data,c=o.data;for(let d=0;d<r;d++){const e=d<a?pp:n;({srcPos:l}=up({src:h,srcPos:l,dest:c,width:i,height:e,nonBlackColor:0})),t.putImageData(o,0,d*pp)}}function yp(t,e){const s=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"];for(const i of s)void 0!==t[i]&&(e[i]=t[i]);void 0!==t.setLineDash&&(e.setLineDash(t.getLineDash()),e.lineDashOffset=t.lineDashOffset)}function xp(t){t.strokeStyle=t.fillStyle="#000000",t.fillRule="nonzero",t.globalAlpha=1,t.lineWidth=1,t.lineCap="butt",t.lineJoin="miter",t.miterLimit=10,t.globalCompositeOperation="source-over",t.font="10px sans-serif",void 0!==t.setLineDash&&(t.setLineDash([]),t.lineDashOffset=0);const{filter:e}=t;"none"!==e&&""!==e&&(t.filter="none")}function Sp(t,e){if(e)return!0;gd.singularValueDecompose2dScale(t,fp);const s=Math.fround(zd.pixelRatio*Sd.PDF_TO_CSS_UNITS);return fp[0]<=s&&fp[1]<=s}const kp=["butt","round","square"],Mp=["miter","round","bevel"],Ep={},Cp={};ei=new WeakSet,si=function(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.current.activeSMask=null,this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null)},ii=function(){if(this.pageColors){const t=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if("none"!==t){const e=this.ctx.filter;this.ctx.filter=t,this.ctx.drawImage(this.ctx.canvas,0,0),this.ctx.filter=e}}},ni=function(t,e,s){const i=new Path2D;return i.addPath(t,new DOMMatrix(s).invertSelf().multiplySelf(e)),i};let Tp=class t{constructor(t,e,s,i,n,{optionalContentConfig:a,markedContentStack:r=null},o,l){lc(this,ei),this.ctx=t,this.current=new bp(this.ctx.canvas.width,this.ctx.canvas.height),this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=e,this.objs=s,this.canvasFactory=i,this.filterFactory=n,this.groupStack=[],this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.suspendedCtx=null,this.contentVisible=!0,this.markedContentStack=r||[],this.optionalContentConfig=a,this.cachedCanvases=new vp(this.canvasFactory),this.cachedPatterns=new Map,this.annotationCanvasMap=o,this.viewportScale=1,this.outputScaleX=1,this.outputScaleY=1,this.pageColors=l,this._cachedScaleForStroking=[-1,0],this._cachedGetSinglePixelWidth=null,this._cachedBitmapsMap=new Map}getObject(t,e=null){return"string"==typeof t?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}beginDrawing({transform:t,viewport:e,transparency:s=!1,background:i=null}){const n=this.ctx.canvas.width,a=this.ctx.canvas.height,r=this.ctx.fillStyle;if(this.ctx.fillStyle=i||"#ffffff",this.ctx.fillRect(0,0,n,a),this.ctx.fillStyle=r,s){const t=this.cachedCanvases.getCanvas("transparent",n,a);this.compositeCtx=this.ctx,this.transparentCanvas=t.canvas,this.ctx=t.context,this.ctx.save(),this.ctx.transform(...Bd(this.compositeCtx))}this.ctx.save(),xp(this.ctx),t&&(this.ctx.transform(...t),this.outputScaleX=t[0],this.outputScaleY=t[0]),this.ctx.transform(...e.transform),this.viewportScale=e.scale,this.baseTransform=Bd(this.ctx)}executeOperatorList(t,e,s,i){const n=t.argsArray,a=t.fnArray;let r=e||0;const o=n.length;if(o===r)return r;const l=o-r>10&&"function"==typeof s,h=l?Date.now()+15:0;let c=0;const d=this.commonObjs,u=this.objs;let p;for(;;){if(void 0!==i&&r===i.nextBreakPoint)return i.breakIt(r,s),r;if(p=a[r],p!==zc.dependency)this[p].apply(this,n[r]);else for(const t of n[r]){const e=t.startsWith("g_")?d:u;if(!e.has(t))return e.get(t,s),r}if(r++,r===o)return r;if(l&&++c>10){if(Date.now()>h)return s(),r;c=0}}}endDrawing(){cc(this,ei,si).call(this),this.cachedCanvases.clear(),this.cachedPatterns.clear();for(const t of this._cachedBitmapsMap.values()){for(const e of t.values())"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&(e.width=e.height=0);t.clear()}this._cachedBitmapsMap.clear(),cc(this,ei,ii).call(this)}_scaleImage(t,e){const s=t.width??t.displayWidth,i=t.height??t.displayHeight;let n,a,r=Math.max(Math.hypot(e[0],e[1]),1),o=Math.max(Math.hypot(e[2],e[3]),1),l=s,h=i,c="prescale1";for(;r>2&&l>1||o>2&&h>1;){let e=l,s=h;r>2&&l>1&&(e=l>=16384?Math.floor(l/2)-1||1:Math.ceil(l/2),r/=l/e),o>2&&h>1&&(s=h>=16384?Math.floor(h/2)-1||1:Math.ceil(h)/2,o/=h/s),n=this.cachedCanvases.getCanvas(c,e,s),a=n.context,a.clearRect(0,0,e,s),a.drawImage(t,0,0,l,h,0,0,e,s),t=n.canvas,l=e,h=s,c="prescale1"===c?"prescale2":"prescale1"}return{img:t,paintWidth:l,paintHeight:h}}_createMaskCanvas(t){const e=this.ctx,{width:s,height:i}=t,n=this.current.fillColor,a=this.current.patternFill,r=Bd(e);let o,l,h,c;if((t.bitmap||t.data)&&t.count>1){const e=t.bitmap||t.data.buffer;l=JSON.stringify(a?r:[r.slice(0,4),n]),o=this._cachedBitmapsMap.get(e),o||(o=new Map,this._cachedBitmapsMap.set(e,o));const s=o.get(l);if(s&&!a){return{canvas:s,offsetX:Math.round(Math.min(r[0],r[2])+r[4]),offsetY:Math.round(Math.min(r[1],r[3])+r[5])}}h=s}h||(c=this.cachedCanvases.getCanvas("maskCanvas",s,i),_p(c.context,t));let d=gd.transform(r,[1/s,0,0,-1/i,0,0]);d=gd.transform(d,[1,0,0,1,0,-i]);const u=mp.slice();gd.axialAlignedBoundingBox([0,0,s,i],d,u);const[p,g,f,m]=u,v=Math.round(f-p)||1,w=Math.round(m-g)||1,b=this.cachedCanvases.getCanvas("fillCanvas",v,w),A=b.context,_=p,y=g;A.translate(-_,-y),A.transform(...d),h||(h=this._scaleImage(c.canvas,$d(A)),h=h.img,o&&a&&o.set(l,h)),A.imageSmoothingEnabled=Sp(Bd(A),t.interpolate),wp(A,h,0,0,h.width,h.height,0,0,s,i),A.globalCompositeOperation="source-in";const x=gd.transform($d(A),[1,0,0,1,-_,-y]);return A.fillStyle=a?n.getPattern(e,this,x,Ju):n,A.fillRect(0,0,s,i),o&&!a&&(this.cachedCanvases.delete("fillCanvas"),o.set(l,b.canvas)),{canvas:b.canvas,offsetX:Math.round(_),offsetY:Math.round(y)}}setLineWidth(t){t!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1),this.current.lineWidth=t,this.ctx.lineWidth=t}setLineCap(t){this.ctx.lineCap=kp[t]}setLineJoin(t){this.ctx.lineJoin=Mp[t]}setMiterLimit(t){this.ctx.miterLimit=t}setDash(t,e){const s=this.ctx;void 0!==s.setLineDash&&(s.setLineDash(t),s.lineDashOffset=e)}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,s]of t)switch(e){case"LW":this.setLineWidth(s);break;case"LC":this.setLineCap(s);break;case"LJ":this.setLineJoin(s);break;case"ML":this.setMiterLimit(s);break;case"D":this.setDash(s[0],s[1]);break;case"RI":this.setRenderingIntent(s);break;case"FL":this.setFlatness(s);break;case"Font":this.setFont(s[0],s[1]);break;case"CA":this.current.strokeAlpha=s;break;case"ca":this.ctx.globalAlpha=this.current.fillAlpha=s;break;case"BM":this.ctx.globalCompositeOperation=s;break;case"SMask":this.current.activeSMask=s?this.tempSMask:null,this.tempSMask=null,this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(s)}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const t=this.inSMaskMode;this.current.activeSMask&&!t?this.beginSMaskMode():!this.current.activeSMask&&t&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const t=this.ctx.canvas.width,e=this.ctx.canvas.height,s="smaskGroupAt"+this.groupLevel,i=this.cachedCanvases.getCanvas(s,t,e);this.suspendedCtx=this.ctx;const n=this.ctx=i.context;n.setTransform(this.suspendedCtx.getTransform()),yp(this.suspendedCtx,n),function(t,e){if(t._removeMirroring)throw new Error("Context is already forwarding operations.");t.__originalSave=t.save,t.__originalRestore=t.restore,t.__originalRotate=t.rotate,t.__originalScale=t.scale,t.__originalTranslate=t.translate,t.__originalTransform=t.transform,t.__originalSetTransform=t.setTransform,t.__originalResetTransform=t.resetTransform,t.__originalClip=t.clip,t.__originalMoveTo=t.moveTo,t.__originalLineTo=t.lineTo,t.__originalBezierCurveTo=t.bezierCurveTo,t.__originalRect=t.rect,t.__originalClosePath=t.closePath,t.__originalBeginPath=t.beginPath,t._removeMirroring=()=>{t.save=t.__originalSave,t.restore=t.__originalRestore,t.rotate=t.__originalRotate,t.scale=t.__originalScale,t.translate=t.__originalTranslate,t.transform=t.__originalTransform,t.setTransform=t.__originalSetTransform,t.resetTransform=t.__originalResetTransform,t.clip=t.__originalClip,t.moveTo=t.__originalMoveTo,t.lineTo=t.__originalLineTo,t.bezierCurveTo=t.__originalBezierCurveTo,t.rect=t.__originalRect,t.closePath=t.__originalClosePath,t.beginPath=t.__originalBeginPath,delete t._removeMirroring},t.save=function(){e.save(),this.__originalSave()},t.restore=function(){e.restore(),this.__originalRestore()},t.translate=function(t,s){e.translate(t,s),this.__originalTranslate(t,s)},t.scale=function(t,s){e.scale(t,s),this.__originalScale(t,s)},t.transform=function(t,s,i,n,a,r){e.transform(t,s,i,n,a,r),this.__originalTransform(t,s,i,n,a,r)},t.setTransform=function(t,s,i,n,a,r){e.setTransform(t,s,i,n,a,r),this.__originalSetTransform(t,s,i,n,a,r)},t.resetTransform=function(){e.resetTransform(),this.__originalResetTransform()},t.rotate=function(t){e.rotate(t),this.__originalRotate(t)},t.clip=function(t){e.clip(t),this.__originalClip(t)},t.moveTo=function(t,s){e.moveTo(t,s),this.__originalMoveTo(t,s)},t.lineTo=function(t,s){e.lineTo(t,s),this.__originalLineTo(t,s)},t.bezierCurveTo=function(t,s,i,n,a,r){e.bezierCurveTo(t,s,i,n,a,r),this.__originalBezierCurveTo(t,s,i,n,a,r)},t.rect=function(t,s,i,n){e.rect(t,s,i,n),this.__originalRect(t,s,i,n)},t.closePath=function(){e.closePath(),this.__originalClosePath()},t.beginPath=function(){e.beginPath(),this.__originalBeginPath()}}(n,this.suspendedCtx),this.setGState([["BM","source-over"]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring(),yp(this.ctx,this.suspendedCtx),this.ctx=this.suspendedCtx,this.suspendedCtx=null}compose(t){if(!this.current.activeSMask)return;t?(t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.ceil(t[2]),t[3]=Math.ceil(t[3])):t=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const e=this.current.activeSMask,s=this.suspendedCtx;this.composeSMask(s,e,this.ctx,t),this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height),this.ctx.restore()}composeSMask(t,e,s,i){const n=i[0],a=i[1],r=i[2]-n,o=i[3]-a;0!==r&&0!==o&&(this.genericComposeSMask(e.context,s,r,o,e.subtype,e.backdrop,e.transferMap,n,a,e.offsetX,e.offsetY),t.save(),t.globalAlpha=1,t.globalCompositeOperation="source-over",t.setTransform(1,0,0,1,0,0),t.drawImage(s.canvas,0,0),t.restore())}genericComposeSMask(t,e,s,i,n,a,r,o,l,h,c){let d=t.canvas,u=o-h,p=l-c;if(a)if(u<0||p<0||u+s>d.width||p+i>d.height){const t=this.cachedCanvases.getCanvas("maskExtension",s,i),e=t.context;e.drawImage(d,-u,-p),e.globalCompositeOperation="destination-atop",e.fillStyle=a,e.fillRect(0,0,s,i),e.globalCompositeOperation="source-over",d=t.canvas,u=p=0}else{t.save(),t.globalAlpha=1,t.setTransform(1,0,0,1,0,0);const e=new Path2D;e.rect(u,p,s,i),t.clip(e),t.globalCompositeOperation="destination-atop",t.fillStyle=a,t.fillRect(u,p,s,i),t.restore()}e.save(),e.globalAlpha=1,e.setTransform(1,0,0,1,0,0),"Alpha"===n&&r?e.filter=this.filterFactory.addAlphaFilter(r):"Luminosity"===n&&(e.filter=this.filterFactory.addLuminosityFilter(r));const g=new Path2D;g.rect(o,l,s,i),e.clip(g),e.globalCompositeOperation="destination-in",e.drawImage(d,u,p,s,i,o,l,s,i),e.restore()}save(){this.inSMaskMode&&yp(this.ctx,this.suspendedCtx),this.ctx.save();const t=this.current;this.stateStack.push(t),this.current=t.clone()}restore(){0!==this.stateStack.length?(this.current=this.stateStack.pop(),this.ctx.restore(),this.inSMaskMode&&yp(this.suspendedCtx,this.ctx),this.checkSMaskState(),this.pendingClip=null,this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null):this.inSMaskMode&&this.endSMaskMode()}transform(t,e,s,i,n,a){this.ctx.transform(t,e,s,i,n,a),this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}constructPath(t,e,s){let[i]=e;if(!s)return i||(i=e[0]=new Path2D),void this[t](i);if(!(i instanceof Path2D)){const t=e[0]=new Path2D;for(let e=0,s=i.length;e<s;)switch(i[e++]){case Gc:t.moveTo(i[e++],i[e++]);break;case jc:t.lineTo(i[e++],i[e++]);break;case Uc:t.bezierCurveTo(i[e++],i[e++],i[e++],i[e++],i[e++],i[e++]);break;case Vc:t.closePath();break;default:Qc(`Unrecognized drawing path operator: ${i[e-1]}`)}i=t}gd.axialAlignedBoundingBox(s,Bd(this.ctx),this.current.minMax),this[t](i)}closePath(){this.ctx.closePath()}stroke(t,e=!0){const s=this.ctx,i=this.current.strokeColor;if(s.globalAlpha=this.current.strokeAlpha,this.contentVisible)if("object"==typeof i&&(null==i?void 0:i.getPattern)){const e=i.isModifyingCurrentTransform()?s.getTransform():null;if(s.save(),s.strokeStyle=i.getPattern(s,this,$d(s),Zu),e){const i=new Path2D;i.addPath(t,s.getTransform().invertSelf().multiplySelf(e)),t=i}this.rescaleAndStroke(t,!1),s.restore()}else this.rescaleAndStroke(t,!0);e&&this.consumePath(t,this.current.getClippedPathBoundingBox(Zu,Bd(this.ctx))),s.globalAlpha=this.current.fillAlpha}closeStroke(t){this.stroke(t)}fill(t,e=!0){const s=this.ctx,i=this.current.fillColor;let n=!1;if(this.current.patternFill){const e=i.isModifyingCurrentTransform()?s.getTransform():null;if(s.save(),s.fillStyle=i.getPattern(s,this,$d(s),Ju),e){const i=new Path2D;i.addPath(t,s.getTransform().invertSelf().multiplySelf(e)),t=i}n=!0}const a=this.current.getClippedPathBoundingBox();this.contentVisible&&null!==a&&(this.pendingEOFill?(s.fill(t,"evenodd"),this.pendingEOFill=!1):s.fill(t)),n&&s.restore(),e&&this.consumePath(t,a)}eoFill(t){this.pendingEOFill=!0,this.fill(t)}fillStroke(t){this.fill(t,!1),this.stroke(t,!1),this.consumePath(t)}eoFillStroke(t){this.pendingEOFill=!0,this.fillStroke(t)}closeFillStroke(t){this.fillStroke(t)}closeEOFillStroke(t){this.pendingEOFill=!0,this.fillStroke(t)}endPath(t){this.consumePath(t)}rawFillPath(t){this.ctx.fill(t)}clip(){this.pendingClip=Ep}eoClip(){this.pendingClip=Cp}beginText(){this.current.textMatrix=null,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}endText(){const t=this.pendingTextPaths,e=this.ctx;if(void 0===t)return;const s=new Path2D,i=e.getTransform().invertSelf();for(const{transform:n,x:a,y:r,fontSize:o,path:l}of t)s.addPath(l,new DOMMatrix(n).preMultiplySelf(i).translate(a,r).scale(o,-o));e.clip(s),delete this.pendingTextPaths}setCharSpacing(t){this.current.charSpacing=t}setWordSpacing(t){this.current.wordSpacing=t}setHScale(t){this.current.textHScale=t/100}setLeading(t){this.current.leading=-t}setFont(t,e){var s;const i=this.commonObjs.get(t),n=this.current;if(!i)throw new Error(`Can't find font for ${t}`);if(n.fontMatrix=i.fontMatrix||pc,0!==n.fontMatrix[0]&&0!==n.fontMatrix[3]||Qc("Invalid font matrix for font "+t),e<0?(e=-e,n.fontDirection=-1):n.fontDirection=1,this.current.font=i,this.current.fontSize=e,i.isType3Font)return;const a=i.loadedName||"sans-serif",r=(null==(s=i.systemFontInfo)?void 0:s.css)||`"${a}", ${i.fallbackName}`;let o="normal";i.black?o="900":i.bold&&(o="bold");const l=i.italic?"italic":"normal";let h=e;e<16?h=16:e>100&&(h=100),this.current.fontSizeScale=e/h,this.ctx.font=`${l} ${o} ${h}px ${r}`}setTextRenderingMode(t){this.current.textRenderingMode=t}setTextRise(t){this.current.textRise=t}moveText(t,e){this.current.x=this.current.lineX+=t,this.current.y=this.current.lineY+=e}setLeadingMoveText(t,e){this.setLeading(-e),this.moveText(t,e)}setTextMatrix(t){const{current:e}=this;e.textMatrix=t,e.textMatrixScale=Math.hypot(t[0],t[1]),e.x=e.lineX=0,e.y=e.lineY=0}nextLine(){this.moveText(0,this.current.leading)}paintChar(t,e,s,i,n){const a=this.ctx,r=this.current,o=r.font,l=r.textRenderingMode,h=r.fontSize/r.fontSizeScale,c=l&Ic,d=!!(l&Lc),u=r.patternFill&&!o.missingFile,p=r.patternStroke&&!o.missingFile;let g;if((o.disableFontFace||d||u||p)&&(g=o.getPathGenerator(this.commonObjs,t)),o.disableFontFace||u||p){let t;if(a.save(),a.translate(e,s),a.scale(h,-h),c!==Cc&&c!==Rc||(i?(t=a.getTransform(),a.setTransform(...i),a.fill(cc(this,ei,ni).call(this,g,t,i))):a.fill(g)),c===Tc||c===Rc)if(n){t||(t=a.getTransform()),a.setTransform(...n);const{a:e,b:s,c:i,d:r}=t,o=gd.inverseTransform(n),l=gd.transform([e,s,i,r,0,0],o);gd.singularValueDecompose2dScale(l,fp),a.lineWidth*=Math.max(fp[0],fp[1])/h,a.stroke(cc(this,ei,ni).call(this,g,t,n))}else a.lineWidth/=h,a.stroke(g);a.restore()}else c!==Cc&&c!==Rc||a.fillText(t,e,s),c!==Tc&&c!==Rc||a.strokeText(t,e,s);if(d){(this.pendingTextPaths||(this.pendingTextPaths=[])).push({transform:Bd(a),x:e,y:s,fontSize:h,path:g})}}get isFontSubpixelAAEnabled(){const{context:t}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);t.scale(1.5,1),t.fillText("I",0,10);const e=t.getImageData(0,0,10,10).data;let s=!1;for(let i=3;i<e.length;i+=4)if(e[i]>0&&e[i]<255){s=!0;break}return sd(this,"isFontSubpixelAAEnabled",s)}showText(t){const e=this.current,s=e.font;if(s.isType3Font)return this.showType3Text(t);const i=e.fontSize;if(0===i)return;const n=this.ctx,a=e.fontSizeScale,r=e.charSpacing,o=e.wordSpacing,l=e.fontDirection,h=e.textHScale*l,c=t.length,d=s.vertical,u=d?1:-1,p=s.defaultVMetrics,g=i*e.fontMatrix[0],f=e.textRenderingMode===Cc&&!s.disableFontFace&&!e.patternFill;let m,v;if(n.save(),e.textMatrix&&n.transform(...e.textMatrix),n.translate(e.x,e.y+e.textRise),l>0?n.scale(h,-1):n.scale(h,1),e.patternFill){n.save();const t=e.fillColor.getPattern(n,this,$d(n),Ju);m=Bd(n),n.restore(),n.fillStyle=t}if(e.patternStroke){n.save();const t=e.strokeColor.getPattern(n,this,$d(n),Zu);v=Bd(n),n.restore(),n.strokeStyle=t}let w=e.lineWidth;const b=e.textMatrixScale;if(0===b||0===w){const t=e.textRenderingMode&Ic;t!==Tc&&t!==Rc||(w=this.getSinglePixelWidth())}else w/=b;if(1!==a&&(n.scale(a,a),w/=a),n.lineWidth=w,s.isInvalidPDFjsFont){const s=[];let i=0;for(const e of t)s.push(e.unicode),i+=e.width;return n.fillText(s.join(""),0,0),e.x+=i*g*h,n.restore(),void this.compose()}let A,_=0;for(A=0;A<c;++A){const e=t[A];if("number"==typeof e){_+=u*e*i/1e3;continue}let h=!1;const c=(e.isSpace?o:0)+r,w=e.fontChar,b=e.accent;let y,x,S=e.width;if(d){const t=e.vmetric||p,s=-(e.vmetric?t[1]:.5*S)*g,i=t[2]*g;S=t?-t[0]:S,y=s/a,x=(_+i)/a}else y=_/a,x=0;if(s.remeasure&&S>0){const t=1e3*n.measureText(w).width/i*a;if(S<t&&this.isFontSubpixelAAEnabled){const e=S/t;h=!0,n.save(),n.scale(e,1),y/=e}else S!==t&&(y+=(S-t)/2e3*i/a)}if(this.contentVisible&&(e.isInFont||s.missingFile))if(f&&!b)n.fillText(w,y,x);else if(this.paintChar(w,y,x,m,v),b){const t=y+i*b.offset.x/a,e=x-i*b.offset.y/a;this.paintChar(b.fontChar,t,e,m,v)}_+=d?S*g-c*l:S*g+c*l,h&&n.restore()}d?e.y-=_:e.x+=_*h,n.restore(),this.compose()}showType3Text(t){const e=this.ctx,s=this.current,i=s.font,n=s.fontSize,a=s.fontDirection,r=i.vertical?1:-1,o=s.charSpacing,l=s.wordSpacing,h=s.textHScale*a,c=s.fontMatrix||pc,d=t.length;let u,p,g,f;if(!(s.textRenderingMode===Pc)&&0!==n){for(this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null,e.save(),s.textMatrix&&e.transform(...s.textMatrix),e.translate(s.x,s.y+s.textRise),e.scale(h,a),u=0;u<d;++u){if(p=t[u],"number"==typeof p){f=r*p*n/1e3,this.ctx.translate(f,0),s.x+=f*h;continue}const a=(p.isSpace?l:0)+o,d=i.charProcOperatorList[p.operatorListId];d?this.contentVisible&&(this.save(),e.scale(n,n),e.transform(...c),this.executeOperatorList(d),this.restore()):Qc(`Type3 character "${p.operatorListId}" is not available.`);const m=[p.width,0];gd.applyTransform(m,c),g=m[0]*n+a,e.translate(g,0),s.x+=g*h}e.restore()}}setCharWidth(t,e){}setCharWidthAndBounds(t,e,s,i,n,a){const r=new Path2D;r.rect(s,i,n-s,a-i),this.ctx.clip(r),this.endPath()}getColorN_Pattern(e){let s;if("TilingPattern"===e[0]){const i=this.baseTransform||Bd(this.ctx),n={createCanvasGraphics:e=>new t(e,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})};s=new dp(e,this.ctx,n,i)}else s=this._getPattern(e[1],e[2]);return s}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments),this.current.patternStroke=!0}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0}setStrokeRGBColor(t){this.ctx.strokeStyle=this.current.strokeColor=t,this.current.patternStroke=!1}setStrokeTransparent(){this.ctx.strokeStyle=this.current.strokeColor="transparent",this.current.patternStroke=!1}setFillRGBColor(t){this.ctx.fillStyle=this.current.fillColor=t,this.current.patternFill=!1}setFillTransparent(){this.ctx.fillStyle=this.current.fillColor="transparent",this.current.patternFill=!1}_getPattern(t,e=null){let s;return this.cachedPatterns.has(t)?s=this.cachedPatterns.get(t):(s=function(t){switch(t[0]){case"RadialAxial":return new ip(t);case"Mesh":return new rp(t);case"Dummy":return new op}throw new Error(`Unknown IR type: ${t[0]}`)}(this.getObject(t)),this.cachedPatterns.set(t,s)),e&&(s.matrix=e),s}shadingFill(t){if(!this.contentVisible)return;const e=this.ctx;this.save();const s=this._getPattern(t);e.fillStyle=s.getPattern(e,this,$d(e),tp);const i=$d(e);if(i){const{width:t,height:s}=e.canvas,n=mp.slice();gd.axialAlignedBoundingBox([0,0,t,s],i,n);const[a,r,o,l]=n;this.ctx.fillRect(a,r,o-a,l-r)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox()),this.restore()}beginInlineImage(){Jc("Should not call beginInlineImage")}beginImageData(){Jc("Should not call beginImageData")}paintFormXObjectBegin(t,e){if(this.contentVisible&&(this.save(),this.baseTransformStack.push(this.baseTransform),t&&this.transform(...t),this.baseTransform=Bd(this.ctx),e)){gd.axialAlignedBoundingBox(e,this.baseTransform,this.current.minMax);const[t,s,i,n]=e,a=new Path2D;a.rect(t,s,i-t,n-s),this.ctx.clip(a),this.endPath()}}paintFormXObjectEnd(){this.contentVisible&&(this.restore(),this.baseTransform=this.baseTransformStack.pop())}beginGroup(t){if(!this.contentVisible)return;this.save(),this.inSMaskMode&&(this.endSMaskMode(),this.current.activeSMask=null);const e=this.ctx;t.isolated||Kc("TODO: Support non-isolated groups."),t.knockout&&Qc("Knockout groups not supported.");const s=Bd(e);if(t.matrix&&e.transform(...t.matrix),!t.bbox)throw new Error("Bounding box is required.");let i=mp.slice();gd.axialAlignedBoundingBox(t.bbox,Bd(e),i);const n=[0,0,e.canvas.width,e.canvas.height];i=gd.intersect(i,n)||[0,0,0,0];const a=Math.floor(i[0]),r=Math.floor(i[1]),o=Math.max(Math.ceil(i[2])-a,1),l=Math.max(Math.ceil(i[3])-r,1);this.current.startNewPathAndClipBox([0,0,o,l]);let h="groupAt"+this.groupLevel;t.smask&&(h+="_smask_"+this.smaskCounter++%2);const c=this.cachedCanvases.getCanvas(h,o,l),d=c.context;d.translate(-a,-r),d.transform(...s);let u=new Path2D;const[p,g,f,m]=t.bbox;if(u.rect(p,g,f-p,m-g),t.matrix){const e=new Path2D;e.addPath(u,new DOMMatrix(t.matrix)),u=e}d.clip(u),t.smask?this.smaskStack.push({canvas:c.canvas,context:d,offsetX:a,offsetY:r,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null}):(e.setTransform(1,0,0,1,0,0),e.translate(a,r),e.save()),yp(e,d),this.ctx=d,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(e),this.groupLevel++}endGroup(t){if(!this.contentVisible)return;this.groupLevel--;const e=this.ctx,s=this.groupStack.pop();if(this.ctx=s,this.ctx.imageSmoothingEnabled=!1,t.smask)this.tempSMask=this.smaskStack.pop(),this.restore();else{this.ctx.restore();const t=Bd(this.ctx);this.restore(),this.ctx.save(),this.ctx.setTransform(...t);const s=mp.slice();gd.axialAlignedBoundingBox([0,0,e.canvas.width,e.canvas.height],t,s),this.ctx.drawImage(e.canvas,0,0),this.ctx.restore(),this.compose(s)}}beginAnnotation(t,e,s,i,n){if(cc(this,ei,si).call(this),xp(this.ctx),this.ctx.save(),this.save(),this.baseTransform&&this.ctx.setTransform(...this.baseTransform),e){const i=e[2]-e[0],a=e[3]-e[1];if(n&&this.annotationCanvasMap){(s=s.slice())[4]-=e[0],s[5]-=e[1],(e=e.slice())[0]=e[1]=0,e[2]=i,e[3]=a,gd.singularValueDecompose2dScale(Bd(this.ctx),fp);const{viewportScale:n}=this,r=Math.ceil(i*this.outputScaleX*n),o=Math.ceil(a*this.outputScaleY*n);this.annotationCanvas=this.canvasFactory.create(r,o);const{canvas:l,context:h}=this.annotationCanvas;this.annotationCanvasMap.set(t,l),this.annotationCanvas.savedCtx=this.ctx,this.ctx=h,this.ctx.save(),this.ctx.setTransform(fp[0],0,0,-fp[1],0,a*fp[1]),xp(this.ctx)}else{xp(this.ctx),this.endPath();const t=new Path2D;t.rect(e[0],e[1],i,a),this.ctx.clip(t)}}this.current=new bp(this.ctx.canvas.width,this.ctx.canvas.height),this.transform(...s),this.transform(...i)}endAnnotation(){this.annotationCanvas&&(this.ctx.restore(),cc(this,ei,ii).call(this),this.ctx=this.annotationCanvas.savedCtx,delete this.annotationCanvas.savedCtx,delete this.annotationCanvas)}paintImageMaskXObject(t){if(!this.contentVisible)return;const e=t.count;(t=this.getObject(t.data,t)).count=e;const s=this.ctx,i=this._createMaskCanvas(t),n=i.canvas;s.save(),s.setTransform(1,0,0,1,0,0),s.drawImage(n,i.offsetX,i.offsetY),s.restore(),this.compose()}paintImageMaskXObjectRepeat(t,e,s=0,i=0,n,a){if(!this.contentVisible)return;t=this.getObject(t.data,t);const r=this.ctx;r.save();const o=Bd(r);r.transform(e,s,i,n,0,0);const l=this._createMaskCanvas(t);r.setTransform(1,0,0,1,l.offsetX-o[4],l.offsetY-o[5]);for(let h=0,c=a.length;h<c;h+=2){const t=gd.transform(o,[e,s,i,n,a[h],a[h+1]]);r.drawImage(l.canvas,t[4],t[5])}r.restore(),this.compose()}paintImageMaskXObjectGroup(t){if(!this.contentVisible)return;const e=this.ctx,s=this.current.fillColor,i=this.current.patternFill;for(const n of t){const{data:t,width:a,height:r,transform:o}=n,l=this.cachedCanvases.getCanvas("maskCanvas",a,r),h=l.context;h.save();_p(h,this.getObject(t,n)),h.globalCompositeOperation="source-in",h.fillStyle=i?s.getPattern(h,this,$d(e),Ju):s,h.fillRect(0,0,a,r),h.restore(),e.save(),e.transform(...o),e.scale(1,-1),wp(e,l.canvas,0,0,a,r,0,-1,1,1),e.restore()}this.compose()}paintImageXObject(t){if(!this.contentVisible)return;const e=this.getObject(t);e?this.paintInlineImageXObject(e):Qc("Dependent image isn't ready yet")}paintImageXObjectRepeat(t,e,s,i){if(!this.contentVisible)return;const n=this.getObject(t);if(!n)return void Qc("Dependent image isn't ready yet");const a=n.width,r=n.height,o=[];for(let l=0,h=i.length;l<h;l+=2)o.push({transform:[e,0,0,s,i[l],i[l+1]],x:0,y:0,w:a,h:r});this.paintInlineImageXObjectGroup(n,o)}applyTransferMapsToCanvas(t){return"none"!==this.current.transferMaps&&(t.filter=this.current.transferMaps,t.drawImage(t.canvas,0,0),t.filter="none"),t.canvas}applyTransferMapsToBitmap(t){if("none"===this.current.transferMaps)return t.bitmap;const{bitmap:e,width:s,height:i}=t,n=this.cachedCanvases.getCanvas("inlineImage",s,i),a=n.context;return a.filter=this.current.transferMaps,a.drawImage(e,0,0),a.filter="none",n.canvas}paintInlineImageXObject(t){if(!this.contentVisible)return;const e=t.width,s=t.height,i=this.ctx;this.save();const{filter:n}=i;let a;if("none"!==n&&""!==n&&(i.filter="none"),i.scale(1/e,-1/s),t.bitmap)a=this.applyTransferMapsToBitmap(t);else if("function"==typeof HTMLElement&&t instanceof HTMLElement||!t.data)a=t;else{const i=this.cachedCanvases.getCanvas("inlineImage",e,s).context;Ap(i,t),a=this.applyTransferMapsToCanvas(i)}const r=this._scaleImage(a,$d(i));i.imageSmoothingEnabled=Sp(Bd(i),t.interpolate),wp(i,r.img,0,0,r.paintWidth,r.paintHeight,0,-s,e,s),this.compose(),this.restore()}paintInlineImageXObjectGroup(t,e){if(!this.contentVisible)return;const s=this.ctx;let i;if(t.bitmap)i=t.bitmap;else{const e=t.width,s=t.height,n=this.cachedCanvases.getCanvas("inlineImage",e,s).context;Ap(n,t),i=this.applyTransferMapsToCanvas(n)}for(const n of e)s.save(),s.transform(...n.transform),s.scale(1,-1),wp(s,i,n.x,n.y,n.w,n.h,0,-1,1,1),s.restore();this.compose()}paintSolidColorImageMask(){this.contentVisible&&(this.ctx.fillRect(0,0,1,1),this.compose())}markPoint(t){}markPointProps(t,e){}beginMarkedContent(t){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(t,e){"OC"===t?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(e)}):this.markedContentStack.push({visible:!0}),this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop(),this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(t,e){const s=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath(),this.pendingClip||this.compose(e);const i=this.ctx;this.pendingClip&&(s||(this.pendingClip===Cp?i.clip(t,"evenodd"):i.clip(t)),this.pendingClip=null),this.current.startNewPathAndClipBox(this.current.clipBox)}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const t=Bd(this.ctx);if(0===t[1]&&0===t[2])this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(t[0]),Math.abs(t[3]));else{const e=Math.abs(t[0]*t[3]-t[2]*t[1]),s=Math.hypot(t[0],t[2]),i=Math.hypot(t[1],t[3]);this._cachedGetSinglePixelWidth=Math.max(s,i)/e}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(-1===this._cachedScaleForStroking[0]){const{lineWidth:t}=this.current,{a:e,b:s,c:i,d:n}=this.ctx.getTransform();let a,r;if(0===s&&0===i){const s=Math.abs(e),i=Math.abs(n);if(s===i)if(0===t)a=r=1/s;else{const e=s*t;a=r=e<1?1/e:1}else if(0===t)a=1/s,r=1/i;else{const e=s*t,n=i*t;a=e<1?1/e:1,r=n<1?1/n:1}}else{const o=Math.abs(e*n-s*i),l=Math.hypot(e,s),h=Math.hypot(i,n);if(0===t)a=h/o,r=l/o;else{const e=t*o;a=h>e?h/e:1,r=l>e?l/e:1}}this._cachedScaleForStroking[0]=a,this._cachedScaleForStroking[1]=r}return this._cachedScaleForStroking}rescaleAndStroke(t,e){const{ctx:s,current:{lineWidth:i}}=this,[n,a]=this.getScaleForStroking();if(n===a)return s.lineWidth=(i||1)*n,void s.stroke(t);const r=s.getLineDash();e&&s.save(),s.scale(n,a),gp.a=1/n,gp.d=1/a;const o=new Path2D;if(o.addPath(t,gp),r.length>0){const t=Math.max(n,a);s.setLineDash(r.map(e=>e/t)),s.lineDashOffset/=t}s.lineWidth=i||1,s.stroke(o),e&&s.restore()}isContentVisible(){for(let t=this.markedContentStack.length-1;t>=0;t--)if(!this.markedContentStack[t].visible)return!1;return!0}};for(const Of in zc)void 0!==Tp.prototype[Of]&&(Tp.prototype[zc[Of]]=Tp.prototype[Of]);class Rp{static get workerPort(){return oc(this,ai)}static set workerPort(t){if(!("undefined"!=typeof Worker&&t instanceof Worker)&&null!==t)throw new Error("Invalid `workerPort` type.");hc(this,ai,t)}static get workerSrc(){return oc(this,ri)}static set workerSrc(t){if("string"!=typeof t)throw new Error("Invalid `workerSrc` type.");hc(this,ri,t)}}ai=new WeakMap,ri=new WeakMap,lc(Rp,ai,null),lc(Rp,ri,"");class Pp{constructor({parsedData:t,rawData:e}){lc(this,oi),lc(this,li),hc(this,oi,t),hc(this,li,e)}getRaw(){return oc(this,li)}get(t){return oc(this,oi).get(t)??null}[Symbol.iterator](){return oc(this,oi).entries()}}oi=new WeakMap,li=new WeakMap;const Ip=Symbol("INTERNAL");class Lp{constructor(t,{name:e,intent:s,usage:i,rbGroups:n}){lc(this,hi,!1),lc(this,ci,!1),lc(this,di,!1),lc(this,ui,!0),hc(this,hi,!!(t&mc)),hc(this,ci,!!(t&vc)),this.name=e,this.intent=s,this.usage=i,this.rbGroups=n}get visible(){if(oc(this,di))return oc(this,ui);if(!oc(this,ui))return!1;const{print:t,view:e}=this.usage;return oc(this,hi)?"OFF"!==(null==e?void 0:e.viewState):!oc(this,ci)||"OFF"!==(null==t?void 0:t.printState)}_setVisible(t,e,s=!1){t!==Ip&&Jc("Internal method `_setVisible` called."),hc(this,di,s),hc(this,ui,e)}}hi=new WeakMap,ci=new WeakMap,di=new WeakMap,ui=new WeakMap;class Dp{constructor(t,e=mc){if(lc(this,vi),lc(this,pi,null),lc(this,gi,new Map),lc(this,fi,null),lc(this,mi,null),this.renderingIntent=e,this.name=null,this.creator=null,null!==t){this.name=t.name,this.creator=t.creator,hc(this,mi,t.order);for(const s of t.groups)oc(this,gi).set(s.id,new Lp(e,s));if("OFF"===t.baseState)for(const t of oc(this,gi).values())t._setVisible(Ip,!1);for(const e of t.on)oc(this,gi).get(e)._setVisible(Ip,!0);for(const e of t.off)oc(this,gi).get(e)._setVisible(Ip,!1);hc(this,fi,this.getHash())}}isVisible(t){if(0===oc(this,gi).size)return!0;if(!t)return Kc("Optional content group not defined."),!0;if("OCG"===t.type)return oc(this,gi).has(t.id)?oc(this,gi).get(t.id).visible:(Qc(`Optional content group not found: ${t.id}`),!0);if("OCMD"===t.type){if(t.expression)return cc(this,vi,wi).call(this,t.expression);if(!t.policy||"AnyOn"===t.policy){for(const e of t.ids){if(!oc(this,gi).has(e))return Qc(`Optional content group not found: ${e}`),!0;if(oc(this,gi).get(e).visible)return!0}return!1}if("AllOn"===t.policy){for(const e of t.ids){if(!oc(this,gi).has(e))return Qc(`Optional content group not found: ${e}`),!0;if(!oc(this,gi).get(e).visible)return!1}return!0}if("AnyOff"===t.policy){for(const e of t.ids){if(!oc(this,gi).has(e))return Qc(`Optional content group not found: ${e}`),!0;if(!oc(this,gi).get(e).visible)return!0}return!1}if("AllOff"===t.policy){for(const e of t.ids){if(!oc(this,gi).has(e))return Qc(`Optional content group not found: ${e}`),!0;if(oc(this,gi).get(e).visible)return!1}return!0}return Qc(`Unknown optional content policy ${t.policy}.`),!0}return Qc(`Unknown group type ${t.type}.`),!0}setVisibility(t,e=!0,s=!0){var i;const n=oc(this,gi).get(t);if(n){if(s&&e&&n.rbGroups.length)for(const e of n.rbGroups)for(const s of e)s!==t&&(null==(i=oc(this,gi).get(s))||i._setVisible(Ip,!1,!0));n._setVisible(Ip,!!e,!0),hc(this,pi,null)}else Qc(`Optional content group not found: ${t}`)}setOCGState({state:t,preserveRB:e}){let s;for(const i of t){switch(i){case"ON":case"OFF":case"Toggle":s=i;continue}const t=oc(this,gi).get(i);if(t)switch(s){case"ON":this.setVisibility(i,!0,e);break;case"OFF":this.setVisibility(i,!1,e);break;case"Toggle":this.setVisibility(i,!t.visible,e)}}hc(this,pi,null)}get hasInitialVisibility(){return null===oc(this,fi)||this.getHash()===oc(this,fi)}getOrder(){return oc(this,gi).size?oc(this,mi)?oc(this,mi).slice():[...oc(this,gi).keys()]:null}getGroup(t){return oc(this,gi).get(t)||null}getHash(){if(null!==oc(this,pi))return oc(this,pi);const t=new pu;for(const[e,s]of oc(this,gi))t.update(`${e}:${s.visible}`);return hc(this,pi,t.hexdigest())}[Symbol.iterator](){return oc(this,gi).entries()}}pi=new WeakMap,gi=new WeakMap,fi=new WeakMap,mi=new WeakMap,vi=new WeakSet,wi=function(t){const e=t.length;if(e<2)return!0;const s=t[0];for(let i=1;i<e;i++){const e=t[i];let n;if(Array.isArray(e))n=cc(this,vi,wi).call(this,e);else{if(!oc(this,gi).has(e))return Qc(`Optional content group not found: ${e}`),!0;n=oc(this,gi).get(e).visible}switch(s){case"And":if(!n)return!1;break;case"Or":if(n)return!0;break;case"Not":return!n;default:return!0}}return"And"===s};class Np{constructor(t,{disableRange:e=!1,disableStream:s=!1}){Zc(t,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.');const{length:i,initialData:n,progressiveDone:a,contentDispositionFilename:r}=t;if(this._queuedChunks=[],this._progressiveDone=a,this._contentDispositionFilename=r,(null==n?void 0:n.length)>0){const t=n instanceof Uint8Array&&n.byteLength===n.buffer.byteLength?n.buffer:new Uint8Array(n).buffer;this._queuedChunks.push(t)}this._pdfDataRangeTransport=t,this._isStreamingSupported=!s,this._isRangeSupported=!e,this._contentLength=i,this._fullRequestReader=null,this._rangeReaders=[],t.addRangeListener((t,e)=>{this._onReceiveData({begin:t,chunk:e})}),t.addProgressListener((t,e)=>{this._onProgress({loaded:t,total:e})}),t.addProgressiveReadListener(t=>{this._onReceiveData({chunk:t})}),t.addProgressiveDoneListener(()=>{this._onProgressiveDone()}),t.transportReady()}_onReceiveData({begin:t,chunk:e}){const s=e instanceof Uint8Array&&e.byteLength===e.buffer.byteLength?e.buffer:new Uint8Array(e).buffer;if(void 0===t)this._fullRequestReader?this._fullRequestReader._enqueue(s):this._queuedChunks.push(s);else{Zc(this._rangeReaders.some(function(e){return e._begin===t&&(e._enqueue(s),!0)}),"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){var t;return(null==(t=this._fullRequestReader)?void 0:t._loaded)??0}_onProgress(t){var e,s,i,n;void 0===t.total?null==(s=null==(e=this._rangeReaders[0])?void 0:e.onProgress)||s.call(e,{loaded:t.loaded}):null==(n=null==(i=this._fullRequestReader)?void 0:i.onProgress)||n.call(i,{loaded:t.loaded,total:t.total})}_onProgressiveDone(){var t;null==(t=this._fullRequestReader)||t.progressiveDone(),this._progressiveDone=!0}_removeRangeReader(t){const e=this._rangeReaders.indexOf(t);e>=0&&this._rangeReaders.splice(e,1)}getFullReader(){Zc(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const t=this._queuedChunks;return this._queuedChunks=null,new Fp(this,t,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new Wp(this,t,e);return this._pdfDataRangeTransport.requestDataRange(t,e),this._rangeReaders.push(s),s}cancelAllRequests(t){var e;null==(e=this._fullRequestReader)||e.cancel(t);for(const s of this._rangeReaders.slice(0))s.cancel(t);this._pdfDataRangeTransport.abort()}}class Fp{constructor(t,e,s=!1,i=null){this._stream=t,this._done=s||!1,this._filename=Td(i)?i:null,this._queuedChunks=e||[],this._loaded=0;for(const n of this._queuedChunks)this._loaded+=n.byteLength;this._requests=[],this._headersReady=Promise.resolve(),t._fullRequestReader=this,this.onProgress=null}_enqueue(t){if(!this._done){if(this._requests.length>0){this._requests.shift().resolve({value:t,done:!1})}else this._queuedChunks.push(t);this._loaded+=t.byteLength}}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0){return{value:this._queuedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class Wp{constructor(t,e,s){this._stream=t,this._begin=e,this._end=s,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}_enqueue(t){if(!this._done){if(0===this._requests.length)this._queuedChunk=t;else{this._requests.shift().resolve({value:t,done:!1});for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0,this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const t=this._queuedChunk;return this._queuedChunk=null,{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._stream._removeRangeReader(this)}}function Op(t,e){const s=new Headers;if(!t||!e||"object"!=typeof e)return s;for(const i in e){const t=e[i];void 0!==t&&s.append(i,t)}return s}function Bp(t){var e;return(null==(e=URL.parse(t))?void 0:e.origin)??null}function $p({responseHeaders:t,isHttp:e,rangeChunkSize:s,disableRange:i}){const n={allowRangeRequests:!1,suggestedLength:void 0},a=parseInt(t.get("Content-Length"),10);if(!Number.isInteger(a))return n;if(n.suggestedLength=a,a<=2*s)return n;if(i||!e)return n;if("bytes"!==t.get("Accept-Ranges"))return n;return"identity"!==(t.get("Content-Encoding")||"identity")||(n.allowRangeRequests=!0),n}function Hp(t){const e=t.get("Content-Disposition");if(e){let t=function(t){let e=!0,s=i("filename\\*","i").exec(t);if(s){s=s[1];let t=r(s);return t=unescape(t),t=o(t),t=l(t),a(t)}if(s=function(t){const e=[];let s;const n=i("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;null!==(s=n.exec(t));){let[,t,i,n]=s;if(t=parseInt(t,10),t in e){if(0===t)break}else e[t]=[i,n]}const a=[];for(let i=0;i<e.length&&i in e;++i){let[t,s]=e[i];s=r(s),t&&(s=unescape(s),0===i&&(s=o(s))),a.push(s)}return a.join("")}(t),s)return a(l(s));if(s=i("filename","i").exec(t),s){s=s[1];let t=r(s);return t=l(t),a(t)}function i(t,e){return new RegExp("(?:^|;)\\s*"+t+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',e)}function n(t,s){if(t){if(!/^[\x00-\xFF]+$/.test(s))return s;try{const i=new TextDecoder(t,{fatal:!0}),n=dd(s);s=i.decode(n),e=!1}catch{}}return s}function a(t){return e&&/[\x80-\xff]/.test(t)&&(t=n("utf-8",t),e&&(t=n("iso-8859-1",t))),t}function r(t){if(t.startsWith('"')){const e=t.slice(1).split('\\"');for(let t=0;t<e.length;++t){const s=e[t].indexOf('"');-1!==s&&(e[t]=e[t].slice(0,s),e.length=t+1),e[t]=e[t].replaceAll(/\\(.)/g,"$1")}t=e.join('"')}return t}function o(t){const e=t.indexOf("'");return-1===e?t:n(t.slice(0,e),t.slice(e+1).replace(/^[^']*'/,""))}function l(t){return!t.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(t)?t:t.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,function(t,e,s,i){if("q"===s||"Q"===s)return n(e,i=(i=i.replaceAll("_"," ")).replaceAll(/=([0-9a-fA-F]{2})/g,function(t,e){return String.fromCharCode(parseInt(e,16))}));try{i=atob(i)}catch{}return n(e,i)})}return""}(e);if(t.includes("%"))try{t=decodeURIComponent(t)}catch{}if(Td(t))return t}return null}function zp(t,e){return new od(`Unexpected server response (${t}) while retrieving PDF "${e}".`,t,404===t||0===t&&e.startsWith("file:"))}function Gp(t){return 200===t||206===t}function jp(t,e,s){return{method:"GET",headers:t,signal:s.signal,mode:"cors",credentials:e?"include":"same-origin",redirect:"follow"}}function Up(t){return t instanceof Uint8Array?t.buffer:t instanceof ArrayBuffer?t:(Qc(`getArrayBuffer - unexpected data format: ${t}`),new Uint8Array(t).buffer)}class Vp{constructor(t){ac(this,"_responseOrigin",null),this.source=t,this.isHttp=/^https?:/i.test(t.url),this.headers=Op(this.isHttp,t.httpHeaders),this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){var t;return(null==(t=this._fullRequestReader)?void 0:t._loaded)??0}getFullReader(){return Zc(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once."),this._fullRequestReader=new qp(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new Xp(this,t,e);return this._rangeRequestReaders.push(s),s}cancelAllRequests(t){var e;null==(e=this._fullRequestReader)||e.cancel(t);for(const s of this._rangeRequestReaders.slice(0))s.cancel(t)}}class qp{constructor(t){this._stream=t,this._reader=null,this._loaded=0,this._filename=null;const e=t.source;this._withCredentials=e.withCredentials||!1,this._contentLength=e.length,this._headersCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._abortController=new AbortController,this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange;const s=new Headers(t.headers),i=e.url;fetch(i,jp(s,this._withCredentials,this._abortController)).then(e=>{if(t._responseOrigin=Bp(e.url),!Gp(e.status))throw zp(e.status,i);this._reader=e.body.getReader(),this._headersCapability.resolve();const s=e.headers,{allowRangeRequests:n,suggestedLength:a}=$p({responseHeaders:s,isHttp:t.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=n,this._contentLength=a||this._contentLength,this._filename=Hp(s),!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new hd("Streaming is disabled."))}).catch(this._headersCapability.reject),this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){var t;await this._headersCapability.promise;const{value:e,done:s}=await this._reader.read();return s?{value:e,done:s}:(this._loaded+=e.byteLength,null==(t=this.onProgress)||t.call(this,{loaded:this._loaded,total:this._contentLength}),{value:Up(e),done:!1})}cancel(t){var e;null==(e=this._reader)||e.cancel(t),this._abortController.abort()}}class Xp{constructor(t,e,s){this._stream=t,this._reader=null,this._loaded=0;const i=t.source;this._withCredentials=i.withCredentials||!1,this._readCapability=Promise.withResolvers(),this._isStreamingSupported=!i.disableStream,this._abortController=new AbortController;const n=new Headers(t.headers);n.append("Range",`bytes=${e}-${s-1}`);const a=i.url;fetch(a,jp(n,this._withCredentials,this._abortController)).then(e=>{const s=Bp(e.url);if(s!==t._responseOrigin)throw new Error(`Expected range response-origin "${s}" to match "${t._responseOrigin}".`);if(!Gp(e.status))throw zp(e.status,a);this._readCapability.resolve(),this._reader=e.body.getReader()}).catch(this._readCapability.reject),this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){var t;await this._readCapability.promise;const{value:e,done:s}=await this._reader.read();return s?{value:e,done:s}:(this._loaded+=e.byteLength,null==(t=this.onProgress)||t.call(this,{loaded:this._loaded}),{value:Up(e),done:!1})}cancel(t){var e;null==(e=this._reader)||e.cancel(t),this._abortController.abort()}}class Yp{constructor({url:t,httpHeaders:e,withCredentials:s}){ac(this,"_responseOrigin",null),this.url=t,this.isHttp=/^https?:/i.test(t),this.headers=Op(this.isHttp,e),this.withCredentials=s||!1,this.currXhrId=0,this.pendingRequests=Object.create(null)}request(t){const e=new XMLHttpRequest,s=this.currXhrId++,i=this.pendingRequests[s]={xhr:e};e.open("GET",this.url),e.withCredentials=this.withCredentials;for(const[n,a]of this.headers)e.setRequestHeader(n,a);return this.isHttp&&"begin"in t&&"end"in t?(e.setRequestHeader("Range",`bytes=${t.begin}-${t.end-1}`),i.expectedStatus=206):i.expectedStatus=200,e.responseType="arraybuffer",Zc(t.onError,"Expected `onError` callback to be provided."),e.onerror=()=>{t.onError(e.status)},e.onreadystatechange=this.onStateChange.bind(this,s),e.onprogress=this.onProgress.bind(this,s),i.onHeadersReceived=t.onHeadersReceived,i.onDone=t.onDone,i.onError=t.onError,i.onProgress=t.onProgress,e.send(null),s}onProgress(t,e){var s;const i=this.pendingRequests[t];i&&(null==(s=i.onProgress)||s.call(i,e))}onStateChange(t,e){const s=this.pendingRequests[t];if(!s)return;const i=s.xhr;if(i.readyState>=2&&s.onHeadersReceived&&(s.onHeadersReceived(),delete s.onHeadersReceived),4!==i.readyState)return;if(!(t in this.pendingRequests))return;if(delete this.pendingRequests[t],0===i.status&&this.isHttp)return void s.onError(i.status);const n=i.status||200;if(!(200===n&&206===s.expectedStatus)&&n!==s.expectedStatus)return void s.onError(i.status);const a=function(t){const e=t.response;return"string"!=typeof e?e:dd(e).buffer}(i);if(206===n){const t=i.getResponseHeader("Content-Range"),e=/bytes (\d+)-(\d+)\/(\d+)/.exec(t);e?s.onDone({begin:parseInt(e[1],10),chunk:a}):(Qc('Missing or invalid "Content-Range" header.'),s.onError(0))}else a?s.onDone({begin:0,chunk:a}):s.onError(i.status)}getRequestXhr(t){return this.pendingRequests[t].xhr}isPendingRequest(t){return t in this.pendingRequests}abortRequest(t){const e=this.pendingRequests[t].xhr;delete this.pendingRequests[t],e.abort()}}class Kp{constructor(t){this._source=t,this._manager=new Yp(t),this._rangeChunkSize=t.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(t){const e=this._rangeRequestReaders.indexOf(t);e>=0&&this._rangeRequestReaders.splice(e,1)}getFullReader(){return Zc(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once."),this._fullRequestReader=new Qp(this._manager,this._source),this._fullRequestReader}getRangeReader(t,e){const s=new Jp(this._manager,t,e);return s.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(s),s}cancelAllRequests(t){var e;null==(e=this._fullRequestReader)||e.cancel(t);for(const s of this._rangeRequestReaders.slice(0))s.cancel(t)}}class Qp{constructor(t,e){this._manager=t,this._url=e.url,this._fullRequestId=t.request({onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)}),this._headersCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._contentLength=e.length,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}_onHeadersReceived(){const t=this._fullRequestId,e=this._manager.getRequestXhr(t);this._manager._responseOrigin=Bp(e.responseURL);const s=e.getAllResponseHeaders(),i=new Headers(s?s.trimStart().replace(/[^\S ]+$/,"").split(/[\r\n]+/).map(t=>{const[e,...s]=t.split(": ");return[e,s.join(": ")]}):[]),{allowRangeRequests:n,suggestedLength:a}=$p({responseHeaders:i,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});n&&(this._isRangeSupported=!0),this._contentLength=a||this._contentLength,this._filename=Hp(i),this._isRangeSupported&&this._manager.abortRequest(t),this._headersCapability.resolve()}_onDone(t){if(t)if(this._requests.length>0){this._requests.shift().resolve({value:t.chunk,done:!1})}else this._cachedChunks.push(t.chunk);if(this._done=!0,!(this._cachedChunks.length>0)){for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(t){this._storedError=zp(t,this._url),this._headersCapability.reject(this._storedError);for(const e of this._requests)e.reject(this._storedError);this._requests.length=0,this._cachedChunks.length=0}_onProgress(t){var e;null==(e=this.onProgress)||e.call(this,{loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersCapability.promise}async read(){if(await this._headersCapability.promise,this._storedError)throw this._storedError;if(this._cachedChunks.length>0){return{value:this._cachedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0,this._headersCapability.reject(t);for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}}class Jp{constructor(t,e,s){this._manager=t,this._url=t.url,this._requestId=t.request({begin:e,end:s,onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)}),this._requests=[],this._queuedChunk=null,this._done=!1,this._storedError=void 0,this.onProgress=null,this.onClosed=null}_onHeadersReceived(){var t;const e=Bp(null==(t=this._manager.getRequestXhr(this._requestId))?void 0:t.responseURL);e!==this._manager._responseOrigin&&(this._storedError=new Error(`Expected range response-origin "${e}" to match "${this._manager._responseOrigin}".`),this._onError(0))}_close(){var t;null==(t=this.onClosed)||t.call(this,this)}_onDone(t){const e=t.chunk;if(this._requests.length>0){this._requests.shift().resolve({value:e,done:!1})}else this._queuedChunk=e;this._done=!0;for(const s of this._requests)s.resolve({value:void 0,done:!0});this._requests.length=0,this._close()}_onError(t){this._storedError??(this._storedError=zp(t,this._url));for(const e of this._requests)e.reject(this._storedError);this._requests.length=0,this._queuedChunk=null}_onProgress(t){var e;this.isStreamingSupported||null==(e=this.onProgress)||e.call(this,{loaded:t.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(null!==this._queuedChunk){const t=this._queuedChunk;return this._queuedChunk=null,{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}}const Zp=/^[a-z][a-z0-9\-+.]+:/i;class tg{constructor(t){this.source=t,this.url=function(t){if(Zp.test(t))return new URL(t);const e=process.getBuiltinModule("url");return new URL(e.pathToFileURL(t))}(t.url),Zc("file:"===this.url.protocol,"PDFNodeStream only supports file:// URLs."),this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){var t;return(null==(t=this._fullRequestReader)?void 0:t._loaded)??0}getFullReader(){return Zc(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once."),this._fullRequestReader=new eg(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new sg(this,t,e);return this._rangeRequestReaders.push(s),s}cancelAllRequests(t){var e;null==(e=this._fullRequestReader)||e.cancel(t);for(const s of this._rangeRequestReaders.slice(0))s.cancel(t)}}class eg{constructor(t){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null;const e=t.source;this._contentLength=e.length,this._loaded=0,this._filename=null,this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange,this._readableStream=null,this._readCapability=Promise.withResolvers(),this._headersCapability=Promise.withResolvers();const s=process.getBuiltinModule("fs");s.promises.lstat(this._url).then(t=>{this._contentLength=t.size,this._setReadableStream(s.createReadStream(this._url)),this._headersCapability.resolve()},t=>{"ENOENT"===t.code&&(t=zp(0,this._url.href)),this._storedError=t,this._headersCapability.reject(t)})}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){var t;if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const e=this._readableStream.read();if(null===e)return this._readCapability=Promise.withResolvers(),this.read();this._loaded+=e.length,null==(t=this.onProgress)||t.call(this,{loaded:this._loaded,total:this._contentLength});return{value:new Uint8Array(e).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",()=>{this._readCapability.resolve()}),t.on("end",()=>{t.destroy(),this._done=!0,this._readCapability.resolve()}),t.on("error",t=>{this._error(t)}),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new hd("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}}class sg{constructor(t,e,s){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=Promise.withResolvers();const i=t.source;this._isStreamingSupported=!i.disableStream;const n=process.getBuiltinModule("fs");this._setReadableStream(n.createReadStream(this._url,{start:e,end:s-1}))}get isStreamingSupported(){return this._isStreamingSupported}async read(){var t;if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const e=this._readableStream.read();if(null===e)return this._readCapability=Promise.withResolvers(),this.read();this._loaded+=e.length,null==(t=this.onProgress)||t.call(this,{loaded:this._loaded});return{value:new Uint8Array(e).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",()=>{this._readCapability.resolve()}),t.on("end",()=>{t.destroy(),this._done=!0,this._readCapability.resolve()}),t.on("error",t=>{this._error(t)}),this._storedError&&this._readableStream.destroy(this._storedError)}}const ig=Symbol("INITIAL_DATA");class ng{constructor(){lc(this,Ai),lc(this,bi,Object.create(null))}get(t,e=null){if(e){const s=cc(this,Ai,_i).call(this,t);return s.promise.then(()=>e(s.data)),null}const s=oc(this,bi)[t];if(!s||s.data===ig)throw new Error(`Requesting object that isn't resolved yet ${t}.`);return s.data}has(t){const e=oc(this,bi)[t];return!!e&&e.data!==ig}delete(t){const e=oc(this,bi)[t];return!(!e||e.data===ig)&&(delete oc(this,bi)[t],!0)}resolve(t,e=null){const s=cc(this,Ai,_i).call(this,t);s.data=e,s.resolve()}clear(){var t;for(const e in oc(this,bi)){const{data:s}=oc(this,bi)[e];null==(t=null==s?void 0:s.bitmap)||t.close()}hc(this,bi,Object.create(null))}*[Symbol.iterator](){for(const t in oc(this,bi)){const{data:e}=oc(this,bi)[t];e!==ig&&(yield[t,e])}}}bi=new WeakMap,Ai=new WeakSet,_i=function(t){var e;return(e=oc(this,bi))[t]||(e[t]={...Promise.withResolvers(),data:ig})};const ag=class t{constructor({textContentSource:e,container:s,viewport:i}){var n;if(lc(this,Vi),lc(this,xi,Promise.withResolvers()),lc(this,Si,null),lc(this,ki,!1),lc(this,Mi,!!(null==(yi=globalThis.FontInspector)?void 0:yi.enabled)),lc(this,Ei,null),lc(this,Ci,null),lc(this,Ti,0),lc(this,Ri,0),lc(this,Pi,null),lc(this,Ii,null),lc(this,Li,0),lc(this,Di,0),lc(this,Ni,Object.create(null)),lc(this,Fi,[]),lc(this,Wi,null),lc(this,Oi,[]),lc(this,Bi,new WeakMap),lc(this,$i,null),e instanceof ReadableStream)hc(this,Wi,e);else{if("object"!=typeof e)throw new Error('No "textContentSource" parameter specified.');hc(this,Wi,new ReadableStream({start(t){t.enqueue(e),t.close()}}))}hc(this,Si,hc(this,Ii,s)),hc(this,Di,i.scale*zd.pixelRatio),hc(this,Li,i.rotation),hc(this,Ci,{div:null,properties:null,ctx:null});const{pageWidth:a,pageHeight:r,pageX:o,pageY:l}=i.rawDims;hc(this,$i,[1,0,0,-1,-o,l+r]),hc(this,Ri,a),hc(this,Ti,r),cc(n=t,Ki,Zi).call(n),Hd(s,i),oc(this,xi).promise.finally(()=>{oc(t,Ui).delete(this),hc(this,Ci,null),hc(this,Ni,null)}).catch(()=>{})}static get fontFamilyMap(){const{isWindows:t,isFirefox:e}=ud.platform;return sd(this,"fontFamilyMap",new Map([["sans-serif",(t&&e?"Calibri, ":"")+"sans-serif"],["monospace",(t&&e?"Lucida Console, ":"")+"monospace"]]))}render(){const e=()=>{oc(this,Pi).read().then(({value:t,done:s})=>{s?oc(this,xi).resolve():(oc(this,Ei)??hc(this,Ei,t.lang),Object.assign(oc(this,Ni),t.styles),cc(this,Vi,qi).call(this,t.items),e())},oc(this,xi).reject)};return hc(this,Pi,oc(this,Wi).getReader()),oc(t,Ui).add(this),e(),oc(this,xi).promise}update({viewport:e,onBefore:s=null}){var i;const n=e.scale*zd.pixelRatio,a=e.rotation;if(a!==oc(this,Li)&&(null==s||s(),hc(this,Li,a),Hd(oc(this,Ii),{rotation:a})),n!==oc(this,Di)){null==s||s(),hc(this,Di,n);const e={div:null,properties:null,ctx:cc(i=t,Ki,Qi).call(i,oc(this,Ei))};for(const t of oc(this,Oi))e.properties=oc(this,Bi).get(t),e.div=t,cc(this,Vi,Yi).call(this,e)}}cancel(){var t;const e=new hd("TextLayer task cancelled.");null==(t=oc(this,Pi))||t.cancel(e).catch(()=>{}),hc(this,Pi,null),oc(this,xi).reject(e)}get textDivs(){return oc(this,Oi)}get textContentItemsStr(){return oc(this,Fi)}static cleanup(){if(!(oc(this,Ui).size>0)){oc(this,Hi).clear();for(const{canvas:t}of oc(this,zi).values())t.remove();oc(this,zi).clear()}}};xi=new WeakMap,Si=new WeakMap,ki=new WeakMap,Mi=new WeakMap,Ei=new WeakMap,Ci=new WeakMap,Ti=new WeakMap,Ri=new WeakMap,Pi=new WeakMap,Ii=new WeakMap,Li=new WeakMap,Di=new WeakMap,Ni=new WeakMap,Fi=new WeakMap,Wi=new WeakMap,Oi=new WeakMap,Bi=new WeakMap,$i=new WeakMap,Hi=new WeakMap,zi=new WeakMap,Gi=new WeakMap,ji=new WeakMap,Ui=new WeakMap,Vi=new WeakSet,qi=function(t){var e,s;if(oc(this,ki))return;(s=oc(this,Ci)).ctx??(s.ctx=cc(e=ag,Ki,Qi).call(e,oc(this,Ei)));const i=oc(this,Oi),n=oc(this,Fi);for(const a of t){if(i.length>1e5)return Qc("Ignoring additional textDivs for performance reasons."),void hc(this,ki,!0);if(void 0!==a.str)n.push(a.str),cc(this,Vi,Xi).call(this,a);else if("beginMarkedContentProps"===a.type||"beginMarkedContent"===a.type){const t=oc(this,Si);hc(this,Si,document.createElement("span")),oc(this,Si).classList.add("markedContent"),null!==a.id&&oc(this,Si).setAttribute("id",`${a.id}`),t.append(oc(this,Si))}else"endMarkedContent"===a.type&&hc(this,Si,oc(this,Si).parentNode)}},Xi=function(t){var e;const s=document.createElement("span"),i={angle:0,canvasWidth:0,hasText:""!==t.str,hasEOL:t.hasEOL,fontSize:0};oc(this,Oi).push(s);const n=gd.transform(oc(this,$i),t.transform);let a=Math.atan2(n[1],n[0]);const r=oc(this,Ni)[t.fontName];r.vertical&&(a+=Math.PI/2);let o=oc(this,Mi)&&r.fontSubstitution||r.fontFamily;o=ag.fontFamilyMap.get(o)||o;const l=Math.hypot(n[2],n[3]),h=l*cc(e=ag,Ki,tn).call(e,o,r,oc(this,Ei));let c,d;0===a?(c=n[4],d=n[5]-h):(c=n[4]+h*Math.sin(a),d=n[5]-h*Math.cos(a));const u="calc(var(--total-scale-factor) *",p=s.style;oc(this,Si)===oc(this,Ii)?(p.left=`${(100*c/oc(this,Ri)).toFixed(2)}%`,p.top=`${(100*d/oc(this,Ti)).toFixed(2)}%`):(p.left=`${u}${c.toFixed(2)}px)`,p.top=`${u}${d.toFixed(2)}px)`),p.fontSize=`${u}${(oc(ag,ji)*l).toFixed(2)}px)`,p.fontFamily=o,i.fontSize=l,s.setAttribute("role","presentation"),s.textContent=t.str,s.dir=t.dir,oc(this,Mi)&&(s.dataset.fontName=r.fontSubstitutionLoadedName||t.fontName),0!==a&&(i.angle=a*(180/Math.PI));let g=!1;if(t.str.length>1)g=!0;else if(" "!==t.str&&t.transform[0]!==t.transform[3]){const e=Math.abs(t.transform[0]),s=Math.abs(t.transform[3]);e!==s&&Math.max(e,s)/Math.min(e,s)>1.5&&(g=!0)}if(g&&(i.canvasWidth=r.vertical?t.height:t.width),oc(this,Bi).set(s,i),oc(this,Ci).div=s,oc(this,Ci).properties=i,cc(this,Vi,Yi).call(this,oc(this,Ci)),i.hasText&&oc(this,Si).append(s),i.hasEOL){const t=document.createElement("br");t.setAttribute("role","presentation"),oc(this,Si).append(t)}},Yi=function(t){var e;const{div:s,properties:i,ctx:n}=t,{style:a}=s;let r="";if(oc(ag,ji)>1&&(r=`scale(${1/oc(ag,ji)})`),0!==i.canvasWidth&&i.hasText){const{fontFamily:t}=a,{canvasWidth:o,fontSize:l}=i;cc(e=ag,Ki,Ji).call(e,n,l*oc(this,Di),t);const{width:h}=n.measureText(s.textContent);h>0&&(r=`scaleX(${o*oc(this,Di)/h}) ${r}`)}0!==i.angle&&(r=`rotate(${i.angle}deg) ${r}`),r.length>0&&(a.transform=r)},Ki=new WeakSet,Qi=function(t=null){let e=oc(this,zi).get(t||(t=""));if(!e){const s=document.createElement("canvas");s.className="hiddenCanvasElement",s.lang=t,document.body.append(s),e=s.getContext("2d",{alpha:!1,willReadFrequently:!0}),oc(this,zi).set(t,e),oc(this,Gi).set(e,{size:0,family:""})}return e},Ji=function(t,e,s){const i=oc(this,Gi).get(t);e===i.size&&s===i.family||(t.font=`${e}px ${s}`,i.size=e,i.family=s)},Zi=function(){if(null!==oc(this,ji))return;const t=document.createElement("div");t.style.opacity=0,t.style.lineHeight=1,t.style.fontSize="1px",t.style.position="absolute",t.textContent="X",document.body.append(t),hc(this,ji,t.getBoundingClientRect().height),t.remove()},tn=function(t,e,s){const i=oc(this,Hi).get(t);if(i)return i;const n=cc(this,Ki,Qi).call(this,s);n.canvas.width=n.canvas.height=30,cc(this,Ki,Ji).call(this,n,30,t);const a=n.measureText(""),r=a.fontBoundingBoxAscent,o=Math.abs(a.fontBoundingBoxDescent);n.canvas.width=n.canvas.height=0;let l=.8;return r?l=r/(r+o):(ud.platform.isFirefox&&Qc("Enable the `dom.textMetrics.fontBoundingBox.enabled` preference in `about:config` to improve TextLayer rendering."),e.ascent?l=e.ascent:e.descent&&(l=1+e.descent)),oc(this,Hi).set(t,l),l},lc(ag,Ki),lc(ag,Hi,new Map),lc(ag,zi,new Map),lc(ag,Gi,new WeakMap),lc(ag,ji,null),lc(ag,Ui,new Set);let rg=ag;class og{static textContent(t){const e=[],s={items:e,styles:Object.create(null)};return function t(s){var i;if(!s)return;let n=null;const a=s.name;if("#text"===a)n=s.value;else{if(!og.shouldBuildText(a))return;(null==(i=null==s?void 0:s.attributes)?void 0:i.textContent)?n=s.attributes.textContent:s.value&&(n=s.value)}if(null!==n&&e.push({str:n}),s.children)for(const e of s.children)t(e)}(t),s}static shouldBuildText(t){return!("textarea"===t||"input"===t||"option"===t||"select"===t)}}function lg(t={}){"string"==typeof t||t instanceof URL?t={url:t}:(t instanceof ArrayBuffer||ArrayBuffer.isView(t))&&(t={data:t});const e=new cg,{docId:s}=e,i=t.url?function(t){if(t instanceof URL)return t.href;if("string"==typeof t){if(uc)return t;const e=URL.parse(t,window.location);if(e)return e.href}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}(t.url):null,n=t.data?function(t){if(uc&&"undefined"!=typeof Buffer&&t instanceof Buffer)throw new Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(t instanceof Uint8Array&&t.byteLength===t.buffer.byteLength)return t;if("string"==typeof t)return dd(t);if(t instanceof ArrayBuffer||ArrayBuffer.isView(t)||"object"==typeof t&&!isNaN(null==t?void 0:t.length))return new Uint8Array(t);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}(t.data):null,a=t.httpHeaders||null,r=!0===t.withCredentials,o=t.password??null,l=t.range instanceof dg?t.range:null,h=Number.isInteger(t.rangeChunkSize)&&t.rangeChunkSize>0?t.rangeChunkSize:65536;let c=t.worker instanceof fg?t.worker:null;const d=t.verbosity,u="string"!=typeof t.docBaseUrl||Cd(t.docBaseUrl)?null:t.docBaseUrl,p=bu(t.cMapUrl),g=!1!==t.cMapPacked,f=t.CMapReaderFactory||(uc?Yu:Bu),m=bu(t.iccUrl),v=bu(t.standardFontDataUrl),w=t.StandardFontDataFactory||(uc?Ku:Gu),b=bu(t.wasmUrl),A=t.WasmFactory||(uc?Qu:Uu),_=!0!==t.stopAtErrors,y=Number.isInteger(t.maxImageSize)&&t.maxImageSize>-1?t.maxImageSize:-1,x=!1!==t.isEvalSupported,S="boolean"==typeof t.isOffscreenCanvasSupported?t.isOffscreenCanvasSupported:!uc,k="boolean"==typeof t.isImageDecoderSupported?t.isImageDecoderSupported:!uc&&(ud.platform.isFirefox||!globalThis.chrome),M=Number.isInteger(t.canvasMaxAreaInBytes)?t.canvasMaxAreaInBytes:-1,E="boolean"==typeof t.disableFontFace?t.disableFontFace:uc,C=!0===t.fontExtraProperties,T=!0===t.enableXfa,R=t.ownerDocument||globalThis.document,P=!0===t.disableRange,I=!0===t.disableStream,L=!0===t.disableAutoFetch,D=!0===t.pdfBug,N=t.CanvasFactory||(uc?Xu:Wu),F=t.FilterFactory||(uc?qu:Hu),W=!0===t.enableHWA,O=!1!==t.useWasm,B=l?l.length:t.length??NaN,$="boolean"==typeof t.useSystemFonts?t.useSystemFonts:!uc&&!E,H="boolean"==typeof t.useWorkerFetch?t.useWorkerFetch:!!(f===Bu&&w===Gu&&A===Uu&&p&&v&&b&&Ld(p,document.baseURI)&&Ld(v,document.baseURI)&&Ld(b,document.baseURI));var z;z=d,Number.isInteger(z)&&(Xc=z);const G={canvasFactory:new N({ownerDocument:R,enableHWA:W}),filterFactory:new F({docId:s,ownerDocument:R}),cMapReaderFactory:H?null:new f({baseUrl:p,isCompressed:g}),standardFontDataFactory:H?null:new w({baseUrl:v}),wasmFactory:H?null:new A({baseUrl:b})};c||(c=fg.create({verbosity:d,port:Rp.workerPort}),e._worker=c);const j={docId:s,apiVersion:"5.3.93",data:n,password:o,disableAutoFetch:L,rangeChunkSize:h,length:B,docBaseUrl:u,enableXfa:T,evaluatorOptions:{maxImageSize:y,disableFontFace:E,ignoreErrors:_,isEvalSupported:x,isOffscreenCanvasSupported:S,isImageDecoderSupported:k,canvasMaxAreaInBytes:M,fontExtraProperties:C,useSystemFonts:$,useWasm:O,useWorkerFetch:H,cMapUrl:p,iccUrl:m,standardFontDataUrl:v,wasmUrl:b}},U={ownerDocument:R,pdfBug:D,styleElement:null,loadingParams:{disableAutoFetch:L,enableXfa:T}};return c.promise.then(function(){if(e.destroyed)throw new Error("Loading aborted");if(c.destroyed)throw new Error("Worker was destroyed");const t=c.messageHandler.sendWithPromise("GetDocRequest",j,n?[n.buffer]:null);let o;if(l)o=new Np(l,{disableRange:P,disableStream:I});else if(!n){if(!i)throw new Error("getDocument - no `url` parameter provided.");const t=Ld(i)?Vp:uc?tg:Kp;o=new t({url:i,length:B,httpHeaders:a,withCredentials:r,rangeChunkSize:h,disableRange:P,disableStream:I})}return t.then(t=>{if(e.destroyed)throw new Error("Loading aborted");if(c.destroyed)throw new Error("Worker was destroyed");const i=new Nu(s,t,c.port),n=new mg(i,e,o,U,G);e._transport=n,i.send("Ready",null)})}).catch(e._capability.reject),e}const hg=class t{constructor(){ac(this,"_capability",Promise.withResolvers()),ac(this,"_transport",null),ac(this,"_worker",null),ac(this,"docId","d"+dc(t,en)._++),ac(this,"destroyed",!1),ac(this,"onPassword",null),ac(this,"onProgress",null)}get promise(){return this._capability.promise}async destroy(){var t,e,s,i;this.destroyed=!0;try{(null==(t=this._worker)?void 0:t.port)&&(this._worker._pendingDestroy=!0),await(null==(e=this._transport)?void 0:e.destroy())}catch(n){throw(null==(s=this._worker)?void 0:s.port)&&delete this._worker._pendingDestroy,n}this._transport=null,null==(i=this._worker)||i.destroy(),this._worker=null}async getData(){return this._transport.getData()}};en=new WeakMap,lc(hg,en,0);let cg=hg;class dg{constructor(t,e,s=!1,i=null){lc(this,sn,Promise.withResolvers()),lc(this,nn,[]),lc(this,an,[]),lc(this,rn,[]),lc(this,on,[]),this.length=t,this.initialData=e,this.progressiveDone=s,this.contentDispositionFilename=i}addRangeListener(t){oc(this,on).push(t)}addProgressListener(t){oc(this,rn).push(t)}addProgressiveReadListener(t){oc(this,an).push(t)}addProgressiveDoneListener(t){oc(this,nn).push(t)}onDataRange(t,e){for(const s of oc(this,on))s(t,e)}onDataProgress(t,e){oc(this,sn).promise.then(()=>{for(const s of oc(this,rn))s(t,e)})}onDataProgressiveRead(t){oc(this,sn).promise.then(()=>{for(const e of oc(this,an))e(t)})}onDataProgressiveDone(){oc(this,sn).promise.then(()=>{for(const t of oc(this,nn))t()})}transportReady(){oc(this,sn).resolve()}requestDataRange(t,e){Jc("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}sn=new WeakMap,nn=new WeakMap,an=new WeakMap,rn=new WeakMap,on=new WeakMap;class ug{constructor(t,e){this._pdfInfo=t,this._transport=e}get annotationStorage(){return this._transport.annotationStorage}get canvasFactory(){return this._transport.canvasFactory}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return sd(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(t){return this._transport.getPage(t)}getPageIndex(t){return this._transport.getPageIndex(t)}getDestinations(){return this._transport.getDestinations()}getDestination(t){return this._transport.getDestination(t)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getOptionalContentConfig(e)}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(t=!1){return this._transport.startCleanup(t||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}cachedPageNumber(t){return this._transport.cachedPageNumber(t)}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}class pg{constructor(t,e,s,i=!1){lc(this,hn),lc(this,ln,!1),this._pageIndex=t,this._pageInfo=e,this._transport=s,this._stats=i?new Id:null,this._pdfBug=i,this.commonObjs=s.commonObjs,this.objs=new ng,this._intentStates=new Map,this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:t,rotation:e=this.rotate,offsetX:s=0,offsetY:i=0,dontFlip:n=!1}={}){return new Md({viewBox:this.view,userUnit:this.userUnit,scale:t,rotation:e,offsetX:s,offsetY:i,dontFlip:n})}getAnnotations({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getAnnotations(this._pageIndex,e)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return sd(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){var t;return(null==(t=this._transport._htmlForXfa)?void 0:t.children[this._pageIndex])||null}render({canvasContext:t,viewport:e,intent:s="display",annotationMode:i=xc.ENABLE,transform:n=null,background:a=null,optionalContentConfigPromise:r=null,annotationCanvasMap:o=null,pageColors:l=null,printAnnotationStorage:h=null,isEditing:c=!1}){var d,u;null==(d=this._stats)||d.time("Overall");const p=this._transport.getRenderingIntent(s,i,h,c),{renderingIntent:g,cacheKey:f}=p;hc(this,ln,!1),r||(r=this._transport.getOptionalContentConfig(g));let m=this._intentStates.get(f);m||(m=Object.create(null),this._intentStates.set(f,m)),m.streamReaderCancelTimeout&&(clearTimeout(m.streamReaderCancelTimeout),m.streamReaderCancelTimeout=null);const v=!!(g&vc);m.displayReadyCapability||(m.displayReadyCapability=Promise.withResolvers(),m.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},null==(u=this._stats)||u.time("Page Request"),this._pumpOperatorList(p));const w=t=>{var e;m.renderTasks.delete(b),v&&hc(this,ln,!0),cc(this,hn,cn).call(this),t?(b.capability.reject(t),this._abortOperatorList({intentState:m,reason:t instanceof Error?t:new Error(t)})):b.capability.resolve(),this._stats&&(this._stats.timeEnd("Rendering"),this._stats.timeEnd("Overall"),(null==(e=globalThis.Stats)?void 0:e.enabled)&&globalThis.Stats.add(this.pageNumber,this._stats))},b=new bg({callback:w,params:{canvasContext:t,viewport:e,transform:n,background:a},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:o,operatorList:m.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!v,pdfBug:this._pdfBug,pageColors:l});(m.renderTasks||(m.renderTasks=new Set)).add(b);const A=b.task;return Promise.all([m.displayReadyCapability.promise,r]).then(([t,e])=>{var s;if(this.destroyed)w();else{if(null==(s=this._stats)||s.time("Rendering"),!(e.renderingIntent&g))throw new Error("Must use the same `intent`-argument when calling the `PDFPageProxy.render` and `PDFDocumentProxy.getOptionalContentConfig` methods.");b.initializeGraphics({transparency:t,optionalContentConfig:e}),b.operatorListChanged()}}).catch(w),A}getOperatorList({intent:t="display",annotationMode:e=xc.ENABLE,printAnnotationStorage:s=null,isEditing:i=!1}={}){var n;const a=this._transport.getRenderingIntent(t,e,s,i,!0);let r,o=this._intentStates.get(a.cacheKey);return o||(o=Object.create(null),this._intentStates.set(a.cacheKey,o)),o.opListReadCapability||(r=Object.create(null),r.operatorListChanged=function(){o.operatorList.lastChunk&&(o.opListReadCapability.resolve(o.operatorList),o.renderTasks.delete(r))},o.opListReadCapability=Promise.withResolvers(),(o.renderTasks||(o.renderTasks=new Set)).add(r),o.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},null==(n=this._stats)||n.time("Page Request"),this._pumpOperatorList(a)),o.opListReadCapability.promise}streamTextContent({includeMarkedContent:t=!1,disableNormalization:e=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:!0===t,disableNormalization:!0===e},{highWaterMark:100,size:t=>t.items.length})}getTextContent(t={}){if(this._transport._htmlForXfa)return this.getXfa().then(t=>og.textContent(t));const e=this.streamTextContent(t);return new Promise(function(t,s){const i=e.getReader(),n={items:[],styles:Object.create(null),lang:null};!function e(){i.read().then(function({value:s,done:i}){i?t(n):(n.lang??(n.lang=s.lang),Object.assign(n.styles,s.styles),n.items.push(...s.items),e())},s)}()})}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const t=[];for(const e of this._intentStates.values())if(this._abortOperatorList({intentState:e,reason:new Error("Page was destroyed."),force:!0}),!e.opListReadCapability)for(const s of e.renderTasks)t.push(s.completed),s.cancel();return this.objs.clear(),hc(this,ln,!1),Promise.all(t)}cleanup(t=!1){hc(this,ln,!0);const e=cc(this,hn,cn).call(this);return t&&e&&this._stats&&(this._stats=new Id),e}_startRenderPage(t,e){var s,i;const n=this._intentStates.get(e);n&&(null==(s=this._stats)||s.timeEnd("Page Request"),null==(i=n.displayReadyCapability)||i.resolve(t))}_renderPageChunk(t,e){for(let s=0,i=t.length;s<i;s++)e.operatorList.fnArray.push(t.fnArray[s]),e.operatorList.argsArray.push(t.argsArray[s]);e.operatorList.lastChunk=t.lastChunk,e.operatorList.separateAnnots=t.separateAnnots;for(const s of e.renderTasks)s.operatorListChanged();t.lastChunk&&cc(this,hn,cn).call(this)}_pumpOperatorList({renderingIntent:t,cacheKey:e,annotationStorageSerializable:s,modifiedIds:i}){const{map:n,transfer:a}=s,r=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:t,cacheKey:e,annotationStorage:n,modifiedIds:i},a).getReader(),o=this._intentStates.get(e);o.streamReader=r;const l=()=>{r.read().then(({value:t,done:e})=>{e?o.streamReader=null:this._transport.destroyed||(this._renderPageChunk(t,o),l())},t=>{if(o.streamReader=null,!this._transport.destroyed){if(o.operatorList){o.operatorList.lastChunk=!0;for(const t of o.renderTasks)t.operatorListChanged();cc(this,hn,cn).call(this)}if(o.displayReadyCapability)o.displayReadyCapability.reject(t);else{if(!o.opListReadCapability)throw t;o.opListReadCapability.reject(t)}}})};l()}_abortOperatorList({intentState:t,reason:e,force:s=!1}){if(t.streamReader){if(t.streamReaderCancelTimeout&&(clearTimeout(t.streamReaderCancelTimeout),t.streamReaderCancelTimeout=null),!s){if(t.renderTasks.size>0)return;if(e instanceof Ed){let s=100;return e.extraDelay>0&&e.extraDelay<1e3&&(s+=e.extraDelay),void(t.streamReaderCancelTimeout=setTimeout(()=>{t.streamReaderCancelTimeout=null,this._abortOperatorList({intentState:t,reason:e,force:!0})},s))}}if(t.streamReader.cancel(new hd(e.message)).catch(()=>{}),t.streamReader=null,!this._transport.destroyed){for(const[e,s]of this._intentStates)if(s===t){this._intentStates.delete(e);break}this.cleanup()}}}get stats(){return this._stats}}ln=new WeakMap,hn=new WeakSet,cn=function(){if(!oc(this,ln)||this.destroyed)return!1;for(const{renderTasks:t,operatorList:e}of this._intentStates.values())if(t.size>0||!e.lastChunk)return!1;return this._intentStates.clear(),this.objs.clear(),hc(this,ln,!1),!0};const gg=class t{constructor({name:e=null,port:s=null,verbosity:i=Yc()}={}){if(lc(this,wn),lc(this,dn,Promise.withResolvers()),lc(this,un,null),lc(this,pn,null),lc(this,gn,null),this.name=e,this.destroyed=!1,this.verbosity=i,s){if(oc(t,vn).has(s))throw new Error("Cannot use more than one PDFWorker per port.");oc(t,vn).set(s,this),cc(this,wn,An).call(this,s)}else cc(this,wn,_n).call(this)}get promise(){return oc(this,dn).promise}get port(){return oc(this,pn)}get messageHandler(){return oc(this,un)}destroy(){var e,s;this.destroyed=!0,null==(e=oc(this,gn))||e.terminate(),hc(this,gn,null),oc(t,vn).delete(oc(this,pn)),hc(this,pn,null),null==(s=oc(this,un))||s.destroy(),hc(this,un,null)}static create(e){const s=oc(this,vn).get(null==e?void 0:e.port);if(s){if(s._pendingDestroy)throw new Error("PDFWorker.create - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return s}return new t(e)}static get workerSrc(){if(Rp.workerSrc)return Rp.workerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get _setupFakeWorkerGlobal(){return sd(this,"_setupFakeWorkerGlobal",(async()=>{if(oc(this,xn,Sn))return oc(this,xn,Sn);return(await import(this.workerSrc)).WorkerMessageHandler})())}};dn=new WeakMap,un=new WeakMap,pn=new WeakMap,gn=new WeakMap,fn=new WeakMap,mn=new WeakMap,vn=new WeakMap,wn=new WeakSet,bn=function(){oc(this,dn).resolve(),oc(this,un).send("configure",{verbosity:this.verbosity})},An=function(t){hc(this,pn,t),hc(this,un,new Nu("main","worker",t)),oc(this,un).on("ready",()=>{}),cc(this,wn,bn).call(this)},_n=function(){if(oc(gg,mn)||oc(gg,xn,Sn))return void cc(this,wn,yn).call(this);let{workerSrc:t}=gg;try{gg._isSameOrigin(window.location,t)||(t=gg._createCDNWrapper(new URL(t,window.location).href));const e=new Worker(t,{type:"module"}),s=new Nu("main","worker",e),i=()=>{n.abort(),s.destroy(),e.terminate(),this.destroyed?oc(this,dn).reject(new Error("Worker was destroyed")):cc(this,wn,yn).call(this)},n=new AbortController;e.addEventListener("error",()=>{oc(this,gn)||i()},{signal:n.signal}),s.on("test",t=>{n.abort(),!this.destroyed&&t?(hc(this,un,s),hc(this,pn,e),hc(this,gn,e),cc(this,wn,bn).call(this)):i()}),s.on("ready",t=>{if(n.abort(),this.destroyed)i();else try{a()}catch{cc(this,wn,yn).call(this)}});const a=()=>{const t=new Uint8Array;s.send("test",t,[t.buffer])};return void a()}catch{Kc("The worker has been disabled.")}cc(this,wn,yn).call(this)},yn=function(){oc(gg,mn)||(Qc("Setting up fake worker."),hc(gg,mn,!0)),gg._setupFakeWorkerGlobal.then(t=>{if(this.destroyed)return void oc(this,dn).reject(new Error("Worker was destroyed"));const e=new yu;hc(this,pn,e);const s="fake"+dc(gg,fn)._++,i=new Nu(s+"_worker",s,e);t.setup(i,e),hc(this,un,new Nu(s,s+"_worker",e)),cc(this,wn,bn).call(this)}).catch(t=>{oc(this,dn).reject(new Error(`Setting up fake worker failed: "${t.message}".`))})},xn=new WeakSet,Sn=function(){var t;try{return(null==(t=globalThis.pdfjsWorker)?void 0:t.WorkerMessageHandler)||null}catch{return null}},lc(gg,xn),lc(gg,fn,0),lc(gg,mn,!1),lc(gg,vn,new WeakMap),uc&&(hc(gg,mn,!0),Rp.workerSrc||(Rp.workerSrc="./pdf.worker.mjs")),gg._isSameOrigin=(t,e)=>{const s=URL.parse(t);if(!(null==s?void 0:s.origin)||"null"===s.origin)return!1;const i=new URL(e,s);return s.origin===i.origin},gg._createCDNWrapper=t=>{const e=`await import("${t}");`;return URL.createObjectURL(new Blob([e],{type:"text/javascript"}))},gg.fromPort=t=>{var e;if(e="`PDFWorker.fromPort` - please use `PDFWorker.create` instead.",console.log("Deprecated API usage: "+e),!(null==t?void 0:t.port))throw new Error("PDFWorker.fromPort - invalid method signature.");return gg.create(t)};let fg=gg;class mg{constructor(t,e,s,i,n){lc(this,Rn),lc(this,kn,new Map),lc(this,Mn,new Map),lc(this,En,new Map),lc(this,Cn,new Map),lc(this,Tn,null),this.messageHandler=t,this.loadingTask=e,this.commonObjs=new ng,this.fontLoader=new vu({ownerDocument:i.ownerDocument,styleElement:i.styleElement}),this.loadingParams=i.loadingParams,this._params=i,this.canvasFactory=n.canvasFactory,this.filterFactory=n.filterFactory,this.cMapReaderFactory=n.cMapReaderFactory,this.standardFontDataFactory=n.standardFontDataFactory,this.wasmFactory=n.wasmFactory,this.destroyed=!1,this.destroyCapability=null,this._networkStream=s,this._fullReader=null,this._lastProgress=null,this.downloadInfoCapability=Promise.withResolvers(),this.setupMessageHandler()}get annotationStorage(){return sd(this,"annotationStorage",new fu)}getRenderingIntent(t,e=xc.ENABLE,s=null,i=!1,n=!1){let a=mc,r=gu;switch(t){case"any":a=fc;break;case"display":break;case"print":a=vc;break;default:Qc(`getRenderingIntent - invalid intent: ${t}`)}const o=a&vc&&s instanceof mu?s:this.annotationStorage;switch(e){case xc.DISABLE:a+=Ac;break;case xc.ENABLE:break;case xc.ENABLE_FORMS:a+=wc;break;case xc.ENABLE_STORAGE:a+=bc,r=o.serializable;break;default:Qc(`getRenderingIntent - invalid annotationMode: ${e}`)}i&&(a+=_c),n&&(a+=yc);const{ids:l,hash:h}=o.modifiedIds;return{renderingIntent:a,cacheKey:[a,r.hash,h].join("_"),annotationStorageSerializable:r,modifiedIds:l}}destroy(){var t;if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=Promise.withResolvers(),null==(t=oc(this,Tn))||t.reject(new Error("Worker was destroyed during onPassword callback"));const e=[];for(const i of oc(this,Mn).values())e.push(i._destroy());oc(this,Mn).clear(),oc(this,En).clear(),oc(this,Cn).clear(),this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const s=this.messageHandler.sendWithPromise("Terminate",null);return e.push(s),Promise.all(e).then(()=>{var t,e;this.commonObjs.clear(),this.fontLoader.clear(),oc(this,kn).clear(),this.filterFactory.destroy(),rg.cleanup(),null==(t=this._networkStream)||t.cancelAllRequests(new hd("Worker was terminated.")),null==(e=this.messageHandler)||e.destroy(),this.messageHandler=null,this.destroyCapability.resolve()},this.destroyCapability.reject),this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:t,loadingTask:e}=this;t.on("GetReader",(t,e)=>{Zc(this._networkStream,"GetReader - no `IPDFStream` instance available."),this._fullReader=this._networkStream.getFullReader(),this._fullReader.onProgress=t=>{this._lastProgress={loaded:t.loaded,total:t.total}},e.onPull=()=>{this._fullReader.read().then(function({value:t,done:s}){s?e.close():(Zc(t instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer."),e.enqueue(new Uint8Array(t),1,[t]))}).catch(t=>{e.error(t)})},e.onCancel=t=>{this._fullReader.cancel(t),e.ready.catch(t=>{if(!this.destroyed)throw t})}}),t.on("ReaderHeadersReady",async t=>{var s;await this._fullReader.headersReady;const{isStreamingSupported:i,isRangeSupported:n,contentLength:a}=this._fullReader;return i&&n||(this._lastProgress&&(null==(s=e.onProgress)||s.call(e,this._lastProgress)),this._fullReader.onProgress=t=>{var s;null==(s=e.onProgress)||s.call(e,{loaded:t.loaded,total:t.total})}),{isStreamingSupported:i,isRangeSupported:n,contentLength:a}}),t.on("GetRangeReader",(t,e)=>{Zc(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const s=this._networkStream.getRangeReader(t.begin,t.end);s?(e.onPull=()=>{s.read().then(function({value:t,done:s}){s?e.close():(Zc(t instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer."),e.enqueue(new Uint8Array(t),1,[t]))}).catch(t=>{e.error(t)})},e.onCancel=t=>{s.cancel(t),e.ready.catch(t=>{if(!this.destroyed)throw t})}):e.close()}),t.on("GetDoc",({pdfInfo:t})=>{this._numPages=t.numPages,this._htmlForXfa=t.htmlForXfa,delete t.htmlForXfa,e._capability.resolve(new ug(t,this))}),t.on("DocException",t=>{e._capability.reject(Du(t))}),t.on("PasswordRequest",t=>{hc(this,Tn,Promise.withResolvers());try{if(!e.onPassword)throw Du(t);const s=t=>{t instanceof Error?oc(this,Tn).reject(t):oc(this,Tn).resolve({password:t})};e.onPassword(s,t.code)}catch(s){oc(this,Tn).reject(s)}return oc(this,Tn).promise}),t.on("DataLoaded",t=>{var s;null==(s=e.onProgress)||s.call(e,{loaded:t.length,total:t.length}),this.downloadInfoCapability.resolve(t)}),t.on("StartRenderPage",t=>{if(this.destroyed)return;oc(this,Mn).get(t.pageIndex)._startRenderPage(t.transparency,t.cacheKey)}),t.on("commonobj",([e,s,i])=>{var n;if(this.destroyed)return null;if(this.commonObjs.has(e))return null;switch(s){case"Font":if("error"in i){const t=i.error;Qc(`Error during font loading: ${t}`),this.commonObjs.resolve(e,t);break}const a=this._params.pdfBug&&(null==(n=globalThis.FontInspector)?void 0:n.enabled)?(t,e)=>globalThis.FontInspector.fontAdded(t,e):null,r=new wu(i,a);this.fontLoader.bind(r).catch(()=>t.sendWithPromise("FontFallback",{id:e})).finally(()=>{!r.fontExtraProperties&&r.data&&(r.data=null),this.commonObjs.resolve(e,r)});break;case"CopyLocalImage":const{imageRef:o}=i;Zc(o,"The imageRef must be defined.");for(const t of oc(this,Mn).values())for(const[,s]of t.objs)if((null==s?void 0:s.ref)===o)return s.dataLen?(this.commonObjs.resolve(e,structuredClone(s)),s.dataLen):null;break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(e,i);break;default:throw new Error(`Got unknown common object type ${s}`)}return null}),t.on("obj",([t,e,s,i])=>{var n;if(this.destroyed)return;const a=oc(this,Mn).get(e);if(!a.objs.has(t))if(0!==a._intentStates.size)switch(s){case"Image":case"Pattern":a.objs.resolve(t,i);break;default:throw new Error(`Got unknown object type ${s}`)}else null==(n=null==i?void 0:i.bitmap)||n.close()}),t.on("DocProgress",t=>{var s;this.destroyed||null==(s=e.onProgress)||s.call(e,{loaded:t.loaded,total:t.total})}),t.on("FetchBinaryData",async t=>{if(this.destroyed)throw new Error("Worker was destroyed.");const e=this[t.type];if(!e)throw new Error(`${t.type} not initialized, see the \`useWorkerFetch\` parameter.`);return e.fetch(t)})}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){var t;this.annotationStorage.size<=0&&Qc("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");const{map:e,transfer:s}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:e,filename:(null==(t=this._fullReader)?void 0:t.filename)??null},s).finally(()=>{this.annotationStorage.resetModified()})}getPage(t){if(!Number.isInteger(t)||t<=0||t>this._numPages)return Promise.reject(new Error("Invalid page request."));const e=t-1,s=oc(this,En).get(e);if(s)return s;const i=this.messageHandler.sendWithPromise("GetPage",{pageIndex:e}).then(s=>{if(this.destroyed)throw new Error("Transport destroyed");s.refStr&&oc(this,Cn).set(s.refStr,t);const i=new pg(e,s,this,this._params.pdfBug);return oc(this,Mn).set(e,i),i});return oc(this,En).set(e,i),i}getPageIndex(t){return Au(t)?this.messageHandler.sendWithPromise("GetPageIndex",{num:t.num,gen:t.gen}):Promise.reject(new Error("Invalid pageIndex request."))}getAnnotations(t,e){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:e})}getFieldObjects(){return cc(this,Rn,Pn).call(this,"GetFieldObjects")}hasJSActions(){return cc(this,Rn,Pn).call(this,"HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(t){return"string"!=typeof t?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:t})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return cc(this,Rn,Pn).call(this,"GetDocJSActions")}getPageJSActions(t){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:t})}getStructTree(t){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:t})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(t){return cc(this,Rn,Pn).call(this,"GetOptionalContentConfig").then(e=>new Dp(e,t))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){const t="GetMetadata",e=oc(this,kn).get(t);if(e)return e;const s=this.messageHandler.sendWithPromise(t,null).then(t=>{var e,s;return{info:t[0],metadata:t[1]?new Pp(t[1]):null,contentDispositionFilename:(null==(e=this._fullReader)?void 0:e.filename)??null,contentLength:(null==(s=this._fullReader)?void 0:s.contentLength)??null}});return oc(this,kn).set(t,s),s}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(t=!1){if(!this.destroyed){await this.messageHandler.sendWithPromise("Cleanup",null);for(const t of oc(this,Mn).values()){if(!t.cleanup())throw new Error(`startCleanup: Page ${t.pageNumber} is currently rendering.`)}this.commonObjs.clear(),t||this.fontLoader.clear(),oc(this,kn).clear(),this.filterFactory.destroy(!0),rg.cleanup()}}cachedPageNumber(t){if(!Au(t))return null;const e=0===t.gen?`${t.num}R`:`${t.num}R${t.gen}`;return oc(this,Cn).get(e)??null}}kn=new WeakMap,Mn=new WeakMap,En=new WeakMap,Cn=new WeakMap,Tn=new WeakMap,Rn=new WeakSet,Pn=function(t,e=null){const s=oc(this,kn).get(t);if(s)return s;const i=this.messageHandler.sendWithPromise(t,e);return oc(this,kn).set(t,i),i};class vg{constructor(t){lc(this,In,null),ac(this,"onContinue",null),ac(this,"onError",null),hc(this,In,t)}get promise(){return oc(this,In).capability.promise}cancel(t=0){oc(this,In).cancel(null,t)}get separateAnnots(){const{separateAnnots:t}=oc(this,In).operatorList;if(!t)return!1;const{annotationCanvasMap:e}=oc(this,In);return t.form||t.canvas&&(null==e?void 0:e.size)>0}}In=new WeakMap;const wg=class t{constructor({callback:t,params:e,objs:s,commonObjs:i,annotationCanvasMap:n,operatorList:a,pageIndex:r,canvasFactory:o,filterFactory:l,useRequestAnimationFrame:h=!1,pdfBug:c=!1,pageColors:d=null}){lc(this,Ln,null),this.callback=t,this.params=e,this.objs=s,this.commonObjs=i,this.annotationCanvasMap=n,this.operatorListIdx=null,this.operatorList=a,this._pageIndex=r,this.canvasFactory=o,this.filterFactory=l,this._pdfBug=c,this.pageColors=d,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=!0===h&&"undefined"!=typeof window,this.cancelled=!1,this.capability=Promise.withResolvers(),this.task=new vg(this),this._cancelBound=this.cancel.bind(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=e.canvasContext.canvas}get completed(){return this.capability.promise.catch(function(){})}initializeGraphics({transparency:e=!1,optionalContentConfig:s}){var i,n;if(this.cancelled)return;if(this._canvas){if(oc(t,Dn).has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");oc(t,Dn).add(this._canvas)}this._pdfBug&&(null==(i=globalThis.StepperManager)?void 0:i.enabled)&&(this.stepper=globalThis.StepperManager.create(this._pageIndex),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());const{canvasContext:a,viewport:r,transform:o,background:l}=this.params;this.gfx=new Tp(a,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:s},this.annotationCanvasMap,this.pageColors),this.gfx.beginDrawing({transform:o,viewport:r,transparency:e,background:l}),this.operatorListIdx=0,this.graphicsReady=!0,null==(n=this.graphicsReadyCallback)||n.call(this)}cancel(e=null,s=0){var i,n,a;this.running=!1,this.cancelled=!0,null==(i=this.gfx)||i.endDrawing(),oc(this,Ln)&&(window.cancelAnimationFrame(oc(this,Ln)),hc(this,Ln,null)),oc(t,Dn).delete(this._canvas),e||(e=new Ed(`Rendering cancelled, page ${this._pageIndex+1}`,s)),this.callback(e),null==(a=(n=this.task).onError)||a.call(n,e)}operatorListChanged(){var t;this.graphicsReady?(null==(t=this.stepper)||t.updateOperatorList(this.operatorList),this.running||this._continue()):this.graphicsReadyCallback||(this.graphicsReadyCallback=this._continueBound)}_continue(){this.running=!0,this.cancelled||(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?hc(this,Ln,window.requestAnimationFrame(()=>{hc(this,Ln,null),this._nextBound().catch(this._cancelBound)})):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){this.cancelled||(this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),oc(t,Dn).delete(this._canvas),this.callback())))}};Ln=new WeakMap,Dn=new WeakMap,lc(wg,Dn,new WeakSet);let bg=wg;const Ag="5.3.93",_g="cbeef3233";function yg(t){return Math.floor(255*Math.max(0,Math.min(1,t))).toString(16).padStart(2,"0")}function xg(t){return Math.max(0,Math.min(255,255*t))}class Sg{static CMYK_G([t,e,s,i]){return["G",1-Math.min(1,.3*t+.59*s+.11*e+i)]}static G_CMYK([t]){return["CMYK",0,0,0,1-t]}static G_RGB([t]){return["RGB",t,t,t]}static G_rgb([t]){return[t=xg(t),t,t]}static G_HTML([t]){const e=yg(t);return`#${e}${e}${e}`}static RGB_G([t,e,s]){return["G",.3*t+.59*e+.11*s]}static RGB_rgb(t){return t.map(xg)}static RGB_HTML(t){return`#${t.map(yg).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB([t,e,s,i]){return["RGB",1-Math.min(1,t+i),1-Math.min(1,s+i),1-Math.min(1,e+i)]}static CMYK_rgb([t,e,s,i]){return[xg(1-Math.min(1,t+i)),xg(1-Math.min(1,s+i)),xg(1-Math.min(1,e+i))]}static CMYK_HTML(t){const e=this.CMYK_RGB(t).slice(1);return this.RGB_HTML(e)}static RGB_CMYK([t,e,s]){const i=1-t,n=1-e,a=1-s;return["CMYK",i,n,a,Math.min(i,n,a)]}}class kg{create(t,e,s=!1){if(t<=0||e<=0)throw new Error("Invalid SVG dimensions");const i=this._createSVG("svg:svg");return i.setAttribute("version","1.1"),s||(i.setAttribute("width",`${t}px`),i.setAttribute("height",`${e}px`)),i.setAttribute("preserveAspectRatio","none"),i.setAttribute("viewBox",`0 0 ${t} ${e}`),i}createElement(t){if("string"!=typeof t)throw new Error("Invalid SVG element type");return this._createSVG(t)}_createSVG(t){Jc("Abstract method `_createSVG` called.")}}class Mg extends kg{_createSVG(t){return document.createElementNS(yd,t)}}class Eg{static setupStorage(t,e,s,i,n){const a=i.getValue(e,{value:null});switch(s.name){case"textarea":if(null!==a.value&&(t.textContent=a.value),"print"===n)break;t.addEventListener("input",t=>{i.setValue(e,{value:t.target.value})});break;case"input":if("radio"===s.attributes.type||"checkbox"===s.attributes.type){if(a.value===s.attributes.xfaOn?t.setAttribute("checked",!0):a.value===s.attributes.xfaOff&&t.removeAttribute("checked"),"print"===n)break;t.addEventListener("change",t=>{i.setValue(e,{value:t.target.checked?t.target.getAttribute("xfaOn"):t.target.getAttribute("xfaOff")})})}else{if(null!==a.value&&t.setAttribute("value",a.value),"print"===n)break;t.addEventListener("input",t=>{i.setValue(e,{value:t.target.value})})}break;case"select":if(null!==a.value){t.setAttribute("value",a.value);for(const t of s.children)t.attributes.value===a.value?t.attributes.selected=!0:t.attributes.hasOwnProperty("selected")&&delete t.attributes.selected}t.addEventListener("input",t=>{const s=t.target.options,n=-1===s.selectedIndex?"":s[s.selectedIndex].value;i.setValue(e,{value:n})})}}static setAttributes({html:t,element:e,storage:s=null,intent:i,linkService:n}){const{attributes:a}=e,r=t instanceof HTMLAnchorElement;"radio"===a.type&&(a.name=`${a.name}-${i}`);for(const[o,l]of Object.entries(a))if(null!=l)switch(o){case"class":l.length&&t.setAttribute(o,l.join(" "));break;case"dataId":break;case"id":t.setAttribute("data-element-id",l);break;case"style":Object.assign(t.style,l);break;case"textContent":t.textContent=l;break;default:(!r||"href"!==o&&"newWindow"!==o)&&t.setAttribute(o,l)}r&&n.addLinkAttributes(t,a.href,a.newWindow),s&&a.dataId&&this.setupStorage(t,a.dataId,e,s)}static render(t){var e,s;const i=t.annotationStorage,n=t.linkService,a=t.xfaHtml,r=t.intent||"display",o=document.createElement(a.name);a.attributes&&this.setAttributes({html:o,element:a,intent:r,linkService:n});const l="richText"!==r,h=t.div;if(h.append(o),t.viewport){const e=`matrix(${t.viewport.transform.join(",")})`;h.style.transform=e}l&&h.setAttribute("class","xfaLayer xfaFont");const c=[];if(0===a.children.length){if(a.value){const t=document.createTextNode(a.value);o.append(t),l&&og.shouldBuildText(a.name)&&c.push(t)}return{textDivs:c}}const d=[[a,-1,o]];for(;d.length>0;){const[t,a,o]=d.at(-1);if(a+1===t.children.length){d.pop();continue}const h=t.children[++d.at(-1)[1]];if(null===h)continue;const{name:u}=h;if("#text"===u){const t=document.createTextNode(h.value);c.push(t),o.append(t);continue}const p=(null==(e=null==h?void 0:h.attributes)?void 0:e.xmlns)?document.createElementNS(h.attributes.xmlns,u):document.createElement(u);if(o.append(p),h.attributes&&this.setAttributes({html:p,element:h,storage:i,intent:r,linkService:n}),(null==(s=h.children)?void 0:s.length)>0)d.push([h,-1,p]);else if(h.value){const t=document.createTextNode(h.value);l&&og.shouldBuildText(u)&&c.push(t),p.append(t)}}for(const u of h.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))u.setAttribute("readOnly",!0);return{textDivs:c}}static update(t){const e=`matrix(${t.viewport.transform.join(",")})`;t.div.style.transform=e,t.div.hidden=!1}}const Cg=new WeakSet;class Tg{static create(t){switch(t.data.annotationType){case Nc.LINK:return new Pg(t);case Nc.TEXT:return new Ig(t);case Nc.WIDGET:switch(t.data.fieldType){case"Tx":return new Dg(t);case"Btn":return t.data.radioButton?new Wg(t):t.data.checkBox?new Fg(t):new Og(t);case"Ch":return new Bg(t);case"Sig":return new Ng(t)}return new Lg(t);case Nc.POPUP:return new $g(t);case Nc.FREETEXT:return new zg(t);case Nc.LINE:return new Gg(t);case Nc.SQUARE:return new jg(t);case Nc.CIRCLE:return new Ug(t);case Nc.POLYLINE:return new Vg(t);case Nc.CARET:return new Xg(t);case Nc.INK:return new Yg(t);case Nc.POLYGON:return new qg(t);case Nc.HIGHLIGHT:return new Kg(t);case Nc.UNDERLINE:return new Qg(t);case Nc.SQUIGGLY:return new Jg(t);case Nc.STRIKEOUT:return new Zg(t);case Nc.STAMP:return new tf(t);case Nc.FILEATTACHMENT:return new ef(t);default:return new Rg(t)}}}Nn=new WeakMap,Fn=new WeakMap,Wn=new WeakMap,On=new WeakSet,Bn=function(t){const{container:{style:e},data:{rect:s,rotation:i},parent:{viewport:{rawDims:{pageWidth:n,pageHeight:a,pageX:r,pageY:o}}}}=this;null==s||s.splice(0,4,...t),e.left=100*(t[0]-r)/n+"%",e.top=100*(a-t[3]+o)/a+"%",0===i?(e.width=100*(t[2]-t[0])/n+"%",e.height=100*(t[3]-t[1])/a+"%"):this.setRotation(i)};let Rg=class t{constructor(t,{isRenderable:e=!1,ignoreBorder:s=!1,createQuadrilaterals:i=!1}={}){lc(this,On),lc(this,Nn,null),lc(this,Fn,!1),lc(this,Wn,null),this.isRenderable=e,this.data=t.data,this.layer=t.layer,this.linkService=t.linkService,this.downloadManager=t.downloadManager,this.imageResourcesPath=t.imageResourcesPath,this.renderForms=t.renderForms,this.svgFactory=t.svgFactory,this.annotationStorage=t.annotationStorage,this.enableScripting=t.enableScripting,this.hasJSActions=t.hasJSActions,this._fieldObjects=t.fieldObjects,this.parent=t.parent,e&&(this.container=this._createContainer(s)),i&&this._createQuadrilaterals()}static _hasPopupData({contentsObj:t,richText:e}){return!(!(null==t?void 0:t.str)&&!(null==e?void 0:e.str))}get _isEditable(){return this.data.isEditable}get hasPopupData(){return t._hasPopupData(this.data)}updateEdited(t){var e;if(!this.container)return;oc(this,Nn)||hc(this,Nn,{rect:this.data.rect.slice(0)});const{rect:s}=t;s&&cc(this,On,Bn).call(this,s),null==(e=oc(this,Wn))||e.popup.updateEdited(t)}resetEdited(){var t;oc(this,Nn)&&(cc(this,On,Bn).call(this,oc(this,Nn).rect),null==(t=oc(this,Wn))||t.popup.resetEdited(),hc(this,Nn,null))}_createContainer(t){const{data:e,parent:{page:s,viewport:i}}=this,n=document.createElement("section");n.setAttribute("data-annotation-id",e.id),this instanceof Lg||(n.tabIndex=0);const{style:a}=n;if(a.zIndex=this.parent.zIndex++,e.alternativeText&&(n.title=e.alternativeText),e.noRotate&&n.classList.add("norotate"),!e.rect||this instanceof $g){const{rotation:t}=e;return e.hasOwnCanvas||0===t||this.setRotation(t,n),n}const{width:r,height:o}=this;if(!t&&e.borderStyle.width>0){a.borderWidth=`${e.borderStyle.width}px`;const t=e.borderStyle.horizontalCornerRadius,s=e.borderStyle.verticalCornerRadius;if(t>0||s>0){const e=`calc(${t}px * var(--total-scale-factor)) / calc(${s}px * var(--total-scale-factor))`;a.borderRadius=e}else if(this instanceof Wg){const t=`calc(${r}px * var(--total-scale-factor)) / calc(${o}px * var(--total-scale-factor))`;a.borderRadius=t}switch(e.borderStyle.style){case Fc:a.borderStyle="solid";break;case Wc:a.borderStyle="dashed";break;case Oc:Qc("Unimplemented border style: beveled");break;case Bc:Qc("Unimplemented border style: inset");break;case $c:a.borderBottomStyle="solid"}const i=e.borderColor||null;i?(hc(this,Fn,!0),a.borderColor=gd.makeHexColor(0|i[0],0|i[1],0|i[2])):a.borderWidth=0}const l=gd.normalizeRect([e.rect[0],s.view[3]-e.rect[1]+s.view[1],e.rect[2],s.view[3]-e.rect[3]+s.view[1]]),{pageWidth:h,pageHeight:c,pageX:d,pageY:u}=i.rawDims;a.left=100*(l[0]-d)/h+"%",a.top=100*(l[1]-u)/c+"%";const{rotation:p}=e;return e.hasOwnCanvas||0===p?(a.width=100*r/h+"%",a.height=100*o/c+"%"):this.setRotation(p,n),n}setRotation(t,e=this.container){if(!this.data.rect)return;const{pageWidth:s,pageHeight:i}=this.parent.viewport.rawDims;let{width:n,height:a}=this;t%180!=0&&([n,a]=[a,n]),e.style.width=100*n/s+"%",e.style.height=100*a/i+"%",e.setAttribute("data-main-rotation",(360-t)%360)}get _commonActions(){const t=(t,e,s)=>{const i=s.detail[t],n=i[0],a=i.slice(1);s.target.style[e]=Sg[`${n}_HTML`](a),this.annotationStorage.setValue(this.data.id,{[e]:Sg[`${n}_rgb`](a)})};return sd(this,"_commonActions",{display:t=>{const{display:e}=t.detail,s=e%2==1;this.container.style.visibility=s?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noView:s,noPrint:1===e||2===e})},print:t=>{this.annotationStorage.setValue(this.data.id,{noPrint:!t.detail.print})},hidden:t=>{const{hidden:e}=t.detail;this.container.style.visibility=e?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noPrint:e,noView:e})},focus:t=>{setTimeout(()=>t.target.focus({preventScroll:!1}),0)},userName:t=>{t.target.title=t.detail.userName},readonly:t=>{t.target.disabled=t.detail.readonly},required:t=>{this._setRequired(t.target,t.detail.required)},bgColor:e=>{t("bgColor","backgroundColor",e)},fillColor:e=>{t("fillColor","backgroundColor",e)},fgColor:e=>{t("fgColor","color",e)},textColor:e=>{t("textColor","color",e)},borderColor:e=>{t("borderColor","borderColor",e)},strokeColor:e=>{t("strokeColor","borderColor",e)},rotation:t=>{const e=t.detail.rotation;this.setRotation(e),this.annotationStorage.setValue(this.data.id,{rotation:e})}})}_dispatchEventFromSandbox(t,e){const s=this._commonActions;for(const i of Object.keys(e.detail)){const n=t[i]||s[i];null==n||n(e)}}_setDefaultPropertiesFromJS(t){if(!this.enableScripting)return;const e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;const s=this._commonActions;for(const[i,n]of Object.entries(e)){const a=s[i];if(a){a({detail:{[i]:n},target:t}),delete e[i]}}}_createQuadrilaterals(){if(!this.container)return;const{quadPoints:t}=this.data;if(!t)return;const[e,s,i,n]=this.data.rect.map(t=>Math.fround(t));if(8===t.length){const[a,r,o,l]=t.subarray(2,6);if(i===a&&n===r&&e===o&&s===l)return}const{style:a}=this.container;let r;if(oc(this,Fn)){const{borderColor:t,borderWidth:e}=a;a.borderWidth=0,r=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${t}" stroke-width="${e}">`],this.container.classList.add("hasBorder")}const o=i-e,l=n-s,{svgFactory:h}=this,c=h.createElement("svg");c.classList.add("quadrilateralsContainer"),c.setAttribute("width",0),c.setAttribute("height",0),c.role="none";const d=h.createElement("defs");c.append(d);const u=h.createElement("clipPath"),p=`clippath_${this.data.id}`;u.setAttribute("id",p),u.setAttribute("clipPathUnits","objectBoundingBox"),d.append(u);for(let g=2,f=t.length;g<f;g+=8){const s=t[g],i=t[g+1],a=t[g+2],c=t[g+3],d=h.createElement("rect"),p=(a-e)/o,f=(n-i)/l,m=(s-a)/o,v=(i-c)/l;d.setAttribute("x",p),d.setAttribute("y",f),d.setAttribute("width",m),d.setAttribute("height",v),u.append(d),null==r||r.push(`<rect vector-effect="non-scaling-stroke" x="${p}" y="${f}" width="${m}" height="${v}"/>`)}oc(this,Fn)&&(r.push("</g></svg>')"),a.backgroundImage=r.join("")),this.container.append(c),this.container.style.clipPath=`url(#${p})`}_createPopup(){const{data:t}=this,e=hc(this,Wn,new $g({data:{color:t.color,titleObj:t.titleObj,modificationDate:t.modificationDate,contentsObj:t.contentsObj,richText:t.richText,parentRect:t.rect,borderStyle:0,id:`popup_${t.id}`,rotation:t.rotation,noRotate:!0},parent:this.parent,elements:[this]}));this.parent.div.append(e.render())}render(){Jc("Abstract method `AnnotationElement.render` called")}_getElementsByName(t,e=null){const s=[];if(this._fieldObjects){const i=this._fieldObjects[t];if(i)for(const{page:t,id:n,exportValues:a}of i){if(-1===t)continue;if(n===e)continue;const i="string"==typeof a?a:null,r=document.querySelector(`[data-element-id="${n}"]`);!r||Cg.has(r)?s.push({id:n,exportValue:i,domElement:r}):Qc(`_getElementsByName - element not allowed: ${n}`)}return s}for(const i of document.getElementsByName(t)){const{exportValue:t}=i,n=i.getAttribute("data-element-id");n!==e&&(Cg.has(i)&&s.push({id:n,exportValue:t,domElement:i}))}return s}show(){var t;this.container&&(this.container.hidden=!1),null==(t=this.popup)||t.maybeShow()}hide(){var t;this.container&&(this.container.hidden=!0),null==(t=this.popup)||t.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){const t=this.getElementsToTriggerPopup();if(Array.isArray(t))for(const e of t)e.classList.add("highlightArea");else t.classList.add("highlightArea")}_editOnDoubleClick(){if(!this._isEditable)return;const{annotationEditorType:t,data:{id:e}}=this;this.container.addEventListener("dblclick",()=>{var s;null==(s=this.linkService.eventBus)||s.dispatch("switchannotationeditormode",{source:this,mode:t,editId:e,mustEnterInEditMode:!0})})}get width(){return this.data.rect[2]-this.data.rect[0]}get height(){return this.data.rect[3]-this.data.rect[1]}};class Pg extends Rg{constructor(t,e=null){super(t,{isRenderable:!0,ignoreBorder:!!(null==e?void 0:e.ignoreBorder),createQuadrilaterals:!0}),lc(this,$n),this.isTooltipOnly=t.data.isTooltipOnly}render(){const{data:t,linkService:e}=this,s=document.createElement("a");s.setAttribute("data-element-id",t.id);let i=!1;return t.url?(e.addLinkAttributes(s,t.url,t.newWindow),i=!0):t.action?(this._bindNamedAction(s,t.action),i=!0):t.attachment?(cc(this,$n,zn).call(this,s,t.attachment,t.attachmentDest),i=!0):t.setOCGState?(cc(this,$n,Gn).call(this,s,t.setOCGState),i=!0):t.dest?(this._bindLink(s,t.dest),i=!0):(t.actions&&(t.actions.Action||t.actions["Mouse Up"]||t.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions&&(this._bindJSAction(s,t),i=!0),t.resetForm?(this._bindResetFormAction(s,t.resetForm),i=!0):this.isTooltipOnly&&!i&&(this._bindLink(s,""),i=!0)),this.container.classList.add("linkAnnotation"),i&&this.container.append(s),this.container}_bindLink(t,e){t.href=this.linkService.getDestinationHash(e),t.onclick=()=>(e&&this.linkService.goToDestination(e),!1),(e||""===e)&&cc(this,$n,Hn).call(this)}_bindNamedAction(t,e){t.href=this.linkService.getAnchorUrl(""),t.onclick=()=>(this.linkService.executeNamedAction(e),!1),cc(this,$n,Hn).call(this)}_bindJSAction(t,e){t.href=this.linkService.getAnchorUrl("");const s=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const i of Object.keys(e.actions)){const n=s.get(i);n&&(t[n]=()=>{var t;return null==(t=this.linkService.eventBus)||t.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e.id,name:i}}),!1})}t.onclick||(t.onclick=()=>!1),cc(this,$n,Hn).call(this)}_bindResetFormAction(t,e){const s=t.onclick;if(s||(t.href=this.linkService.getAnchorUrl("")),cc(this,$n,Hn).call(this),!this._fieldObjects)return Qc('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.'),void(s||(t.onclick=()=>!1));t.onclick=()=>{var t;null==s||s();const{fields:i,refs:n,include:a}=e,r=[];if(0!==i.length||0!==n.length){const t=new Set(n);for(const e of i){const s=this._fieldObjects[e]||[];for(const{id:e}of s)t.add(e)}for(const e of Object.values(this._fieldObjects))for(const s of e)t.has(s.id)===a&&r.push(s)}else for(const e of Object.values(this._fieldObjects))r.push(...e);const o=this.annotationStorage,l=[];for(const e of r){const{id:t}=e;switch(l.push(t),e.type){case"text":{const s=e.defaultValue||"";o.setValue(t,{value:s});break}case"checkbox":case"radiobutton":{const s=e.defaultValue===e.exportValues;o.setValue(t,{value:s});break}case"combobox":case"listbox":{const s=e.defaultValue||"";o.setValue(t,{value:s});break}default:continue}const s=document.querySelector(`[data-element-id="${t}"]`);s&&(Cg.has(s)?s.dispatchEvent(new Event("resetform")):Qc(`_bindResetFormAction - element not allowed: ${t}`))}return this.enableScripting&&(null==(t=this.linkService.eventBus)||t.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:l,name:"ResetForm"}})),!1}}}$n=new WeakSet,Hn=function(){this.container.setAttribute("data-internal-link","")},zn=function(t,e,s=null){t.href=this.linkService.getAnchorUrl(""),e.description&&(t.title=e.description),t.onclick=()=>{var t;return null==(t=this.downloadManager)||t.openOrDownloadData(e.content,e.filename,s),!1},cc(this,$n,Hn).call(this)},Gn=function(t,e){t.href=this.linkService.getAnchorUrl(""),t.onclick=()=>(this.linkService.executeSetOCGState(e),!1),cc(this,$n,Hn).call(this)};class Ig extends Rg{constructor(t){super(t,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");const t=document.createElement("img");return t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",t.setAttribute("data-l10n-id","pdfjs-text-annotation-type"),t.setAttribute("data-l10n-args",JSON.stringify({type:this.data.name})),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.append(t),this.container}}class Lg extends Rg{render(){return this.container}showElementAndHideCanvas(t){var e;this.data.hasOwnCanvas&&("CANVAS"===(null==(e=t.previousSibling)?void 0:e.nodeName)&&(t.previousSibling.hidden=!0),t.hidden=!1)}_getKeyModifier(t){return ud.platform.isMac?t.metaKey:t.ctrlKey}_setEventListener(t,e,s,i,n){s.includes("mouse")?t.addEventListener(s,t=>{var e;null==(e=this.linkService.eventBus)||e.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:i,value:n(t),shift:t.shiftKey,modifier:this._getKeyModifier(t)}})}):t.addEventListener(s,t=>{var a;if("blur"===s){if(!e.focused||!t.relatedTarget)return;e.focused=!1}else if("focus"===s){if(e.focused)return;e.focused=!0}n&&(null==(a=this.linkService.eventBus)||a.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:i,value:n(t)}}))})}_setEventListeners(t,e,s,i){var n,a,r;for(const[o,l]of s)("Action"===l||(null==(n=this.data.actions)?void 0:n[l]))&&("Focus"!==l&&"Blur"!==l||e||(e={focused:!1}),this._setEventListener(t,e,o,l,i),"Focus"!==l||(null==(a=this.data.actions)?void 0:a.Blur)?"Blur"!==l||(null==(r=this.data.actions)?void 0:r.Focus)||this._setEventListener(t,e,"focus","Focus",null):this._setEventListener(t,e,"blur","Blur",null))}_setBackgroundColor(t){const e=this.data.backgroundColor||null;t.style.backgroundColor=null===e?"transparent":gd.makeHexColor(e[0],e[1],e[2])}_setTextStyle(t){const e=["left","center","right"],{fontColor:s}=this.data.defaultAppearanceData,i=this.data.defaultAppearanceData.fontSize||9,n=t.style;let a;const r=t=>Math.round(10*t)/10;if(this.data.multiLine){const t=Math.abs(this.data.rect[3]-this.data.rect[1]-2),e=t/(Math.round(t/(gc*i))||1);a=Math.min(i,r(e/gc))}else{const t=Math.abs(this.data.rect[3]-this.data.rect[1]-2);a=Math.min(i,r(t/gc))}n.fontSize=`calc(${a}px * var(--total-scale-factor))`,n.color=gd.makeHexColor(s[0],s[1],s[2]),null!==this.data.textAlignment&&(n.textAlign=e[this.data.textAlignment])}_setRequired(t,e){e?t.setAttribute("required",!0):t.removeAttribute("required"),t.setAttribute("aria-required",e)}}class Dg extends Lg{constructor(t){super(t,{isRenderable:t.renderForms||t.data.hasOwnCanvas||!t.data.hasAppearance&&!!t.data.fieldValue})}setPropertyOnSiblings(t,e,s,i){const n=this.annotationStorage;for(const a of this._getElementsByName(t.name,t.id))a.domElement&&(a.domElement[e]=s),n.setValue(a.id,{[i]:s})}render(){var t,e;const s=this.annotationStorage,i=this.data.id;this.container.classList.add("textWidgetAnnotation");let n=null;if(this.renderForms){const a=s.getValue(i,{value:this.data.fieldValue});let r=a.value||"";const o=s.getValue(i,{charLimit:this.data.maxLen}).charLimit;o&&r.length>o&&(r=r.slice(0,o));let l=a.formattedValue||(null==(t=this.data.textContent)?void 0:t.join("\n"))||null;l&&this.data.comb&&(l=l.replaceAll(/\s+/g,""));const h={userValue:r,formattedValue:l,lastCommittedValue:null,commitKey:1,focused:!1};this.data.multiLine?(n=document.createElement("textarea"),n.textContent=l??r,this.data.doNotScroll&&(n.style.overflowY="hidden")):(n=document.createElement("input"),n.type=this.data.password?"password":"text",n.setAttribute("value",l??r),this.data.doNotScroll&&(n.style.overflowX="hidden")),this.data.hasOwnCanvas&&(n.hidden=!0),Cg.add(n),n.setAttribute("data-element-id",i),n.disabled=this.data.readOnly,n.name=this.data.fieldName,n.tabIndex=0;const c=this.data.dateFormat||this.data.timeFormat;c&&(n.title=c),this._setRequired(n,this.data.required),o&&(n.maxLength=o),n.addEventListener("input",t=>{s.setValue(i,{value:t.target.value}),this.setPropertyOnSiblings(n,"value",t.target.value,"value"),h.formattedValue=null}),n.addEventListener("resetform",t=>{const e=this.data.defaultFieldValue??"";n.value=h.userValue=e,h.formattedValue=null});let d=t=>{const{formattedValue:e}=h;null!=e&&(t.target.value=e),t.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){n.addEventListener("focus",t=>{var e;if(h.focused)return;const{target:s}=t;h.userValue&&(s.value=h.userValue),h.lastCommittedValue=s.value,h.commitKey=1,(null==(e=this.data.actions)?void 0:e.Focus)||(h.focused=!0)}),n.addEventListener("updatefromsandbox",t=>{this.showElementAndHideCanvas(t.target);const e={value(t){h.userValue=t.detail.value??"",s.setValue(i,{value:h.userValue.toString()}),t.target.value=h.userValue},formattedValue(t){const{formattedValue:e}=t.detail;h.formattedValue=e,null!=e&&t.target!==document.activeElement&&(t.target.value=e),s.setValue(i,{formattedValue:e})},selRange(t){t.target.setSelectionRange(...t.detail.selRange)},charLimit:t=>{var e;const{charLimit:n}=t.detail,{target:a}=t;if(0===n)return void a.removeAttribute("maxLength");a.setAttribute("maxLength",n);let r=h.userValue;!r||r.length<=n||(r=r.slice(0,n),a.value=h.userValue=r,s.setValue(i,{value:r}),null==(e=this.linkService.eventBus)||e.dispatch("dispatcheventinsandbox",{source:this,detail:{id:i,name:"Keystroke",value:r,willCommit:!0,commitKey:1,selStart:a.selectionStart,selEnd:a.selectionEnd}}))}};this._dispatchEventFromSandbox(e,t)}),n.addEventListener("keydown",t=>{var e;h.commitKey=1;let s=-1;if("Escape"===t.key?s=0:"Enter"!==t.key||this.data.multiLine?"Tab"===t.key&&(h.commitKey=3):s=2,-1===s)return;const{value:n}=t.target;h.lastCommittedValue!==n&&(h.lastCommittedValue=n,h.userValue=n,null==(e=this.linkService.eventBus)||e.dispatch("dispatcheventinsandbox",{source:this,detail:{id:i,name:"Keystroke",value:n,willCommit:!0,commitKey:s,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}}))});const t=d;d=null,n.addEventListener("blur",e=>{var s,n;if(!h.focused||!e.relatedTarget)return;(null==(s=this.data.actions)?void 0:s.Blur)||(h.focused=!1);const{value:a}=e.target;h.userValue=a,h.lastCommittedValue!==a&&(null==(n=this.linkService.eventBus)||n.dispatch("dispatcheventinsandbox",{source:this,detail:{id:i,name:"Keystroke",value:a,willCommit:!0,commitKey:h.commitKey,selStart:e.target.selectionStart,selEnd:e.target.selectionEnd}})),t(e)}),(null==(e=this.data.actions)?void 0:e.Keystroke)&&n.addEventListener("beforeinput",t=>{var e;h.lastCommittedValue=null;const{data:s,target:n}=t,{value:a,selectionStart:r,selectionEnd:o}=n;let l=r,c=o;switch(t.inputType){case"deleteWordBackward":{const t=a.substring(0,r).match(/\w*[^\w]*$/);t&&(l-=t[0].length);break}case"deleteWordForward":{const t=a.substring(r).match(/^[^\w]*\w*/);t&&(c+=t[0].length);break}case"deleteContentBackward":r===o&&(l-=1);break;case"deleteContentForward":r===o&&(c+=1)}t.preventDefault(),null==(e=this.linkService.eventBus)||e.dispatch("dispatcheventinsandbox",{source:this,detail:{id:i,name:"Keystroke",value:a,change:s||"",willCommit:!1,selStart:l,selEnd:c}})}),this._setEventListeners(n,h,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],t=>t.target.value)}if(d&&n.addEventListener("blur",d),this.data.comb){const t=(this.data.rect[2]-this.data.rect[0])/o;n.classList.add("comb"),n.style.letterSpacing=`calc(${t}px * var(--total-scale-factor) - 1ch)`}}else n=document.createElement("div"),n.textContent=this.data.fieldValue,n.style.verticalAlign="middle",n.style.display="table-cell",this.data.hasOwnCanvas&&(n.hidden=!0);return this._setTextStyle(n),this._setBackgroundColor(n),this._setDefaultPropertiesFromJS(n),this.container.append(n),this.container}}class Ng extends Lg{constructor(t){super(t,{isRenderable:!!t.data.hasOwnCanvas})}}class Fg extends Lg{constructor(t){super(t,{isRenderable:t.renderForms})}render(){const t=this.annotationStorage,e=this.data,s=e.id;let i=t.getValue(s,{value:e.exportValue===e.fieldValue}).value;"string"==typeof i&&(i="Off"!==i,t.setValue(s,{value:i})),this.container.classList.add("buttonWidgetAnnotation","checkBox");const n=document.createElement("input");return Cg.add(n),n.setAttribute("data-element-id",s),n.disabled=e.readOnly,this._setRequired(n,this.data.required),n.type="checkbox",n.name=e.fieldName,i&&n.setAttribute("checked",!0),n.setAttribute("exportValue",e.exportValue),n.tabIndex=0,n.addEventListener("change",i=>{const{name:n,checked:a}=i.target;for(const r of this._getElementsByName(n,s)){const s=a&&r.exportValue===e.exportValue;r.domElement&&(r.domElement.checked=s),t.setValue(r.id,{value:s})}t.setValue(s,{value:a})}),n.addEventListener("resetform",t=>{const s=e.defaultFieldValue||"Off";t.target.checked=s===e.exportValue}),this.enableScripting&&this.hasJSActions&&(n.addEventListener("updatefromsandbox",e=>{const i={value(e){e.target.checked="Off"!==e.detail.value,t.setValue(s,{value:e.target.checked})}};this._dispatchEventFromSandbox(i,e)}),this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],t=>t.target.checked)),this._setBackgroundColor(n),this._setDefaultPropertiesFromJS(n),this.container.append(n),this.container}}class Wg extends Lg{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");const t=this.annotationStorage,e=this.data,s=e.id;let i=t.getValue(s,{value:e.fieldValue===e.buttonValue}).value;if("string"==typeof i&&(i=i!==e.buttonValue,t.setValue(s,{value:i})),i)for(const a of this._getElementsByName(e.fieldName,s))t.setValue(a.id,{value:!1});const n=document.createElement("input");if(Cg.add(n),n.setAttribute("data-element-id",s),n.disabled=e.readOnly,this._setRequired(n,this.data.required),n.type="radio",n.name=e.fieldName,i&&n.setAttribute("checked",!0),n.tabIndex=0,n.addEventListener("change",e=>{const{name:i,checked:n}=e.target;for(const a of this._getElementsByName(i,s))t.setValue(a.id,{value:!1});t.setValue(s,{value:n})}),n.addEventListener("resetform",t=>{const s=e.defaultFieldValue;t.target.checked=null!=s&&s===e.buttonValue}),this.enableScripting&&this.hasJSActions){const i=e.buttonValue;n.addEventListener("updatefromsandbox",e=>{const n={value:e=>{const n=i===e.detail.value;for(const i of this._getElementsByName(e.target.name)){const e=n&&i.id===s;i.domElement&&(i.domElement.checked=e),t.setValue(i.id,{value:e})}}};this._dispatchEventFromSandbox(n,e)}),this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],t=>t.target.checked)}return this._setBackgroundColor(n),this._setDefaultPropertiesFromJS(n),this.container.append(n),this.container}}class Og extends Pg{constructor(t){super(t,{ignoreBorder:t.data.hasAppearance})}render(){const t=super.render();t.classList.add("buttonWidgetAnnotation","pushButton");const e=t.lastChild;return this.enableScripting&&this.hasJSActions&&e&&(this._setDefaultPropertiesFromJS(e),e.addEventListener("updatefromsandbox",t=>{this._dispatchEventFromSandbox({},t)})),t}}class Bg extends Lg{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");const t=this.annotationStorage,e=this.data.id,s=t.getValue(e,{value:this.data.fieldValue}),i=document.createElement("select");Cg.add(i),i.setAttribute("data-element-id",e),i.disabled=this.data.readOnly,this._setRequired(i,this.data.required),i.name=this.data.fieldName,i.tabIndex=0;let n=this.data.combo&&this.data.options.length>0;this.data.combo||(i.size=this.data.options.length,this.data.multiSelect&&(i.multiple=!0)),i.addEventListener("resetform",t=>{const e=this.data.defaultFieldValue;for(const s of i.options)s.selected=s.value===e});for(const h of this.data.options){const t=document.createElement("option");t.textContent=h.displayValue,t.value=h.exportValue,s.value.includes(h.exportValue)&&(t.setAttribute("selected",!0),n=!1),i.append(t)}let a=null;if(n){const t=document.createElement("option");t.value=" ",t.setAttribute("hidden",!0),t.setAttribute("selected",!0),i.prepend(t),a=()=>{t.remove(),i.removeEventListener("input",a),a=null},i.addEventListener("input",a)}const r=t=>{const e=t?"value":"textContent",{options:s,multiple:n}=i;return n?Array.prototype.filter.call(s,t=>t.selected).map(t=>t[e]):-1===s.selectedIndex?null:s[s.selectedIndex][e]};let o=r(!1);const l=t=>{const e=t.target.options;return Array.prototype.map.call(e,t=>({displayValue:t.textContent,exportValue:t.value}))};return this.enableScripting&&this.hasJSActions?(i.addEventListener("updatefromsandbox",s=>{const n={value(s){null==a||a();const n=s.detail.value,l=new Set(Array.isArray(n)?n:[n]);for(const t of i.options)t.selected=l.has(t.value);t.setValue(e,{value:r(!0)}),o=r(!1)},multipleSelection(t){i.multiple=!0},remove(s){const n=i.options,a=s.detail.remove;if(n[a].selected=!1,i.remove(a),n.length>0){-1===Array.prototype.findIndex.call(n,t=>t.selected)&&(n[0].selected=!0)}t.setValue(e,{value:r(!0),items:l(s)}),o=r(!1)},clear(s){for(;0!==i.length;)i.remove(0);t.setValue(e,{value:null,items:[]}),o=r(!1)},insert(s){const{index:n,displayValue:a,exportValue:h}=s.detail.insert,c=i.children[n],d=document.createElement("option");d.textContent=a,d.value=h,c?c.before(d):i.append(d),t.setValue(e,{value:r(!0),items:l(s)}),o=r(!1)},items(s){const{items:n}=s.detail;for(;0!==i.length;)i.remove(0);for(const t of n){const{displayValue:e,exportValue:s}=t,n=document.createElement("option");n.textContent=e,n.value=s,i.append(n)}i.options.length>0&&(i.options[0].selected=!0),t.setValue(e,{value:r(!0),items:l(s)}),o=r(!1)},indices(s){const i=new Set(s.detail.indices);for(const t of s.target.options)t.selected=i.has(t.index);t.setValue(e,{value:r(!0)}),o=r(!1)},editable(t){t.target.disabled=!t.detail.editable}};this._dispatchEventFromSandbox(n,s)}),i.addEventListener("input",s=>{var i;const n=r(!0),a=r(!1);t.setValue(e,{value:n}),s.preventDefault(),null==(i=this.linkService.eventBus)||i.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:o,change:a,changeEx:n,willCommit:!1,commitKey:1,keyDown:!1}})}),this._setEventListeners(i,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],t=>t.target.value)):i.addEventListener("input",function(s){t.setValue(e,{value:r(!0)})}),this.data.combo&&this._setTextStyle(i),this._setBackgroundColor(i),this._setDefaultPropertiesFromJS(i),this.container.append(i),this.container}}class $g extends Rg{constructor(t){const{data:e,elements:s}=t;super(t,{isRenderable:Rg._hasPopupData(e)}),this.elements=s,this.popup=null}render(){const{container:t}=this;t.classList.add("popupAnnotation"),t.role="comment";const e=this.popup=new Hg({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),s=[];for(const i of this.elements)i.popup=e,i.container.ariaHasPopup="dialog",s.push(i.data.id),i.addHighlightArea();return this.container.setAttribute("aria-controls",s.map(t=>`${bd}${t}`).join(",")),this.container}}class Hg{constructor({container:t,color:e,elements:s,titleObj:i,modificationDate:n,contentsObj:a,richText:r,parent:o,rect:l,parentRect:h,open:c}){var d;lc(this,ha),lc(this,jn,cc(this,ha,ga).bind(this)),lc(this,Un,cc(this,ha,wa).bind(this)),lc(this,Vn,cc(this,ha,va).bind(this)),lc(this,qn,cc(this,ha,ma).bind(this)),lc(this,Xn,null),lc(this,Yn,null),lc(this,Kn,null),lc(this,Qn,null),lc(this,Jn,null),lc(this,Zn,null),lc(this,ta,null),lc(this,ea,!1),lc(this,sa,null),lc(this,ia,null),lc(this,na,null),lc(this,aa,null),lc(this,ra,null),lc(this,oa,null),lc(this,la,!1),hc(this,Yn,t),hc(this,ra,i),hc(this,Kn,a),hc(this,aa,r),hc(this,Zn,o),hc(this,Xn,e),hc(this,na,l),hc(this,ta,h),hc(this,Jn,s),hc(this,Qn,Fd.toDateObject(n)),this.trigger=s.flatMap(t=>t.getElementsToTriggerPopup());for(const u of this.trigger)u.addEventListener("click",oc(this,qn)),u.addEventListener("mouseenter",oc(this,Vn)),u.addEventListener("mouseleave",oc(this,Un)),u.classList.add("popupTriggerArea");for(const u of s)null==(d=u.container)||d.addEventListener("keydown",oc(this,jn));oc(this,Yn).hidden=!0,c&&cc(this,ha,ma).call(this)}render(){if(oc(this,sa))return;const t=hc(this,sa,document.createElement("div"));if(t.className="popup",oc(this,Xn)){const e=t.style.outlineColor=gd.makeHexColor(...oc(this,Xn));t.style.backgroundColor=`color-mix(in srgb, ${e} 30%, white)`}const e=document.createElement("span");e.className="header";const s=document.createElement("h1");if(e.append(s),({dir:s.dir,str:s.textContent}=oc(this,ra)),t.append(e),oc(this,Qn)){const t=document.createElement("time");t.classList.add("popupDate"),t.setAttribute("data-l10n-id","pdfjs-annotation-date-time-string"),t.setAttribute("data-l10n-args",JSON.stringify({dateObj:oc(this,Qn).valueOf()})),t.dateTime=oc(this,Qn).toISOString(),e.append(t)}const i=oc(this,ha,ca);if(i)Eg.render({xfaHtml:i,intent:"richText",div:t}),t.lastChild.classList.add("richText","popupContent");else{const e=this._formatContents(oc(this,Kn));t.append(e)}oc(this,Yn).append(t)}_formatContents({str:t,dir:e}){const s=document.createElement("p");s.classList.add("popupContent"),s.dir=e;const i=t.split(/(?:\r\n?|\n)/);for(let n=0,a=i.length;n<a;++n){const t=i[n];s.append(document.createTextNode(t)),n<a-1&&s.append(document.createElement("br"))}return s}updateEdited({rect:t,popupContent:e}){var s;oc(this,oa)||hc(this,oa,{contentsObj:oc(this,Kn),richText:oc(this,aa)}),t&&hc(this,ia,null),e&&(hc(this,aa,cc(this,ha,pa).call(this,e)),hc(this,Kn,null)),null==(s=oc(this,sa))||s.remove(),hc(this,sa,null)}resetEdited(){var t;oc(this,oa)&&(({contentsObj:dc(this,Kn)._,richText:dc(this,aa)._}=oc(this,oa)),hc(this,oa,null),null==(t=oc(this,sa))||t.remove(),hc(this,sa,null),hc(this,ia,null))}forceHide(){hc(this,la,this.isVisible),oc(this,la)&&(oc(this,Yn).hidden=!0)}maybeShow(){oc(this,la)&&(oc(this,sa)||cc(this,ha,va).call(this),hc(this,la,!1),oc(this,Yn).hidden=!1)}get isVisible(){return!1===oc(this,Yn).hidden}}jn=new WeakMap,Un=new WeakMap,Vn=new WeakMap,qn=new WeakMap,Xn=new WeakMap,Yn=new WeakMap,Kn=new WeakMap,Qn=new WeakMap,Jn=new WeakMap,Zn=new WeakMap,ta=new WeakMap,ea=new WeakMap,sa=new WeakMap,ia=new WeakMap,na=new WeakMap,aa=new WeakMap,ra=new WeakMap,oa=new WeakMap,la=new WeakMap,ha=new WeakSet,ca=function(){const t=oc(this,aa),e=oc(this,Kn);return!(null==t?void 0:t.str)||(null==e?void 0:e.str)&&e.str!==t.str?null:oc(this,aa).html||null},da=function(){var t,e,s;return(null==(s=null==(e=null==(t=oc(this,ha,ca))?void 0:t.attributes)?void 0:e.style)?void 0:s.fontSize)||0},ua=function(){var t,e,s;return(null==(s=null==(e=null==(t=oc(this,ha,ca))?void 0:t.attributes)?void 0:e.style)?void 0:s.color)||null},pa=function(t){const e=[],s={str:t,html:{name:"div",attributes:{dir:"auto"},children:[{name:"p",children:e}]}},i={style:{color:oc(this,ha,ua),fontSize:oc(this,ha,da)?`calc(${oc(this,ha,da)}px * var(--total-scale-factor))`:""}};for(const n of t.split("\n"))e.push({name:"span",value:n,attributes:i});return s},ga=function(t){t.altKey||t.shiftKey||t.ctrlKey||t.metaKey||("Enter"===t.key||"Escape"===t.key&&oc(this,ea))&&cc(this,ha,ma).call(this)},fa=function(){if(null!==oc(this,ia))return;const{page:{view:t},viewport:{rawDims:{pageWidth:e,pageHeight:s,pageX:i,pageY:n}}}=oc(this,Zn);let a=!!oc(this,ta),r=oc(this,a?ta:na);for(const u of oc(this,Jn))if(!r||null!==gd.intersect(u.data.rect,r)){r=u.data.rect,a=!0;break}const o=gd.normalizeRect([r[0],t[3]-r[1]+t[1],r[2],t[3]-r[3]+t[1]]),l=a?r[2]-r[0]+5:0,h=o[0]+l,c=o[1];hc(this,ia,[100*(h-i)/e,100*(c-n)/s]);const{style:d}=oc(this,Yn);d.left=`${oc(this,ia)[0]}%`,d.top=`${oc(this,ia)[1]}%`},ma=function(){hc(this,ea,!oc(this,ea)),oc(this,ea)?(cc(this,ha,va).call(this),oc(this,Yn).addEventListener("click",oc(this,qn)),oc(this,Yn).addEventListener("keydown",oc(this,jn))):(cc(this,ha,wa).call(this),oc(this,Yn).removeEventListener("click",oc(this,qn)),oc(this,Yn).removeEventListener("keydown",oc(this,jn)))},va=function(){oc(this,sa)||this.render(),this.isVisible?oc(this,ea)&&oc(this,Yn).classList.add("focused"):(cc(this,ha,fa).call(this),oc(this,Yn).hidden=!1,oc(this,Yn).style.zIndex=parseInt(oc(this,Yn).style.zIndex)+1e3)},wa=function(){oc(this,Yn).classList.remove("focused"),!oc(this,ea)&&this.isVisible&&(oc(this,Yn).hidden=!0,oc(this,Yn).style.zIndex=parseInt(oc(this,Yn).style.zIndex)-1e3)};class zg extends Rg{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.textContent=t.data.textContent,this.textPosition=t.data.textPosition,this.annotationEditorType=kc.FREETEXT}render(){if(this.container.classList.add("freeTextAnnotation"),this.textContent){const t=document.createElement("div");t.classList.add("annotationTextContent"),t.setAttribute("role","comment");for(const e of this.textContent){const s=document.createElement("span");s.textContent=e,t.append(s)}this.container.append(t)}return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}class Gg extends Rg{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),lc(this,ba,null)}render(){this.container.classList.add("lineAnnotation");const{data:t,width:e,height:s}=this,i=this.svgFactory.create(e,s,!0),n=hc(this,ba,this.svgFactory.createElement("svg:line"));return n.setAttribute("x1",t.rect[2]-t.lineCoordinates[0]),n.setAttribute("y1",t.rect[3]-t.lineCoordinates[1]),n.setAttribute("x2",t.rect[2]-t.lineCoordinates[2]),n.setAttribute("y2",t.rect[3]-t.lineCoordinates[3]),n.setAttribute("stroke-width",t.borderStyle.width||1),n.setAttribute("stroke","transparent"),n.setAttribute("fill","transparent"),i.append(n),this.container.append(i),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return oc(this,ba)}addHighlightArea(){this.container.classList.add("highlightArea")}}ba=new WeakMap;class jg extends Rg{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),lc(this,Aa,null)}render(){this.container.classList.add("squareAnnotation");const{data:t,width:e,height:s}=this,i=this.svgFactory.create(e,s,!0),n=t.borderStyle.width,a=hc(this,Aa,this.svgFactory.createElement("svg:rect"));return a.setAttribute("x",n/2),a.setAttribute("y",n/2),a.setAttribute("width",e-n),a.setAttribute("height",s-n),a.setAttribute("stroke-width",n||1),a.setAttribute("stroke","transparent"),a.setAttribute("fill","transparent"),i.append(a),this.container.append(i),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return oc(this,Aa)}addHighlightArea(){this.container.classList.add("highlightArea")}}Aa=new WeakMap;class Ug extends Rg{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),lc(this,_a,null)}render(){this.container.classList.add("circleAnnotation");const{data:t,width:e,height:s}=this,i=this.svgFactory.create(e,s,!0),n=t.borderStyle.width,a=hc(this,_a,this.svgFactory.createElement("svg:ellipse"));return a.setAttribute("cx",e/2),a.setAttribute("cy",s/2),a.setAttribute("rx",e/2-n/2),a.setAttribute("ry",s/2-n/2),a.setAttribute("stroke-width",n||1),a.setAttribute("stroke","transparent"),a.setAttribute("fill","transparent"),i.append(a),this.container.append(i),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return oc(this,_a)}addHighlightArea(){this.container.classList.add("highlightArea")}}_a=new WeakMap;class Vg extends Rg{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),lc(this,ya,null),this.containerClassName="polylineAnnotation",this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);const{data:{rect:t,vertices:e,borderStyle:s,popupRef:i},width:n,height:a}=this;if(!e)return this.container;const r=this.svgFactory.create(n,a,!0);let o=[];for(let h=0,c=e.length;h<c;h+=2){const s=e[h]-t[0],i=t[3]-e[h+1];o.push(`${s},${i}`)}o=o.join(" ");const l=hc(this,ya,this.svgFactory.createElement(this.svgElementName));return l.setAttribute("points",o),l.setAttribute("stroke-width",s.width||1),l.setAttribute("stroke","transparent"),l.setAttribute("fill","transparent"),r.append(l),this.container.append(r),!i&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return oc(this,ya)}addHighlightArea(){this.container.classList.add("highlightArea")}}ya=new WeakMap;class qg extends Vg{constructor(t){super(t),this.containerClassName="polygonAnnotation",this.svgElementName="svg:polygon"}}class Xg extends Rg{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("caretAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}class Yg extends Rg{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),lc(this,ka),lc(this,xa,null),lc(this,Sa,[]),this.containerClassName="inkAnnotation",this.svgElementName="svg:polyline",this.annotationEditorType="InkHighlight"===this.data.it?kc.HIGHLIGHT:kc.INK}render(){this.container.classList.add(this.containerClassName);const{data:{rect:t,rotation:e,inkLists:s,borderStyle:i,popupRef:n}}=this,{transform:a,width:r,height:o}=cc(this,ka,Ma).call(this,e,t),l=this.svgFactory.create(r,o,!0),h=hc(this,xa,this.svgFactory.createElement("svg:g"));l.append(h),h.setAttribute("stroke-width",i.width||1),h.setAttribute("stroke-linecap","round"),h.setAttribute("stroke-linejoin","round"),h.setAttribute("stroke-miterlimit",10),h.setAttribute("stroke","transparent"),h.setAttribute("fill","transparent"),h.setAttribute("transform",a);for(let c=0,d=s.length;c<d;c++){const t=this.svgFactory.createElement(this.svgElementName);oc(this,Sa).push(t),t.setAttribute("points",s[c].join(",")),h.append(t)}return!n&&this.hasPopupData&&this._createPopup(),this.container.append(l),this._editOnDoubleClick(),this.container}updateEdited(t){super.updateEdited(t);const{thickness:e,points:s,rect:i}=t,n=oc(this,xa);if(e>=0&&n.setAttribute("stroke-width",e||1),s)for(let a=0,r=oc(this,Sa).length;a<r;a++)oc(this,Sa)[a].setAttribute("points",s[a].join(","));if(i){const{transform:t,width:e,height:s}=cc(this,ka,Ma).call(this,this.data.rotation,i);n.parentElement.setAttribute("viewBox",`0 0 ${e} ${s}`),n.setAttribute("transform",t)}}getElementsToTriggerPopup(){return oc(this,Sa)}addHighlightArea(){this.container.classList.add("highlightArea")}}xa=new WeakMap,Sa=new WeakMap,ka=new WeakSet,Ma=function(t,e){switch(t){case 90:return{transform:`rotate(90) translate(${-e[0]},${e[1]}) scale(1,-1)`,width:e[3]-e[1],height:e[2]-e[0]};case 180:return{transform:`rotate(180) translate(${-e[2]},${e[1]}) scale(1,-1)`,width:e[2]-e[0],height:e[3]-e[1]};case 270:return{transform:`rotate(270) translate(${-e[2]},${e[3]}) scale(1,-1)`,width:e[3]-e[1],height:e[2]-e[0]};default:return{transform:`translate(${-e[0]},${e[3]}) scale(1,-1)`,width:e[2]-e[0],height:e[3]-e[1]}}};class Kg extends Rg{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0}),this.annotationEditorType=kc.HIGHLIGHT}render(){const{data:{overlaidText:t,popupRef:e}}=this;if(!e&&this.hasPopupData&&this._createPopup(),this.container.classList.add("highlightAnnotation"),this._editOnDoubleClick(),t){const e=document.createElement("mark");e.classList.add("overlaidText"),e.textContent=t,this.container.append(e)}return this.container}}class Qg extends Rg{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){const{data:{overlaidText:t,popupRef:e}}=this;if(!e&&this.hasPopupData&&this._createPopup(),this.container.classList.add("underlineAnnotation"),t){const e=document.createElement("u");e.classList.add("overlaidText"),e.textContent=t,this.container.append(e)}return this.container}}class Jg extends Rg{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){const{data:{overlaidText:t,popupRef:e}}=this;if(!e&&this.hasPopupData&&this._createPopup(),this.container.classList.add("squigglyAnnotation"),t){const e=document.createElement("u");e.classList.add("overlaidText"),e.textContent=t,this.container.append(e)}return this.container}}class Zg extends Rg{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){const{data:{overlaidText:t,popupRef:e}}=this;if(!e&&this.hasPopupData&&this._createPopup(),this.container.classList.add("strikeoutAnnotation"),t){const e=document.createElement("s");e.classList.add("overlaidText"),e.textContent=t,this.container.append(e)}return this.container}}class tf extends Rg{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.annotationEditorType=kc.STAMP}render(){return this.container.classList.add("stampAnnotation"),this.container.setAttribute("role","img"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}class ef extends Rg{constructor(t){var e;super(t,{isRenderable:!0}),lc(this,Ca),lc(this,Ea,null);const{file:s}=this.data;this.filename=s.filename,this.content=s.content,null==(e=this.linkService.eventBus)||e.dispatch("fileattachmentannotation",{source:this,...s})}render(){this.container.classList.add("fileAttachmentAnnotation");const{container:t,data:e}=this;let s;e.hasAppearance||0===e.fillAlpha?s=document.createElement("div"):(s=document.createElement("img"),s.src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(e.name)?"paperclip":"pushpin"}.svg`,e.fillAlpha&&e.fillAlpha<1&&(s.style=`filter: opacity(${Math.round(100*e.fillAlpha)}%);`)),s.addEventListener("dblclick",cc(this,Ca,Ta).bind(this)),hc(this,Ea,s);const{isMac:i}=ud.platform;return t.addEventListener("keydown",t=>{"Enter"===t.key&&(i?t.metaKey:t.ctrlKey)&&cc(this,Ca,Ta).call(this)}),!e.popupRef&&this.hasPopupData?this._createPopup():s.classList.add("popupTriggerArea"),t.append(s),t}getElementsToTriggerPopup(){return oc(this,Ea)}addHighlightArea(){this.container.classList.add("highlightArea")}}Ea=new WeakMap,Ca=new WeakSet,Ta=function(){var t;null==(t=this.downloadManager)||t.openOrDownloadData(this.content,this.filename)};Ra=new WeakMap,Pa=new WeakMap,Ia=new WeakMap,La=new WeakMap,Da=new WeakSet,Na=async function(t,e,s){var i,n;const a=t.firstChild||t,r=a.id=`${bd}${e}`,o=await(null==(i=oc(this,La))?void 0:i.getAriaAttributes(r));if(o)for(const[l,h]of o)a.setAttribute(l,h);s?s.at(-1).container.after(t):(this.div.append(t),null==(n=oc(this,Ra))||n.moveElementInDOM(this.div,t,a,!1))},Fa=function(){var t;if(!oc(this,Pa))return;const e=this.div;for(const[s,i]of oc(this,Pa)){const n=e.querySelector(`[data-annotation-id="${s}"]`);if(!n)continue;i.className="annotationContent";const{firstChild:a}=n;a?"CANVAS"===a.nodeName?a.replaceWith(i):a.classList.contains("annotationContent")?a.after(i):a.before(i):n.append(i);const r=oc(this,Ia).get(s);r&&(r._hasNoCanvas?(null==(t=this._annotationEditorUIManager)||t.setMissingCanvas(s,n.id,i),r._hasNoCanvas=!1):r.canvas=i)}oc(this,Pa).clear()};let sf=class t{constructor({div:t,accessibilityManager:e,annotationCanvasMap:s,annotationEditorUIManager:i,page:n,viewport:a,structTreeLayer:r}){lc(this,Da),lc(this,Ra,null),lc(this,Pa,null),lc(this,Ia,new Map),lc(this,La,null),this.div=t,hc(this,Ra,e),hc(this,Pa,s),hc(this,La,r||null),this.page=n,this.viewport=a,this.zIndex=0,this._annotationEditorUIManager=i}hasEditableAnnotations(){return oc(this,Ia).size>0}async render(t){var e;const{annotations:s}=t,i=this.div;Hd(i,this.viewport);const n=new Map,a={data:null,layer:i,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:!1!==t.renderForms,svgFactory:new Mg,annotationStorage:t.annotationStorage||new fu,enableScripting:!0===t.enableScripting,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects,parent:this,elements:null};for(const r of s){if(r.noHTML)continue;const t=r.annotationType===Nc.POPUP;if(t){const t=n.get(r.id);if(!t)continue;a.elements=t}else if(r.rect[2]===r.rect[0]||r.rect[3]===r.rect[1])continue;a.data=r;const s=Tg.create(a);if(!s.isRenderable)continue;if(!t&&r.popupRef){const t=n.get(r.popupRef);t?t.push(s):n.set(r.popupRef,[s])}const i=s.render();r.hidden&&(i.style.visibility="hidden"),await cc(this,Da,Na).call(this,i,r.id,a.elements),s._isEditable&&(oc(this,Ia).set(s.data.id,s),null==(e=this._annotationEditorUIManager)||e.renderAnnotationElement(s))}cc(this,Da,Fa).call(this)}async addLinkAnnotations(e,s){const i={data:null,layer:this.div,linkService:s,svgFactory:new Mg,parent:this};for(const n of e){n.borderStyle||(n.borderStyle=t._defaultBorderStyle),i.data=n;const e=Tg.create(i);if(!e.isRenderable)continue;const s=e.render();await cc(this,Da,Na).call(this,s,n.id,null)}}update({viewport:t}){const e=this.div;this.viewport=t,Hd(e,{rotation:t.rotation}),cc(this,Da,Fa).call(this),e.hidden=!1}getEditableAnnotations(){return Array.from(oc(this,Ia).values())}getEditableAnnotation(t){return oc(this,Ia).get(t)}static get _defaultBorderStyle(){return sd(this,"_defaultBorderStyle",Object.freeze({width:1,rawWidth:1,style:Fc,dashArray:[3],horizontalCornerRadius:0,verticalCornerRadius:0}))}};const nf=/\r\n?|\n/g,af=class t extends lu{constructor(e){super({...e,name:"freeTextEditor"}),lc(this,za),lc(this,Wa),lc(this,Oa,""),lc(this,Ba,`${this.id}-editor`),lc(this,$a,null),lc(this,Ha),hc(this,Wa,e.color||t._defaultColor||lu._defaultLineColor),hc(this,Ha,e.fontSize||t._defaultFontSize),this.annotationElementId||this._uiManager.a11yAlert("pdfjs-editor-freetext-added-alert")}static get _keyboardManager(){const e=t.prototype,s=t=>t.isEmpty(),i=su.TRANSLATE_SMALL,n=su.TRANSLATE_BIG;return sd(this,"_keyboardManager",new Jd([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],e.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],e.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],e._translateEmpty,{args:[-i,0],checker:s}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],e._translateEmpty,{args:[-n,0],checker:s}],[["ArrowRight","mac+ArrowRight"],e._translateEmpty,{args:[i,0],checker:s}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],e._translateEmpty,{args:[n,0],checker:s}],[["ArrowUp","mac+ArrowUp"],e._translateEmpty,{args:[0,-i],checker:s}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],e._translateEmpty,{args:[0,-n],checker:s}],[["ArrowDown","mac+ArrowDown"],e._translateEmpty,{args:[0,i],checker:s}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],e._translateEmpty,{args:[0,n],checker:s}]]))}static initialize(t,e){lu.initialize(t,e);const s=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(s.getPropertyValue("--freetext-padding"))}static updateDefaultParams(e,s){switch(e){case Mc.FREETEXT_SIZE:t._defaultFontSize=s;break;case Mc.FREETEXT_COLOR:t._defaultColor=s}}updateParams(t,e){switch(t){case Mc.FREETEXT_SIZE:cc(this,za,Ga).call(this,e);break;case Mc.FREETEXT_COLOR:cc(this,za,ja).call(this,e)}}static get defaultPropertiesToUpdate(){return[[Mc.FREETEXT_SIZE,t._defaultFontSize],[Mc.FREETEXT_COLOR,t._defaultColor||lu._defaultLineColor]]}get propertiesToUpdate(){return[[Mc.FREETEXT_SIZE,oc(this,Ha)],[Mc.FREETEXT_COLOR,oc(this,Wa)]]}_translateEmpty(t,e){this._uiManager.translateSelectedEditors(t,e,!0)}getInitialTranslation(){const e=this.parentScale;return[-t._internalPadding*e,-(t._internalPadding+oc(this,Ha))*e]}rebuild(){this.parent&&(super.rebuild(),null!==this.div&&(this.isAttachedToDOM||this.parent.add(this)))}enableEditMode(){if(!super.enableEditMode())return!1;this.overlayDiv.classList.remove("enabled"),this.editorDiv.contentEditable=!0,this._isDraggable=!1,this.div.removeAttribute("aria-activedescendant"),hc(this,$a,new AbortController);const t=this._uiManager.combinedSignal(oc(this,$a));return this.editorDiv.addEventListener("keydown",this.editorDivKeydown.bind(this),{signal:t}),this.editorDiv.addEventListener("focus",this.editorDivFocus.bind(this),{signal:t}),this.editorDiv.addEventListener("blur",this.editorDivBlur.bind(this),{signal:t}),this.editorDiv.addEventListener("input",this.editorDivInput.bind(this),{signal:t}),this.editorDiv.addEventListener("paste",this.editorDivPaste.bind(this),{signal:t}),!0}disableEditMode(){var t;return!!super.disableEditMode()&&(this.overlayDiv.classList.add("enabled"),this.editorDiv.contentEditable=!1,this.div.setAttribute("aria-activedescendant",oc(this,Ba)),this._isDraggable=!0,null==(t=oc(this,$a))||t.abort(),hc(this,$a,null),this.div.focus({preventScroll:!0}),this.isEditing=!1,this.parent.div.classList.add("freetextEditing"),!0)}focusin(t){this._focusEventsAllowed&&(super.focusin(t),t.target!==this.editorDiv&&this.editorDiv.focus())}onceAdded(t){var e;this.width||(this.enableEditMode(),t&&this.editorDiv.focus(),(null==(e=this._initialOptions)?void 0:e.isCentered)&&this.center(),this._initialOptions=null)}isEmpty(){return!this.editorDiv||""===this.editorDiv.innerText.trim()}remove(){this.isEditing=!1,this.parent&&(this.parent.setEditingState(!0),this.parent.div.classList.add("freetextEditing")),super.remove()}commit(){if(!this.isInEditMode())return;super.commit(),this.disableEditMode();const t=oc(this,Oa),e=hc(this,Oa,cc(this,za,Ua).call(this).trimEnd());if(t===e)return;const s=t=>{hc(this,Oa,t),t?(cc(this,za,Ya).call(this),this._uiManager.rebuild(this),cc(this,za,Va).call(this)):this.remove()};this.addCommands({cmd:()=>{s(e)},undo:()=>{s(t)},mustExec:!1}),cc(this,za,Va).call(this)}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode(),this.editorDiv.focus()}keydown(t){t.target===this.div&&"Enter"===t.key&&(this.enterInEditMode(),t.preventDefault())}editorDivKeydown(e){t._keyboardManager.exec(this,e)}editorDivFocus(t){this.isEditing=!0}editorDivBlur(t){this.isEditing=!1}editorDivInput(t){this.parent.div.classList.toggle("freetextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment"),this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox"),this.editorDiv.setAttribute("aria-multiline",!0)}get canChangeContent(){return!0}render(){if(this.div)return this.div;let t,e;(this._isCopy||this.annotationElementId)&&(t=this.x,e=this.y),super.render(),this.editorDiv=document.createElement("div"),this.editorDiv.className="internal",this.editorDiv.setAttribute("id",oc(this,Ba)),this.editorDiv.setAttribute("data-l10n-id","pdfjs-free-text2"),this.editorDiv.setAttribute("data-l10n-attrs","default-content"),this.enableEditing(),this.editorDiv.contentEditable=!0;const{style:s}=this.editorDiv;if(s.fontSize=`calc(${oc(this,Ha)}px * var(--total-scale-factor))`,s.color=oc(this,Wa),this.div.append(this.editorDiv),this.overlayDiv=document.createElement("div"),this.overlayDiv.classList.add("overlay","enabled"),this.div.append(this.overlayDiv),this._isCopy||this.annotationElementId){const[s,i]=this.parentDimensions;if(this.annotationElementId){const{position:n}=this._initialData;let[a,r]=this.getInitialTranslation();[a,r]=this.pageTranslationToScreen(a,r);const[o,l]=this.pageDimensions,[h,c]=this.pageTranslation;let d,u;switch(this.rotation){case 0:d=t+(n[0]-h)/o,u=e+this.height-(n[1]-c)/l;break;case 90:d=t+(n[0]-h)/o,u=e-(n[1]-c)/l,[a,r]=[r,-a];break;case 180:d=t-this.width+(n[0]-h)/o,u=e-(n[1]-c)/l,[a,r]=[-a,-r];break;case 270:d=t+(n[0]-h-this.height*l)/o,u=e+(n[1]-c-this.width*o)/l,[a,r]=[-r,a]}this.setAt(d*s,u*i,a,r)}else this._moveAfterPaste(t,e);cc(this,za,Ya).call(this),this._isDraggable=!0,this.editorDiv.contentEditable=!1}else this._isDraggable=!1,this.editorDiv.contentEditable=!0;return this.div}editorDivPaste(e){var s,i,n;const a=e.clipboardData||window.clipboardData,{types:r}=a;if(1===r.length&&"text/plain"===r[0])return;e.preventDefault();const o=cc(s=t,qa,Qa).call(s,a.getData("text")||"").replaceAll(nf,"\n");if(!o)return;const l=window.getSelection();if(!l.rangeCount)return;this.editorDiv.normalize(),l.deleteFromDocument();const h=l.getRangeAt(0);if(!o.includes("\n"))return h.insertNode(document.createTextNode(o)),this.editorDiv.normalize(),void l.collapseToStart();const{startContainer:c,startOffset:d}=h,u=[],p=[];if(c.nodeType===Node.TEXT_NODE){const e=c.parentElement;if(p.push(c.nodeValue.slice(d).replaceAll(nf,"")),e!==this.editorDiv){let s=u;for(const n of this.editorDiv.childNodes)n!==e?s.push(cc(i=t,qa,Xa).call(i,n)):s=p}u.push(c.nodeValue.slice(0,d).replaceAll(nf,""))}else if(c===this.editorDiv){let e=u,s=0;for(const i of this.editorDiv.childNodes)s++===d&&(e=p),e.push(cc(n=t,qa,Xa).call(n,i))}hc(this,Oa,`${u.join("\n")}${o}${p.join("\n")}`),cc(this,za,Ya).call(this);const g=new Range;let f=Math.sumPrecise(u.map(t=>t.length));for(const{firstChild:t}of this.editorDiv.childNodes)if(t.nodeType===Node.TEXT_NODE){const e=t.nodeValue.length;if(f<=e){g.setStart(t,f),g.setEnd(t,f);break}f-=e}l.removeAllRanges(),l.addRange(g)}get contentDiv(){return this.editorDiv}static async deserialize(e,s,i){var n;let a=null;if(e instanceof zg){const{data:{defaultAppearanceData:{fontSize:t,fontColor:s},rect:i,rotation:n,id:r,popupRef:o},textContent:l,textPosition:h,parent:{page:{pageNumber:c}}}=e;if(!l||0===l.length)return null;a=e={annotationType:kc.FREETEXT,color:Array.from(s),fontSize:t,value:l.join("\n"),position:h,pageIndex:c-1,rect:i.slice(0),rotation:n,annotationElementId:r,id:r,deleted:!1,popupRef:o}}const r=await super.deserialize(e,s,i);return hc(r,Ha,e.fontSize),hc(r,Wa,gd.makeHexColor(...e.color)),hc(r,Oa,cc(n=t,qa,Qa).call(n,e.value)),r._initialData=a,r}serialize(e=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const s=t._internalPadding*this.parentScale,i=this.getRect(s,s),n=lu._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:oc(this,Wa)),a={annotationType:kc.FREETEXT,color:n,fontSize:oc(this,Ha),value:cc(this,za,Ka).call(this),pageIndex:this.pageIndex,rect:i,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return e?(a.isCopy=!0,a):this.annotationElementId&&!cc(this,za,Ja).call(this,a)?null:(a.id=this.annotationElementId,a)}renderAnnotationElement(e){const s=super.renderAnnotationElement(e);if(this.deleted)return s;const{style:i}=s;i.fontSize=`calc(${oc(this,Ha)}px * var(--total-scale-factor))`,i.color=oc(this,Wa),s.replaceChildren();for(const t of oc(this,Oa).split("\n")){const e=document.createElement("div");e.append(t?document.createTextNode(t):document.createElement("br")),s.append(e)}const n=t._internalPadding*this.parentScale;return e.updateEdited({rect:this.getRect(n,n),popupContent:oc(this,Oa)}),s}resetAnnotationElement(t){super.resetAnnotationElement(t),t.resetEdited()}};Wa=new WeakMap,Oa=new WeakMap,Ba=new WeakMap,$a=new WeakMap,Ha=new WeakMap,za=new WeakSet,Ga=function(t){const e=t=>{this.editorDiv.style.fontSize=`calc(${t}px * var(--total-scale-factor))`,this.translate(0,-(t-oc(this,Ha))*this.parentScale),hc(this,Ha,t),cc(this,za,Va).call(this)},s=oc(this,Ha);this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,s),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:Mc.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})},ja=function(t){const e=t=>{hc(this,Wa,this.editorDiv.style.color=t)},s=oc(this,Wa);this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,s),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:Mc.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})},Ua=function(){var t;const e=[];this.editorDiv.normalize();let s=null;for(const i of this.editorDiv.childNodes)(null==s?void 0:s.nodeType)===Node.TEXT_NODE&&"BR"===i.nodeName||(e.push(cc(t=af,qa,Xa).call(t,i)),s=i);return e.join("\n")},Va=function(){const[t,e]=this.parentDimensions;let s;if(this.isAttachedToDOM)s=this.div.getBoundingClientRect();else{const{currentLayer:t,div:e}=this,i=e.style.display,n=e.classList.contains("hidden");e.classList.remove("hidden"),e.style.display="hidden",t.div.append(this.div),s=e.getBoundingClientRect(),e.remove(),e.style.display=i,e.classList.toggle("hidden",n)}this.rotation%180==this.parentRotation%180?(this.width=s.width/t,this.height=s.height/e):(this.width=s.height/t,this.height=s.width/e),this.fixAndSetPosition()},qa=new WeakSet,Xa=function(t){return(t.nodeType===Node.TEXT_NODE?t.nodeValue:t.innerText).replaceAll(nf,"")},Ya=function(){if(this.editorDiv.replaceChildren(),oc(this,Oa))for(const t of oc(this,Oa).split("\n")){const e=document.createElement("div");e.append(t?document.createTextNode(t):document.createElement("br")),this.editorDiv.append(e)}},Ka=function(){return oc(this,Oa).replaceAll(" "," ")},Qa=function(t){return t.replaceAll(" "," ")},Ja=function(t){const{value:e,fontSize:s,color:i,pageIndex:n}=this._initialData;return this._hasBeenMoved||t.value!==e||t.fontSize!==s||t.color.some((t,e)=>t!==i[e])||t.pageIndex!==n},lc(af,qa),ac(af,"_freeTextDefaultContent",""),ac(af,"_internalPadding",0),ac(af,"_defaultColor",null),ac(af,"_defaultFontSize",10),ac(af,"_type","freetext"),ac(af,"_editorType",kc.FREETEXT);let rf=af;class of{toSVGPath(){Jc("Abstract method `toSVGPath` must be implemented.")}get box(){Jc("Abstract getter `box` must be implemented.")}serialize(t,e){Jc("Abstract method `serialize` must be implemented.")}static _rescale(t,e,s,i,n,a){a||(a=new Float32Array(t.length));for(let r=0,o=t.length;r<o;r+=2)a[r]=e+t[r]*i,a[r+1]=s+t[r+1]*n;return a}static _rescaleAndSwap(t,e,s,i,n,a){a||(a=new Float32Array(t.length));for(let r=0,o=t.length;r<o;r+=2)a[r]=e+t[r+1]*i,a[r+1]=s+t[r]*n;return a}static _translate(t,e,s,i){i||(i=new Float32Array(t.length));for(let n=0,a=t.length;n<a;n+=2)i[n]=e+t[n],i[n+1]=s+t[n+1];return i}static svgRound(t){return Math.round(1e4*t)}static _normalizePoint(t,e,s,i,n){switch(n){case 90:return[1-e/s,t/i];case 180:return[1-t/s,1-e/i];case 270:return[e/s,1-t/i];default:return[t/s,e/i]}}static _normalizePagePoint(t,e,s){switch(s){case 90:return[1-e,t];case 180:return[1-t,1-e];case 270:return[e,1-t];default:return[t,e]}}static createBezierPoints(t,e,s,i,n,a){return[(t+5*s)/6,(e+5*i)/6,(5*s+n)/6,(5*i+a)/6,(s+n)/2,(i+a)/2]}}ac(of,"PRECISION",1e-4);const lf=class t{constructor({x:e,y:s},i,n,a,r,o=0){lc(this,fr),lc(this,Za),lc(this,tr,[]),lc(this,er),lc(this,sr),lc(this,ir,[]),lc(this,nr,new Float32Array(18)),lc(this,ar),lc(this,rr),lc(this,or),lc(this,lr),lc(this,hr),lc(this,cr),lc(this,dr,[]),hc(this,Za,i),hc(this,cr,a*n),hc(this,sr,r),oc(this,nr).set([NaN,NaN,NaN,NaN,e,s],6),hc(this,er,o),hc(this,lr,oc(t,ur)*n),hc(this,or,oc(t,gr)*n),hc(this,hr,n),oc(this,dr).push(e,s)}isEmpty(){return isNaN(oc(this,nr)[8])}add({x:t,y:e}){var s;hc(this,ar,t),hc(this,rr,e);const[i,n,a,r]=oc(this,Za);let[o,l,h,c]=oc(this,nr).subarray(8,12);const d=t-h,u=e-c,p=Math.hypot(d,u);if(p<oc(this,or))return!1;const g=p-oc(this,lr),f=g/p,m=f*d,v=f*u;let w=o,b=l;o=h,l=c,h+=m,c+=v,null==(s=oc(this,dr))||s.push(t,e);const A=m/g,_=-v/g*oc(this,cr),y=A*oc(this,cr);if(oc(this,nr).set(oc(this,nr).subarray(2,8),0),oc(this,nr).set([h+_,c+y],4),oc(this,nr).set(oc(this,nr).subarray(14,18),12),oc(this,nr).set([h-_,c-y],16),isNaN(oc(this,nr)[6]))return 0===oc(this,ir).length&&(oc(this,nr).set([o+_,l+y],2),oc(this,ir).push(NaN,NaN,NaN,NaN,(o+_-i)/a,(l+y-n)/r),oc(this,nr).set([o-_,l-y],14),oc(this,tr).push(NaN,NaN,NaN,NaN,(o-_-i)/a,(l-y-n)/r)),oc(this,nr).set([w,b,o,l,h,c],6),!this.isEmpty();oc(this,nr).set([w,b,o,l,h,c],6);return Math.abs(Math.atan2(b-l,w-o)-Math.atan2(v,m))<Math.PI/2?([o,l,h,c]=oc(this,nr).subarray(2,6),oc(this,ir).push(NaN,NaN,NaN,NaN,((o+h)/2-i)/a,((l+c)/2-n)/r),[o,l,w,b]=oc(this,nr).subarray(14,18),oc(this,tr).push(NaN,NaN,NaN,NaN,((w+o)/2-i)/a,((b+l)/2-n)/r),!0):([w,b,o,l,h,c]=oc(this,nr).subarray(0,6),oc(this,ir).push(((w+5*o)/6-i)/a,((b+5*l)/6-n)/r,((5*o+h)/6-i)/a,((5*l+c)/6-n)/r,((o+h)/2-i)/a,((l+c)/2-n)/r),[h,c,o,l,w,b]=oc(this,nr).subarray(12,18),oc(this,tr).push(((w+5*o)/6-i)/a,((b+5*l)/6-n)/r,((5*o+h)/6-i)/a,((5*l+c)/6-n)/r,((o+h)/2-i)/a,((l+c)/2-n)/r),!0)}toSVGPath(){if(this.isEmpty())return"";const t=oc(this,ir),e=oc(this,tr);if(isNaN(oc(this,nr)[6])&&!this.isEmpty())return cc(this,fr,vr).call(this);const s=[];s.push(`M${t[4]} ${t[5]}`);for(let i=6;i<t.length;i+=6)isNaN(t[i])?s.push(`L${t[i+4]} ${t[i+5]}`):s.push(`C${t[i]} ${t[i+1]} ${t[i+2]} ${t[i+3]} ${t[i+4]} ${t[i+5]}`);cc(this,fr,br).call(this,s);for(let i=e.length-6;i>=6;i-=6)isNaN(e[i])?s.push(`L${e[i+4]} ${e[i+5]}`):s.push(`C${e[i]} ${e[i+1]} ${e[i+2]} ${e[i+3]} ${e[i+4]} ${e[i+5]}`);return cc(this,fr,wr).call(this,s),s.join(" ")}newFreeDrawOutline(t,e,s,i,n,a){return new cf(t,e,s,i,n,a)}getOutlines(){var t;const e=oc(this,ir),s=oc(this,tr),i=oc(this,nr),[n,a,r,o]=oc(this,Za),l=new Float32Array(((null==(t=oc(this,dr))?void 0:t.length)??0)+2);for(let d=0,u=l.length-2;d<u;d+=2)l[d]=(oc(this,dr)[d]-n)/r,l[d+1]=(oc(this,dr)[d+1]-a)/o;if(l[l.length-2]=(oc(this,ar)-n)/r,l[l.length-1]=(oc(this,rr)-a)/o,isNaN(i[6])&&!this.isEmpty())return cc(this,fr,Ar).call(this,l);const h=new Float32Array(oc(this,ir).length+24+oc(this,tr).length);let c=e.length;for(let d=0;d<c;d+=2)isNaN(e[d])?h[d]=h[d+1]=NaN:(h[d]=e[d],h[d+1]=e[d+1]);c=cc(this,fr,yr).call(this,h,c);for(let d=s.length-6;d>=6;d-=6)for(let t=0;t<6;t+=2)isNaN(s[d+t])?(h[c]=h[c+1]=NaN,c+=2):(h[c]=s[d+t],h[c+1]=s[d+t+1],c+=2);return cc(this,fr,_r).call(this,h,c),this.newFreeDrawOutline(h,l,oc(this,Za),oc(this,hr),oc(this,er),oc(this,sr))}};Za=new WeakMap,tr=new WeakMap,er=new WeakMap,sr=new WeakMap,ir=new WeakMap,nr=new WeakMap,ar=new WeakMap,rr=new WeakMap,or=new WeakMap,lr=new WeakMap,hr=new WeakMap,cr=new WeakMap,dr=new WeakMap,ur=new WeakMap,pr=new WeakMap,gr=new WeakMap,fr=new WeakSet,mr=function(){const t=oc(this,nr).subarray(4,6),e=oc(this,nr).subarray(16,18),[s,i,n,a]=oc(this,Za);return[(oc(this,ar)+(t[0]-e[0])/2-s)/n,(oc(this,rr)+(t[1]-e[1])/2-i)/a,(oc(this,ar)+(e[0]-t[0])/2-s)/n,(oc(this,rr)+(e[1]-t[1])/2-i)/a]},vr=function(){const[t,e,s,i]=oc(this,Za),[n,a,r,o]=cc(this,fr,mr).call(this);return`M${(oc(this,nr)[2]-t)/s} ${(oc(this,nr)[3]-e)/i} L${(oc(this,nr)[4]-t)/s} ${(oc(this,nr)[5]-e)/i} L${n} ${a} L${r} ${o} L${(oc(this,nr)[16]-t)/s} ${(oc(this,nr)[17]-e)/i} L${(oc(this,nr)[14]-t)/s} ${(oc(this,nr)[15]-e)/i} Z`},wr=function(t){const e=oc(this,tr);t.push(`L${e[4]} ${e[5]} Z`)},br=function(t){const[e,s,i,n]=oc(this,Za),a=oc(this,nr).subarray(4,6),r=oc(this,nr).subarray(16,18),[o,l,h,c]=cc(this,fr,mr).call(this);t.push(`L${(a[0]-e)/i} ${(a[1]-s)/n} L${o} ${l} L${h} ${c} L${(r[0]-e)/i} ${(r[1]-s)/n}`)},Ar=function(t){const e=oc(this,nr),[s,i,n,a]=oc(this,Za),[r,o,l,h]=cc(this,fr,mr).call(this),c=new Float32Array(36);return c.set([NaN,NaN,NaN,NaN,(e[2]-s)/n,(e[3]-i)/a,NaN,NaN,NaN,NaN,(e[4]-s)/n,(e[5]-i)/a,NaN,NaN,NaN,NaN,r,o,NaN,NaN,NaN,NaN,l,h,NaN,NaN,NaN,NaN,(e[16]-s)/n,(e[17]-i)/a,NaN,NaN,NaN,NaN,(e[14]-s)/n,(e[15]-i)/a],0),this.newFreeDrawOutline(c,t,oc(this,Za),oc(this,hr),oc(this,er),oc(this,sr))},_r=function(t,e){const s=oc(this,tr);return t.set([NaN,NaN,NaN,NaN,s[4],s[5]],e),e+6},yr=function(t,e){const s=oc(this,nr).subarray(4,6),i=oc(this,nr).subarray(16,18),[n,a,r,o]=oc(this,Za),[l,h,c,d]=cc(this,fr,mr).call(this);return t.set([NaN,NaN,NaN,NaN,(s[0]-n)/r,(s[1]-a)/o,NaN,NaN,NaN,NaN,l,h,NaN,NaN,NaN,NaN,c,d,NaN,NaN,NaN,NaN,(i[0]-n)/r,(i[1]-a)/o],e),e+24},lc(lf,ur,8),lc(lf,pr,2),lc(lf,gr,oc(lf,ur)+oc(lf,pr));let hf=lf;class cf extends of{constructor(t,e,s,i,n,a){super(),lc(this,Rr),lc(this,xr),lc(this,Sr,new Float32Array(4)),lc(this,kr),lc(this,Mr),lc(this,Er),lc(this,Cr),lc(this,Tr),hc(this,Tr,t),hc(this,Er,e),hc(this,xr,s),hc(this,Cr,i),hc(this,kr,n),hc(this,Mr,a),this.lastPoint=[NaN,NaN],cc(this,Rr,Pr).call(this,a);const[r,o,l,h]=oc(this,Sr);for(let c=0,d=t.length;c<d;c+=2)t[c]=(t[c]-r)/l,t[c+1]=(t[c+1]-o)/h;for(let c=0,d=e.length;c<d;c+=2)e[c]=(e[c]-r)/l,e[c+1]=(e[c+1]-o)/h}toSVGPath(){const t=[`M${oc(this,Tr)[4]} ${oc(this,Tr)[5]}`];for(let e=6,s=oc(this,Tr).length;e<s;e+=6)isNaN(oc(this,Tr)[e])?t.push(`L${oc(this,Tr)[e+4]} ${oc(this,Tr)[e+5]}`):t.push(`C${oc(this,Tr)[e]} ${oc(this,Tr)[e+1]} ${oc(this,Tr)[e+2]} ${oc(this,Tr)[e+3]} ${oc(this,Tr)[e+4]} ${oc(this,Tr)[e+5]}`);return t.push("Z"),t.join(" ")}serialize([t,e,s,i],n){const a=s-t,r=i-e;let o,l;switch(n){case 0:o=of._rescale(oc(this,Tr),t,i,a,-r),l=of._rescale(oc(this,Er),t,i,a,-r);break;case 90:o=of._rescaleAndSwap(oc(this,Tr),t,e,a,r),l=of._rescaleAndSwap(oc(this,Er),t,e,a,r);break;case 180:o=of._rescale(oc(this,Tr),s,e,-a,r),l=of._rescale(oc(this,Er),s,e,-a,r);break;case 270:o=of._rescaleAndSwap(oc(this,Tr),s,i,-a,-r),l=of._rescaleAndSwap(oc(this,Er),s,i,-a,-r)}return{outline:Array.from(o),points:[Array.from(l)]}}get box(){return oc(this,Sr)}newOutliner(t,e,s,i,n,a=0){return new hf(t,e,s,i,n,a)}getNewOutline(t,e){const[s,i,n,a]=oc(this,Sr),[r,o,l,h]=oc(this,xr),c=n*l,d=a*h,u=s*l+r,p=i*h+o,g=this.newOutliner({x:oc(this,Er)[0]*c+u,y:oc(this,Er)[1]*d+p},oc(this,xr),oc(this,Cr),t,oc(this,Mr),e??oc(this,kr));for(let f=2;f<oc(this,Er).length;f+=2)g.add({x:oc(this,Er)[f]*c+u,y:oc(this,Er)[f+1]*d+p});return g.getOutlines()}}xr=new WeakMap,Sr=new WeakMap,kr=new WeakMap,Mr=new WeakMap,Er=new WeakMap,Cr=new WeakMap,Tr=new WeakMap,Rr=new WeakSet,Pr=function(t){const e=oc(this,Tr);let s=e[4],i=e[5];const n=[s,i,s,i];let a=s,r=i;const o=t?Math.max:Math.min;for(let h=6,c=e.length;h<c;h+=6){const t=e[h+4],l=e[h+5];if(isNaN(e[h]))gd.pointBoundingBox(t,l,n),r<l?(a=t,r=l):r===l&&(a=o(a,t));else{const t=[1/0,1/0,-1/0,-1/0];gd.bezierBoundingBox(s,i,...e.slice(h,h+6),t),gd.rectBoundingBox(...t,n),r<t[3]?(a=t[2],r=t[3]):r===t[3]&&(a=o(a,t[2]))}s=t,i=l}const l=oc(this,Sr);l[0]=n[0]-oc(this,kr),l[1]=n[1]-oc(this,kr),l[2]=n[2]-n[0]+2*oc(this,kr),l[3]=n[3]-n[1]+2*oc(this,kr),this.lastPoint=[a,r]};class df{constructor(t,e=0,s=0,i=!0){lc(this,Fr),lc(this,Ir),lc(this,Lr),lc(this,Dr,[]),lc(this,Nr,[]);const n=[1/0,1/0,-1/0,-1/0],a=10**-4;for(const{x:u,y:p,width:g,height:f}of t){const t=Math.floor((u-e)/a)*a,s=Math.ceil((u+g+e)/a)*a,i=Math.floor((p-e)/a)*a,r=Math.ceil((p+f+e)/a)*a,o=[t,i,r,!0],l=[s,i,r,!1];oc(this,Dr).push(o,l),gd.rectBoundingBox(t,i,s,r,n)}const r=n[2]-n[0]+2*s,o=n[3]-n[1]+2*s,l=n[0]-s,h=n[1]-s,c=oc(this,Dr).at(i?-1:-2),d=[c[0],c[2]];for(const u of oc(this,Dr)){const[t,e,s]=u;u[0]=(t-l)/r,u[1]=(e-h)/o,u[2]=(s-h)/o}hc(this,Ir,new Float32Array([l,h,r,o])),hc(this,Lr,d)}getOutlines(){oc(this,Dr).sort((t,e)=>t[0]-e[0]||t[1]-e[1]||t[2]-e[2]);const t=[];for(const e of oc(this,Dr))e[3]?(t.push(...cc(this,Fr,Hr).call(this,e)),cc(this,Fr,Br).call(this,e)):(cc(this,Fr,$r).call(this,e),t.push(...cc(this,Fr,Hr).call(this,e)));return cc(this,Fr,Wr).call(this,t)}}Ir=new WeakMap,Lr=new WeakMap,Dr=new WeakMap,Nr=new WeakMap,Fr=new WeakSet,Wr=function(t){const e=[],s=new Set;for(const a of t){const[t,s,i]=a;e.push([t,s,a],[t,i,a])}e.sort((t,e)=>t[1]-e[1]||t[0]-e[0]);for(let a=0,r=e.length;a<r;a+=2){const t=e[a][2],i=e[a+1][2];t.push(i),i.push(t),s.add(t),s.add(i)}const i=[];let n;for(;s.size>0;){const t=s.values().next().value;let[e,a,r,o,l]=t;s.delete(t);let h=e,c=a;for(n=[e,r],i.push(n);;){let t;if(s.has(o))t=o;else{if(!s.has(l))break;t=l}s.delete(t),[e,a,r,o,l]=t,h!==e&&(n.push(h,c,e,c===a?a:r),h=e),c=c===a?r:a}n.push(h,c)}return new uf(i,oc(this,Ir),oc(this,Lr))},Or=function(t){const e=oc(this,Nr);let s=0,i=e.length-1;for(;s<=i;){const n=s+i>>1,a=e[n][0];if(a===t)return n;a<t?s=n+1:i=n-1}return i+1},Br=function([,t,e]){const s=cc(this,Fr,Or).call(this,t);oc(this,Nr).splice(s,0,[t,e])},$r=function([,t,e]){const s=cc(this,Fr,Or).call(this,t);for(let i=s;i<oc(this,Nr).length;i++){const[s,n]=oc(this,Nr)[i];if(s!==t)break;if(s===t&&n===e)return void oc(this,Nr).splice(i,1)}for(let i=s-1;i>=0;i--){const[s,n]=oc(this,Nr)[i];if(s!==t)break;if(s===t&&n===e)return void oc(this,Nr).splice(i,1)}},Hr=function(t){const[e,s,i]=t,n=[[e,s,i]],a=cc(this,Fr,Or).call(this,i);for(let r=0;r<a;r++){const[t,s]=oc(this,Nr)[r];for(let i=0,a=n.length;i<a;i++){const[,r,o]=n[i];if(!(s<=r||o<=t))if(r>=t)if(o>s)n[i][1]=s;else{if(1===a)return[];n.splice(i,1),i--,a--}else n[i][2]=t,o>s&&n.push([e,s,o])}}return n};class uf extends of{constructor(t,e,s){super(),lc(this,zr),lc(this,Gr),hc(this,Gr,t),hc(this,zr,e),this.lastPoint=s}toSVGPath(){const t=[];for(const e of oc(this,Gr)){let[s,i]=e;t.push(`M${s} ${i}`);for(let n=2;n<e.length;n+=2){const a=e[n],r=e[n+1];a===s?(t.push(`V${r}`),i=r):r===i&&(t.push(`H${a}`),s=a)}t.push("Z")}return t.join(" ")}serialize([t,e,s,i],n){const a=[],r=s-t,o=i-e;for(const l of oc(this,Gr)){const e=new Array(l.length);for(let s=0;s<l.length;s+=2)e[s]=t+l[s]*r,e[s+1]=i-l[s+1]*o;a.push(e)}return a}get box(){return oc(this,zr)}get classNamesForOutlining(){return["highlightOutline"]}}zr=new WeakMap,Gr=new WeakMap;class pf extends hf{newFreeDrawOutline(t,e,s,i,n,a){return new gf(t,e,s,i,n,a)}}class gf extends cf{newOutliner(t,e,s,i,n,a=0){return new pf(t,e,s,i,n,a)}}const ff=class t{constructor({editor:e=null,uiManager:s=null}){var i,n;lc(this,so),lc(this,jr,null),lc(this,Ur,null),lc(this,Vr),lc(this,qr,null),lc(this,Xr,!1),lc(this,Yr,!1),lc(this,Kr,null),lc(this,Qr),lc(this,Jr,null),lc(this,Zr,null),lc(this,to),e?(hc(this,Yr,!1),hc(this,to,Mc.HIGHLIGHT_COLOR),hc(this,Kr,e)):(hc(this,Yr,!0),hc(this,to,Mc.HIGHLIGHT_DEFAULT_COLOR)),hc(this,Zr,(null==e?void 0:e._uiManager)||s),hc(this,Qr,oc(this,Zr)._eventBus),hc(this,Vr,(null==(i=null==e?void 0:e.color)?void 0:i.toUpperCase())||(null==(n=oc(this,Zr))?void 0:n.highlightColors.values().next().value)||"#FFFF98"),oc(t,eo)||hc(t,eo,Object.freeze({blue:"pdfjs-editor-colorpicker-blue",green:"pdfjs-editor-colorpicker-green",pink:"pdfjs-editor-colorpicker-pink",red:"pdfjs-editor-colorpicker-red",yellow:"pdfjs-editor-colorpicker-yellow"}))}static get _keyboardManager(){return sd(this,"_keyboardManager",new Jd([[["Escape","mac+Escape"],t.prototype._hideDropdownFromKeyboard],[[" ","mac+ "],t.prototype._colorSelectFromKeyboard],[["ArrowDown","ArrowRight","mac+ArrowDown","mac+ArrowRight"],t.prototype._moveToNext],[["ArrowUp","ArrowLeft","mac+ArrowUp","mac+ArrowLeft"],t.prototype._moveToPrevious],[["Home","mac+Home"],t.prototype._moveToBeginning],[["End","mac+End"],t.prototype._moveToEnd]]))}renderButton(){const t=hc(this,jr,document.createElement("button"));t.className="colorPicker",t.tabIndex="0",t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-button"),t.ariaHasPopup="true",oc(this,Kr)&&(t.ariaControls=`${oc(this,Kr).id}_colorpicker_dropdown`);const e=oc(this,Zr)._signal;t.addEventListener("click",cc(this,so,ro).bind(this),{signal:e}),t.addEventListener("keydown",cc(this,so,ao).bind(this),{signal:e});const s=hc(this,Ur,document.createElement("span"));return s.className="swatch",s.ariaHidden="true",s.style.backgroundColor=oc(this,Vr),t.append(s),t}renderMainDropdown(){const t=hc(this,qr,cc(this,so,io).call(this));return t.ariaOrientation="horizontal",t.ariaLabelledBy="highlightColorPickerLabel",t}_colorSelectFromKeyboard(t){if(t.target===oc(this,jr))return void cc(this,so,ro).call(this,t);const e=t.target.getAttribute("data-color");e&&cc(this,so,no).call(this,e,t)}_moveToNext(t){var e,s;oc(this,so,lo)?t.target!==oc(this,jr)?null==(s=t.target.nextSibling)||s.focus():null==(e=oc(this,qr).firstChild)||e.focus():cc(this,so,ro).call(this,t)}_moveToPrevious(t){var e,s;t.target!==(null==(e=oc(this,qr))?void 0:e.firstChild)&&t.target!==oc(this,jr)?(oc(this,so,lo)||cc(this,so,ro).call(this,t),null==(s=t.target.previousSibling)||s.focus()):oc(this,so,lo)&&this._hideDropdownFromKeyboard()}_moveToBeginning(t){var e;oc(this,so,lo)?null==(e=oc(this,qr).firstChild)||e.focus():cc(this,so,ro).call(this,t)}_moveToEnd(t){var e;oc(this,so,lo)?null==(e=oc(this,qr).lastChild)||e.focus():cc(this,so,ro).call(this,t)}hideDropdown(){var t,e;null==(t=oc(this,qr))||t.classList.add("hidden"),oc(this,jr).ariaExpanded="false",null==(e=oc(this,Jr))||e.abort(),hc(this,Jr,null)}_hideDropdownFromKeyboard(){var t;oc(this,Yr)||(oc(this,so,lo)?(this.hideDropdown(),oc(this,jr).focus({preventScroll:!0,focusVisible:oc(this,Xr)})):null==(t=oc(this,Kr))||t.unselect())}updateColor(t){if(oc(this,Ur)&&(oc(this,Ur).style.backgroundColor=t),!oc(this,qr))return;const e=oc(this,Zr).highlightColors.values();for(const s of oc(this,qr).children)s.ariaSelected=e.next().value===t.toUpperCase()}destroy(){var t,e;null==(t=oc(this,jr))||t.remove(),hc(this,jr,null),hc(this,Ur,null),null==(e=oc(this,qr))||e.remove(),hc(this,qr,null)}};jr=new WeakMap,Ur=new WeakMap,Vr=new WeakMap,qr=new WeakMap,Xr=new WeakMap,Yr=new WeakMap,Kr=new WeakMap,Qr=new WeakMap,Jr=new WeakMap,Zr=new WeakMap,to=new WeakMap,eo=new WeakMap,so=new WeakSet,io=function(){const t=document.createElement("div"),e=oc(this,Zr)._signal;t.addEventListener("contextmenu",Dd,{signal:e}),t.className="dropdown",t.role="listbox",t.ariaMultiSelectable="false",t.ariaOrientation="vertical",t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-dropdown"),oc(this,Kr)&&(t.id=`${oc(this,Kr).id}_colorpicker_dropdown`);for(const[s,i]of oc(this,Zr).highlightColors){const n=document.createElement("button");n.tabIndex="0",n.role="option",n.setAttribute("data-color",i),n.title=s,n.setAttribute("data-l10n-id",oc(ff,eo)[s]);const a=document.createElement("span");n.append(a),a.className="swatch",a.style.backgroundColor=i,n.ariaSelected=i===oc(this,Vr),n.addEventListener("click",cc(this,so,no).bind(this,i),{signal:e}),t.append(n)}return t.addEventListener("keydown",cc(this,so,ao).bind(this),{signal:e}),t},no=function(t,e){e.stopPropagation(),oc(this,Qr).dispatch("switchannotationeditorparams",{source:this,type:oc(this,to),value:t})},ao=function(t){ff._keyboardManager.exec(this,t)},ro=function(t){if(oc(this,so,lo))return void this.hideDropdown();if(hc(this,Xr,0===t.detail),oc(this,Jr)||(hc(this,Jr,new AbortController),window.addEventListener("pointerdown",cc(this,so,oo).bind(this),{signal:oc(this,Zr).combinedSignal(oc(this,Jr))})),oc(this,jr).ariaExpanded="true",oc(this,qr))return void oc(this,qr).classList.remove("hidden");const e=hc(this,qr,cc(this,so,io).call(this));oc(this,jr).append(e)},oo=function(t){var e;(null==(e=oc(this,qr))?void 0:e.contains(t.target))||this.hideDropdown()},lo=function(){return oc(this,qr)&&!oc(this,qr).classList.contains("hidden")},lc(ff,eo,null);let mf=ff;const vf=class t extends lu{constructor(e){super({...e,name:"highlightEditor"}),lc(this,Co),lc(this,ho,null),lc(this,co,0),lc(this,uo),lc(this,po,null),lc(this,go,null),lc(this,fo,null),lc(this,mo,null),lc(this,vo,0),lc(this,wo,null),lc(this,bo,null),lc(this,Ao,null),lc(this,_o,!1),lc(this,yo,null),lc(this,xo),lc(this,So,null),lc(this,ko,""),lc(this,Mo),lc(this,Eo,""),this.color=e.color||t._defaultColor,hc(this,Mo,e.thickness||t._defaultThickness),hc(this,xo,e.opacity||t._defaultOpacity),hc(this,uo,e.boxes||null),hc(this,Eo,e.methodOfCreation||""),hc(this,ko,e.text||""),this._isDraggable=!1,this.defaultL10nId="pdfjs-editor-highlight-editor",e.highlightId>-1?(hc(this,_o,!0),cc(this,Co,Ro).call(this,e),cc(this,Co,No).call(this)):oc(this,uo)&&(hc(this,ho,e.anchorNode),hc(this,co,e.anchorOffset),hc(this,mo,e.focusNode),hc(this,vo,e.focusOffset),cc(this,Co,To).call(this),cc(this,Co,No).call(this),this.rotate(this.rotation)),this.annotationElementId||this._uiManager.a11yAlert("pdfjs-editor-highlight-added-alert")}static get _keyboardManager(){const e=t.prototype;return sd(this,"_keyboardManager",new Jd([[["ArrowLeft","mac+ArrowLeft"],e._moveCaret,{args:[0]}],[["ArrowRight","mac+ArrowRight"],e._moveCaret,{args:[1]}],[["ArrowUp","mac+ArrowUp"],e._moveCaret,{args:[2]}],[["ArrowDown","mac+ArrowDown"],e._moveCaret,{args:[3]}]]))}get telemetryInitialData(){return{action:"added",type:oc(this,_o)?"free_highlight":"highlight",color:this._uiManager.highlightColorNames.get(this.color),thickness:oc(this,Mo),methodOfCreation:oc(this,Eo)}}get telemetryFinalData(){return{type:"highlight",color:this._uiManager.highlightColorNames.get(this.color)}}static computeTelemetryFinalData(t){return{numberOfColors:t.get("color").size}}static initialize(e,s){var i;lu.initialize(e,s),t._defaultColor||(t._defaultColor=(null==(i=s.highlightColors)?void 0:i.values().next().value)||"#fff066")}static updateDefaultParams(e,s){switch(e){case Mc.HIGHLIGHT_DEFAULT_COLOR:t._defaultColor=s;break;case Mc.HIGHLIGHT_THICKNESS:t._defaultThickness=s}}translateInPage(t,e){}get toolbarPosition(){return oc(this,yo)}updateParams(t,e){switch(t){case Mc.HIGHLIGHT_COLOR:cc(this,Co,Po).call(this,e);break;case Mc.HIGHLIGHT_THICKNESS:cc(this,Co,Io).call(this,e)}}static get defaultPropertiesToUpdate(){return[[Mc.HIGHLIGHT_DEFAULT_COLOR,t._defaultColor],[Mc.HIGHLIGHT_THICKNESS,t._defaultThickness]]}get propertiesToUpdate(){return[[Mc.HIGHLIGHT_COLOR,this.color||t._defaultColor],[Mc.HIGHLIGHT_THICKNESS,oc(this,Mo)||t._defaultThickness],[Mc.HIGHLIGHT_FREE,oc(this,_o)]]}get toolbarButtons(){if(this._uiManager.highlightColors){return[["colorPicker",hc(this,go,new mf({editor:this}))]]}return super.toolbarButtons}disableEditing(){super.disableEditing(),this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing(),this.div.classList.toggle("disabled",!1)}fixAndSetPosition(){return super.fixAndSetPosition(cc(this,Co,$o).call(this))}getBaseTranslation(){return[0,0]}getRect(t,e){return super.getRect(t,e,cc(this,Co,$o).call(this))}onceAdded(t){this.annotationElementId||this.parent.addUndoableEditor(this),t&&this.div.focus()}remove(){cc(this,Co,Do).call(this),this._reportTelemetry({action:"deleted"}),super.remove()}rebuild(){this.parent&&(super.rebuild(),null!==this.div&&(cc(this,Co,No).call(this),this.isAttachedToDOM||this.parent.add(this)))}setParent(t){var e;let s=!1;this.parent&&!t?cc(this,Co,Do).call(this):t&&(cc(this,Co,No).call(this,t),s=!this.parent&&(null==(e=this.div)?void 0:e.classList.contains("selectedEditor"))),super.setParent(t),this.show(this._isVisible),s&&this.select()}rotate(e){var s,i,n;const{drawLayer:a}=this.parent;let r;oc(this,_o)?(e=(e-this.rotation+360)%360,r=cc(s=t,Fo,Wo).call(s,oc(this,bo).box,e)):r=cc(i=t,Fo,Wo).call(i,[this.x,this.y,this.width,this.height],e),a.updateProperties(oc(this,Ao),{bbox:r,root:{"data-main-rotation":e}}),a.updateProperties(oc(this,So),{bbox:cc(n=t,Fo,Wo).call(n,oc(this,fo).box,e),root:{"data-main-rotation":e}})}render(){if(this.div)return this.div;const t=super.render();oc(this,ko)&&(t.setAttribute("aria-label",oc(this,ko)),t.setAttribute("role","mark")),oc(this,_o)?t.classList.add("free"):this.div.addEventListener("keydown",cc(this,Co,Oo).bind(this),{signal:this._uiManager._signal});const e=hc(this,wo,document.createElement("div"));t.append(e),e.setAttribute("aria-hidden","true"),e.className="internal",e.style.clipPath=oc(this,po);const[s,i]=this.parentDimensions;return this.setDims(this.width*s,this.height*i),qd(this,oc(this,wo),["pointerover","pointerleave"]),this.enableEditing(),t}pointerover(){var t;this.isSelected||null==(t=this.parent)||t.drawLayer.updateProperties(oc(this,So),{rootClass:{hovered:!0}})}pointerleave(){var t;this.isSelected||null==(t=this.parent)||t.drawLayer.updateProperties(oc(this,So),{rootClass:{hovered:!1}})}_moveCaret(t){switch(this.parent.unselect(this),t){case 0:case 2:cc(this,Co,Bo).call(this,!0);break;case 1:case 3:cc(this,Co,Bo).call(this,!1)}}select(){var t;super.select(),oc(this,So)&&(null==(t=this.parent)||t.drawLayer.updateProperties(oc(this,So),{rootClass:{hovered:!1,selected:!0}}))}unselect(){var t;super.unselect(),oc(this,So)&&(null==(t=this.parent)||t.drawLayer.updateProperties(oc(this,So),{rootClass:{selected:!1}}),oc(this,_o)||cc(this,Co,Bo).call(this,!1))}get _mustFixPosition(){return!oc(this,_o)}show(t=this._isVisible){super.show(t),this.parent&&(this.parent.drawLayer.updateProperties(oc(this,Ao),{rootClass:{hidden:!t}}),this.parent.drawLayer.updateProperties(oc(this,So),{rootClass:{hidden:!t}}))}static startHighlighting(t,e,{target:s,x:i,y:n}){const{x:a,y:r,width:o,height:l}=s.getBoundingClientRect(),h=new AbortController,c=t.combinedSignal(h),d=e=>{h.abort(),cc(this,Fo,jo).call(this,t,e)};window.addEventListener("blur",d,{signal:c}),window.addEventListener("pointerup",d,{signal:c}),window.addEventListener("pointerdown",Nd,{capture:!0,passive:!1,signal:c}),window.addEventListener("contextmenu",Dd,{signal:c}),s.addEventListener("pointermove",cc(this,Fo,Go).bind(this,t),{signal:c}),this._freeHighlight=new pf({x:i,y:n},[a,r,o,l],t.scale,this._defaultThickness/2,e,.001),({id:this._freeHighlightId,clipPathId:this._freeHighlightClipId}=t.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:this._defaultColor,"fill-opacity":this._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:this._freeHighlight.toSVGPath()}},!0,!0))}static async deserialize(t,e,s){var i,n,a,r;let o=null;if(t instanceof Kg){const{data:{quadPoints:e,rect:s,rotation:i,id:n,color:a,opacity:r,popupRef:l},parent:{page:{pageNumber:h}}}=t;o=t={annotationType:kc.HIGHLIGHT,color:Array.from(a),opacity:r,quadPoints:e,boxes:null,pageIndex:h-1,rect:s.slice(0),rotation:i,annotationElementId:n,id:n,deleted:!1,popupRef:l}}else if(t instanceof Yg){const{data:{inkLists:e,rect:s,rotation:i,id:n,color:a,borderStyle:{rawWidth:r},popupRef:l},parent:{page:{pageNumber:h}}}=t;o=t={annotationType:kc.HIGHLIGHT,color:Array.from(a),thickness:r,inkLists:e,boxes:null,pageIndex:h-1,rect:s.slice(0),rotation:i,annotationElementId:n,id:n,deleted:!1,popupRef:l}}const{color:l,quadPoints:h,inkLists:c,opacity:d}=t,u=await super.deserialize(t,e,s);u.color=gd.makeHexColor(...l),hc(u,xo,d||1),c&&hc(u,Mo,t.thickness),u._initialData=o;const[p,g]=u.pageDimensions,[f,m]=u.pageTranslation;if(h){const t=hc(u,uo,[]);for(let e=0;e<h.length;e+=8)t.push({x:(h[e]-f)/p,y:1-(h[e+1]-m)/g,width:(h[e+2]-h[e])/p,height:(h[e+1]-h[e+5])/g});cc(i=u,Co,To).call(i),cc(n=u,Co,No).call(n),u.rotate(u.rotation)}else if(c){hc(u,_o,!0);const t=c[0],s={x:t[0]-f,y:g-(t[1]-m)},i=new pf(s,[0,0,p,g],1,oc(u,Mo)/2,!0,.001);for(let e=0,a=t.length;e<a;e+=2)s.x=t[e]-f,s.y=g-(t[e+1]-m),i.add(s);const{id:n,clipPathId:o}=e.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:u.color,"fill-opacity":u._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:i.toSVGPath()}},!0,!0);cc(a=u,Co,Ro).call(a,{highlightOutlines:i.getOutlines(),highlightId:n,clipPathId:o}),cc(r=u,Co,No).call(r),u.rotate(u.parentRotation)}return u}serialize(t=!1){if(this.isEmpty()||t)return null;if(this.deleted)return this.serializeDeleted();const e=this.getRect(0,0),s=lu._colorManager.convert(this.color),i={annotationType:kc.HIGHLIGHT,color:s,opacity:oc(this,xo),thickness:oc(this,Mo),quadPoints:cc(this,Co,Ho).call(this),outlines:cc(this,Co,zo).call(this,e),pageIndex:this.pageIndex,rect:e,rotation:cc(this,Co,$o).call(this),structTreeParentId:this._structTreeParentId};return this.annotationElementId&&!cc(this,Co,Uo).call(this,i)?null:(i.id=this.annotationElementId,i)}renderAnnotationElement(t){return t.updateEdited({rect:this.getRect(0,0)}),null}static canCreateNewEmptyEditor(){return!1}};ho=new WeakMap,co=new WeakMap,uo=new WeakMap,po=new WeakMap,go=new WeakMap,fo=new WeakMap,mo=new WeakMap,vo=new WeakMap,wo=new WeakMap,bo=new WeakMap,Ao=new WeakMap,_o=new WeakMap,yo=new WeakMap,xo=new WeakMap,So=new WeakMap,ko=new WeakMap,Mo=new WeakMap,Eo=new WeakMap,Co=new WeakSet,To=function(){const t=new df(oc(this,uo),.001);hc(this,bo,t.getOutlines()),[this.x,this.y,this.width,this.height]=oc(this,bo).box;const e=new df(oc(this,uo),.0025,.001,"ltr"===this._uiManager.direction);hc(this,fo,e.getOutlines());const{lastPoint:s}=oc(this,fo);hc(this,yo,[(s[0]-this.x)/this.width,(s[1]-this.y)/this.height])},Ro=function({highlightOutlines:t,highlightId:e,clipPathId:s}){var i,n;hc(this,bo,t);if(hc(this,fo,t.getNewOutline(oc(this,Mo)/2+1.5,.0025)),e>=0)hc(this,Ao,e),hc(this,po,s),this.parent.drawLayer.finalizeDraw(e,{bbox:t.box,path:{d:t.toSVGPath()}}),hc(this,So,this.parent.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:!0},bbox:oc(this,fo).box,path:{d:oc(this,fo).toSVGPath()}},!0));else if(this.parent){const e=this.parent.viewport.rotation;this.parent.drawLayer.updateProperties(oc(this,Ao),{bbox:cc(i=vf,Fo,Wo).call(i,oc(this,bo).box,(e-this.rotation+360)%360),path:{d:t.toSVGPath()}}),this.parent.drawLayer.updateProperties(oc(this,So),{bbox:cc(n=vf,Fo,Wo).call(n,oc(this,fo).box,e),path:{d:oc(this,fo).toSVGPath()}})}const[a,r,o,l]=t.box;switch(this.rotation){case 0:this.x=a,this.y=r,this.width=o,this.height=l;break;case 90:{const[t,e]=this.parentDimensions;this.x=r,this.y=1-a,this.width=o*e/t,this.height=l*t/e;break}case 180:this.x=1-a,this.y=1-r,this.width=o,this.height=l;break;case 270:{const[t,e]=this.parentDimensions;this.x=1-r,this.y=a,this.width=o*e/t,this.height=l*t/e;break}}const{lastPoint:h}=oc(this,fo);hc(this,yo,[(h[0]-a)/o,(h[1]-r)/l])},Po=function(t){const e=(t,e)=>{var s,i;this.color=t,hc(this,xo,e),null==(s=this.parent)||s.drawLayer.updateProperties(oc(this,Ao),{root:{fill:t,"fill-opacity":e}}),null==(i=oc(this,go))||i.updateColor(t)},s=this.color,i=oc(this,xo);this.addCommands({cmd:e.bind(this,t,vf._defaultOpacity),undo:e.bind(this,s,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:Mc.HIGHLIGHT_COLOR,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"color_changed",color:this._uiManager.highlightColorNames.get(t)},!0)},Io=function(t){const e=oc(this,Mo),s=t=>{hc(this,Mo,t),cc(this,Co,Lo).call(this,t)};this.addCommands({cmd:s.bind(this,t),undo:s.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:Mc.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"thickness_changed",thickness:t},!0)},Lo=function(t){if(!oc(this,_o))return;cc(this,Co,Ro).call(this,{highlightOutlines:oc(this,bo).getNewOutline(t/2)}),this.fixAndSetPosition();const[e,s]=this.parentDimensions;this.setDims(this.width*e,this.height*s)},Do=function(){null!==oc(this,Ao)&&this.parent&&(this.parent.drawLayer.remove(oc(this,Ao)),hc(this,Ao,null),this.parent.drawLayer.remove(oc(this,So)),hc(this,So,null))},No=function(t=this.parent){null===oc(this,Ao)&&(({id:dc(this,Ao)._,clipPathId:dc(this,po)._}=t.drawLayer.draw({bbox:oc(this,bo).box,root:{viewBox:"0 0 1 1",fill:this.color,"fill-opacity":oc(this,xo)},rootClass:{highlight:!0,free:oc(this,_o)},path:{d:oc(this,bo).toSVGPath()}},!1,!0)),hc(this,So,t.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:oc(this,_o)},bbox:oc(this,fo).box,path:{d:oc(this,fo).toSVGPath()}},oc(this,_o))),oc(this,wo)&&(oc(this,wo).style.clipPath=oc(this,po)))},Fo=new WeakSet,Wo=function([t,e,s,i],n){switch(n){case 90:return[1-e-i,t,i,s];case 180:return[1-t-s,1-e-i,s,i];case 270:return[e,1-t-s,i,s]}return[t,e,s,i]},Oo=function(t){vf._keyboardManager.exec(this,t)},Bo=function(t){if(!oc(this,ho))return;const e=window.getSelection();t?e.setPosition(oc(this,ho),oc(this,co)):e.setPosition(oc(this,mo),oc(this,vo))},$o=function(){return oc(this,_o)?this.rotation:0},Ho=function(){if(oc(this,_o))return null;const[t,e]=this.pageDimensions,[s,i]=this.pageTranslation,n=oc(this,uo),a=new Float32Array(8*n.length);let r=0;for(const{x:o,y:l,width:h,height:c}of n){const n=o*t+s,d=(1-l)*e+i;a[r]=a[r+4]=n,a[r+1]=a[r+3]=d,a[r+2]=a[r+6]=n+h*t,a[r+5]=a[r+7]=d-c*e,r+=8}return a},zo=function(t){return oc(this,bo).serialize(t,cc(this,Co,$o).call(this))},Go=function(t,e){this._freeHighlight.add(e)&&t.drawLayer.updateProperties(this._freeHighlightId,{path:{d:this._freeHighlight.toSVGPath()}})},jo=function(t,e){this._freeHighlight.isEmpty()?t.drawLayer.remove(this._freeHighlightId):t.createAndAddNewEditor(e,!1,{highlightId:this._freeHighlightId,highlightOutlines:this._freeHighlight.getOutlines(),clipPathId:this._freeHighlightClipId,methodOfCreation:"main_toolbar"}),this._freeHighlightId=-1,this._freeHighlight=null,this._freeHighlightClipId=""},Uo=function(t){const{color:e}=this._initialData;return t.color.some((t,s)=>t!==e[s])},lc(vf,Fo),ac(vf,"_defaultColor",null),ac(vf,"_defaultOpacity",1),ac(vf,"_defaultThickness",12),ac(vf,"_type","highlight"),ac(vf,"_editorType",kc.HIGHLIGHT),ac(vf,"_freeHighlightId",-1),ac(vf,"_freeHighlight",null),ac(vf,"_freeHighlightClipId","");let wf=vf;class bf{constructor(){lc(this,Vo,Object.create(null))}updateProperty(t,e){this[t]=e,this.updateSVGProperty(t,e)}updateProperties(t){if(t)for(const[e,s]of Object.entries(t))e.startsWith("_")||this.updateProperty(e,s)}updateSVGProperty(t,e){oc(this,Vo)[t]=e}toSVGProperties(){const t=oc(this,Vo);return hc(this,Vo,Object.create(null)),{root:t}}reset(){hc(this,Vo,Object.create(null))}updateAll(t=this){this.updateProperties(t)}clone(){Jc("Not implemented")}}Vo=new WeakMap;const Af=class t extends lu{constructor(t){super(t),lc(this,sl),lc(this,qo,null),lc(this,Xo),ac(this,"_drawId",null),hc(this,Xo,t.mustBeCommitted||!1),this._addOutlines(t)}_addOutlines(t){t.drawOutlines&&(cc(this,sl,il).call(this,t),cc(this,sl,rl).call(this))}static _mergeSVGProperties(t,e){const s=new Set(Object.keys(t));for(const[i,n]of Object.entries(e))s.has(i)?Object.assign(t[i],n):t[i]=n;return t}static getDefaultDrawingOptions(t){Jc("Not implemented")}static get typesMap(){Jc("Not implemented")}static get isDrawer(){return!0}static get supportMultipleDrawings(){return!1}static updateDefaultParams(e,s){const i=this.typesMap.get(e);i&&this._defaultDrawingOptions.updateProperty(i,s),this._currentParent&&(oc(t,Yo).updateProperty(i,s),this._currentParent.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties()))}updateParams(t,e){const s=this.constructor.typesMap.get(t);s&&this._updateProperty(t,s,e)}static get defaultPropertiesToUpdate(){const t=[],e=this._defaultDrawingOptions;for(const[s,i]of this.typesMap)t.push([s,e[i]]);return t}get propertiesToUpdate(){const t=[],{_drawingOptions:e}=this;for(const[s,i]of this.constructor.typesMap)t.push([s,e[i]]);return t}_updateProperty(t,e,s){const i=this._drawingOptions,n=i[e],a=t=>{var s;i.updateProperty(e,t);const n=oc(this,qo).updateProperty(e,t);n&&cc(this,sl,hl).call(this,n),null==(s=this.parent)||s.drawLayer.updateProperties(this._drawId,i.toSVGProperties())};this.addCommands({cmd:a.bind(this,s),undo:a.bind(this,n),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:t,overwriteIfSameType:!0,keepUndo:!0})}_onResizing(){var e;null==(e=this.parent)||e.drawLayer.updateProperties(this._drawId,t._mergeSVGProperties(oc(this,qo).getPathResizingSVGProperties(cc(this,sl,ll).call(this)),{bbox:cc(this,sl,cl).call(this)}))}_onResized(){var e;null==(e=this.parent)||e.drawLayer.updateProperties(this._drawId,t._mergeSVGProperties(oc(this,qo).getPathResizedSVGProperties(cc(this,sl,ll).call(this)),{bbox:cc(this,sl,cl).call(this)}))}_onTranslating(t,e){var s;null==(s=this.parent)||s.drawLayer.updateProperties(this._drawId,{bbox:cc(this,sl,cl).call(this)})}_onTranslated(){var e;null==(e=this.parent)||e.drawLayer.updateProperties(this._drawId,t._mergeSVGProperties(oc(this,qo).getPathTranslatedSVGProperties(cc(this,sl,ll).call(this),this.parentDimensions),{bbox:cc(this,sl,cl).call(this)}))}_onStartDragging(){var t;null==(t=this.parent)||t.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!0}})}_onStopDragging(){var t;null==(t=this.parent)||t.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!1}})}commit(){super.commit(),this.disableEditMode(),this.disableEditing()}disableEditing(){super.disableEditing(),this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing(),this.div.classList.toggle("disabled",!1)}getBaseTranslation(){return[0,0]}get isResizable(){return!0}onceAdded(t){this.annotationElementId||this.parent.addUndoableEditor(this),this._isDraggable=!0,oc(this,Xo)&&(hc(this,Xo,!1),this.commit(),this.parent.setSelected(this),t&&this.isOnScreen&&this.div.focus())}remove(){cc(this,sl,al).call(this),super.remove()}rebuild(){this.parent&&(super.rebuild(),null!==this.div&&(cc(this,sl,rl).call(this),cc(this,sl,hl).call(this,oc(this,qo).box),this.isAttachedToDOM||this.parent.add(this)))}setParent(t){var e;let s=!1;this.parent&&!t?(this._uiManager.removeShouldRescale(this),cc(this,sl,al).call(this)):t&&(this._uiManager.addShouldRescale(this),cc(this,sl,rl).call(this,t),s=!this.parent&&(null==(e=this.div)?void 0:e.classList.contains("selectedEditor"))),super.setParent(t),s&&this.select()}rotate(){this.parent&&this.parent.drawLayer.updateProperties(this._drawId,t._mergeSVGProperties({bbox:cc(this,sl,cl).call(this)},oc(this,qo).updateRotation((this.parentRotation-this.rotation+360)%360)))}onScaleChanging(){this.parent&&cc(this,sl,hl).call(this,oc(this,qo).updateParentDimensions(this.parentDimensions,this.parent.scale))}static onScaleChangingWhenDrawing(){}render(){if(this.div)return this.div;let t,e;this._isCopy&&(t=this.x,e=this.y);const s=super.render();s.classList.add("draw");const i=document.createElement("div");s.append(i),i.setAttribute("aria-hidden","true"),i.className="internal";const[n,a]=this.parentDimensions;return this.setDims(this.width*n,this.height*a),this._uiManager.addShouldRescale(this),this.disableEditing(),this._isCopy&&this._moveAfterPaste(t,e),s}static createDrawerInstance(t,e,s,i,n){Jc("Not implemented")}static startDrawing(e,s,i,n){var a;const{target:r,offsetX:o,offsetY:l,pointerId:h,pointerType:c}=n;if(oc(t,Zo)&&oc(t,Zo)!==c)return;const{viewport:{rotation:d}}=e,{width:u,height:p}=r.getBoundingClientRect(),g=hc(t,Ko,new AbortController),f=e.combinedSignal(g);oc(t,Jo)||hc(t,Jo,h),oc(t,Zo)??hc(t,Zo,c),window.addEventListener("pointerup",e=>{var s;oc(t,Jo)===e.pointerId?this._endDraw(e):null==(s=oc(t,tl))||s.delete(e.pointerId)},{signal:f}),window.addEventListener("pointercancel",e=>{var s;oc(t,Jo)===e.pointerId?this._currentParent.endDrawingSession():null==(s=oc(t,tl))||s.delete(e.pointerId)},{signal:f}),window.addEventListener("pointerdown",e=>{oc(t,Zo)===e.pointerType&&((oc(t,tl)||hc(t,tl,new Set)).add(e.pointerId),oc(t,Yo).isCancellable()&&(oc(t,Yo).removeLastElement(),oc(t,Yo).isEmpty()?this._currentParent.endDrawingSession(!0):this._endDraw(null)))},{capture:!0,passive:!1,signal:f}),window.addEventListener("contextmenu",Dd,{signal:f}),r.addEventListener("pointermove",this._drawMove.bind(this),{signal:f}),r.addEventListener("touchmove",e=>{e.timeStamp===oc(t,el)&&Nd(e)},{signal:f}),e.toggleDrawing(),null==(a=s._editorUndoBar)||a.hide(),oc(t,Yo)?e.drawLayer.updateProperties(this._currentDrawId,oc(t,Yo).startNew(o,l,u,p,d)):(s.updateUIForDefaultProperties(this),hc(t,Yo,this.createDrawerInstance(o,l,u,p,d)),hc(t,Qo,this.getDefaultDrawingOptions()),this._currentParent=e,({id:this._currentDrawId}=e.drawLayer.draw(this._mergeSVGProperties(oc(t,Qo).toSVGProperties(),oc(t,Yo).defaultSVGProperties),!0,!1)))}static _drawMove(e){var s;if(hc(t,el,-1),!oc(t,Yo))return;const{offsetX:i,offsetY:n,pointerId:a}=e;oc(t,Jo)===a&&((null==(s=oc(t,tl))?void 0:s.size)>=1?this._endDraw(e):(this._currentParent.drawLayer.updateProperties(this._currentDrawId,oc(t,Yo).add(i,n)),hc(t,el,e.timeStamp),Nd(e)))}static _cleanup(e){e&&(this._currentDrawId=-1,this._currentParent=null,hc(t,Yo,null),hc(t,Qo,null),hc(t,Zo,null),hc(t,el,NaN)),oc(t,Ko)&&(oc(t,Ko).abort(),hc(t,Ko,null),hc(t,Jo,NaN),hc(t,tl,null))}static _endDraw(e){const s=this._currentParent;if(s){if(s.toggleDrawing(!0),this._cleanup(!1),(null==e?void 0:e.target)===s.div&&s.drawLayer.updateProperties(this._currentDrawId,oc(t,Yo).end(e.offsetX,e.offsetY)),this.supportMultipleDrawings){const e=oc(t,Yo),i=this._currentDrawId,n=e.getLastElement();return void s.addCommands({cmd:()=>{s.drawLayer.updateProperties(i,e.setLastElement(n))},undo:()=>{s.drawLayer.updateProperties(i,e.removeLastElement())},mustExec:!1,type:Mc.DRAW_STEP})}this.endDrawing(!1)}}static endDrawing(e){const s=this._currentParent;if(!s)return null;if(s.toggleDrawing(!0),s.cleanUndoStack(Mc.DRAW_STEP),!oc(t,Yo).isEmpty()){const{pageDimensions:[i,n],scale:a}=s,r=s.createAndAddNewEditor({offsetX:0,offsetY:0},!1,{drawId:this._currentDrawId,drawOutlines:oc(t,Yo).getOutlines(i*a,n*a,a,this._INNER_MARGIN),drawingOptions:oc(t,Qo),mustBeCommitted:!e});return this._cleanup(!0),r}return s.drawLayer.remove(this._currentDrawId),this._cleanup(!0),null}createDrawingOptions(t){}static deserializeDraw(t,e,s,i,n,a){Jc("Not implemented")}static async deserialize(t,e,s){var i,n;const{rawDims:{pageWidth:a,pageHeight:r,pageX:o,pageY:l}}=e.viewport,h=this.deserializeDraw(o,l,a,r,this._INNER_MARGIN,t),c=await super.deserialize(t,e,s);return c.createDrawingOptions(t),cc(i=c,sl,il).call(i,{drawOutlines:h}),cc(n=c,sl,rl).call(n),c.onScaleChanging(),c.rotate(),c}serializeDraw(t){const[e,s]=this.pageTranslation,[i,n]=this.pageDimensions;return oc(this,qo).serialize([e,s,i,n],t)}renderAnnotationElement(t){return t.updateEdited({rect:this.getRect(0,0)}),null}static canCreateNewEmptyEditor(){return!1}};qo=new WeakMap,Xo=new WeakMap,Yo=new WeakMap,Ko=new WeakMap,Qo=new WeakMap,Jo=new WeakMap,Zo=new WeakMap,tl=new WeakMap,el=new WeakMap,sl=new WeakSet,il=function({drawOutlines:t,drawId:e,drawingOptions:s}){hc(this,qo,t),this._drawingOptions||(this._drawingOptions=s),this.annotationElementId||this._uiManager.a11yAlert(`pdfjs-editor-${this.editorType}-added-alert`),e>=0?(this._drawId=e,this.parent.drawLayer.finalizeDraw(e,t.defaultProperties)):this._drawId=cc(this,sl,nl).call(this,t,this.parent),cc(this,sl,hl).call(this,t.box)},nl=function(t,e){const{id:s}=e.drawLayer.draw(Af._mergeSVGProperties(this._drawingOptions.toSVGProperties(),t.defaultSVGProperties),!1,!1);return s},al=function(){null!==this._drawId&&this.parent&&(this.parent.drawLayer.remove(this._drawId),this._drawId=null,this._drawingOptions.reset())},rl=function(t=this.parent){null!==this._drawId&&this.parent===t||(null===this._drawId?(this._drawingOptions.updateAll(),this._drawId=cc(this,sl,nl).call(this,oc(this,qo),t)):this.parent.drawLayer.updateParent(this._drawId,t.drawLayer))},ol=function([t,e,s,i]){const{parentDimensions:[n,a],rotation:r}=this;switch(r){case 90:return[e,1-t,s*(a/n),i*(n/a)];case 180:return[1-t,1-e,s,i];case 270:return[1-e,t,s*(a/n),i*(n/a)];default:return[t,e,s,i]}},ll=function(){const{x:t,y:e,width:s,height:i,parentDimensions:[n,a],rotation:r}=this;switch(r){case 90:return[1-e,t,s*(n/a),i*(a/n)];case 180:return[1-t,1-e,s,i];case 270:return[e,1-t,s*(n/a),i*(a/n)];default:return[t,e,s,i]}},hl=function(t){if([this.x,this.y,this.width,this.height]=cc(this,sl,ol).call(this,t),this.div){this.fixAndSetPosition();const[t,e]=this.parentDimensions;this.setDims(this.width*t,this.height*e)}this._onResized()},cl=function(){const{x:t,y:e,width:s,height:i,rotation:n,parentRotation:a,parentDimensions:[r,o]}=this;switch((4*n+a)/90){case 1:return[1-e-i,t,i,s];case 2:return[1-t-s,1-e-i,s,i];case 3:return[e,1-t-s,i,s];case 4:return[t,e-s*(r/o),i*(o/r),s*(r/o)];case 5:return[1-e,t,s*(r/o),i*(o/r)];case 6:return[1-t-i*(o/r),1-e,i*(o/r),s*(r/o)];case 7:return[e-s*(r/o),1-t-i*(o/r),s*(r/o),i*(o/r)];case 8:return[t-s,e-i,s,i];case 9:return[1-e,t-s,i,s];case 10:return[1-t,1-e,s,i];case 11:return[e-i,1-t,i,s];case 12:return[t-i*(o/r),e,i*(o/r),s*(r/o)];case 13:return[1-e-s*(r/o),t-i*(o/r),s*(r/o),i*(o/r)];case 14:return[1-t,1-e-s*(r/o),i*(o/r),s*(r/o)];case 15:return[e,1-t,s*(r/o),i*(o/r)];default:return[t,e,s,i]}},ac(Af,"_currentDrawId",-1),ac(Af,"_currentParent",null),lc(Af,Yo,null),lc(Af,Ko,null),lc(Af,Qo,null),lc(Af,Jo,NaN),lc(Af,Zo,null),lc(Af,tl,null),lc(Af,el,NaN),ac(Af,"_INNER_MARGIN",3);let _f=Af;class yf{constructor(t,e,s,i,n,a){lc(this,yl),lc(this,dl,new Float64Array(6)),lc(this,ul),lc(this,pl),lc(this,gl),lc(this,fl),lc(this,ml),lc(this,vl,""),lc(this,wl,0),lc(this,bl,new xf),lc(this,Al),lc(this,_l),hc(this,Al,s),hc(this,_l,i),hc(this,gl,n),hc(this,fl,a),[t,e]=cc(this,yl,xl).call(this,t,e);const r=hc(this,ul,[NaN,NaN,NaN,NaN,t,e]);hc(this,ml,[t,e]),hc(this,pl,[{line:r,points:oc(this,ml)}]),oc(this,dl).set(r,0)}updateProperty(t,e){"stroke-width"===t&&hc(this,fl,e)}isEmpty(){return!oc(this,pl)||0===oc(this,pl).length}isCancellable(){return oc(this,ml).length<=10}add(t,e){[t,e]=cc(this,yl,xl).call(this,t,e);const[s,i,n,a]=oc(this,dl).subarray(2,6),r=t-n,o=e-a;return Math.hypot(oc(this,Al)*r,oc(this,_l)*o)<=2?null:(oc(this,ml).push(t,e),isNaN(s)?(oc(this,dl).set([n,a,t,e],2),oc(this,ul).push(NaN,NaN,NaN,NaN,t,e),{path:{d:this.toSVGPath()}}):(isNaN(oc(this,dl)[0])&&oc(this,ul).splice(6,6),oc(this,dl).set([s,i,n,a,t,e],0),oc(this,ul).push(...of.createBezierPoints(s,i,n,a,t,e)),{path:{d:this.toSVGPath()}}))}end(t,e){const s=this.add(t,e);return s||(2===oc(this,ml).length?{path:{d:this.toSVGPath()}}:null)}startNew(t,e,s,i,n){hc(this,Al,s),hc(this,_l,i),hc(this,gl,n),[t,e]=cc(this,yl,xl).call(this,t,e);const a=hc(this,ul,[NaN,NaN,NaN,NaN,t,e]);hc(this,ml,[t,e]);const r=oc(this,pl).at(-1);return r&&(r.line=new Float32Array(r.line),r.points=new Float32Array(r.points)),oc(this,pl).push({line:a,points:oc(this,ml)}),oc(this,dl).set(a,0),hc(this,wl,0),this.toSVGPath(),null}getLastElement(){return oc(this,pl).at(-1)}setLastElement(t){return oc(this,pl)?(oc(this,pl).push(t),hc(this,ul,t.line),hc(this,ml,t.points),hc(this,wl,0),{path:{d:this.toSVGPath()}}):oc(this,bl).setLastElement(t)}removeLastElement(){if(!oc(this,pl))return oc(this,bl).removeLastElement();oc(this,pl).pop(),hc(this,vl,"");for(let t=0,e=oc(this,pl).length;t<e;t++){const{line:e,points:s}=oc(this,pl)[t];hc(this,ul,e),hc(this,ml,s),hc(this,wl,0),this.toSVGPath()}return{path:{d:oc(this,vl)}}}toSVGPath(){const t=of.svgRound(oc(this,ul)[4]),e=of.svgRound(oc(this,ul)[5]);if(2===oc(this,ml).length)return hc(this,vl,`${oc(this,vl)} M ${t} ${e} Z`),oc(this,vl);if(oc(this,ml).length<=6){const s=oc(this,vl).lastIndexOf("M");hc(this,vl,`${oc(this,vl).slice(0,s)} M ${t} ${e}`),hc(this,wl,6)}if(4===oc(this,ml).length){const t=of.svgRound(oc(this,ul)[10]),e=of.svgRound(oc(this,ul)[11]);return hc(this,vl,`${oc(this,vl)} L ${t} ${e}`),hc(this,wl,12),oc(this,vl)}const s=[];0===oc(this,wl)&&(s.push(`M ${t} ${e}`),hc(this,wl,6));for(let i=oc(this,wl),n=oc(this,ul).length;i<n;i+=6){const[t,e,n,a,r,o]=oc(this,ul).slice(i,i+6).map(of.svgRound);s.push(`C${t} ${e} ${n} ${a} ${r} ${o}`)}return hc(this,vl,oc(this,vl)+s.join(" ")),hc(this,wl,oc(this,ul).length),oc(this,vl)}getOutlines(t,e,s,i){const n=oc(this,pl).at(-1);return n.line=new Float32Array(n.line),n.points=new Float32Array(n.points),oc(this,bl).build(oc(this,pl),t,e,s,oc(this,gl),oc(this,fl),i),hc(this,dl,null),hc(this,ul,null),hc(this,pl,null),hc(this,vl,null),oc(this,bl)}get defaultSVGProperties(){return{root:{viewBox:"0 0 10000 10000"},rootClass:{draw:!0},bbox:[0,0,1,1]}}}dl=new WeakMap,ul=new WeakMap,pl=new WeakMap,gl=new WeakMap,fl=new WeakMap,ml=new WeakMap,vl=new WeakMap,wl=new WeakMap,bl=new WeakMap,Al=new WeakMap,_l=new WeakMap,yl=new WeakSet,xl=function(t,e){return of._normalizePoint(t,e,oc(this,Al),oc(this,_l),oc(this,gl))};class xf extends of{constructor(){super(...arguments),lc(this,Ll),lc(this,Sl),lc(this,kl,0),lc(this,Ml),lc(this,El),lc(this,Cl),lc(this,Tl),lc(this,Rl),lc(this,Pl),lc(this,Il)}build(t,e,s,i,n,a,r){hc(this,Cl,e),hc(this,Tl,s),hc(this,Rl,i),hc(this,Pl,n),hc(this,Il,a),hc(this,Ml,r??0),hc(this,El,t),cc(this,Ll,Fl).call(this)}get thickness(){return oc(this,Il)}setLastElement(t){return oc(this,El).push(t),{path:{d:this.toSVGPath()}}}removeLastElement(){return oc(this,El).pop(),{path:{d:this.toSVGPath()}}}toSVGPath(){const t=[];for(const{line:e}of oc(this,El))if(t.push(`M${of.svgRound(e[4])} ${of.svgRound(e[5])}`),6!==e.length)if(12===e.length&&isNaN(e[6]))t.push(`L${of.svgRound(e[10])} ${of.svgRound(e[11])}`);else for(let s=6,i=e.length;s<i;s+=6){const[i,n,a,r,o,l]=e.subarray(s,s+6).map(of.svgRound);t.push(`C${i} ${n} ${a} ${r} ${o} ${l}`)}else t.push("Z");return t.join("")}serialize([t,e,s,i],n){const a=[],r=[],[o,l,h,c]=cc(this,Ll,Nl).call(this);let d,u,p,g,f,m,v,w,b;switch(oc(this,Pl)){case 0:b=of._rescale,d=t,u=e+i,p=s,g=-i,f=t+o*s,m=e+(1-l-c)*i,v=t+(o+h)*s,w=e+(1-l)*i;break;case 90:b=of._rescaleAndSwap,d=t,u=e,p=s,g=i,f=t+l*s,m=e+o*i,v=t+(l+c)*s,w=e+(o+h)*i;break;case 180:b=of._rescale,d=t+s,u=e,p=-s,g=i,f=t+(1-o-h)*s,m=e+l*i,v=t+(1-o)*s,w=e+(l+c)*i;break;case 270:b=of._rescaleAndSwap,d=t+s,u=e+i,p=-s,g=-i,f=t+(1-l-c)*s,m=e+(1-o-h)*i,v=t+(1-l)*s,w=e+(1-o)*i}for(const{line:A,points:_}of oc(this,El))a.push(b(A,d,u,p,g,n?new Array(A.length):null)),r.push(b(_,d,u,p,g,n?new Array(_.length):null));return{lines:a,points:r,rect:[f,m,v,w]}}static deserialize(t,e,s,i,n,{paths:{lines:a,points:r},rotation:o,thickness:l}){const h=[];let c,d,u,p,g;switch(o){case 0:g=of._rescale,c=-t/s,d=e/i+1,u=1/s,p=-1/i;break;case 90:g=of._rescaleAndSwap,c=-e/i,d=-t/s,u=1/i,p=1/s;break;case 180:g=of._rescale,c=t/s+1,d=-e/i,u=-1/s,p=1/i;break;case 270:g=of._rescaleAndSwap,c=e/i+1,d=t/s+1,u=-1/i,p=-1/s}if(!a){a=[];for(const t of r){const e=t.length;if(2===e){a.push(new Float32Array([NaN,NaN,NaN,NaN,t[0],t[1]]));continue}if(4===e){a.push(new Float32Array([NaN,NaN,NaN,NaN,t[0],t[1],NaN,NaN,NaN,NaN,t[2],t[3]]));continue}const s=new Float32Array(3*(e-2));a.push(s);let[i,n,r,o]=t.subarray(0,4);s.set([NaN,NaN,NaN,NaN,i,n],0);for(let a=4;a<e;a+=2){const e=t[a],l=t[a+1];s.set(of.createBezierPoints(i,n,r,o,e,l),3*(a-2)),[i,n,r,o]=[r,o,e,l]}}}for(let m=0,v=a.length;m<v;m++)h.push({line:g(a[m].map(t=>t??NaN),c,d,u,p),points:g(r[m].map(t=>t??NaN),c,d,u,p)});const f=new this.prototype.constructor;return f.build(h,s,i,1,o,l,n),f}get box(){return oc(this,Sl)}updateProperty(t,e){return"stroke-width"===t?cc(this,Ll,Wl).call(this,e):null}updateParentDimensions([t,e],s){const[i,n]=cc(this,Ll,Dl).call(this);hc(this,Cl,t),hc(this,Tl,e),hc(this,Rl,s);const[a,r]=cc(this,Ll,Dl).call(this),o=a-i,l=r-n,h=oc(this,Sl);return h[0]-=o,h[1]-=l,h[2]+=2*o,h[3]+=2*l,h}updateRotation(t){return hc(this,kl,t),{path:{transform:this.rotationTransform}}}get viewBox(){return oc(this,Sl).map(of.svgRound).join(" ")}get defaultProperties(){const[t,e]=oc(this,Sl);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${of.svgRound(t)} ${of.svgRound(e)}`}}}get rotationTransform(){const[,,t,e]=oc(this,Sl);let s=0,i=0,n=0,a=0,r=0,o=0;switch(oc(this,kl)){case 90:i=e/t,n=-t/e,r=t;break;case 180:s=-1,a=-1,r=t,o=e;break;case 270:i=-e/t,n=t/e,o=e;break;default:return""}return`matrix(${s} ${i} ${n} ${a} ${of.svgRound(r)} ${of.svgRound(o)})`}getPathResizingSVGProperties([t,e,s,i]){const[n,a]=cc(this,Ll,Dl).call(this),[r,o,l,h]=oc(this,Sl);if(Math.abs(l-n)<=of.PRECISION||Math.abs(h-a)<=of.PRECISION){const n=t+s/2-(r+l/2),a=e+i/2-(o+h/2);return{path:{"transform-origin":`${of.svgRound(t)} ${of.svgRound(e)}`,transform:`${this.rotationTransform} translate(${n} ${a})`}}}const c=(s-2*n)/(l-2*n),d=(i-2*a)/(h-2*a),u=l/s,p=h/i;return{path:{"transform-origin":`${of.svgRound(r)} ${of.svgRound(o)}`,transform:`${this.rotationTransform} scale(${u} ${p}) translate(${of.svgRound(n)} ${of.svgRound(a)}) scale(${c} ${d}) translate(${of.svgRound(-n)} ${of.svgRound(-a)})`}}}getPathResizedSVGProperties([t,e,s,i]){const[n,a]=cc(this,Ll,Dl).call(this),r=oc(this,Sl),[o,l,h,c]=r;if(r[0]=t,r[1]=e,r[2]=s,r[3]=i,Math.abs(h-n)<=of.PRECISION||Math.abs(c-a)<=of.PRECISION){const n=t+s/2-(o+h/2),a=e+i/2-(l+c/2);for(const{line:t,points:e}of oc(this,El))of._translate(t,n,a,t),of._translate(e,n,a,e);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${of.svgRound(t)} ${of.svgRound(e)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}const d=(s-2*n)/(h-2*n),u=(i-2*a)/(c-2*a),p=-d*(o+n)+t+n,g=-u*(l+a)+e+a;if(1!==d||1!==u||0!==p||0!==g)for(const{line:f,points:m}of oc(this,El))of._rescale(f,p,g,d,u,f),of._rescale(m,p,g,d,u,m);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${of.svgRound(t)} ${of.svgRound(e)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}getPathTranslatedSVGProperties([t,e],s){const[i,n]=s,a=oc(this,Sl),r=t-a[0],o=e-a[1];if(oc(this,Cl)===i&&oc(this,Tl)===n)for(const{line:l,points:h}of oc(this,El))of._translate(l,r,o,l),of._translate(h,r,o,h);else{const t=oc(this,Cl)/i,e=oc(this,Tl)/n;hc(this,Cl,i),hc(this,Tl,n);for(const{line:s,points:i}of oc(this,El))of._rescale(s,r,o,t,e,s),of._rescale(i,r,o,t,e,i);a[2]*=t,a[3]*=e}return a[0]=t,a[1]=e,{root:{viewBox:this.viewBox},path:{d:this.toSVGPath(),"transform-origin":`${of.svgRound(t)} ${of.svgRound(e)}`}}}get defaultSVGProperties(){const t=oc(this,Sl);return{root:{viewBox:this.viewBox},rootClass:{draw:!0},path:{d:this.toSVGPath(),"transform-origin":`${of.svgRound(t[0])} ${of.svgRound(t[1])}`,transform:this.rotationTransform||null},bbox:t}}}Sl=new WeakMap,kl=new WeakMap,Ml=new WeakMap,El=new WeakMap,Cl=new WeakMap,Tl=new WeakMap,Rl=new WeakMap,Pl=new WeakMap,Il=new WeakMap,Ll=new WeakSet,Dl=function(t=oc(this,Il)){const e=oc(this,Ml)+t/2*oc(this,Rl);return oc(this,Pl)%180==0?[e/oc(this,Cl),e/oc(this,Tl)]:[e/oc(this,Tl),e/oc(this,Cl)]},Nl=function(){const[t,e,s,i]=oc(this,Sl),[n,a]=cc(this,Ll,Dl).call(this,0);return[t+n,e+a,s-2*n,i-2*a]},Fl=function(){const t=hc(this,Sl,new Float32Array([1/0,1/0,-1/0,-1/0]));for(const{line:i}of oc(this,El)){if(i.length<=12){for(let e=4,s=i.length;e<s;e+=6)gd.pointBoundingBox(i[e],i[e+1],t);continue}let e=i[4],s=i[5];for(let n=6,a=i.length;n<a;n+=6){const[a,r,o,l,h,c]=i.subarray(n,n+6);gd.bezierBoundingBox(e,s,a,r,o,l,h,c,t),e=h,s=c}}const[e,s]=cc(this,Ll,Dl).call(this);t[0]=Ad(t[0]-e,0,1),t[1]=Ad(t[1]-s,0,1),t[2]=Ad(t[2]+e,0,1),t[3]=Ad(t[3]+s,0,1),t[2]-=t[0],t[3]-=t[1]},Wl=function(t){const[e,s]=cc(this,Ll,Dl).call(this);hc(this,Il,t);const[i,n]=cc(this,Ll,Dl).call(this),[a,r]=[i-e,n-s],o=oc(this,Sl);return o[0]-=a,o[1]-=r,o[2]+=2*a,o[3]+=2*r,o};class Sf extends bf{constructor(t){super(),this._viewParameters=t,super.updateProperties({fill:"none",stroke:lu._defaultLineColor,"stroke-opacity":1,"stroke-width":1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":10})}updateSVGProperty(t,e){"stroke-width"===t&&(e??(e=this["stroke-width"]),e*=this._viewParameters.realScale),super.updateSVGProperty(t,e)}clone(){const t=new Sf(this._viewParameters);return t.updateAll(this),t}}const kf=class t extends _f{constructor(t){super({...t,name:"inkEditor"}),lc(this,Ol),this._willKeepAspectRatio=!0,this.defaultL10nId="pdfjs-editor-ink-editor"}static initialize(t,e){lu.initialize(t,e),this._defaultDrawingOptions=new Sf(e.viewParameters)}static getDefaultDrawingOptions(t){const e=this._defaultDrawingOptions.clone();return e.updateProperties(t),e}static get supportMultipleDrawings(){return!0}static get typesMap(){return sd(this,"typesMap",new Map([[Mc.INK_THICKNESS,"stroke-width"],[Mc.INK_COLOR,"stroke"],[Mc.INK_OPACITY,"stroke-opacity"]]))}static createDrawerInstance(t,e,s,i,n){return new yf(t,e,s,i,n,this._defaultDrawingOptions["stroke-width"])}static deserializeDraw(t,e,s,i,n,a){return xf.deserialize(t,e,s,i,n,a)}static async deserialize(t,e,s){let i=null;if(t instanceof Yg){const{data:{inkLists:e,rect:s,rotation:n,id:a,color:r,opacity:o,borderStyle:{rawWidth:l},popupRef:h},parent:{page:{pageNumber:c}}}=t;i=t={annotationType:kc.INK,color:Array.from(r),thickness:l,opacity:o,paths:{points:e},boxes:null,pageIndex:c-1,rect:s.slice(0),rotation:n,annotationElementId:a,id:a,deleted:!1,popupRef:h}}const n=await super.deserialize(t,e,s);return n._initialData=i,n}onScaleChanging(){if(!this.parent)return;super.onScaleChanging();const{_drawId:t,_drawingOptions:e,parent:s}=this;e.updateSVGProperty("stroke-width"),s.drawLayer.updateProperties(t,e.toSVGProperties())}static onScaleChangingWhenDrawing(){const t=this._currentParent;t&&(super.onScaleChangingWhenDrawing(),this._defaultDrawingOptions.updateSVGProperty("stroke-width"),t.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties()))}createDrawingOptions({color:e,thickness:s,opacity:i}){this._drawingOptions=t.getDefaultDrawingOptions({stroke:gd.makeHexColor(...e),"stroke-width":s,"stroke-opacity":i})}serialize(t=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const{lines:e,points:s,rect:i}=this.serializeDraw(t),{_drawingOptions:{stroke:n,"stroke-opacity":a,"stroke-width":r}}=this,o={annotationType:kc.INK,color:lu._colorManager.convert(n),opacity:a,thickness:r,paths:{lines:e,points:s},pageIndex:this.pageIndex,rect:i,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return t?(o.isCopy=!0,o):this.annotationElementId&&!cc(this,Ol,Bl).call(this,o)?null:(o.id=this.annotationElementId,o)}renderAnnotationElement(t){const{points:e,rect:s}=this.serializeDraw(!1);return t.updateEdited({rect:s,thickness:this._drawingOptions["stroke-width"],points:e}),null}};Ol=new WeakSet,Bl=function(t){const{color:e,thickness:s,opacity:i,pageIndex:n}=this._initialData;return this._hasBeenMoved||this._hasBeenResized||t.color.some((t,s)=>t!==e[s])||t.thickness!==s||t.opacity!==i||t.pageIndex!==n},ac(kf,"_type","ink"),ac(kf,"_editorType",kc.INK),ac(kf,"_defaultDrawingOptions",null);let Mf=kf;class Ef extends xf{toSVGPath(){let t=super.toSVGPath();return t.endsWith("Z")||(t+="Z"),t}}class Cf{static extractContoursFromText(t,{fontFamily:e,fontStyle:s,fontWeight:i},n,a,r,o){let l=new OffscreenCanvas(1,1),h=l.getContext("2d",{alpha:!1});const c=h.font=`${s} ${i} 200px ${e}`,{actualBoundingBoxLeft:d,actualBoundingBoxRight:u,actualBoundingBoxAscent:p,actualBoundingBoxDescent:g,fontBoundingBoxAscent:f,fontBoundingBoxDescent:m,width:v}=h.measureText(t),w=1.5,b=Math.ceil(Math.max(Math.abs(d)+Math.abs(u)||0,v)*w),A=Math.ceil(Math.max(Math.abs(p)+Math.abs(g)||200,Math.abs(f)+Math.abs(m)||200)*w);l=new OffscreenCanvas(b,A),h=l.getContext("2d",{alpha:!0,willReadFrequently:!0}),h.font=c,h.filter="grayscale(1)",h.fillStyle="white",h.fillRect(0,0,b,A),h.fillStyle="black",h.fillText(t,.5*b/2,1.5*A/2);const _=cc(this,Hl,Ql).call(this,h.getImageData(0,0,b,A).data),y=cc(this,Hl,Kl).call(this,_),x=cc(this,Hl,Jl).call(this,y),S=cc(this,Hl,Vl).call(this,_,b,A,x);return this.processDrawnLines({lines:{curves:S,width:b,height:A},pageWidth:n,pageHeight:a,rotation:r,innerMargin:o,mustSmooth:!0,areContours:!0})}static process(t,e,s,i,n){const[a,r,o]=cc(this,Hl,Zl).call(this,t),[l,h]=cc(this,Hl,Yl).call(this,a,r,o,Math.hypot(r,o)*oc(this,$l).sigmaSFactor,oc(this,$l).sigmaR,oc(this,$l).kernelSize),c=cc(this,Hl,Jl).call(this,h),d=cc(this,Hl,Vl).call(this,l,r,o,c);return this.processDrawnLines({lines:{curves:d,width:r,height:o},pageWidth:e,pageHeight:s,rotation:i,innerMargin:n,mustSmooth:!0,areContours:!0})}static processDrawnLines({lines:t,pageWidth:e,pageHeight:s,rotation:i,innerMargin:n,mustSmooth:a,areContours:r}){i%180!=0&&([e,s]=[s,e]);const{curves:o,width:l,height:h}=t,c=t.thickness??0,d=[],u=Math.min(e/l,s/h),p=u/e,g=u/s,f=[];for(const{points:v}of o){const t=a?cc(this,Hl,Xl).call(this,v):v;if(!t)continue;f.push(t);const e=t.length,s=new Float32Array(e),i=new Float32Array(3*(2===e?2:e-2));if(d.push({line:i,points:s}),2===e){s[0]=t[0]*p,s[1]=t[1]*g,i.set([NaN,NaN,NaN,NaN,s[0],s[1]],0);continue}let[n,r,o,l]=t;n*=p,r*=g,o*=p,l*=g,s.set([n,r,o,l],0),i.set([NaN,NaN,NaN,NaN,n,r],0);for(let a=4;a<e;a+=2){const e=s[a]=t[a]*p,h=s[a+1]=t[a+1]*g;i.set(of.createBezierPoints(n,r,o,l,e,h),3*(a-2)),[n,r,o,l]=[o,l,e,h]}}if(0===d.length)return null;const m=r?new Ef:new xf;return m.build(d,e,s,1,i,r?0:c,n),{outline:m,newCurves:f,areContours:r,thickness:c,width:l,height:h}}static async compressSignature({outlines:t,areContours:e,thickness:s,width:i,height:n}){let a,r=1/0,o=-1/0,l=0;for(const v of t){l+=v.length;for(let t=2,e=v.length;t<e;t++){const e=v[t]-v[t-2];r=Math.min(r,e),o=Math.max(o,e)}}a=r>=-128&&o<=127?Int8Array:r>=-32768&&o<=32767?Int16Array:Int32Array;const h=t.length,c=8+3*h,d=new Uint32Array(c);let u=0;d[u++]=c*Uint32Array.BYTES_PER_ELEMENT+(l-2*h)*a.BYTES_PER_ELEMENT,d[u++]=0,d[u++]=i,d[u++]=n,d[u++]=e?0:1,d[u++]=Math.max(0,Math.floor(s??0)),d[u++]=h,d[u++]=a.BYTES_PER_ELEMENT;for(const v of t)d[u++]=v.length-2,d[u++]=v[0],d[u++]=v[1];const p=new CompressionStream("deflate-raw"),g=p.writable.getWriter();await g.ready,g.write(d);const f=a.prototype.constructor;for(const v of t){const t=new f(v.length-2);for(let e=2,s=v.length;e<s;e++)t[e-2]=v[e]-v[e-2];g.write(t)}g.close();const m=await new Response(p.readable).arrayBuffer();return _d(new Uint8Array(m))}static async decompressSignature(t){try{const s=(e=t,Uint8Array.fromBase64?Uint8Array.fromBase64(e):dd(atob(e))),{readable:i,writable:n}=new DecompressionStream("deflate-raw"),a=n.getWriter();await a.ready,a.write(s).then(async()=>{await a.ready,await a.close()}).catch(()=>{});let r=null,o=0;for await(const t of i)r||(r=new Uint8Array(new Uint32Array(t.buffer,0,4)[0])),r.set(t,o),o+=t.length;const l=new Uint32Array(r.buffer,0,r.length>>2),h=l[1];if(0!==h)throw new Error(`Invalid version: ${h}`);const c=l[2],d=l[3],u=0===l[4],p=l[5],g=l[6],f=l[7],m=[],v=(8+3*g)*Uint32Array.BYTES_PER_ELEMENT;let w;switch(f){case Int8Array.BYTES_PER_ELEMENT:w=new Int8Array(r.buffer,v);break;case Int16Array.BYTES_PER_ELEMENT:w=new Int16Array(r.buffer,v);break;case Int32Array.BYTES_PER_ELEMENT:w=new Int32Array(r.buffer,v)}o=0;for(let t=0;t<g;t++){const e=l[3*t+8],s=new Float32Array(e+2);m.push(s);for(let i=0;i<2;i++)s[i]=l[3*t+8+i+1];for(let t=0;t<e;t++)s[t+2]=s[t]+w[o++]}return{areContours:u,thickness:p,outlines:m,width:c,height:d}}catch(s){return Qc(`decompressSignature: ${s}`),null}var e}}$l=new WeakMap,Hl=new WeakSet,zl=function(t,e,s,i){return i-=e,0===(s-=t)?i>0?0:4:1===s?i+6:2-i},Gl=new WeakMap,jl=function(t,e,s,i,n,a,r){const o=cc(this,Hl,zl).call(this,s,i,n,a);for(let l=0;l<8;l++){const n=(-l+o-r+16)%8;if(0!==t[(s+oc(this,Gl)[2*n])*e+(i+oc(this,Gl)[2*n+1])])return n}return-1},Ul=function(t,e,s,i,n,a,r){const o=cc(this,Hl,zl).call(this,s,i,n,a);for(let l=0;l<8;l++){const n=(l+o+r+16)%8;if(0!==t[(s+oc(this,Gl)[2*n])*e+(i+oc(this,Gl)[2*n+1])])return n}return-1},Vl=function(t,e,s,i){const n=t.length,a=new Int32Array(n);for(let h=0;h<n;h++)a[h]=t[h]<=i?1:0;for(let h=1;h<s-1;h++)a[h*e]=a[h*e+e-1]=0;for(let h=0;h<e;h++)a[h]=a[e*s-1-h]=0;let r,o=1;const l=[];for(let h=1;h<s-1;h++){r=1;for(let t=1;t<e-1;t++){const s=h*e+t,i=a[s];if(0===i)continue;let n=h,c=t;if(1===i&&0===a[s-1])o+=1,c-=1;else{if(!(i>=1&&0===a[s+1])){1!==i&&(r=Math.abs(i));continue}o+=1,c+=1,i>1&&(r=i)}const d=[t,h],u=c===t+1,p={isHole:u,points:d,id:o,parent:0};let g;l.push(p);for(const t of l)if(t.id===r){g=t;break}g?g.isHole?p.parent=u?g.parent:r:p.parent=u?r:g.parent:p.parent=u?r:0;const f=cc(this,Hl,jl).call(this,a,e,h,t,n,c,0);if(-1===f){a[s]=-o,1!==a[s]&&(r=Math.abs(a[s]));continue}let m=oc(this,Gl)[2*f],v=oc(this,Gl)[2*f+1];const w=h+m,b=t+v;n=w,c=b;let A=h,_=t;for(;;){const i=cc(this,Hl,Ul).call(this,a,e,A,_,n,c,1);m=oc(this,Gl)[2*i],v=oc(this,Gl)[2*i+1];const l=A+m,u=_+v;d.push(u,l);const p=A*e+_;if(0===a[p+1]?a[p]=-o:1===a[p]&&(a[p]=o),l===h&&u===t&&A===w&&_===b){1!==a[s]&&(r=Math.abs(a[s]));break}n=A,c=_,A=l,_=u}}}return l},ql=function(t,e,s,i){if(s-e<=4){for(let n=e;n<s-2;n+=2)i.push(t[n],t[n+1]);return}const n=t[e],a=t[e+1],r=t[s-4]-n,o=t[s-3]-a,l=Math.hypot(r,o),h=r/l,c=o/l,d=h*a-c*n,u=o/r,p=1/l,g=Math.atan(u),f=Math.cos(g),m=Math.sin(g),v=p*(Math.abs(f)+Math.abs(m)),w=p*(1-v+v**2),b=Math.max(Math.atan(Math.abs(m+f)*w),Math.atan(Math.abs(m-f)*w));let A=0,_=e;for(let y=e+2;y<s-2;y+=2){const e=Math.abs(d-h*t[y+1]+c*t[y]);e>A&&(_=y,A=e)}A>(l*b)**2?(cc(this,Hl,ql).call(this,t,e,_+2,i),cc(this,Hl,ql).call(this,t,_,s,i)):i.push(n,a)},Xl=function(t){const e=[],s=t.length;return cc(this,Hl,ql).call(this,t,0,s,e),e.push(t[s-2],t[s-1]),e.length<=4?null:e},Yl=function(t,e,s,i,n,a){const r=new Float32Array(a**2),o=-2*i**2,l=a>>1;for(let g=0;g<a;g++){const t=(g-l)**2;for(let e=0;e<a;e++)r[g*a+e]=Math.exp((t+(e-l)**2)/o)}const h=new Float32Array(256),c=-2*n**2;for(let g=0;g<256;g++)h[g]=Math.exp(g**2/c);const d=t.length,u=new Uint8Array(d),p=new Uint32Array(256);for(let g=0;g<s;g++)for(let i=0;i<e;i++){const n=g*e+i,o=t[n];let c=0,d=0;for(let u=0;u<a;u++){const n=g+u-l;if(!(n<0||n>=s))for(let s=0;s<a;s++){const p=i+s-l;if(p<0||p>=e)continue;const g=t[n*e+p],f=r[u*a+s]*h[Math.abs(g-o)];c+=g*f,d+=f}}p[u[n]=Math.round(c/d)]++}return[u,p]},Kl=function(t){const e=new Uint32Array(256);for(const s of t)e[s]++;return e},Ql=function(t){const e=t.length,s=new Uint8ClampedArray(e>>2);let i=-1/0,n=1/0;for(let r=0,o=s.length;r<o;r++){if(0===t[3+(r<<2)]){i=s[r]=255;continue}const e=s[r]=t[r<<2];e>i&&(i=e),e<n&&(n=e)}const a=255/(i-n);for(let r=0;r<e;r++)s[r]=(s[r]-n)*a;return s},Jl=function(t){let e,s=-1/0,i=-1/0;const n=t.findIndex(t=>0!==t);let a=n,r=n;for(e=n;e<256;e++){const n=t[e];n>s&&(e-a>i&&(i=e-a,r=e-1),s=n,a=e)}for(e=r-1;e>=0&&!(t[e]>t[e+1]);e--);return e},Zl=function(t){const e=t,{width:s,height:i}=t,{maxDim:n}=oc(this,$l);let a=s,r=i;if(s>n||i>n){let o=s,l=i,h=Math.log2(Math.max(s,i)/n);const c=Math.floor(h);h=h===c?c-1:c;for(let s=0;s<h;s++){a=Math.ceil(o/2),r=Math.ceil(l/2);const s=new OffscreenCanvas(a,r);s.getContext("2d").drawImage(t,0,0,o,l,0,0,a,r),o=a,l=r,t!==e&&t.close(),t=s.transferToImageBitmap()}const d=Math.min(n/a,n/r);a=Math.round(a*d),r=Math.round(r*d)}const o=new OffscreenCanvas(a,r).getContext("2d",{willReadFrequently:!0});o.filter="grayscale(1)",o.drawImage(t,0,0,t.width,t.height,0,0,a,r);const l=o.getImageData(0,0,a,r).data;return[cc(this,Hl,Ql).call(this,l),a,r]},lc(Cf,Hl),lc(Cf,$l,{maxDim:512,sigmaSFactor:.02,sigmaR:25,kernelSize:16}),lc(Cf,Gl,new Int32Array([0,1,-1,1,-1,0,-1,-1,0,-1,1,-1,1,0,1,1]));class Tf extends bf{constructor(){super(),super.updateProperties({fill:lu._defaultLineColor,"stroke-width":0})}clone(){const t=new Tf;return t.updateAll(this),t}}class Rf extends Sf{constructor(t){super(t),super.updateProperties({stroke:lu._defaultLineColor,"stroke-width":1})}clone(){const t=new Rf(this._viewParameters);return t.updateAll(this),t}}const Pf=class t extends _f{constructor(t){super({...t,mustBeCommitted:!0,name:"signatureEditor"}),lc(this,th,!1),lc(this,eh,null),lc(this,sh,null),lc(this,ih,null),this._willKeepAspectRatio=!0,hc(this,sh,t.signatureData||null),hc(this,eh,null),this.defaultL10nId="pdfjs-editor-signature-editor1"}static initialize(t,e){lu.initialize(t,e),this._defaultDrawingOptions=new Tf,this._defaultDrawnSignatureOptions=new Rf(e.viewParameters)}static getDefaultDrawingOptions(t){const e=this._defaultDrawingOptions.clone();return e.updateProperties(t),e}static get supportMultipleDrawings(){return!1}static get typesMap(){return sd(this,"typesMap",new Map)}static get isDrawer(){return!1}get telemetryFinalData(){return{type:"signature",hasDescription:!!oc(this,eh)}}static computeTelemetryFinalData(t){const e=t.get("hasDescription");return{hasAltText:e.get(!0)??0,hasNoAltText:e.get(!1)??0}}get isResizable(){return!0}onScaleChanging(){null!==this._drawId&&super.onScaleChanging()}render(){if(this.div)return this.div;let e,s;const{_isCopy:i}=this;if(i&&(this._isCopy=!1,e=this.x,s=this.y),super.render(),null===this._drawId)if(oc(this,sh)){const{lines:e,mustSmooth:s,areContours:i,description:n,uuid:a,heightInPage:r}=oc(this,sh),{rawDims:{pageWidth:o,pageHeight:l},rotation:h}=this.parent.viewport,c=Cf.processDrawnLines({lines:e,pageWidth:o,pageHeight:l,rotation:h,innerMargin:t._INNER_MARGIN,mustSmooth:s,areContours:i});this.addSignature(c,r,n,a)}else this.div.setAttribute("data-l10n-args",JSON.stringify({description:""})),this.div.hidden=!0,this._uiManager.getSignature(this);return i&&(this._isCopy=!0,this._moveAfterPaste(e,s)),this.div}setUuid(t){hc(this,ih,t),this.addEditToolbar()}getUuid(){return oc(this,ih)}get description(){return oc(this,eh)}set description(t){hc(this,eh,t),super.addEditToolbar().then(e=>{null==e||e.updateEditSignatureButton(t)})}getSignaturePreview(){const{newCurves:t,areContours:e,thickness:s,width:i,height:n}=oc(this,sh),a=Math.max(i,n);return{areContours:e,outline:Cf.processDrawnLines({lines:{curves:t.map(t=>({points:t})),thickness:s,width:i,height:n},pageWidth:a,pageHeight:a,rotation:0,innerMargin:0,mustSmooth:!1,areContours:e}).outline}}get toolbarButtons(){return this._uiManager.signatureManager?[["editSignature",this._uiManager.signatureManager]]:super.toolbarButtons}addSignature(e,s,i,n){const{x:a,y:r}=this,{outline:o}=hc(this,sh,e);let l;hc(this,th,o instanceof Ef),this.description=i,this.div.setAttribute("data-l10n-args",JSON.stringify({description:i})),oc(this,th)?l=t.getDefaultDrawingOptions():(l=t._defaultDrawnSignatureOptions.clone(),l.updateProperties({"stroke-width":o.thickness})),this._addOutlines({drawOutlines:o,drawingOptions:l});const[h,c]=this.parentDimensions,[,d]=this.pageDimensions;let u=s/d;u=u>=1?.5:u,this.width*=u/this.height,this.width>=1&&(u*=.9/this.width,this.width=.9),this.height=u,this.setDims(h*this.width,c*this.height),this.x=a,this.y=r,this.center(),this._onResized(),this.onScaleChanging(),this.rotate(),this._uiManager.addToAnnotationStorage(this),this.setUuid(n),this._reportTelemetry({action:"pdfjs.signature.inserted",data:{hasBeenSaved:!!n,hasDescription:!!i}}),this.div.hidden=!1}getFromImage(e){const{rawDims:{pageWidth:s,pageHeight:i},rotation:n}=this.parent.viewport;return Cf.process(e,s,i,n,t._INNER_MARGIN)}getFromText(e,s){const{rawDims:{pageWidth:i,pageHeight:n},rotation:a}=this.parent.viewport;return Cf.extractContoursFromText(e,s,i,n,a,t._INNER_MARGIN)}getDrawnSignature(e){const{rawDims:{pageWidth:s,pageHeight:i},rotation:n}=this.parent.viewport;return Cf.processDrawnLines({lines:e,pageWidth:s,pageHeight:i,rotation:n,innerMargin:t._INNER_MARGIN,mustSmooth:!1,areContours:!1})}createDrawingOptions({areContours:e,thickness:s}){e?this._drawingOptions=t.getDefaultDrawingOptions():(this._drawingOptions=t._defaultDrawnSignatureOptions.clone(),this._drawingOptions.updateProperties({"stroke-width":s}))}serialize(t=!1){if(this.isEmpty())return null;const{lines:e,points:s,rect:i}=this.serializeDraw(t),{_drawingOptions:{"stroke-width":n}}=this,a={annotationType:kc.SIGNATURE,isSignature:!0,areContours:oc(this,th),color:[0,0,0],thickness:oc(this,th)?0:n,pageIndex:this.pageIndex,rect:i,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return t?(a.paths={lines:e,points:s},a.uuid=oc(this,ih),a.isCopy=!0):a.lines=e,oc(this,eh)&&(a.accessibilityData={type:"Figure",alt:oc(this,eh)}),a}static deserializeDraw(t,e,s,i,n,a){return a.areContours?Ef.deserialize(t,e,s,i,n,a):xf.deserialize(t,e,s,i,n,a)}static async deserialize(t,e,s){var i;const n=await super.deserialize(t,e,s);return hc(n,th,t.areContours),hc(n,eh,(null==(i=t.accessibilityData)?void 0:i.alt)||""),hc(n,ih,t.uuid),n}};th=new WeakMap,eh=new WeakMap,sh=new WeakMap,ih=new WeakMap,ac(Pf,"_type","signature"),ac(Pf,"_editorType",kc.SIGNATURE),ac(Pf,"_defaultDrawingOptions",null);let If=Pf;class Lf extends lu{constructor(t){super({...t,name:"stampEditor"}),lc(this,fh),lc(this,nh,null),lc(this,ah,null),lc(this,rh,null),lc(this,oh,null),lc(this,lh,null),lc(this,hh,""),lc(this,ch,null),lc(this,dh,!1),lc(this,uh,null),lc(this,ph,!1),lc(this,gh,!1),hc(this,oh,t.bitmapUrl),hc(this,lh,t.bitmapFile),this.defaultL10nId="pdfjs-editor-stamp-editor"}static initialize(t,e){lu.initialize(t,e)}static isHandlingMimeForPasting(t){return Gd.includes(t)}static paste(t,e){e.pasteEditor({mode:kc.STAMP},{bitmapFile:t.getAsFile()})}altTextFinish(){this._uiManager.useNewAltTextFlow&&(this.div.hidden=!1),super.altTextFinish()}get telemetryFinalData(){var t;return{type:"stamp",hasAltText:!!(null==(t=this.altTextData)?void 0:t.altText)}}static computeTelemetryFinalData(t){const e=t.get("hasAltText");return{hasAltText:e.get(!0)??0,hasNoAltText:e.get(!1)??0}}async mlGuessAltText(t=null,e=!0){if(this.hasAltTextData())return null;const{mlManager:s}=this._uiManager;if(!s)throw new Error("No ML.");if(!(await s.isEnabledFor("altText")))throw new Error("ML isn't enabled for alt text.");const{data:i,width:n,height:a}=t||this.copyCanvas(null,null,!0).imageData,r=await s.guess({name:"altText",request:{data:i,width:n,height:a,channels:i.length/(n*a)}});if(!r)throw new Error("No response from the AI service.");if(r.error)throw new Error("Error from the AI service.");if(r.cancel)return null;if(!r.output)throw new Error("No valid response from the AI service.");const o=r.output;return await this.setGuessedAltText(o),e&&!this.hasAltTextData()&&(this.altTextData={alt:o,decorative:!1}),o}remove(){var t;oc(this,ah)&&(hc(this,nh,null),this._uiManager.imageManager.deleteId(oc(this,ah)),null==(t=oc(this,ch))||t.remove(),hc(this,ch,null),oc(this,uh)&&(clearTimeout(oc(this,uh)),hc(this,uh,null))),super.remove()}rebuild(){this.parent?(super.rebuild(),null!==this.div&&(oc(this,ah)&&null===oc(this,ch)&&cc(this,fh,wh).call(this),this.isAttachedToDOM||this.parent.add(this))):oc(this,ah)&&cc(this,fh,wh).call(this)}onceAdded(t){this._isDraggable=!0,t&&this.div.focus()}isEmpty(){return!(oc(this,rh)||oc(this,nh)||oc(this,oh)||oc(this,lh)||oc(this,ah)||oc(this,dh))}get toolbarButtons(){return[["altText",this.createAltText()]]}get isResizable(){return!0}render(){if(this.div)return this.div;let t,e;return this._isCopy&&(t=this.x,e=this.y),super.render(),this.div.hidden=!0,this.createAltText(),oc(this,dh)||(oc(this,nh)?cc(this,fh,bh).call(this):cc(this,fh,wh).call(this)),this._isCopy&&this._moveAfterPaste(t,e),this._uiManager.addShouldRescale(this),this.div}setCanvas(t,e){const{id:s,bitmap:i}=this._uiManager.imageManager.getFromCanvas(t,e);e.remove(),s&&this._uiManager.imageManager.isValidId(s)&&(hc(this,ah,s),i&&hc(this,nh,i),hc(this,dh,!1),cc(this,fh,bh).call(this))}_onResized(){this.onScaleChanging()}onScaleChanging(){if(!this.parent)return;null!==oc(this,uh)&&clearTimeout(oc(this,uh));hc(this,uh,setTimeout(()=>{hc(this,uh,null),cc(this,fh,_h).call(this)},200))}copyCanvas(t,e,s=!1){var i;t||(t=224);const{width:n,height:a}=oc(this,nh),r=new zd;let o=oc(this,nh),l=n,h=a,c=null;if(e){if(n>e||a>e){const t=Math.min(e/n,e/a);l=Math.floor(n*t),h=Math.floor(a*t)}c=document.createElement("canvas");const t=c.width=Math.ceil(l*r.sx),s=c.height=Math.ceil(h*r.sy);oc(this,ph)||(o=cc(this,fh,Ah).call(this,t,s));const d=c.getContext("2d");d.filter=this._uiManager.hcmFilter;let u="white",p="#cfcfd8";"none"!==this._uiManager.hcmFilter?p="black":(null==(i=window.matchMedia)?void 0:i.call(window,"(prefers-color-scheme: dark)").matches)&&(u="#8f8f9d",p="#42414d");const g=15,f=g*r.sx,m=g*r.sy,v=new OffscreenCanvas(2*f,2*m),w=v.getContext("2d");w.fillStyle=u,w.fillRect(0,0,2*f,2*m),w.fillStyle=p,w.fillRect(0,0,f,m),w.fillRect(f,m,f,m),d.fillStyle=d.createPattern(v,"repeat"),d.fillRect(0,0,t,s),d.drawImage(o,0,0,o.width,o.height,0,0,t,s)}let d=null;if(s){let e,s;if(r.symmetric&&o.width<t&&o.height<t)e=o.width,s=o.height;else if(o=oc(this,nh),n>t||a>t){const i=Math.min(t/n,t/a);e=Math.floor(n*i),s=Math.floor(a*i),oc(this,ph)||(o=cc(this,fh,Ah).call(this,e,s))}const i=new OffscreenCanvas(e,s).getContext("2d",{willReadFrequently:!0});i.drawImage(o,0,0,o.width,o.height,0,0,e,s),d={width:e,height:s,data:i.getImageData(0,0,e,s).data}}return{canvas:c,width:l,height:h,imageData:d}}static async deserialize(t,e,s){var i;let n=null,a=!1;if(t instanceof tf){const{data:{rect:r,rotation:o,id:l,structParent:h,popupRef:c},container:d,parent:{page:{pageNumber:u}},canvas:p}=t;let g,f;p?(delete t.canvas,({id:g,bitmap:f}=s.imageManager.getFromCanvas(d.id,p)),p.remove()):(a=!0,t._hasNoCanvas=!0);const m=(null==(i=await e._structTree.getAriaAttributes(`${bd}${l}`))?void 0:i.get("aria-label"))||"";n=t={annotationType:kc.STAMP,bitmapId:g,bitmap:f,pageIndex:u-1,rect:r.slice(0),rotation:o,annotationElementId:l,id:l,deleted:!1,accessibilityData:{decorative:!1,altText:m},isSvg:!1,structParent:h,popupRef:c}}const r=await super.deserialize(t,e,s),{rect:o,bitmap:l,bitmapUrl:h,bitmapId:c,isSvg:d,accessibilityData:u}=t;a?(s.addMissingCanvas(t.id,r),hc(r,dh,!0)):c&&s.imageManager.isValidId(c)?(hc(r,ah,c),l&&hc(r,nh,l)):hc(r,oh,h),hc(r,ph,d);const[p,g]=r.pageDimensions;return r.width=(o[2]-o[0])/p,r.height=(o[3]-o[1])/g,u&&(r.altTextData=u),r._initialData=n,hc(r,gh,!!n),r}serialize(t=!1,e=null){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const s={annotationType:kc.STAMP,bitmapId:oc(this,ah),pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:oc(this,ph),structTreeParentId:this._structTreeParentId};if(t)return s.bitmapUrl=cc(this,fh,yh).call(this,!0),s.accessibilityData=this.serializeAltText(!0),s.isCopy=!0,s;const{decorative:i,altText:n}=this.serializeAltText(!1);if(!i&&n&&(s.accessibilityData={type:"Figure",alt:n}),this.annotationElementId){const t=cc(this,fh,xh).call(this,s);if(t.isSame)return null;t.isSameAltText?delete s.accessibilityData:s.accessibilityData.structParent=this._initialData.structParent??-1}if(s.id=this.annotationElementId,null===e)return s;e.stamps||(e.stamps=new Map);const a=oc(this,ph)?(s.rect[2]-s.rect[0])*(s.rect[3]-s.rect[1]):null;if(e.stamps.has(oc(this,ah))){if(oc(this,ph)){const t=e.stamps.get(oc(this,ah));a>t.area&&(t.area=a,t.serialized.bitmap.close(),t.serialized.bitmap=cc(this,fh,yh).call(this,!1))}}else e.stamps.set(oc(this,ah),{area:a,serialized:s}),s.bitmap=cc(this,fh,yh).call(this,!1);return s}renderAnnotationElement(t){return t.updateEdited({rect:this.getRect(0,0)}),null}}nh=new WeakMap,ah=new WeakMap,rh=new WeakMap,oh=new WeakMap,lh=new WeakMap,hh=new WeakMap,ch=new WeakMap,dh=new WeakMap,uh=new WeakMap,ph=new WeakMap,gh=new WeakMap,fh=new WeakSet,mh=function(t,e=!1){t?(hc(this,nh,t.bitmap),e||(hc(this,ah,t.id),hc(this,ph,t.isSvg)),t.file&&hc(this,hh,t.file.name),cc(this,fh,bh).call(this)):this.remove()},vh=function(){if(hc(this,rh,null),this._uiManager.enableWaiting(!1),oc(this,ch))if(this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&oc(this,nh))this.addEditToolbar().then(()=>{this._editToolbar.hide(),this._uiManager.editAltText(this,!0)});else{if(!this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&oc(this,nh)){this._reportTelemetry({action:"pdfjs.image.image_added",data:{alt_text_modal:!1,alt_text_type:"empty"}});try{this.mlGuessAltText()}catch{}}this.div.focus()}},wh=function(){if(oc(this,ah))return this._uiManager.enableWaiting(!0),void this._uiManager.imageManager.getFromId(oc(this,ah)).then(t=>cc(this,fh,mh).call(this,t,!0)).finally(()=>cc(this,fh,vh).call(this));if(oc(this,oh)){const t=oc(this,oh);return hc(this,oh,null),this._uiManager.enableWaiting(!0),void hc(this,rh,this._uiManager.imageManager.getFromUrl(t).then(t=>cc(this,fh,mh).call(this,t)).finally(()=>cc(this,fh,vh).call(this)))}if(oc(this,lh)){const t=oc(this,lh);return hc(this,lh,null),this._uiManager.enableWaiting(!0),void hc(this,rh,this._uiManager.imageManager.getFromFile(t).then(t=>cc(this,fh,mh).call(this,t)).finally(()=>cc(this,fh,vh).call(this)))}const t=document.createElement("input");t.type="file",t.accept=Gd.join(",");const e=this._uiManager._signal;hc(this,rh,new Promise(s=>{t.addEventListener("change",async()=>{if(t.files&&0!==t.files.length){this._uiManager.enableWaiting(!0);const e=await this._uiManager.imageManager.getFromFile(t.files[0]);this._reportTelemetry({action:"pdfjs.image.image_selected",data:{alt_text_modal:this._uiManager.useNewAltTextFlow}}),cc(this,fh,mh).call(this,e)}else this.remove();s()},{signal:e}),t.addEventListener("cancel",()=>{this.remove(),s()},{signal:e})}).finally(()=>cc(this,fh,vh).call(this))),t.click()},bh=function(){var t;const{div:e}=this;let{width:s,height:i}=oc(this,nh);const[n,a]=this.pageDimensions,r=.75;if(this.width)s=this.width*n,i=this.height*a;else if(s>r*n||i>r*a){const t=Math.min(r*n/s,r*a/i);s*=t,i*=t}const[o,l]=this.parentDimensions;this.setDims(s*o/n,i*l/a),this._uiManager.enableWaiting(!1);const h=hc(this,ch,document.createElement("canvas"));h.setAttribute("role","img"),this.addContainer(h),this.width=s/n,this.height=i/a,(null==(t=this._initialOptions)?void 0:t.isCentered)?this.center():this.fixAndSetPosition(),this._initialOptions=null,this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&!this.annotationElementId||(e.hidden=!1),cc(this,fh,_h).call(this),oc(this,gh)||(this.parent.addUndoableEditor(this),hc(this,gh,!0)),this._reportTelemetry({action:"inserted_image"}),oc(this,hh)&&this.div.setAttribute("aria-description",oc(this,hh)),this.annotationElementId||this._uiManager.a11yAlert("pdfjs-editor-stamp-added-alert")},Ah=function(t,e){const{width:s,height:i}=oc(this,nh);let n=s,a=i,r=oc(this,nh);for(;n>2*t||a>2*e;){const s=n,i=a;n>2*t&&(n=n>=16384?Math.floor(n/2)-1:Math.ceil(n/2)),a>2*e&&(a=a>=16384?Math.floor(a/2)-1:Math.ceil(a/2));const o=new OffscreenCanvas(n,a);o.getContext("2d").drawImage(r,0,0,s,i,0,0,n,a),r=o.transferToImageBitmap()}return r},_h=function(){const[t,e]=this.parentDimensions,{width:s,height:i}=this,n=new zd,a=Math.ceil(s*t*n.sx),r=Math.ceil(i*e*n.sy),o=oc(this,ch);if(!o||o.width===a&&o.height===r)return;o.width=a,o.height=r;const l=oc(this,ph)?oc(this,nh):cc(this,fh,Ah).call(this,a,r),h=o.getContext("2d");h.filter=this._uiManager.hcmFilter,h.drawImage(l,0,0,l.width,l.height,0,0,a,r)},yh=function(t){if(t){if(oc(this,ph)){const t=this._uiManager.imageManager.getSvgUrl(oc(this,ah));if(t)return t}const t=document.createElement("canvas");({width:t.width,height:t.height}=oc(this,nh));return t.getContext("2d").drawImage(oc(this,nh),0,0),t.toDataURL()}if(oc(this,ph)){const[t,e]=this.pageDimensions,s=Math.round(this.width*t*Sd.PDF_TO_CSS_UNITS),i=Math.round(this.height*e*Sd.PDF_TO_CSS_UNITS),n=new OffscreenCanvas(s,i);return n.getContext("2d").drawImage(oc(this,nh),0,0,oc(this,nh).width,oc(this,nh).height,0,0,s,i),n.transferToImageBitmap()}return structuredClone(oc(this,nh))},xh=function(t){var e;const{pageIndex:s,accessibilityData:{altText:i}}=this._initialData,n=t.pageIndex===s,a=((null==(e=t.accessibilityData)?void 0:e.alt)||"")===i;return{isSame:!this._hasBeenMoved&&!this._hasBeenResized&&n&&a,isSameAltText:a}},ac(Lf,"_type","stamp"),ac(Lf,"_editorType",kc.STAMP);const Df=class t{constructor({uiManager:e,pageIndex:s,div:i,structTreeLayer:n,accessibilityManager:a,annotationLayer:r,drawLayer:o,textLayer:l,viewport:h,l10n:c}){lc(this,Hh),lc(this,Sh),lc(this,kh,!1),lc(this,Mh,null),lc(this,Eh,null),lc(this,Ch,null),lc(this,Th,new Map),lc(this,Rh,!1),lc(this,Ph,!1),lc(this,Ih,!1),lc(this,Lh,null),lc(this,Dh,null),lc(this,Nh,null),lc(this,Fh,null),lc(this,Wh,null),lc(this,Oh,-1),lc(this,Bh);const d=[...oc(t,$h).values()];if(!t._initialized){t._initialized=!0;for(const t of d)t.initialize(c,e)}e.registerEditorTypes(d),hc(this,Bh,e),this.pageIndex=s,this.div=i,hc(this,Sh,a),hc(this,Mh,r),this.viewport=h,hc(this,Nh,l),this.drawLayer=o,this._structTree=n,oc(this,Bh).addLayer(this)}get isEmpty(){return 0===oc(this,Th).size}get isInvisible(){return this.isEmpty&&oc(this,Bh).getMode()===kc.NONE}updateToolbar(t){oc(this,Bh).updateToolbar(t)}updateMode(e=oc(this,Bh).getMode()){switch(cc(this,Hh,Vh).call(this),e){case kc.NONE:return this.disableTextSelection(),this.togglePointerEvents(!1),this.toggleAnnotationLayerPointerEvents(!0),void this.disableClick();case kc.INK:this.disableTextSelection(),this.togglePointerEvents(!0),this.enableClick();break;case kc.HIGHLIGHT:this.enableTextSelection(),this.togglePointerEvents(!1),this.disableClick();break;default:this.disableTextSelection(),this.togglePointerEvents(!0),this.enableClick()}this.toggleAnnotationLayerPointerEvents(!1);const{classList:s}=this.div;for(const i of oc(t,$h).values())s.toggle(`${i._type}Editing`,e===i._editorType);this.div.hidden=!1}hasTextLayer(t){var e;return t===(null==(e=oc(this,Nh))?void 0:e.div)}setEditingState(t){oc(this,Bh).setEditingState(t)}addCommands(t){oc(this,Bh).addCommands(t)}cleanUndoStack(t){oc(this,Bh).cleanUndoStack(t)}toggleDrawing(t=!1){this.div.classList.toggle("drawing",!t)}togglePointerEvents(t=!1){this.div.classList.toggle("disabled",!t)}toggleAnnotationLayerPointerEvents(t=!1){var e;null==(e=oc(this,Mh))||e.div.classList.toggle("disabled",!t)}async enable(){var t;hc(this,Ih,!0),this.div.tabIndex=0,this.togglePointerEvents(!0),null==(t=oc(this,Wh))||t.abort(),hc(this,Wh,null);const e=new Set;for(const i of oc(this,Th).values())i.enableEditing(),i.show(!0),i.annotationElementId&&(oc(this,Bh).removeChangedExistingAnnotation(i),e.add(i.annotationElementId));if(!oc(this,Mh))return void hc(this,Ih,!1);const s=oc(this,Mh).getEditableAnnotations();for(const i of s){if(i.hide(),oc(this,Bh).isDeletedAnnotationElement(i.data.id))continue;if(e.has(i.data.id))continue;const t=await this.deserialize(i);t&&(this.addOrRebuild(t),t.enableEditing())}hc(this,Ih,!1)}disable(){var e;if(hc(this,Ph,!0),this.div.tabIndex=-1,this.togglePointerEvents(!1),oc(this,Nh)&&!oc(this,Wh)){hc(this,Wh,new AbortController);const t=oc(this,Bh).combinedSignal(oc(this,Wh));oc(this,Nh).div.addEventListener("pointerdown",t=>{const{clientX:e,clientY:s,timeStamp:i}=t;if(i-oc(this,Oh)>500)return void hc(this,Oh,i);hc(this,Oh,-1);const{classList:n}=this.div;n.toggle("getElements",!0);const a=document.elementsFromPoint(e,s);if(n.toggle("getElements",!1),!this.div.contains(a[0]))return;let r;const o=new RegExp(`^${Sc}[0-9]+$`);for(const h of a)if(o.test(h.id)){r=h.id;break}if(!r)return;const l=oc(this,Th).get(r);null===(null==l?void 0:l.annotationElementId)&&(t.stopPropagation(),t.preventDefault(),l.dblclick())},{signal:t,capture:!0})}const s=new Map,i=new Map;for(const t of oc(this,Th).values())t.disableEditing(),t.annotationElementId&&(null===t.serialize()?(i.set(t.annotationElementId,t),null==(e=this.getEditableAnnotation(t.annotationElementId))||e.show(),t.remove()):s.set(t.annotationElementId,t));if(oc(this,Mh)){const t=oc(this,Mh).getEditableAnnotations();for(const e of t){const{id:t}=e.data;if(oc(this,Bh).isDeletedAnnotationElement(t))continue;let n=i.get(t);n?(n.resetAnnotationElement(e),n.show(!1),e.show()):(n=s.get(t),n&&(oc(this,Bh).addChangedExistingAnnotation(n),n.renderAnnotationElement(e)&&n.show(!1)),e.show())}}cc(this,Hh,Vh).call(this),this.isEmpty&&(this.div.hidden=!0);const{classList:n}=this.div;for(const a of oc(t,$h).values())n.remove(`${a._type}Editing`);this.disableTextSelection(),this.toggleAnnotationLayerPointerEvents(!0),hc(this,Ph,!1)}getEditableAnnotation(t){var e;return(null==(e=oc(this,Mh))?void 0:e.getEditableAnnotation(t))||null}setActiveEditor(t){oc(this,Bh).getActive()!==t&&oc(this,Bh).setActiveEditor(t)}enableTextSelection(){var t;if(this.div.tabIndex=-1,(null==(t=oc(this,Nh))?void 0:t.div)&&!oc(this,Fh)){hc(this,Fh,new AbortController);const t=oc(this,Bh).combinedSignal(oc(this,Fh));oc(this,Nh).div.addEventListener("pointerdown",cc(this,Hh,zh).bind(this),{signal:t}),oc(this,Nh).div.classList.add("highlighting")}}disableTextSelection(){var t;this.div.tabIndex=0,(null==(t=oc(this,Nh))?void 0:t.div)&&oc(this,Fh)&&(oc(this,Fh).abort(),hc(this,Fh,null),oc(this,Nh).div.classList.remove("highlighting"))}enableClick(){if(oc(this,Eh))return;hc(this,Eh,new AbortController);const t=oc(this,Bh).combinedSignal(oc(this,Eh));this.div.addEventListener("pointerdown",this.pointerdown.bind(this),{signal:t});const e=this.pointerup.bind(this);this.div.addEventListener("pointerup",e,{signal:t}),this.div.addEventListener("pointercancel",e,{signal:t})}disableClick(){var t;null==(t=oc(this,Eh))||t.abort(),hc(this,Eh,null)}attach(t){oc(this,Th).set(t.id,t);const{annotationElementId:e}=t;e&&oc(this,Bh).isDeletedAnnotationElement(e)&&oc(this,Bh).removeDeletedAnnotationElement(t)}detach(t){var e;oc(this,Th).delete(t.id),null==(e=oc(this,Sh))||e.removePointerInTextLayer(t.contentDiv),!oc(this,Ph)&&t.annotationElementId&&oc(this,Bh).addDeletedAnnotationElement(t)}remove(t){this.detach(t),oc(this,Bh).removeEditor(t),t.div.remove(),t.isAttachedToDOM=!1}changeParent(t){var e;t.parent!==this&&(t.parent&&t.annotationElementId&&(oc(this,Bh).addDeletedAnnotationElement(t.annotationElementId),lu.deleteAnnotationElement(t),t.annotationElementId=null),this.attach(t),null==(e=t.parent)||e.detach(t),t.setParent(this),t.div&&t.isAttachedToDOM&&(t.div.remove(),this.div.append(t.div)))}add(t){if(t.parent!==this||!t.isAttachedToDOM){if(this.changeParent(t),oc(this,Bh).addEditor(t),this.attach(t),!t.isAttachedToDOM){const e=t.render();this.div.append(e),t.isAttachedToDOM=!0}t.fixAndSetPosition(),t.onceAdded(!oc(this,Ih)),oc(this,Bh).addToAnnotationStorage(t),t._reportTelemetry(t.telemetryInitialData)}}moveEditorInDOM(t){var e;if(!t.isAttachedToDOM)return;const{activeElement:s}=document;t.div.contains(s)&&!oc(this,Ch)&&(t._focusEventsAllowed=!1,hc(this,Ch,setTimeout(()=>{hc(this,Ch,null),t.div.contains(document.activeElement)?t._focusEventsAllowed=!0:(t.div.addEventListener("focusin",()=>{t._focusEventsAllowed=!0},{once:!0,signal:oc(this,Bh)._signal}),s.focus())},0))),t._structTreeParentId=null==(e=oc(this,Sh))?void 0:e.moveElementInDOM(this.div,t.div,t.contentDiv,!0)}addOrRebuild(t){t.needsToBeRebuilt()?(t.parent||(t.parent=this),t.rebuild(),t.show()):this.add(t)}addUndoableEditor(t){this.addCommands({cmd:()=>t._uiManager.rebuild(t),undo:()=>{t.remove()},mustExec:!1})}getNextId(){return oc(this,Bh).getId()}combinedSignal(t){return oc(this,Bh).combinedSignal(t)}canCreateNewEmptyEditor(){var t;return null==(t=oc(this,Hh,Gh))?void 0:t.canCreateNewEmptyEditor()}async pasteEditor(t,e){this.updateToolbar(t),await oc(this,Bh).updateMode(t.mode);const{offsetX:s,offsetY:i}=cc(this,Hh,Uh).call(this),n=this.getNextId(),a=cc(this,Hh,jh).call(this,{parent:this,id:n,x:s,y:i,uiManager:oc(this,Bh),isCentered:!0,...e});a&&this.add(a)}async deserialize(e){var s;return await(null==(s=oc(t,$h).get(e.annotationType??e.annotationEditorType))?void 0:s.deserialize(e,this,oc(this,Bh)))||null}createAndAddNewEditor(t,e,s={}){const i=this.getNextId(),n=cc(this,Hh,jh).call(this,{parent:this,id:i,x:t.offsetX,y:t.offsetY,uiManager:oc(this,Bh),isCentered:e,...s});return n&&this.add(n),n}addNewEditor(t={}){this.createAndAddNewEditor(cc(this,Hh,Uh).call(this),!0,t)}setSelected(t){oc(this,Bh).setSelected(t)}toggleSelected(t){oc(this,Bh).toggleSelected(t)}unselect(t){oc(this,Bh).unselect(t)}pointerup(t){var e;const{isMac:s}=ud.platform;if(0!==t.button||t.ctrlKey&&s)return;if(t.target!==this.div)return;if(!oc(this,Rh))return;if(hc(this,Rh,!1),(null==(e=oc(this,Hh,Gh))?void 0:e.isDrawer)&&oc(this,Hh,Gh).supportMultipleDrawings)return;if(!oc(this,kh))return void hc(this,kh,!0);const i=oc(this,Bh).getMode();i!==kc.STAMP&&i!==kc.SIGNATURE?this.createAndAddNewEditor(t,!1):oc(this,Bh).unselectAll()}pointerdown(t){var e;if(oc(this,Bh).getMode()===kc.HIGHLIGHT&&this.enableTextSelection(),oc(this,Rh))return void hc(this,Rh,!1);const{isMac:s}=ud.platform;if(0!==t.button||t.ctrlKey&&s)return;if(t.target!==this.div)return;if(hc(this,Rh,!0),null==(e=oc(this,Hh,Gh))?void 0:e.isDrawer)return void this.startDrawingSession(t);const i=oc(this,Bh).getActive();hc(this,kh,!i||i.isEmpty())}startDrawingSession(t){if(this.div.focus({preventScroll:!0}),oc(this,Lh))return void oc(this,Hh,Gh).startDrawing(this,oc(this,Bh),!1,t);oc(this,Bh).setCurrentDrawingSession(this),hc(this,Lh,new AbortController);const e=oc(this,Bh).combinedSignal(oc(this,Lh));this.div.addEventListener("blur",({relatedTarget:t})=>{t&&!this.div.contains(t)&&(hc(this,Dh,null),this.commitOrRemove())},{signal:e}),oc(this,Hh,Gh).startDrawing(this,oc(this,Bh),!1,t)}pause(t){if(t){const{activeElement:t}=document;return void(this.div.contains(t)&&hc(this,Dh,t))}oc(this,Dh)&&setTimeout(()=>{var t;null==(t=oc(this,Dh))||t.focus(),hc(this,Dh,null)},0)}endDrawingSession(t=!1){return oc(this,Lh)?(oc(this,Bh).setCurrentDrawingSession(null),oc(this,Lh).abort(),hc(this,Lh,null),hc(this,Dh,null),oc(this,Hh,Gh).endDrawing(t)):null}findNewParent(t,e,s){const i=oc(this,Bh).findParent(e,s);return null!==i&&i!==this&&(i.changeParent(t),!0)}commitOrRemove(){return!!oc(this,Lh)&&(this.endDrawingSession(),!0)}onScaleChanging(){oc(this,Lh)&&oc(this,Hh,Gh).onScaleChangingWhenDrawing(this)}destroy(){var t,e;this.commitOrRemove(),(null==(t=oc(this,Bh).getActive())?void 0:t.parent)===this&&(oc(this,Bh).commitOrRemove(),oc(this,Bh).setActiveEditor(null)),oc(this,Ch)&&(clearTimeout(oc(this,Ch)),hc(this,Ch,null));for(const s of oc(this,Th).values())null==(e=oc(this,Sh))||e.removePointerInTextLayer(s.contentDiv),s.setParent(null),s.isAttachedToDOM=!1,s.div.remove();this.div=null,oc(this,Th).clear(),oc(this,Bh).removeLayer(this)}render({viewport:t}){this.viewport=t,Hd(this.div,t);for(const e of oc(this,Bh).getEditors(this.pageIndex))this.add(e),e.rebuild();this.updateMode()}update({viewport:t}){oc(this,Bh).commitOrRemove(),cc(this,Hh,Vh).call(this);const e=this.viewport.rotation,s=t.rotation;if(this.viewport=t,Hd(this.div,{rotation:s}),e!==s)for(const i of oc(this,Th).values())i.rotate(s)}get pageDimensions(){const{pageWidth:t,pageHeight:e}=this.viewport.rawDims;return[t,e]}get scale(){return oc(this,Bh).viewParameters.realScale}};Sh=new WeakMap,kh=new WeakMap,Mh=new WeakMap,Eh=new WeakMap,Ch=new WeakMap,Th=new WeakMap,Rh=new WeakMap,Ph=new WeakMap,Ih=new WeakMap,Lh=new WeakMap,Dh=new WeakMap,Nh=new WeakMap,Fh=new WeakMap,Wh=new WeakMap,Oh=new WeakMap,Bh=new WeakMap,$h=new WeakMap,Hh=new WeakSet,zh=function(t){oc(this,Bh).unselectAll();const{target:e}=t;if(e===oc(this,Nh).div||("img"===e.getAttribute("role")||e.classList.contains("endOfContent"))&&oc(this,Nh).div.contains(e)){const{isMac:e}=ud.platform;if(0!==t.button||t.ctrlKey&&e)return;oc(this,Bh).showAllEditors("highlight",!0,!0),oc(this,Nh).div.classList.add("free"),this.toggleDrawing(),wf.startHighlighting(this,"ltr"===oc(this,Bh).direction,{target:oc(this,Nh).div,x:t.x,y:t.y}),oc(this,Nh).div.addEventListener("pointerup",()=>{oc(this,Nh).div.classList.remove("free"),this.toggleDrawing(!0)},{once:!0,signal:oc(this,Bh)._signal}),t.preventDefault()}},Gh=function(){return oc(Df,$h).get(oc(this,Bh).getMode())},jh=function(t){const e=oc(this,Hh,Gh);return e?new e.prototype.constructor(t):null},Uh=function(){const{x:t,y:e,width:s,height:i}=this.div.getBoundingClientRect(),n=Math.max(0,t),a=Math.max(0,e),r=(n+Math.min(window.innerWidth,t+s))/2-t,o=(a+Math.min(window.innerHeight,e+i))/2-e,[l,h]=this.viewport.rotation%180==0?[r,o]:[o,r];return{offsetX:l,offsetY:h}},Vh=function(){for(const t of oc(this,Th).values())t.isEmpty()&&t.remove()},ac(Df,"_initialized",!1),lc(Df,$h,new Map([rf,Mf,Lf,wf,If].map(t=>[t._editorType,t])));let Nf=Df;const Ff=class t{constructor({pageIndex:t}){lc(this,Zh),lc(this,qh,null),lc(this,Xh,new Map),lc(this,Yh,new Map),this.pageIndex=t}setParent(t){if(oc(this,qh)){if(oc(this,qh)!==t){if(oc(this,Xh).size>0)for(const e of oc(this,Xh).values())e.remove(),t.append(e);hc(this,qh,t)}}else hc(this,qh,t)}static get _svgFactory(){return sd(this,"_svgFactory",new Mg)}draw(e,s=!1,i=!1){const n=dc(t,Kh)._++,a=cc(this,Zh,tc).call(this),r=t._svgFactory.createElement("defs");a.append(r);const o=t._svgFactory.createElement("path");r.append(o);const l=`path_p${this.pageIndex}_${n}`;o.setAttribute("id",l),o.setAttribute("vector-effect","non-scaling-stroke"),s&&oc(this,Yh).set(n,o);const h=i?cc(this,Zh,ec).call(this,r,l):null,c=t._svgFactory.createElement("use");return a.append(c),c.setAttribute("href",`#${l}`),this.updateProperties(a,e),oc(this,Xh).set(n,a),{id:n,clipPathId:`url(#${h})`}}drawOutline(e,s){const i=dc(t,Kh)._++,n=cc(this,Zh,tc).call(this),a=t._svgFactory.createElement("defs");n.append(a);const r=t._svgFactory.createElement("path");a.append(r);const o=`path_p${this.pageIndex}_${i}`;let l;if(r.setAttribute("id",o),r.setAttribute("vector-effect","non-scaling-stroke"),s){const e=t._svgFactory.createElement("mask");a.append(e),l=`mask_p${this.pageIndex}_${i}`,e.setAttribute("id",l),e.setAttribute("maskUnits","objectBoundingBox");const s=t._svgFactory.createElement("rect");e.append(s),s.setAttribute("width","1"),s.setAttribute("height","1"),s.setAttribute("fill","white");const n=t._svgFactory.createElement("use");e.append(n),n.setAttribute("href",`#${o}`),n.setAttribute("stroke","none"),n.setAttribute("fill","black"),n.setAttribute("fill-rule","nonzero"),n.classList.add("mask")}const h=t._svgFactory.createElement("use");n.append(h),h.setAttribute("href",`#${o}`),l&&h.setAttribute("mask",`url(#${l})`);const c=h.cloneNode();return n.append(c),h.classList.add("mainOutline"),c.classList.add("secondaryOutline"),this.updateProperties(n,e),oc(this,Xh).set(i,n),i}finalizeDraw(t,e){oc(this,Yh).delete(t),this.updateProperties(t,e)}updateProperties(e,s){var i;if(!s)return;const{root:n,bbox:a,rootClass:r,path:o}=s,l="number"==typeof e?oc(this,Xh).get(e):e;if(l){if(n&&cc(this,Zh,sc).call(this,l,n),a&&cc(i=t,Qh,Jh).call(i,l,a),r){const{classList:t}=l;for(const[e,s]of Object.entries(r))t.toggle(e,s)}if(o){const t=l.firstChild.firstChild;cc(this,Zh,sc).call(this,t,o)}}}updateParent(t,e){if(e===this)return;const s=oc(this,Xh).get(t);s&&(oc(e,qh).append(s),oc(this,Xh).delete(t),oc(e,Xh).set(t,s))}remove(t){oc(this,Yh).delete(t),null!==oc(this,qh)&&(oc(this,Xh).get(t).remove(),oc(this,Xh).delete(t))}destroy(){hc(this,qh,null);for(const t of oc(this,Xh).values())t.remove();oc(this,Xh).clear(),oc(this,Yh).clear()}};qh=new WeakMap,Xh=new WeakMap,Yh=new WeakMap,Kh=new WeakMap,Qh=new WeakSet,Jh=function(t,[e,s,i,n]){const{style:a}=t;a.top=100*s+"%",a.left=100*e+"%",a.width=100*i+"%",a.height=100*n+"%"},Zh=new WeakSet,tc=function(){const t=Ff._svgFactory.create(1,1,!0);return oc(this,qh).append(t),t.setAttribute("aria-hidden",!0),t},ec=function(t,e){const s=Ff._svgFactory.createElement("clipPath");t.append(s);const i=`clip_${e}`;s.setAttribute("id",i),s.setAttribute("clipPathUnits","objectBoundingBox");const n=Ff._svgFactory.createElement("use");return s.append(n),n.setAttribute("href",`#${e}`),n.classList.add("clip"),i},sc=function(t,e){for(const[s,i]of Object.entries(e))null===i?t.removeAttribute(s):t.setAttribute(s,i)},lc(Ff,Qh),lc(Ff,Kh,0);let Wf=Ff;globalThis._pdfjsTestingUtils={HighlightOutliner:df},globalThis.pdfjsLib={AbortException:hd,AnnotationEditorLayer:Nf,AnnotationEditorParamsType:Mc,AnnotationEditorType:kc,AnnotationEditorUIManager:su,AnnotationLayer:sf,AnnotationMode:xc,AnnotationType:Nc,build:_g,ColorPicker:mf,createValidAbsoluteUrl:td,DOMSVGFactory:Mg,DrawLayer:Wf,FeatureTest:ud,fetchData:kd,getDocument:lg,getFilenameFromUrl:Rd,getPdfFilenameFromUrl:Pd,getUuid:wd,getXfaPageViewport:Wd,GlobalWorkerOptions:Rp,ImageKind:Dc,InvalidPDFException:rd,isDataScheme:Cd,isPdfFile:Td,isValidExplicitDest:_u,MathClamp:Ad,noContextMenu:Dd,normalizeUnicode:vd,OPS:zc,OutputScale:zd,PasswordResponses:qc,PDFDataRangeTransport:dg,PDFDateString:Fd,PDFWorker:fg,PermissionFlag:Ec,PixelsPerInch:Sd,RenderingCancelledException:Ed,ResponseException:od,setLayerDimensions:Hd,shadow:sd,SignatureExtractor:Cf,stopEvent:Nd,SupportedImageMimeTypes:Gd,TextLayer:rg,TouchManager:ru,updateUrlHash:ed,Util:gd,VerbosityLevel:Hc,version:Ag,XfaLayer:Eg};export{hd as AbortException,Nf as AnnotationEditorLayer,Mc as AnnotationEditorParamsType,kc as AnnotationEditorType,su as AnnotationEditorUIManager,sf as AnnotationLayer,xc as AnnotationMode,Nc as AnnotationType,mf as ColorPicker,Mg as DOMSVGFactory,Wf as DrawLayer,ud as FeatureTest,Rp as GlobalWorkerOptions,Dc as ImageKind,rd as InvalidPDFException,Ad as MathClamp,zc as OPS,zd as OutputScale,dg as PDFDataRangeTransport,Fd as PDFDateString,fg as PDFWorker,qc as PasswordResponses,Ec as PermissionFlag,Sd as PixelsPerInch,Ed as RenderingCancelledException,od as ResponseException,Cf as SignatureExtractor,Gd as SupportedImageMimeTypes,rg as TextLayer,ru as TouchManager,gd as Util,Hc as VerbosityLevel,Eg as XfaLayer,_g as build,td as createValidAbsoluteUrl,kd as fetchData,lg as getDocument,Rd as getFilenameFromUrl,Pd as getPdfFilenameFromUrl,wd as getUuid,Wd as getXfaPageViewport,Cd as isDataScheme,Td as isPdfFile,_u as isValidExplicitDest,Dd as noContextMenu,vd as normalizeUnicode,Hd as setLayerDimensions,sd as shadow,Nd as stopEvent,ed as updateUrlHash,Ag as version};
