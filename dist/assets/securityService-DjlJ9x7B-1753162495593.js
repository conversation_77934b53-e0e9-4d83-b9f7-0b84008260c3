let e={passwordPolicy:{minLength:8,requireUppercase:!0,requireLowercase:!0,requireNumbers:!0,requireSpecialChars:!1,passwordExpireDays:90,preventReuse:5,complexityScore:3},loginPolicy:{maxFailAttempts:5,lockoutDuration:30,sessionTimeout:120,allowMultipleLogin:!1,requireCaptcha:!0,captchaAfterFails:3,ipWhitelist:[],enableTwoFactor:!1,rememberMeDays:7},auditPolicy:{enableAudit:!0,auditLevel:"detailed",retentionDays:365,sensitiveOperations:["delete","export","import","user_create","role_modify"],realTimeAlert:!0,alertThreshold:10,exportAuditLogs:!0},accessControl:{workingHours:{enabled:!1,startTime:"08:00",endTime:"18:00",weekdays:[1,2,3,4,5],timezone:"Asia/Shanghai"},ipRestriction:{enabled:!1,allowedIPs:[],deniedIPs:[],allowPrivateIPs:!0},deviceControl:{enabled:!1,maxDevicesPerUser:3,requireDeviceApproval:!1,deviceSessionTimeout:24}},dataProtection:{enableEncryption:!0,encryptionLevel:"advanced",backupEncryption:!0,dataRetentionDays:2555,anonymizeData:!1,gdprCompliance:!0},threatDetection:{enableDetection:!0,suspiciousLoginDetection:!0,bruteForceDetection:!0,anomalyDetection:!1,alertEmail:"<EMAIL>",autoBlock:!0,blockDuration:60}},t=[{id:"1",type:"login_fail",severity:"medium",userId:"2",username:"test_user",ip:"***********00",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",description:"连续登录失败",details:{attempts:3,lastAttempt:"2024-01-15 10:30:00"},timestamp:"2024-01-15 10:30:00",resolved:!1},{id:"2",type:"suspicious_activity",severity:"high",userId:"3",username:"admin",ip:"***********",userAgent:"curl/7.68.0",description:"异常IP地址登录",details:{location:"未知地区",previousIP:"***********"},timestamp:"2024-01-15 09:15:00",resolved:!0,resolvedBy:"admin",resolvedAt:"2024-01-15 09:30:00"}];const i=e=>new Promise(t=>setTimeout(t,e)),s=e=>{const i={...e,id:Date.now().toString(),timestamp:(new Date).toISOString(),resolved:!1};t.unshift(i),t.length>1e3&&(t=t.slice(0,1e3)),console.log("Security Event:",i)},r={getSecurityConfig:async()=>(await i(500),{...e}),updateSecurityConfig:async t=>{await i(800),e={...t},s({type:"policy_violation",severity:"medium",ip:"127.0.0.1",userAgent:navigator.userAgent,description:"安全策略配置已更新",details:{configType:"security_policy",action:"update"}})},validatePasswordStrength:t=>{const i=[];let s=0;t.length>=e.passwordPolicy.minLength?s+=1:i.push(`密码长度至少需要${e.passwordPolicy.minLength}位`),e.passwordPolicy.requireUppercase&&/[A-Z]/.test(t)?s+=1:e.passwordPolicy.requireUppercase&&i.push("密码需要包含大写字母"),e.passwordPolicy.requireLowercase&&/[a-z]/.test(t)?s+=1:e.passwordPolicy.requireLowercase&&i.push("密码需要包含小写字母"),e.passwordPolicy.requireNumbers&&/\d/.test(t)?s+=1:e.passwordPolicy.requireNumbers&&i.push("密码需要包含数字"),e.passwordPolicy.requireSpecialChars&&/[!@#$%^&*(),.?":{}|<>]/.test(t)?s+=1:e.passwordPolicy.requireSpecialChars&&i.push("密码需要包含特殊字符");return{score:s,feedback:i,passed:s>=e.passwordPolicy.complexityScore}},checkIPAccess:t=>{const{ipRestriction:i}=e.accessControl;return!i.enabled||!i.deniedIPs.includes(t)&&(!(i.allowedIPs.length>0)||i.allowedIPs.includes(t))},checkWorkingHours:()=>{const{workingHours:t}=e.accessControl;if(!t.enabled)return!0;const i=new Date,s=i.getDay(),r=60*i.getHours()+i.getMinutes();if(!t.weekdays.includes(s))return!1;const[a,o]=t.startTime.split(":").map(Number),[n,l]=t.endTime.split(":").map(Number);return r>=60*a+o&&r<=60*n+l},logSecurityEvent:s,getSecurityEvents:async(e=1,s=20,r)=>{await i(300);let a=[...t];r&&(r.type&&(a=a.filter(e=>e.type===r.type)),r.severity&&(a=a.filter(e=>e.severity===r.severity)),void 0!==r.resolved&&(a=a.filter(e=>e.resolved===r.resolved)),r.startDate&&(a=a.filter(e=>e.timestamp>=r.startDate)),r.endDate&&(a=a.filter(e=>e.timestamp<=r.endDate)));const o=a.length,n=(e-1)*s;return{events:a.slice(n,n+s),total:o,page:e,pageSize:s}},resolveSecurityEvent:async(e,s)=>{await i(200);const r=t.findIndex(t=>t.id===e);-1!==r&&(t[r].resolved=!0,t[r].resolvedBy=s,t[r].resolvedAt=(new Date).toISOString())},getSecurityReport:async(e="7d")=>{await i(600);const s=new Date,r=new Date;switch(e){case"1d":r.setDate(s.getDate()-1);break;case"7d":default:r.setDate(s.getDate()-7);break;case"30d":r.setDate(s.getDate()-30)}const a=t.filter(e=>new Date(e.timestamp)>=r),o=a.length,n=a.filter(e=>"critical"===e.severity).length,l=a.filter(e=>"login_fail"===e.type).length,c=l,p=a.reduce((e,t)=>(e[t.type]=(e[t.type]||0)+1,e),{}),d=Object.entries(p).map(([e,t])=>({type:e,count:t,percentage:Math.round(t/o*100)})).sort((e,t)=>t.count-e.count).slice(0,5);return{period:e,totalEvents:o,criticalEvents:n,loginAttempts:l,failedLogins:c,blockedIPs:[],topThreats:d,recommendations:["建议启用双因素认证以提高账户安全性","定期检查和更新IP白名单配置","监控异常登录活动并及时处理","确保所有用户使用强密码","定期备份和测试安全配置"]}},testSecurityConfig:async()=>{await i(400);const t=[],s=[];e.passwordPolicy.minLength<8&&t.push("密码最小长度过短，建议至少8位"),e.passwordPolicy.requireNumbers||s.push("建议要求密码包含数字"),e.loginPolicy.maxFailAttempts>5&&t.push("最大失败次数过高，存在暴力破解风险"),e.loginPolicy.requireCaptcha||s.push("建议启用验证码以防止自动化攻击"),e.auditPolicy.enableAudit||t.push("未启用操作审计，无法追踪安全事件"),e.auditPolicy.retentionDays<90&&s.push("建议将审计日志保留期设置为至少90天");return{passed:0===t.length,issues:t,recommendations:s}}};export{r as s};
