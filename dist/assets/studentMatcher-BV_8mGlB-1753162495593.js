import{r as e}from"./index-BbdVriMF-1753162495593.js";const a=[{studentId:"2021001001",name:"张三",class:"计科2101",major:"计算机科学与技术",college:"计算机学院",phone:"13800138001",gender:"男",grade:"2021"},{studentId:"2021001002",name:"李四",class:"软工2101",major:"软件工程",college:"计算机学院",phone:"13800138002",gender:"女",grade:"2021"},{studentId:"2021001003",name:"王五",class:"数据2101",major:"数据科学与大数据技术",college:"计算机学院",phone:"13800138003",gender:"男",grade:"2021"},{studentId:"2021001004",name:"赵六",class:"AI2101",major:"人工智能",college:"计算机学院",phone:"13800138004",gender:"女",grade:"2021"},{studentId:"2022001001",name:"钱七",class:"计科2201",major:"计算机科学与技术",college:"计算机学院",phone:"13800138005",gender:"男",grade:"2022"},{studentId:"2022001002",name:"孙八",class:"软工2201",major:"软件工程",college:"计算机学院",phone:"13800138006",gender:"女",grade:"2022"},{studentId:"2023001001",name:"周九",class:"计科2301",major:"计算机科学与技术",college:"计算机学院",phone:"13800138007",gender:"男",grade:"2023"},{studentId:"2023001002",name:"吴十",class:"网工2301",major:"网络工程",college:"计算机学院",phone:"13800138008",gender:"女",grade:"2023"}],n=async(n,s,d,o)=>{const r={studentName:"studentName",class:"class",major:"major",college:"college",phone:"phone",gender:"gender",grade:"grade",email:"email",...d};if(!n){const e={};return Object.values(r).forEach(a=>{e[a]=""}),void s.setFieldsValue(e)}try{const d=await(async n=>{if(!n)return null;try{const a=await e.get(`/students?student_id=${encodeURIComponent(n)}`);if(a.success&&a.data.length>0){const e=a.data[0];return{studentId:e.student_id,name:e.name,class:e.class,major:e.major||"",college:e.college||"计算机学院",phone:e.phone||"",gender:e.gender||"",grade:e.grade||"",email:e.email||""}}}catch(d){console.warn("从后端API获取学生数据失败:",d)}const s=a.find(e=>e.studentId===n);return s?{...s,email:""}:null})(n);if(d){const e={};e[r.studentName]=d.name,e[r.class]=d.class,e[r.major]=d.major,e[r.college]=d.college,d.phone&&(e[r.phone]=d.phone),d.gender&&(e[r.gender]=d.gender),d.grade&&(e[r.grade]=d.grade),d.email&&(e[r.email]=d.email),s.setFieldsValue(e);const l=a.some(e=>e.studentId===n)?`已匹配到学生：${d.name}（使用默认数据）`:`已匹配到学生：${d.name}`;o&&o.success(l)}else{const e={};Object.values(r).forEach(a=>{e[a]=""}),s.setFieldsValue(e),o&&o.warning("未找到该学号对应的学生信息，请检查学号是否正确")}}catch(l){console.error("学号匹配错误:",l),o&&o.error("学号匹配失败，请重试")}};export{n as h};
