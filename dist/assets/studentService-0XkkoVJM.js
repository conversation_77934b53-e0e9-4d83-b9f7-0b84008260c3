import{r as e}from"./index-Cu_U9Dm3.js";const a=async a=>{if(console.log(`🔍 [getStudentInfo] 开始获取学生信息: ${a}`),console.log(`🔍 [getStudentInfo] 参数类型: ${typeof a}, 长度: ${null==a?void 0:a.length}`),!a||""===a.trim())return console.error("❌ [getStudentInfo] 学生ID为空"),null;try{console.log("🌐 [getStudentInfo] 尝试从后端 API 获取数据...");const t=await(async a=>{try{const t=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(a),o=t?`/students/by-id/${a}`:`/students/by-student-id/${a}`;console.log(`🌐 [getStudentById] 调用 API: ${o} (UUID: ${t})`);const n=await e.get(o);if(console.log("📡 [getStudentById] API 响应:",n),console.log("📡 [getStudentById] response.success:",n.success),console.log("📡 [getStudentById] response.data:",n.data),console.log("📡 [getStudentById] response.data 类型:",typeof n.data),n.success&&n.data){console.log(`✅ [getStudentById] API 返回成功，学生姓名: ${n.data.name}`),console.log("✅ [getStudentById] 家庭信息:",{fatherName:n.data.fatherName,motherName:n.data.motherName,homeAddress:n.data.homeAddress,bloodType:n.data.bloodType,healthStatus:n.data.healthStatus});const e={id:n.data.id,studentId:n.data.studentId,name:n.data.name,gender:n.data.gender,ethnicity:n.data.ethnicity,birthplace:n.data.birthplace,politicalStatus:n.data.politicalStatus,position:n.data.position,college:n.data.college,level:n.data.level||"本科",grade:n.data.grade,major:n.data.major,class:n.data.class,qq:n.data.qq,wechat:n.data.wechat,phone:n.data.phone,idCard:n.data.idCard,dormBuilding:n.data.dormBuilding,dormRoom:n.data.dormRoom,status:n.data.status,avatar:n.data.avatar,email:n.data.email,householdLocation:n.data.householdLocation||n.data.birthplace,homeAddress:n.data.homeAddress,fatherName:n.data.fatherName,fatherPhone:n.data.fatherPhone,fatherWork:n.data.fatherWork,motherName:n.data.motherName,motherPhone:n.data.motherPhone,motherWork:n.data.motherWork,parentsDisabled:n.data.parentsDisabled,singleParent:n.data.singleParent,guardianName:n.data.guardianName,guardianPhone:n.data.guardianPhone,guardianRelation:n.data.guardianRelation,familyMembers:n.data.familyMembers,familyIncome:n.data.familyIncome,economicStatus:n.data.economicStatus,tuitionSource:n.data.tuitionSource,martyrChild:1===n.data.isPoor?"是":"否",orphan:1===n.data.isOrphan?"是":"否",disabled:1===n.data.isDisabled?"是":"否",caregiverName:n.data.guardianName,caregiverRelation:n.data.guardianRelation,caregiverPhone:n.data.guardianPhone,orphanDescription:n.data.specialNotes,economicDifficulty:n.data.economicDifficulty,bloodType:n.data.bloodType,height:n.data.height,weight:n.data.weight,visionLeft:n.data.visionLeft,visionRight:n.data.visionRight,chronicDisease:n.data.chronicDisease,allergyHistory:n.data.allergyHistory,medication:n.data.medication,emergencyContact:n.data.emergencyContact,emergencyPhone:n.data.emergencyPhone,healthStatus:n.data.healthStatus,disciplinaryAction:n.data.disciplinaryAction,minorProgram:n.data.minorProgram,religiousBelief:n.data.religiousBelief,religiousActivity:n.data.religiousActivity,familyReligious:n.data.familyReligious,tuitionPaid:n.data.tuitionPaid,hasLoan:n.data.hasLoan,hasGrant:n.data.hasGrant,hasPassport:n.data.hasPassport,abroadExperience:n.data.abroadExperience,graduateProgram:n.data.graduateProgram,microStudyAbroad:n.data.microStudyAbroad,counselorName:n.data.counselorName,counselorPhone:n.data.counselorPhone,internationalEducation:n.data.internationalEducation,studyAbroad:n.data.studyAbroad,otherNotes:n.data.otherNotes,remarks:n.data.remarks,specialSkills:n.data.specialSkills,hobbies:n.data.hobbies,socialActivities:n.data.socialActivities,volunteerExperience:n.data.volunteerExperience,rewardRecords:n.data.rewardRecords||[],mentalHealthRecords:n.data.mentalHealthRecords||[],partyInfo:n.data.partyInfo};return console.log("🎯 [getStudentById] 返回完整的学生数据:",e),e}return console.log("⚠️ [getStudentById] API 返回空数据或失败"),console.log("⚠️ [getStudentById] response.success:",n.success),console.log("⚠️ [getStudentById] response.data:",n.data),null}catch(t){return console.error("💥 [getStudentById] API获取学生信息失败:",t),null}})(a);if(t)return console.log("✅ [getStudentInfo] 后端 API 获取成功，返回数据"),console.log("📋 [getStudentInfo] API 返回的数据:",{studentId:t.studentId,name:t.name,gender:t.gender,college:t.college,major:t.major,class:t.class,fatherName:t.fatherName,motherName:t.motherName,homeAddress:t.homeAddress}),console.log("🎯 [getStudentInfo] 直接返回API数据，不使用测试数据"),t;console.log("⚠️ [getStudentInfo] 后端 API 未找到数据，尝试本地存储...");const o=(e=>{try{console.log(`🔍 查找学号: ${e}`);const t=Object.keys(localStorage);console.log("🗂️ localStorage中的所有键:",t);const o=["studentsList","importedStudentsData","academicRecordsData","students","studentData","importedData","studentsInfo"];let n=!1,r=null,s=null,l=null;l=(e=>{const a=localStorage.getItem("academicRecordsData");if(a)try{const t=JSON.parse(a);if(console.log("🎓 检查学业数据:",t),t[e])return console.log(`✅ 找到学号 ${e} 的学业数据:`,t[e]),t[e]}catch(o){console.log("❌ 解析学业数据失败:",o)}const t=localStorage.getItem("academicRecordsList");if(t)try{const a=JSON.parse(t);if(console.log("🎓 检查学业列表:",a),Array.isArray(a)){const t=a.find(a=>a.studentId===e||a.学号===e);if(t)return console.log(`✅ 在学业列表中找到学号 ${e} 的数据:`,t),t}}catch(o){console.log("❌ 解析学业列表失败:",o)}return console.log(`❌ 未找到学号 ${e} 的学业数据`),null})(e);for(const m of o){const t=localStorage.getItem(m);if(t)try{const a=JSON.parse(t);if(console.log(`📂 检查 ${m}:`,typeof a,Array.isArray(a)?`数组(${a.length}项)`:`对象(${Object.keys(a).length}键)`),Array.isArray(a)){if(a.length>0){console.log(`📂 ${m} 数组示例:`,a[0]);const t=a.find(a=>a.studentId===e||a.id===e||a.学号===e||a["学号"]===e);t&&(console.log(`✅ 在 ${m} 中找到学生:`,t),m.includes("academic")||m.includes("Academic")?l=t:m.includes("imported")||m.includes("Imported")?s=t:r=t,n=!0)}}else if("object"==typeof a&&null!==a){const t=Object.values(a);if(t.length>0){console.log(`📂 ${m} 对象示例:`,t[0]);const a=t.find(a=>a&&(a.studentId===e||a.id===e||a.学号===e||a["学号"]===e));a&&(console.log(`✅ 在 ${m} 中找到学生:`,a),m.includes("academic")||m.includes("Academic")?l=a:m.includes("imported")||m.includes("Imported")?s=a:r=a,n=!0)}}}catch(a){console.log(`❌ 解析 ${m} 失败:`,a)}}if(!n){console.log("❌ 在所有数据源中都未找到学生数据"),console.log("🔍 完整localStorage内容:");for(const e of t){const t=localStorage.getItem(e);if(t&&t.length<2e3)try{const a=JSON.parse(t);console.log(`  ${e}:`,a)}catch(a){console.log(`  ${e}: [非JSON数据] ${t.substring(0,100)}...`)}else if(t){console.log(`  ${e}: [大数据，长度${t.length}]`);try{const e=JSON.parse(t);if(Array.isArray(e))console.log(`    -> 数组，${e.length}项，示例:`,e[0]);else if("object"==typeof e){console.log(`    -> 对象，${Object.keys(e).length}键，键名:`,Object.keys(e).slice(0,10));const a=Object.values(e)[0];console.log("    -> 第一个值:",a)}}catch(a){console.log("    -> 解析失败:",a.message)}}}return null}const i=r||{},d=s||{},c=(e,a,t="")=>{if(!e||"object"!=typeof e)return t;for(const o of a)if(void 0!==e[o]&&null!==e[o]&&""!==e[o])return e[o];return t};console.log("🔄 合并数据中..."),console.log("📊 基础数据:",i),console.log("📊 详细数据:",d),console.log("📊 学业数据:",l),console.log("🔑 基础数据类型:",typeof i,"是否为对象:",i&&"object"==typeof i),console.log("🔑 详细数据类型:",typeof d,"是否为对象:",d&&"object"==typeof d),i&&"object"==typeof i?(console.log("🔑 基础数据字段名:",Object.keys(i)),console.log("🔑 基础数据内容:",i)):console.log("🔑 基础数据为空或非对象:",i),d&&"object"==typeof d?(console.log("🔑 详细数据字段名:",Object.keys(d)),console.log("🔑 详细数据内容:",d)):console.log("🔑 详细数据为空或非对象:",d);const g={id:e,studentId:c(i,["studentId","id","学号","学生学号","学生编号"])||c(d,["studentId","id","学号","学生学号","学生编号"])||e,name:c(i,["name","姓名","学生姓名","真实姓名"])||c(d,["name","姓名","学生姓名","真实姓名"]),gender:c(i,["gender","性别"])||c(d,["gender","性别"]),ethnicity:c(d,["ethnicity","民族"]),birthplace:c(d,["birthplace","籍贯","出生地","生源地"]),politicalStatus:c(d,["politicalStatus","政治面貌","政治状况"]),position:c(d,["position","职务","学生干部","担任职务"]),college:c(d,["college","学院","所属学院","院系"])||c(i,["college","学院","所属学院","院系"])||"计算机学院",level:c(d,["level","层次","学历层次","培养层次"])||"本科",grade:c(i,["grade","年级","入学年份","年级班级"])||c(d,["grade","年级","入学年份","年级班级"]),major:c(i,["major","专业","专业名称","所学专业"])||c(d,["major","专业","专业名称","所学专业"]),class:c(i,["class","班级","班级名称","所在班级"])||c(d,["class","班级","班级名称","所在班级"]),qq:c(d,["qq","QQ","QQ号","QQ号码"]),wechat:c(d,["wechat","微信","微信号","微信号码"]),phone:c(i,["phone","电话","联系电话","手机号","手机号码","联系方式"])||c(d,["phone","电话","联系电话","手机号","手机号码","联系方式"]),idCard:c(d,["idCard","身份证号","身份证","身份证号码"]),dormBuilding:c(d,["dormBuilding","楼栋","宿舍楼","楼栋号"]),dormRoom:c(d,["dormRoom","宿舍号","房间号","宿舍"]),status:c(i,["status","状态","学籍状态"])||c(d,["status","状态","学籍状态"])||"active",avatar:c(i,["avatar","头像","照片"])||c(d,["avatar","头像","照片"]),email:c(d,["email","邮箱","电子邮箱","邮件地址"])||`${e}@example.com`,householdLocation:c(d,["householdLocation","户口所在地"]),homeAddress:c(d,["homeAddress","家庭住址","现家庭住址"]),fatherName:c(d,["fatherName","父亲姓名"]),fatherPhone:c(d,["fatherPhone","父亲电话","父亲联系方式"]),fatherWork:c(d,["fatherWork","父亲工作单位"]),motherName:c(d,["motherName","母亲姓名"]),motherPhone:c(d,["motherPhone","母亲电话","母亲联系方式"]),motherWork:c(d,["motherWork","母亲工作单位"]),parentsDisabled:c(d,["parentsDisabled","父母是否残疾"]),singleParent:c(d,["singleParent","是否单亲家庭"]),guardianName:c(d,["guardianName","监护人姓名"]),guardianRelation:c(d,["guardianRelation","监护人关系"]),familyMembers:c(d,["familyMembers","家庭成员"]),tuitionSource:c(d,["tuitionSource","学费来源"]),martyrChild:c(d,["martyrChild","是否烈士子女"]),orphan:c(d,["orphan","是否孤儿"]),caregiverName:c(d,["caregiverName","抚养人姓名"]),caregiverRelation:c(d,["caregiverRelation","抚养人关系"]),caregiverPhone:c(d,["caregiverPhone","抚养人电话"]),orphanDescription:c(d,["orphanDescription","孤儿情况说明"]),disabled:c(d,["disabled","是否残疾"]),disabilityType:c(d,["disabilityType","残疾类型"]),disabilityLevel:c(d,["disabilityLevel","残疾等级"]),disabilityDescription:c(d,["disabilityDescription","残疾情况说明"]),healthCondition:c(d,["healthCondition","健康状况","身体状况"]),dayStudent:c(d,["dayStudent","是否走读"]),academicStatus:c(d,["academicStatus","学业状况"]),disciplinaryAction:c(d,["disciplinaryAction","违纪情况"]),minorProgram:c(d,["minorProgram","是否辅修"]),academicFailedCourses:c(l,["failedCourses","挂科课程"]),academicCourseSemester:c(l,["courseSemester","挂科学期"]),academicRetakeRegistered:c(l,["retakeRegistered","是否报名重修"]),academicExpectedGraduation:c(l,["expectedGraduation","预计毕业时间"]),academicTuitionOwed:c(l,["tuitionOwed","是否欠费"]),academicOwedAmount:c(l,["owedAmount","欠费金额"]),academicCounselorContact:c(l,["counselorContact","辅导员联系方式"]),academicOtherSituation:c(l,["otherSituation","其他情况"]),religiousBelief:c(d,["religiousBelief","宗教信仰"]),religiousActivity:c(d,["religiousActivity","宗教活动"]),familyReligious:c(d,["familyReligious","家庭宗教背景"]),tuitionPaid:c(d,["tuitionPaid","学费缴纳情况"]),hasLoan:c(d,["hasLoan","是否贷款"]),hasGrant:c(d,["hasGrant","是否助学金"]),hasPassport:c(d,["hasPassport","是否有护照"]),abroadExperience:c(d,["abroadExperience","出国经历"]),graduateProgram:c(d,["graduateProgram","本硕直通"]),microStudyAbroad:c(d,["microStudyAbroad","微留学"]),counselorName:c(d,["counselorName","辅导员姓名"]),counselorPhone:c(d,["counselorPhone","辅导员电话"]),otherNotes:c(d,["otherNotes","其他说明","备注"]),remarks:c(d,["remarks","备注","说明"])};console.log("✅ 学生数据合并完成:",g.name||"未知姓名"),console.log("📋 最终结果:",g);const u=["studentId","name","gender","college","major","class"].filter(e=>!g[e]);if(u.length>0&&(console.warn("⚠️ 缺少关键字段:",u),u.includes("name")&&u.includes("gender")&&u.includes("major"))){console.log("🚨 关键字段缺失过多，创建测试数据以验证页面功能");const a={...g,name:g.name||`测试学生-${e}`,gender:g.gender||"男",major:g.major||"计算机科学与技术",class:g.class||"计科2101",college:g.college||"计算机学院",grade:g.grade||"2021",phone:g.phone||"13800138000",status:g.status||"active"};return console.log("🧪 返回测试数据:",a),a}return console.log("🎯 返回学生数据，字段数量:",Object.keys(g).length),g}catch(t){return console.error("从localStorage获取学生信息失败:",t),null}})(a);return console.log("📊 [getStudentInfo] 本地数据获取结果:",o?"成功":"失败"),o?(console.log("✅ [getStudentInfo] 本地存储获取成功，返回数据"),console.log("📋 [getStudentInfo] 返回的数据:",{studentId:o.studentId,name:o.name,gender:o.gender,college:o.college,major:o.major,class:o.class}),o):(console.log("❌ [getStudentInfo] 本地存储也未找到学生信息"),console.log("🚨 [getStudentInfo] 所有数据源都失败，返回null而不是测试数据"),null)}catch(t){return console.error("💥 [getStudentInfo] 获取学生信息时发生错误:",t),null}};export{a as g};
