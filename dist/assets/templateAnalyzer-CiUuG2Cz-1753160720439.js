var e=Object.defineProperty,n=(n,t,r)=>((n,t,r)=>t in n?e(n,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):n[t]=r)(n,"symbol"!=typeof t?t+"":t,r);import{A as t}from"./AdvancedDocumentParser-D0HhWqXv-1753160720439.js";class r{constructor(){n(this,"parser"),this.parser=t.getInstance()}async analyzeTemplate(e){try{console.log("开始分析模板文件:",e);const n=await this.loadTemplateFile(e),t=await this.parser.analyzeDocument(n),r=this.evaluateAnalysis(t);return{success:!0,analysis:t,recommendations:r.recommendations,formatScore:r.formatScore,variableScore:r.variableScore,overallScore:r.overallScore}}catch(n){return console.error("模板分析失败:",n),{success:!1,error:n.message,recommendations:["请检查模板文件是否存在且格式正确"],formatScore:0,variableScore:0,overallScore:0}}}async loadTemplateFile(e){try{const n=await fetch(e);if(!n.ok)throw new Error(`无法加载模板文件: ${n.status}`);const t=await n.blob(),r=e.split("/").pop()||"template.docx";return new File([t],r,{type:"application/vnd.openxmlformats-officedocument.wordprocessingml.document"})}catch(n){throw new Error(`模板文件加载失败: ${n.message}`)}}evaluateAnalysis(e){const n=[];let t=0,r=0;if(e.styles.length>0?(t+=30,n.push("✅ 检测到文档样式信息")):n.push("⚠️ 未检测到明确的样式信息，可能影响格式保持"),e.tables.length>0&&(t+=30,n.push(`✅ 检测到 ${e.tables.length} 个表格结构`)),e.paragraphs.length>0&&(t+=20,n.push("✅ 检测到段落格式信息")),e.htmlContent.length>1.2*e.content.length&&(t+=20,n.push("✅ 文档包含丰富的格式信息")),e.variables.length>0){r+=40,n.push(`✅ 检测到 ${e.variables.length} 个变量占位符`);const t=e.variables.filter(e=>e.confidence>.8);t.length>0&&(r+=30,n.push(`✅ ${t.length} 个变量具有高置信度映射`));e.variables.filter(e=>["studentName","studentId","class","major","college"].includes(e.suggestedMapping)).length>=3&&(r+=30,n.push("✅ 包含标准学生信息变量"))}else n.push("⚠️ 未检测到变量占位符，建议添加变量标记");const a=(t+r)/2;return a<60?n.push("🔧 建议优化模板格式和变量设置"):a<80?n.push("👍 模板质量良好，可进行小幅优化"):n.push("🎉 模板质量优秀，可直接使用"),{recommendations:n,formatScore:t,variableScore:r,overallScore:a}}generateReport(e){if(!e.success)return`\n# 模板分析报告\n\n## ❌ 分析失败\n错误信息: ${e.error}\n\n## 建议\n${e.recommendations.join("\n")}\n      `;const n=e.analysis;return`\n# 模板分析报告\n\n## 📊 评分概览\n- 格式质量: ${e.formatScore}/100\n- 变量质量: ${e.variableScore}/100\n- 综合评分: ${e.overallScore}/100\n\n## 📄 文档基本信息\n- 内容长度: ${n.content.length} 字符\n- HTML长度: ${n.htmlContent.length} 字符\n- 检测到的样式: ${n.styles.length} 个\n- 段落数量: ${n.paragraphs.length} 个\n- 表格数量: ${n.tables.length} 个\n- 变量数量: ${n.variables.length} 个\n\n## 🎨 格式分析\n${n.tables.map((e,n)=>`### 表格 ${n+1}\n- 行数: ${e.rows}\n- 列数: ${e.columns}\n- 边框: ${e.hasBorder?"有":"无"}\n- 边框样式: ${e.borderStyle||"无"}`).join("\n\n")}\n\n## 🔤 变量分析\n${n.variables.map((e,n)=>`### 变量 ${n+1}\n- 原文: ${e.text}\n- 建议映射: ${e.suggestedMapping}\n- 置信度: ${(100*e.confidence).toFixed(1)}%\n- 上下文: ${e.context.trim()}`).join("\n\n")}\n\n## 💡 建议与推荐\n${e.recommendations.join("\n")}\n\n## 📋 内容预览\n\`\`\`\n${n.content.substring(0,500)}${n.content.length>500?"...":""}\n\`\`\`\n    `}}export{r as T};
