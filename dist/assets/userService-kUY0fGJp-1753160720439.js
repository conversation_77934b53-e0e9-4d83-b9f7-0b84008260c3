let e=[{id:"1",username:"admin",realName:"系统管理员",email:"<EMAIL>",phone:"13800138000",role:"admin",department:"信息中心",status:"active",lastLogin:"2024-01-15 10:30:00",createdAt:"2024-01-01 00:00:00",updatedAt:"2024-01-01 00:00:00",permissions:["*"]},{id:"2",username:"counselor001",realName:"张辅导员",email:"<EMAIL>",phone:"13800138001",role:"counselor",department:"计算机学院",status:"active",lastLogin:"2024-01-15 09:15:00",createdAt:"2024-01-02 00:00:00",updatedAt:"2024-01-02 00:00:00",permissions:["student:read","student:write","class:read","class:write"]},{id:"3",username:"teacher001",realName:"李老师",email:"<EMAIL>",phone:"13800138002",role:"teacher",department:"计算机学院",status:"active",lastLogin:"2024-01-14 16:45:00",createdAt:"2024-01-03 00:00:00",updatedAt:"2024-01-03 00:00:00",permissions:["teacher:read","teacher:write"]},{id:"4",username:"party001",realName:"王专员",email:"<EMAIL>",phone:"13800138003",role:"party_secretary",department:"计算机学院",status:"active",lastLogin:"2024-01-14 14:20:00",createdAt:"2024-01-04 00:00:00",updatedAt:"2024-01-04 00:00:00",permissions:["party:read","party:write","student:read"]},{id:"5",username:"dean001",realName:"刘院长",email:"<EMAIL>",phone:"13800138004",role:"dean",department:"计算机学院",status:"active",lastLogin:"2024-01-13 11:30:00",createdAt:"2024-01-05 00:00:00",updatedAt:"2024-01-05 00:00:00",permissions:["student:read","teacher:read","class:read","academic:read"]}];const a={admin:["*"],counselor:["student:read","student:write","student:import","student:export","class:read","class:write","academic:read","academic:write","award:read","award:write","financial:read","financial:write","psychology:read","psychology:write","certificate:read","certificate:write","teacher:read","teacher:write"],teacher:["teacher:read","teacher:write","academic:read"],party_secretary:["party:read","party:write","party:delete","party:develop","student:read"],dean:["student:read","teacher:read","class:read","academic:read","award:read","financial:read","party:read","psychology:read","certificate:read","system:log"]},t=e=>new Promise(a=>setTimeout(a,e)),r=(e,a,t,r,n,s)=>{console.log("Operation Log:",{action:e,resource:a,resourceId:t,description:r,oldValue:n,newValue:s,timestamp:(new Date).toISOString()})},n=(e,a,t,r)=>{console.log("Login Log:",{userId:e,username:a,realName:t,success:r,timestamp:(new Date).toISOString(),ip:"127.0.0.1",userAgent:navigator.userAgent})},s=e=>a[e]||[],i={getUsers:async()=>(await t(500),[...e]),getUserById:async a=>{await t(300);const r=e.find(e=>e.id===a);return r?{...r}:null},getUserByUsername:async a=>{await t(300);const r=e.find(e=>e.username===a);return r?{...r}:null},createUser:async n=>{await t(800);if(e.find(e=>e.username===n.username))throw new Error("用户名已存在");if(e.find(e=>e.email===n.email))throw new Error("邮箱已存在");if(e.find(e=>e.phone===n.phone))throw new Error("手机号已存在");const s=(new Date).toISOString(),i={id:Date.now().toString(),username:n.username,realName:n.realName,email:n.email,phone:n.phone,role:n.role,department:n.department,status:n.status,createdAt:s,updatedAt:s,permissions:a[n.role]||[]};return e.push(i),r("create","user",i.id,`创建用户：${i.realName} (${i.username})`),{...i}},updateUser:async(n,s)=>{await t(600);const i=e.findIndex(e=>e.id===n);if(-1===i)throw new Error("用户不存在");const d={...e[i]};if(e.find(e=>e.email===s.email&&e.id!==n))throw new Error("邮箱已被其他用户使用");if(e.find(e=>e.phone===s.phone&&e.id!==n))throw new Error("手机号已被其他用户使用");const o={...e[i],realName:s.realName,email:s.email,phone:s.phone,role:s.role,department:s.department,status:s.status,permissions:a[s.role]||[],updatedAt:(new Date).toISOString()};return e[i]=o,r("update","user",n,`修改用户：${o.realName}`,d,o),{...o}},deleteUser:async a=>{await t(400);const n=e.findIndex(e=>e.id===a);if(-1===n)throw new Error("用户不存在");const s=e[n];if("admin"===s.role)throw new Error("不能删除管理员账户");e.splice(n,1),r("delete","user",a,`删除用户：${s.realName} (${s.username})`)},toggleUserStatus:async(a,n)=>{await t(300);const s=e.findIndex(e=>e.id===a);if(-1===s)throw new Error("用户不存在");e[s].status,e[s].status=n,e[s].updatedAt=(new Date).toISOString();return r("update","user",a,`${{active:"启用",inactive:"禁用",locked:"锁定"}[n]}用户：${e[s].realName}`),{...e[s]}},resetUserPassword:async(a,n)=>{await t(400);const s=e.findIndex(e=>e.id===a);if(-1===s)throw new Error("用户不存在");e[s].updatedAt=(new Date).toISOString(),r("update","user",a,`重置密码：${e[s].realName}`)},validateLogin:async(a,r)=>{await t(800);const s=e.find(e=>e.username===a);if(!s)return null;if("inactive"===s.status)throw new Error("账户已被禁用");if("locked"===s.status)throw new Error("账户已被锁定");if(!["123456",a,"password"].includes(r))return null;const i=e.findIndex(e=>e.id===s.id);return e[i].lastLogin=(new Date).toISOString(),n(s.id,s.username,s.realName,!0),{...e[i]}},getRolePermissions:s,getUserStats:async()=>{await t(200);return{total:e.length,active:e.filter(e=>"active"===e.status).length,inactive:e.filter(e=>"inactive"===e.status).length,locked:e.filter(e=>"locked"===e.status).length,roleStats:e.reduce((e,a)=>(e[a.role]=(e[a.role]||0)+1,e),{})}}};export{s as g,i as u};
