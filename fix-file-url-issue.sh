#!/bin/bash

echo "=== 修复文件URL端口问题脚本 ==="

# 设置变量
BACKEND_DIR="/www/wwwroot/xuegong-system/backend"
FRONTEND_DIR="/www/wwwroot/xuegong-system/dist"
LOG_DIR="/www/wwwroot/xuegong-system/log"

# 1. 停止所有相关服务
echo "1. 停止现有服务..."
pkill -f "node server.js" 2>/dev/null
pm2 delete all 2>/dev/null
sleep 3

# 2. 修复ecosystem.config.js文件
echo "2. 修复ecosystem.config.js配置..."
cd "$BACKEND_DIR"

# 备份原文件
cp ecosystem.config.js ecosystem.config.js.backup

# 创建修复后的ecosystem.config.js
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'xuegong-backend',
    script: 'server.js',
    cwd: '/www/wwwroot/xuegong-system/backend',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      PORT: 3002,
      DB_HOST: 'localhost',
      DB_PORT: 3306,
      DB_USER: 'root',
      DB_PASSWORD: '12345678',
      DB_NAME: 'xuegong',
      JWT_SECRET: 'xuegong_jwt_secret_key_2024_production_secure',
      JWT_EXPIRES_IN: '7d',
      FRONTEND_URL: 'http://***********:8085',
      UPLOAD_PATH: './uploads',
      MAX_FILE_SIZE: '10485760',
      LOG_LEVEL: 'info'
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3002,
      DB_HOST: 'localhost',
      DB_PORT: 3306,
      DB_USER: 'root',
      DB_PASSWORD: '12345678',
      DB_NAME: 'xuegong',
      JWT_SECRET: 'xuegong_jwt_secret_key_2024_production_secure',
      JWT_EXPIRES_IN: '7d',
      FRONTEND_URL: 'http://***********:8085',
      UPLOAD_PATH: './uploads',
      MAX_FILE_SIZE: '10485760',
      LOG_LEVEL: 'info'
    },
    error_file: '/www/wwwroot/xuegong-system/log/pm2-err.log',
    out_file: '/www/wwwroot/xuegong-system/log/pm2-out.log',
    log_file: '/www/wwwroot/xuegong-system/log/pm2-combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max_old_space_size=1024',
    watch: false,
    ignore_watch: ['node_modules', 'log', 'uploads'],
    restart_delay: 4000,
    max_restarts: 10,
    min_uptime: '10s'
  }]
};
EOF

echo "✅ ecosystem.config.js已修复"

# 3. 更新nginx配置
echo "3. 更新nginx配置..."
cat > /etc/nginx/conf.d/xuegong.conf << 'EOF'
server {
    listen 8085;
    server_name ***********;
    root /www/wwwroot/xuegong-system/dist;
    index index.html index.htm;
    
    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API代理到后端服务
    location /api/ {
        proxy_pass http://127.0.0.1:3002;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        client_max_body_size 10M;
    }
    
    # 文件上传和下载代理 - 关键修复
    location /uploads/ {
        proxy_pass http://127.0.0.1:3002/uploads/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_buffering off;
        proxy_request_buffering off;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 日志配置
    access_log /www/wwwroot/xuegong-system/log/access.log;
    error_log /www/wwwroot/xuegong-system/log/error.log;
}
EOF

# 4. 测试nginx配置
echo "4. 测试nginx配置..."
if nginx -t; then
    echo "✅ nginx配置正确"
    systemctl restart nginx
    echo "✅ nginx重启成功"
else
    echo "❌ nginx配置错误"
    nginx -t
    exit 1
fi

# 5. 修复数据库中的错误URL（如果存在）
echo "5. 检查并修复数据库中的文件URL..."
mysql -u root -p12345678 xuegong << 'SQL'
-- 修复teachers表中的证书URL
UPDATE teachers 
SET title_certificate = REPLACE(title_certificate, 'http://***********:8085/uploads/', '/uploads/') 
WHERE title_certificate LIKE '%***********:8085/uploads/%';

UPDATE teachers 
SET degree_certificate = REPLACE(degree_certificate, 'http://***********:8085/uploads/', '/uploads/') 
WHERE degree_certificate LIKE '%***********:8085/uploads/%';

-- 修复teacher_materials表中的文件URL
UPDATE teacher_materials 
SET file_url = REPLACE(file_url, 'http://***********:8085/uploads/', '/uploads/') 
WHERE file_url LIKE '%***********:8085/uploads/%';

-- 修复teacher_achievements表中的文件URL
UPDATE teacher_achievements 
SET file_url = REPLACE(file_url, 'http://***********:8085/uploads/', '/uploads/') 
WHERE file_url LIKE '%***********:8085/uploads/%';

-- 修复teacher_trainings表中的文件URL
UPDATE teacher_trainings 
SET certificate_url = REPLACE(certificate_url, 'http://***********:8085/uploads/', '/uploads/') 
WHERE certificate_url LIKE '%***********:8085/uploads/%';

-- 修复teacher_mentorships表中的文件URL
UPDATE teacher_mentorships 
SET file_url = REPLACE(file_url, 'http://***********:8085/uploads/', '/uploads/') 
WHERE file_url LIKE '%***********:8085/uploads/%';

-- 修复file_uploads表中的文件URL
UPDATE file_uploads 
SET file_path = REPLACE(file_path, 'http://***********:8085/uploads/', '/uploads/') 
WHERE file_path LIKE '%***********:8085/uploads/%';

SELECT 'Database URLs fixed' as status;
SQL

echo "✅ 数据库URL修复完成"

# 6. 启动后端服务
echo "6. 启动后端服务..."
cd "$BACKEND_DIR"
pm2 start ecosystem.config.js --env production

# 等待服务启动
sleep 5

# 7. 检查服务状态
echo "7. 检查服务状态..."
pm2 status
pm2 logs --lines 10

# 8. 测试文件访问
echo "8. 测试文件访问..."
echo "测试后端uploads直接访问:"
curl -s -I http://localhost:3002/uploads/ | head -1

echo "测试前端uploads代理访问:"
curl -s -I http://localhost:8085/uploads/ | head -1

echo ""
echo "=== 修复完成 ==="
echo "前端地址: http://***********:8085"
echo "后端地址: http://***********:3002"
echo ""
echo "现在教师证书文件应该能够正常访问了！"
echo "文件URL格式: http://***********:8085/uploads/filename"
echo "实际代理到: http://127.0.0.1:3002/uploads/filename"
