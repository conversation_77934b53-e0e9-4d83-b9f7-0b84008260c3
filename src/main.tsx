import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { Provider } from 'react-redux';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import App from './App';
import store from './store';
import ThemeProvider from './components/providers/ThemeProvider';
import './index.css';
import './styles/mobile.css';

// 引入控制台过滤器，过滤浏览器扩展相关的错误
import './utils/consoleFilter';

// 引入全局URL拦截器
import { interceptFileUrl } from './utils/fileUtils';

// 全局拦截window.open，修复8085端口问题
const originalWindowOpen = window.open;
window.open = function(url?: string | URL, target?: string, features?: string) {
  if (typeof url === 'string') {
    const fixedUrl = interceptFileUrl(url);
    if (url !== fixedUrl) {
      console.log('🌐 window.open 拦截修复:', url, '->', fixedUrl);
    }
    return originalWindowOpen.call(this, fixedUrl, target, features);
  }
  return originalWindowOpen.call(this, url, target, features);
};

// 拦截所有a标签的点击事件
document.addEventListener('click', (event) => {
  const target = event.target as HTMLElement;
  if (target.tagName === 'A') {
    const href = (target as HTMLAnchorElement).href;
    if (href && href.includes('8.130.81.46:8085')) {
      console.log('🔗 拦截a标签点击:', href);
      event.preventDefault();
      const fixedUrl = interceptFileUrl(href);
      window.open(fixedUrl, '_blank');
    }
  }
});

ReactDOM.createRoot(document.getElementById('root')!).render(
  <Provider store={store}>
    <ConfigProvider locale={zhCN}>
      <ThemeProvider>
        <BrowserRouter>
          <App />
        </BrowserRouter>
      </ThemeProvider>
    </ConfigProvider>
  </Provider>
);