import React, { useState, useEffect } from 'react';
import {
  Card, Button, Modal, Row, Col, Tabs, Form, Input, Select, DatePicker,
  Upload, message, Table, Tag, Space, Popconfirm, Divider, Spin
} from 'antd';
import {
  EyeOutlined, EditOutlined, PlusOutlined,
  UploadOutlined, FileTextOutlined
} from '@ant-design/icons';
import request from '../services/api';
import dayjs from 'dayjs';
import { getFileUrl } from '../utils/fileUtils';

const TeachingWorkFinal: React.FC = () => {
  // 基础状态
  const [teachers, setTeachers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  
  // 模态框状态
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any>(null);
  const [editingRecord, setEditingRecord] = useState<any>(null);
  
  // 表单实例
  const [editForm] = Form.useForm();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      setError('');
      
      const teachersResponse = await request.get('/teachers') as any;
      
      if (teachersResponse.data && teachersResponse.data.success && teachersResponse.data.data) {
        const teachersData = teachersResponse.data.data;
        setTeachers(teachersData);
        console.log('✅ 教师数据加载成功，数量:', teachersData.length);
      } else {
        setError('API响应格式错误');
        console.error('❌ API响应格式错误:', teachersResponse);
      }
    } catch (error: any) {
      console.error('❌ 加载教师数据失败:', error);
      setError('加载数据失败: ' + (error?.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  const handleView = (record: any) => {
    console.log('查看教师:', record);
    setSelectedRecord(record);
    setDetailModalVisible(true);
  };

  const handleEdit = (record: any) => {
    console.log('编辑教师:', record);
    setEditingRecord(record);
    
    // 填充表单数据
    editForm.setFieldsValue({
      name: record.name,
      employeeId: record.employee_id,
      gender: record.gender,
      idCard: record.id_card,
      ethnicity: record.ethnicity,
      politicalStatus: record.political_status,
      phone: record.phone,
      title: record.title,
      degree: record.degree,
      entryYear: record.join_year ? dayjs().year(record.join_year) : null,
      homeAddress: record.address,
      emergencyContact: record.emergency_contact,
      emergencyPhone: record.emergency_phone,
      resume: record.resume
    });
    
    setEditModalVisible(true);
  };

  const handleSubmit = async (values: any) => {
    try {
      const basicData = {
        employee_id: values.employeeId || `T${Date.now()}`,
        name: values.name || '未命名教师',
        gender: values.gender,
        id_card: values.idCard || null,
        ethnicity: values.ethnicity,
        political_status: values.politicalStatus,
        phone: values.phone,
        title: values.title,
        degree: values.degree || '',
        join_year: values.entryYear ? parseInt(values.entryYear.format('YYYY')) : new Date().getFullYear(),
        address: values.homeAddress || null,
        emergency_contact: values.emergencyContact || null,
        emergency_phone: values.emergencyPhone || null,
        resume: values.resume || '',
        status: 'active'
      };

      if (editingRecord?.id) {
        const response = await request.put(`/teachers/${editingRecord.id}`, basicData);
        message.success('教师信息更新成功');
      } else {
        const response = await request.post('/teachers', basicData);
        message.success('教师信息创建成功');
      }

      setEditModalVisible(false);
      setEditingRecord(null);
      editForm.resetFields();
      loadData();
    } catch (error: any) {
      console.error('提交失败:', error);
      message.error('操作失败: ' + (error?.message || '未知错误'));
    }
  };

  // 证书上传处理
  const handleCertificateUpload = async (file: any, certificateType: string) => {
    try {
      if (!selectedRecord?.id) {
        message.error('请先选择教师');
        return false;
      }

      const formData = new FormData();
      formData.append('file', file);
      formData.append('module', 'teacher_certificate');
      formData.append('reference_id', selectedRecord.id.toString());
      formData.append('certificate_type', certificateType);

      // 使用通用文件上传API
      const response = await request.post('/upload/single', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }) as any;

      console.log('上传响应:', response);

      if (response.data && response.data.success) {
        const fileUrl = response.data.data.url;

        // 更新教师的证书字段
        const updateData = {
          [certificateType]: fileUrl
        };

        const updateResponse = await request.put(`/teachers/${selectedRecord.id}`, updateData) as any;

        if (updateResponse.data && updateResponse.data.success) {
          message.success('证书上传成功');
          loadData(); // 重新加载数据
          setDetailModalVisible(false); // 关闭模态框
          return fileUrl;
        } else {
          message.error('证书信息保存失败');
          return false;
        }
      } else {
        message.error('证书上传失败');
        return false;
      }
    } catch (error: any) {
      console.error('证书上传失败:', error);
      message.error('证书上传失败: ' + (error?.message || '未知错误'));
      return false;
    }
  };

  // 证书删除处理
  const handleCertificateDelete = async (certificateType: string) => {
    try {
      if (!selectedRecord?.id) {
        message.error('请先选择教师');
        return;
      }

      const updateData = {
        [certificateType]: null
      };

      const response = await request.put(`/teachers/${selectedRecord.id}`, updateData) as any;
      if (response.data && response.data.success) {
        message.success('证书删除成功');
        loadData(); // 重新加载数据
        setDetailModalVisible(false); // 关闭模态框
      } else {
        message.error('证书删除失败');
      }
    } catch (error: any) {
      console.error('证书删除失败:', error);
      message.error('证书删除失败: ' + (error?.message || '未知错误'));
    }
  };

  // 证书查看
  const handleCertificateView = (url: string) => {
    if (url) {
      const correctedUrl = getFileUrl(url);
      window.open(correctedUrl, '_blank');
    } else {
      message.info('证书文件不存在');
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">教师业务档案</h1>
          <p className="text-gray-600 mt-1">管理教师基础信息和教学工作记录</p>
        </div>
        <Button 
          type="primary" 
          icon={<PlusOutlined />}
          onClick={() => handleEdit(null)}
        >
          新增教师
        </Button>
      </div>

      {/* 主要内容 */}
      <Tabs
        defaultActiveKey="basicInfo"
        items={[
          {
            key: 'basicInfo',
            label: '基础信息',
            children: (
              <Card title="教师列表管理">
                {loading ? (
                  <div className="text-center py-8">
                    <Spin size="large" />
                    <div className="mt-4">正在加载教师信息...</div>
                  </div>
                ) : (
                  <Table
                    dataSource={teachers}
                    rowKey="id"
                    pagination={{ pageSize: 10 }}
                    columns={[
                      {
                        title: '姓名',
                        dataIndex: 'name',
                        key: 'name',
                      },
                      {
                        title: '工号',
                        dataIndex: 'employee_id',
                        key: 'employee_id',
                      },
                      {
                        title: '性别',
                        dataIndex: 'gender',
                        key: 'gender',
                        render: (gender: string) => gender === 'male' ? '男' : '女',
                      },
                      {
                        title: '身份证号',
                        dataIndex: 'id_card',
                        key: 'id_card',
                        render: (idCard: string) => idCard || '未填写',
                      },
                      {
                        title: '职称',
                        dataIndex: 'title',
                        key: 'title',
                        render: (title: string) => title || '未填写',
                      },
                      {
                        title: '联系电话',
                        dataIndex: 'phone',
                        key: 'phone',
                        render: (phone: string) => phone || '未填写',
                      },
                      {
                        title: '状态',
                        dataIndex: 'status',
                        key: 'status',
                        render: (status: string) => (
                          <Tag color={status === 'active' ? 'green' : 'red'}>
                            {status === 'active' ? '在职' : '离职'}
                          </Tag>
                        ),
                      },
                      {
                        title: '操作',
                        key: 'action',
                        width: 150,
                        fixed: 'right',
                        render: (_, record) => (
                          <Space size="small">
                            <Button
                              type="text"
                              icon={<EyeOutlined />}
                              onClick={() => handleView(record)}
                            >
                              查看
                            </Button>
                            <Button
                              type="text"
                              icon={<EditOutlined />}
                              onClick={() => handleEdit(record)}
                            >
                              编辑
                            </Button>
                          </Space>
                        ),
                      },
                    ]}
                  />
                )}
              </Card>
            )
          },
          {
            key: 'statistics',
            label: '数据统计',
            children: (
              <Card title="教师数据统计">
                <div className="text-center py-8 text-gray-500">
                  数据统计功能开发中...
                </div>
              </Card>
            )
          }
        ]}
      />

      {/* 详情查看模态框 */}
      <Modal
        title={`教师详细信息 - ${selectedRecord?.name || ''}`}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={1200}
      >
        {selectedRecord && (
          <Tabs
            defaultActiveKey="basicInfo"
            items={[
              {
                key: 'basicInfo',
                label: '基础信息',
                children: (
                  <div className="space-y-6">
                {/* 基础信息 */}
                <div>
                  <h3 className="text-lg font-medium mb-4">基础信息</h3>
                  <Row gutter={16}>
                    <Col span={8}>
                      <div><strong>姓名:</strong> {selectedRecord.name || '未填写'}</div>
                    </Col>
                    <Col span={8}>
                      <div><strong>身份证号:</strong> {selectedRecord.id_card || '未填写'}</div>
                    </Col>
                    <Col span={8}>
                      <div><strong>性别:</strong> {selectedRecord.gender === 'male' ? '男' : selectedRecord.gender === 'female' ? '女' : '未填写'}</div>
                    </Col>
                  </Row>
                  <Row gutter={16} className="mt-4">
                    <Col span={8}>
                      <div><strong>民族:</strong> {selectedRecord.ethnicity || '未填写'}</div>
                    </Col>
                    <Col span={8}>
                      <div><strong>政治面貌:</strong> {selectedRecord.political_status || '未填写'}</div>
                    </Col>
                    <Col span={8}>
                      <div><strong>联系电话:</strong> {selectedRecord.phone || '未填写'}</div>
                    </Col>
                  </Row>
                  <Row gutter={16} className="mt-4">
                    <Col span={8}>
                      <div><strong>职称:</strong> {selectedRecord.title || '未填写'}</div>
                    </Col>
                    <Col span={8}>
                      <div><strong>学历:</strong> {selectedRecord.degree || '未填写'}</div>
                    </Col>
                    <Col span={8}>
                      <div><strong>入职年份:</strong> {selectedRecord.join_year || '未填写'}</div>
                    </Col>
                  </Row>
                  <Row gutter={16} className="mt-4">
                    <Col span={12}>
                      <div><strong>家庭住址:</strong> {selectedRecord.address || '未填写'}</div>
                    </Col>
                    <Col span={12}>
                      <div><strong>个人简历:</strong> {selectedRecord.resume || '未填写'}</div>
                    </Col>
                  </Row>
                  <Row gutter={16} className="mt-4">
                    <Col span={12}>
                      <div><strong>紧急联系人:</strong> {selectedRecord.emergency_contact || '未填写'}</div>
                    </Col>
                    <Col span={12}>
                      <div><strong>联系人电话:</strong> {selectedRecord.emergency_phone || '未填写'}</div>
                    </Col>
                  </Row>
                </div>

                {/* 证书管理 */}
                <Divider />
                <div>
                  <h3 className="text-lg font-medium mb-4">证书管理</h3>
                  <Row gutter={16}>
                    {/* 职称证书 */}
                    <Col span={6}>
                      <div className="text-center p-4 border rounded">
                        <FileTextOutlined className="text-2xl text-blue-500 mb-2" />
                        <div className="text-sm mb-2">职称证书</div>
                        {selectedRecord.title_cert ? (
                          <div>
                            <Button
                              type="link"
                              size="small"
                              onClick={() => handleCertificateView(selectedRecord.title_cert)}
                            >
                              查看
                            </Button>
                            <Popconfirm
                              title="确定要删除这个证书吗？"
                              onConfirm={() => handleCertificateDelete('title_cert')}
                              okText="确定"
                              cancelText="取消"
                            >
                              <Button type="link" size="small" danger>
                                删除
                              </Button>
                            </Popconfirm>
                          </div>
                        ) : (
                          <Upload
                            accept=".pdf,.jpg,.jpeg,.png"
                            showUploadList={false}
                            beforeUpload={(file) => {
                              handleCertificateUpload(file, 'title_cert');
                              return false;
                            }}
                          >
                            <Button size="small" icon={<UploadOutlined />}>
                              上传
                            </Button>
                          </Upload>
                        )}
                      </div>
                    </Col>

                    {/* 学位证书 */}
                    <Col span={6}>
                      <div className="text-center p-4 border rounded">
                        <FileTextOutlined className="text-2xl text-green-500 mb-2" />
                        <div className="text-sm mb-2">学位证书</div>
                        {selectedRecord.degree_cert ? (
                          <div>
                            <Button
                              type="link"
                              size="small"
                              onClick={() => handleCertificateView(selectedRecord.degree_cert)}
                            >
                              查看
                            </Button>
                            <Popconfirm
                              title="确定要删除这个证书吗？"
                              onConfirm={() => handleCertificateDelete('degree_cert')}
                              okText="确定"
                              cancelText="取消"
                            >
                              <Button type="link" size="small" danger>
                                删除
                              </Button>
                            </Popconfirm>
                          </div>
                        ) : (
                          <Upload
                            accept=".pdf,.jpg,.jpeg,.png"
                            showUploadList={false}
                            beforeUpload={(file) => {
                              handleCertificateUpload(file, 'degree_cert');
                              return false;
                            }}
                          >
                            <Button size="small" icon={<UploadOutlined />}>
                              上传
                            </Button>
                          </Upload>
                        )}
                      </div>
                    </Col>

                    {/* 毕业证书 */}
                    <Col span={6}>
                      <div className="text-center p-4 border rounded">
                        <FileTextOutlined className="text-2xl text-orange-500 mb-2" />
                        <div className="text-sm mb-2">毕业证书</div>
                        {selectedRecord.graduation_cert ? (
                          <div>
                            <Button
                              type="link"
                              size="small"
                              onClick={() => handleCertificateView(selectedRecord.graduation_cert)}
                            >
                              查看
                            </Button>
                            <Popconfirm
                              title="确定要删除这个证书吗？"
                              onConfirm={() => handleCertificateDelete('graduation_cert')}
                              okText="确定"
                              cancelText="取消"
                            >
                              <Button type="link" size="small" danger>
                                删除
                              </Button>
                            </Popconfirm>
                          </div>
                        ) : (
                          <Upload
                            accept=".pdf,.jpg,.jpeg,.png"
                            showUploadList={false}
                            beforeUpload={(file) => {
                              handleCertificateUpload(file, 'graduation_cert');
                              return false;
                            }}
                          >
                            <Button size="small" icon={<UploadOutlined />}>
                              上传
                            </Button>
                          </Upload>
                        )}
                      </div>
                    </Col>

                    {/* 技能证书 */}
                    <Col span={6}>
                      <div className="text-center p-4 border rounded">
                        <FileTextOutlined className="text-2xl text-purple-500 mb-2" />
                        <div className="text-sm mb-2">技能证书</div>
                        <Upload
                          accept=".pdf,.jpg,.jpeg,.png"
                          showUploadList={false}
                          beforeUpload={(file) => {
                            handleCertificateUpload(file, 'skill_certs');
                            return false;
                          }}
                        >
                          <Button size="small" icon={<UploadOutlined />}>
                            上传
                          </Button>
                        </Upload>
                        {selectedRecord.skill_certs && (
                          <div className="mt-2">
                            <Button
                              type="link"
                              size="small"
                              onClick={() => handleCertificateView(selectedRecord.skill_certs)}
                            >
                              查看证书
                            </Button>
                          </div>
                        )}
                      </div>
                    </Col>
                  </Row>
                </div>
                  </div>
                )
              },
              {
                key: 'courses',
                label: '任课情况',
                children: (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-medium">任课情况管理</h3>
                      <Button type="primary" icon={<PlusOutlined />}>
                        新增课程
                      </Button>
                    </div>
                    <div className="text-center py-8 text-gray-500">
                      任课情况功能开发中...
                    </div>
                  </div>
                )
              },
              {
                key: 'materials',
                label: '教学材料',
                children: (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-medium">教学材料管理</h3>
                      <Button type="primary" icon={<PlusOutlined />}>
                        上传材料
                      </Button>
                    </div>
                    <div className="text-center py-8 text-gray-500">
                      教学材料功能开发中...
                    </div>
                  </div>
                )
              },
              {
                key: 'achievements',
                label: '个人成果',
                children: (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-medium">个人成果管理</h3>
                      <Button type="primary" icon={<PlusOutlined />}>
                        新增成果
                      </Button>
                    </div>
                    <div className="text-center py-8 text-gray-500">
                      个人成果功能开发中...
                    </div>
                  </div>
                )
              },
              {
                key: 'trainings',
                label: '师资培训',
                children: (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-medium">师资培训管理</h3>
                      <Button type="primary" icon={<PlusOutlined />}>
                        新增培训
                      </Button>
                    </div>
                    <div className="text-center py-8 text-gray-500">
                      师资培训功能开发中...
                    </div>
                  </div>
                )
              },
              {
                key: 'mentorships',
                label: '青蓝筑梦',
                children: (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-medium">青蓝筑梦项目管理</h3>
                      <Button type="primary" icon={<PlusOutlined />}>
                        新增项目
                      </Button>
                    </div>
                    <div className="text-center py-8 text-gray-500">
                      青蓝筑梦功能开发中...
                    </div>
                  </div>
                )
              }
            ]}
          />
        )}
      </Modal>

      {/* 编辑模态框 */}
      <Modal
        title={editingRecord ? "编辑基础信息" : "新增教师"}
        open={editModalVisible}
        onOk={() => editForm.submit()}
        onCancel={() => {
          setEditModalVisible(false);
          setEditingRecord(null);
          editForm.resetFields();
        }}
        width={800}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="姓名"
                name="name"
                rules={[{ required: true, message: '请输入姓名' }]}
              >
                <Input placeholder="请输入姓名" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="工号"
                name="employeeId"
              >
                <Input placeholder="请输入工号" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="性别"
                name="gender"
                rules={[{ required: true, message: '请选择性别' }]}
              >
                <Select placeholder="请选择性别">
                  <Select.Option value="male">男</Select.Option>
                  <Select.Option value="female">女</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="身份证号"
                name="idCard"
                rules={[
                  { pattern: /^[1-9]\d{17}[0-9Xx]$/, message: '请输入正确的身份证号' }
                ]}
              >
                <Input placeholder="请输入身份证号" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="民族"
                name="ethnicity"
              >
                <Input placeholder="请输入民族" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="政治面貌"
                name="politicalStatus"
              >
                <Select placeholder="请选择政治面貌">
                  <Select.Option value="party_member">中共党员</Select.Option>
                  <Select.Option value="league_member">共青团员</Select.Option>
                  <Select.Option value="masses">群众</Select.Option>
                  <Select.Option value="other">其他</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="联系电话"
                name="phone"
                rules={[
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
                ]}
              >
                <Input placeholder="请输入联系电话" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="职称"
                name="title"
              >
                <Select placeholder="请选择职称">
                  <Select.Option value="professor">教授</Select.Option>
                  <Select.Option value="associate_professor">副教授</Select.Option>
                  <Select.Option value="lecturer">讲师</Select.Option>
                  <Select.Option value="assistant">助教</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="学历"
                name="degree"
              >
                <Select placeholder="请选择学历">
                  <Select.Option value="bachelor">本科</Select.Option>
                  <Select.Option value="master">硕士</Select.Option>
                  <Select.Option value="doctor">博士</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="入职年份"
                name="entryYear"
              >
                <DatePicker picker="year" placeholder="请选择入职年份" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={16}>
              <Form.Item
                label="家庭住址"
                name="homeAddress"
              >
                <Input placeholder="请输入家庭住址" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="紧急联系人"
                name="emergencyContact"
              >
                <Input placeholder="请输入紧急联系人" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="联系人电话"
                name="emergencyPhone"
                rules={[
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
                ]}
              >
                <Input placeholder="请输入联系人电话" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="个人简历"
            name="resume"
          >
            <Input.TextArea rows={4} placeholder="请输入个人简历" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TeachingWorkFinal;
