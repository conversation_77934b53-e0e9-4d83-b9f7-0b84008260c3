import React, { useState, useEffect } from 'react';
// 强制更新标记 - 2025-07-15 - v4 - 修复模态框显示完成
import {
  Card,
  Tabs,
  Button,
  Table,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Upload,
  message,
  Statistic,
  Row,
  Col,
  Progress,
  Tooltip,
  Badge,
  App,
  Spin,
  Empty,
  Divider
} from 'antd';
import request from '../services/api';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  UploadOutlined,
  DownloadOutlined,
  BookOutlined,
  TrophyOutlined,
  TeamOutlined,
  FileTextOutlined,
  BarChartOutlined,
  StarOutlined,
  UserOutlined,
} from '@ant-design/icons';
import type {
  CourseAssignment,
  TeachingMaterial,
  TeachingAchievement,
  TrainingRecord,
  MentorshipProject,
  TeachingStatistics,
} from '../types/teaching';
import dayjs from 'dayjs';
import { getFileUrl } from '../utils/fileUtils';

const { Option } = Select;
const { TextArea } = Input;
const { RangePicker } = DatePicker;

const TeachingWorkList: React.FC = () => {
  console.log('🔥 TeachingWorkList组件加载 - 强制更新测试');
  const [modal, contextHolder] = Modal.useModal();
  const [activeTab, setActiveTab] = useState('courses');
  const [loading, setLoading] = useState(false);

  // 数据状态
  const [basicInfo, setBasicInfo] = useState<any>(null);
  const [courseAssignments, setCourseAssignments] = useState<CourseAssignment[]>([]);
  const [teachingMaterials, setTeachingMaterials] = useState<TeachingMaterial[]>([]);
  const [achievements, setAchievements] = useState<TeachingAchievement[]>([]);
  const [trainings, setTrainings] = useState<TrainingRecord[]>([]);
  const [mentorships, setMentorships] = useState<MentorshipProject[]>([]);
  const [statistics, setStatistics] = useState<TeachingStatistics | null>(null);
  const [fileList, setFileList] = useState<any[]>([]);

  // 模态框状态
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any>(null);
  
  const [form] = Form.useForm();

  // 加载数据
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      console.log('正在从后端加载教师数据...');

      // 获取教师列表
      const teachersResponse = await request.get('/teachers');
      console.log('教师API响应:', teachersResponse);

      if (teachersResponse.success && teachersResponse.data && teachersResponse.data.length > 0) {
        // 使用第一个教师的数据作为当前教师
        const teacher = teachersResponse.data[0];
        console.log('加载的教师数据:', teacher);
        console.log('教师的关键字段:', {
          id_card: teacher.id_card,
          address: teacher.address,
          emergency_contact: teacher.emergency_contact,
          emergency_phone: teacher.emergency_phone
        });

        // 映射后端数据到前端格式
        const mappedBasicInfo = {
          id: teacher.id,
          name: teacher.name,
          idCard: teacher.id_card,
          gender: teacher.gender,
          ethnicity: teacher.ethnicity,
          politicalStatus: teacher.political_status,
          phone: teacher.phone,
          title: teacher.title,
          entryYear: teacher.join_year,
          homeAddress: teacher.address,
          emergencyContact: teacher.emergency_contact,
          emergencyPhone: teacher.emergency_phone,
          resume: teacher.resume || '',
          employeeId: teacher.employee_id,
          degree: teacher.degree,
          graduationCert: teacher.graduation_cert,
          degreeCert: teacher.degree_cert,
          titleCert: teacher.title_cert,
          skillCerts: teacher.skill_certs ? JSON.parse(teacher.skill_certs) : [],
          otherCerts: teacher.other_certs ? JSON.parse(teacher.other_certs) : [],
          status: teacher.status
        };

        console.log('映射后的基础信息:', mappedBasicInfo);
        console.log('映射后的关键字段:', {
          idCard: mappedBasicInfo.idCard,
          homeAddress: mappedBasicInfo.homeAddress,
          emergencyContact: mappedBasicInfo.emergencyContact,
          emergencyPhone: mappedBasicInfo.emergencyPhone
        });
        console.log('设置basicInfo状态...');
        setBasicInfo(mappedBasicInfo);
        console.log('basicInfo状态已设置');

        // 获取教学工作记录
        await loadTeachingWorkRecords(teacher.id);
      } else {
        // 如果没有教师数据，使用测试数据
        console.log('未找到教师数据，API响应:', teachersResponse);
        console.log('使用测试数据...');
        loadTestData();
      }
    } catch (error) {
      console.error('加载教师数据失败:', error);
      message.error('加载数据失败，使用测试数据');
      loadTestData();
    } finally {
      setLoading(false);
    }
  };

  // 加载教学工作记录
  const loadTeachingWorkRecords = async (teacherId: string) => {
    try {
      const response = await request.get(`/teachers/${teacherId}/work-records`);
      if (response.success) {
        const records = response.data;

        // 按类型分类记录
        const courseRecords = records.filter((r: any) => r.record_type === 'course_assignment');
        const materialRecords = records.filter((r: any) => r.record_type === 'teaching_material');
        const achievementRecords = records.filter((r: any) => r.record_type === 'achievement');
        const trainingRecords = records.filter((r: any) => r.record_type === 'training');
        const mentorshipRecords = records.filter((r: any) => r.record_type === 'mentorship');

        // 映射课程数据
        const mappedCourses = courseRecords.map((record: any) => ({
          id: record.id,
          teacherId: record.teacher_id,
          teacherName: basicInfo?.name || '',
          courseId: record.id,
          courseName: record.course_name || record.title,
          semester: record.semester || '',
          academicYear: record.semester ? record.semester.split('-')[0] : '',
          classIds: [],
          classNames: [],
          courseHours: record.hours || 0,
          courseType: 'theory',
          studentCount: record.students_count || 0,
          status: record.status === 'completed' ? 'completed' : 'active',
          createTime: record.created_at,
          updateTime: record.updated_at
        }));

        setCourseAssignments(mappedCourses);

        // 映射其他记录...
        // 这里可以继续映射其他类型的记录

        // 计算统计数据
        calculateStatistics(mappedCourses, [], [], [], []);
      }
    } catch (error) {
      console.error('加载教学工作记录失败:', error);
    }
  };

  // 保留原有的测试数据加载函数作为备用
  const loadTestData = () => {
    console.log('加载测试数据...');

    try {
    // 基础信息测试数据
    const savedBasicInfo = {
      id: 'teacher_001',
      name: '张教授',
      idCard: '110101198001011234',
      gender: 'male',
      ethnicity: '汉族',
      politicalStatus: 'party_member',
      phone: '13800138001',
      title: 'professor',
      entryYear: '2010',
      homeAddress: '北京市海淀区中关村大街1号',
      emergencyContact: '李女士',
      emergencyPhone: '13900139001',
      resume: '张教授，计算机科学与技术专业博士，教授职称。2010年入职本校，主要从事人工智能、机器学习等领域的教学和科研工作。',
        createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      };

      // 任课情况测试数据
      const savedCourses = [
        {
          id: 'course_001',
          courseName: '人工智能基础',
          courseType: 'theory',
          semester: 'spring',
          academicYear: '2023-2024',
          classNames: ['计算机2021-1班', '计算机2021-2班'],
          courseHours: 64,
          studentCount: 80,
          status: 'active',
          teacherId: 'teacher_001',
          teacherName: '张教授',
          createTime: dayjs().subtract(2, 'month').format('YYYY-MM-DD HH:mm:ss'),
          updateTime: dayjs().subtract(1, 'month').format('YYYY-MM-DD HH:mm:ss'),
        },
        {
          id: 'course_002',
          courseName: '机器学习实践',
          courseType: 'mixed',
          semester: 'fall',
          academicYear: '2023-2024',
          classNames: ['计算机2020-1班'],
          courseHours: 48,
          studentCount: 40,
          status: 'completed',
          teacherId: 'teacher_001',
          teacherName: '张教授',
          createTime: dayjs().subtract(6, 'month').format('YYYY-MM-DD HH:mm:ss'),
          updateTime: dayjs().subtract(3, 'month').format('YYYY-MM-DD HH:mm:ss'),
        }
      ];

      // 教学材料测试数据
      const savedMaterials = [
        {
          id: 'material_001',
          materialType: 'syllabus',
          materialName: '人工智能基础教学大纲',
          courseName: '人工智能基础',
          semester: 'spring',
          academicYear: '2023-2024',
          description: '人工智能基础课程的教学大纲，包含课程目标、教学内容、考核方式等',
          fileName: '人工智能基础教学大纲.pdf',
          fileSize: 1024000,
          teacherId: 'teacher_001',
          teacherName: '张教授',
          createTime: dayjs().subtract(1, 'month').format('YYYY-MM-DD HH:mm:ss'),
          updateTime: dayjs().subtract(1, 'month').format('YYYY-MM-DD HH:mm:ss'),
        },
        {
          id: 'material_002',
          materialType: 'calendar',
          materialName: '机器学习实践教学日历',
          courseName: '机器学习实践',
          semester: 'fall',
          academicYear: '2023-2024',
          description: '机器学习实践课程的教学日历安排',
          fileName: '机器学习实践教学日历.xlsx',
          fileSize: 512000,
          teacherId: 'teacher_001',
          teacherName: '张教授',
          createTime: dayjs().subtract(3, 'month').format('YYYY-MM-DD HH:mm:ss'),
          updateTime: dayjs().subtract(3, 'month').format('YYYY-MM-DD HH:mm:ss'),
        }
      ];

      // 个人成果测试数据
      const savedAchievements = [
        {
          id: 'achievement_001',
          achievementType: 'competition',
          level: 'provincial',
          title: '全国高校人工智能教学竞赛',
          description: '指导学生参加全国高校人工智能教学竞赛，获得省级一等奖',
          rank: '一等奖',
          organizer: '教育部高等学校计算机类专业教学指导委员会',
          awardTime: dayjs().subtract(6, 'month').format('YYYY-MM-DD'),
          points: 80,
          teacherId: 'teacher_001',
          teacherName: '张教授',
          createTime: dayjs().subtract(6, 'month').format('YYYY-MM-DD HH:mm:ss'),
          updateTime: dayjs().subtract(6, 'month').format('YYYY-MM-DD HH:mm:ss'),
        },
        {
          id: 'achievement_002',
          achievementType: 'paper',
          level: 'national',
          title: '基于深度学习的图像识别算法研究',
          description: '在国际顶级期刊发表关于深度学习图像识别的研究论文',
          rank: '第一作者',
          organizer: 'IEEE Transactions on Pattern Analysis and Machine Intelligence',
          awardTime: dayjs().subtract(1, 'year').format('YYYY-MM-DD'),
          points: 80,
          teacherId: 'teacher_001',
          teacherName: '张教授',
          createTime: dayjs().subtract(1, 'year').format('YYYY-MM-DD HH:mm:ss'),
          updateTime: dayjs().subtract(1, 'year').format('YYYY-MM-DD HH:mm:ss'),
        }
      ];

      // 师资培训测试数据
      const savedTrainings = [
        {
          id: 'training_001',
          trainingName: '高等学校人工智能教学能力提升培训',
          trainingType: 'academic',
          organizer: '教育部高等教育司',
          startDate: dayjs().subtract(3, 'month').format('YYYY-MM-DD'),
          endDate: dayjs().subtract(3, 'month').add(5, 'day').format('YYYY-MM-DD'),
          duration: 40,
          location: '北京大学',
          trainingContent: '本次培训主要内容包括：\n1. 人工智能教学理念与方法\n2. 深度学习课程设计\n3. 实践教学案例分析\n4. 教学评价与改进\n5. 前沿技术在教学中的应用',
          teacherId: 'teacher_001',
          teacherName: '张教授',
          createTime: dayjs().subtract(3, 'month').format('YYYY-MM-DD HH:mm:ss'),
          updateTime: dayjs().subtract(3, 'month').format('YYYY-MM-DD HH:mm:ss'),
        }
      ];

      // 青蓝筑梦项目测试数据
      const savedMentorships = [
        {
          id: 'mentorship_001',
          projectName: '青年教师教学能力提升项目',
          projectType: 'teaching',
          menteeName: '李老师',
          mentorName: '张教授',
          mentorId: 'teacher_001',
          menteeId: 'teacher_002',
          startDate: dayjs().subtract(6, 'month').format('YYYY-MM-DD'),
          endDate: dayjs().add(6, 'month').format('YYYY-MM-DD'),
          objectives: ['提升教学能力', '课程建设', '教学改革'],
          currentProgress: '项目进展顺利，已完成教学观摩、课程设计指导等环节，目前正在进行教学实践阶段。',
          progressPercentage: 60,
          status: 'active',
          milestones: [
            { title: '教学观摩', status: 'completed' },
            { title: '课程设计', status: 'completed' },
            { title: '教学实践', status: 'active' },
            { title: '成果总结', status: 'pending' }
          ],
          createTime: dayjs().subtract(6, 'month').format('YYYY-MM-DD HH:mm:ss'),
          updateTime: dayjs().subtract(1, 'week').format('YYYY-MM-DD HH:mm:ss'),
        }
      ];

      // 保存测试数据到localStorage
      localStorage.setItem('teacherBasicInfo', JSON.stringify(savedBasicInfo));
      localStorage.setItem('courseAssignments', JSON.stringify(savedCourses));
      localStorage.setItem('teachingMaterials', JSON.stringify(savedMaterials));
      localStorage.setItem('teachingAchievements', JSON.stringify(savedAchievements));
      localStorage.setItem('trainingRecords', JSON.stringify(savedTrainings));
      localStorage.setItem('mentorshipProjects', JSON.stringify(savedMentorships));

      console.log('测试数据已保存到localStorage');

      // 设置状态
      setBasicInfo(savedBasicInfo);
      setCourseAssignments(savedCourses);
      setTeachingMaterials(savedMaterials);
      setAchievements(savedAchievements);
      setTrainings(savedTrainings);
      setMentorships(savedMentorships);

      // 计算统计数据
      calculateStatistics(savedCourses, savedMaterials, savedAchievements, savedTrainings, savedMentorships);

      console.log('测试数据加载完成');
      message.success('张教授的测试数据已加载！');

    } catch (error) {
      console.error('加载测试数据失败:', error);
      message.error('加载测试数据失败');
    }
  };

  const calculateStatistics = (
    courses: CourseAssignment[],
    materials: TeachingMaterial[],
    achievements: TeachingAchievement[],
    trainings: TrainingRecord[],
    mentorships: MentorshipProject[]
  ) => {
    const stats: TeachingStatistics = {
      totalCourses: courses.length,
      totalHours: courses.reduce((sum, course) => sum + course.courseHours, 0),
      totalStudents: courses.reduce((sum, course) => sum + course.studentCount, 0),
      totalAchievements: achievements.length,
      totalPoints: achievements.reduce((sum, achievement) => sum + achievement.points, 0),
      trainingHours: trainings.reduce((sum, training) => sum + training.duration, 0),
      activeMentorships: mentorships.filter(m => m.status === 'active').length,
      monthlyData: generateMonthlyData(courses, achievements),
    };
    setStatistics(stats);
  };

  const generateMonthlyData = (courses: CourseAssignment[], achievements: TeachingAchievement[]) => {
    const months = [];
    for (let i = 5; i >= 0; i--) {
      const month = dayjs().subtract(i, 'month').format('YYYY-MM');
      months.push({
        month,
        courses: courses.filter(c => c.createTime.startsWith(month)).length,
        hours: courses.filter(c => c.createTime.startsWith(month)).reduce((sum, c) => sum + c.courseHours, 0),
        achievements: achievements.filter(a => a.createTime.startsWith(month)).length,
        points: achievements.filter(a => a.createTime.startsWith(month)).reduce((sum, a) => sum + a.points, 0),
      });
    }
    return months;
  };

  const handleAdd = () => {
    setEditingRecord(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleLoadTestData = () => {
    modal.confirm({
      title: '加载测试数据',
      content: '这将清除现有数据并加载张教授的测试数据，确定要继续吗？',
      onOk: () => {
        // 清除现有数据
        localStorage.removeItem('teacherBasicInfo');
        localStorage.removeItem('courseAssignments');
        localStorage.removeItem('teachingMaterials');
        localStorage.removeItem('teachingAchievements');
        localStorage.removeItem('trainingRecords');
        localStorage.removeItem('mentorshipProjects');

        // 重新加载数据（会触发测试数据生成）
        loadData();
        message.success('测试数据加载成功！');
      },
    });
  };

  const handleEdit = (record: any) => {
    setEditingRecord(record);
    form.setFieldsValue({
      ...record,
      startDate: record.startDate ? dayjs(record.startDate) : undefined,
      endDate: record.endDate ? dayjs(record.endDate) : undefined,
      awardTime: record.awardTime ? dayjs(record.awardTime) : undefined,
    });
    setModalVisible(true);
  };

  const handleDelete = (record: any) => {
    modal.confirm({
      title: '确认删除',
      content: '确定要删除这条记录吗？删除后无法恢复。',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          if (activeTab === 'basicInfo') {
            // 删除教师基础信息
            if (basicInfo?.id) {
              const response = await request.delete(`/teachers/${basicInfo.id}`);
              if (response.success) {
                setBasicInfo(null);
                message.success('基础信息删除成功');
              } else {
                message.error('删除失败');
              }
            }
          } else {
            // 删除教学工作记录
            const response = await request.delete(`/teachers/work-records/${record.id}`);
            if (response.success) {
              message.success('删除成功');
              if (basicInfo?.id) {
                await loadTeachingWorkRecords(basicInfo.id);
              }
            } else {
              message.error('删除失败');
            }
          }
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败');
        }
      },
    });
  };

  const handleView = (record: any) => {
    setSelectedRecord(record);
    setDetailModalVisible(true);
  };

  // 文件预览
  const handleFilePreview = (record: any) => {
    if (record.fileUrl) {
      const correctedUrl = getFileUrl(record.fileUrl);
      window.open(correctedUrl, '_blank');
    } else {
      message.info('文件预览功能开发中');
    }
  };

  // 文件下载
  const handleFileDownload = (record: any) => {
    if (record.fileUrl) {
      const correctedUrl = getFileUrl(record.fileUrl);
      const link = document.createElement('a');
      link.href = correctedUrl;
      link.download = record.fileName;
      link.click();
    } else {
      message.info('文件下载功能开发中');
    }
  };

  // 证书查看
  const handleCertificateView = (record: any) => {
    if (record.certificateUrl) {
      const correctedUrl = getFileUrl(record.certificateUrl);
      window.open(correctedUrl, '_blank');
    } else {
      message.info('证书查看功能开发中');
    }
  };

  // 处理文件上传
  const handleFileUpload = async (file: any) => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      // 调用后端文件上传API
      const response = await request.post('/teachers/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.success) {
        message.success('文件上传成功');
        return {
          name: response.data.originalname,
          url: response.data.url,
          size: response.data.size,
          filename: response.data.filename,
        };
      } else {
        message.error('文件上传失败');
        return null;
      }
    } catch (error) {
      console.error('文件上传错误:', error);
      message.error('文件上传失败');
      return null;
    }
  };

  // 处理批量文件上传
  const handleMultipleFileUpload = async (files: any[]) => {
    try {
      const formData = new FormData();
      files.forEach(file => {
        formData.append('files', file);
      });

      const response = await request.post('/teachers/upload-multiple', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.success) {
        message.success(`成功上传${files.length}个文件`);
        return response.data.map((file: any) => ({
          name: file.originalname,
          url: file.url,
          size: file.size,
          filename: file.filename,
        }));
      } else {
        message.error('批量文件上传失败');
        return [];
      }
    } catch (error) {
      console.error('批量文件上传错误:', error);
      message.error('批量文件上传失败');
      return [];
    }
  };

  // 文件预览
  const handleFilePreview = (file: any) => {
    if (file.url) {
      const correctedUrl = getFileUrl(file.url);
      window.open(correctedUrl, '_blank');
    } else {
      message.info('文件预览功能暂不可用');
    }
  };

  // 文件下载
  const handleFileDownload = (file: any) => {
    if (file.url) {
      const correctedUrl = getFileUrl(file.url);
      const link = document.createElement('a');
      link.href = correctedUrl;
      link.download = file.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      message.error('文件下载失败');
    }
  };

  // 导出档案
  const handleExportArchive = async () => {
    try {
      if (!basicInfo?.id) {
        message.error('请先录入基础信息');
        return;
      }

      message.loading('正在生成档案...', 0);

      // 调用后端导出API
      const response = await request.get(`/teachers/${basicInfo.id}/export`, {
        responseType: 'blob',
      });

      // 创建下载链接
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${basicInfo.name}_教师档案.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.destroy();
      message.success('档案导出成功');
    } catch (error) {
      message.destroy();
      console.error('导出档案错误:', error);
      message.error('档案导出失败');
    }
  };

  // 进度更新
  const handleProgressUpdate = (record: any) => {
    modal.confirm({
      title: '更新项目进度',
      content: (
        <div>
          <p>当前进度: {record.progressPercentage}%</p>
          <p>是否将进度更新为下一个里程碑？</p>
        </div>
      ),
      onOk: () => {
        const newProgress = Math.min(record.progressPercentage + 25, 100);
        const updatedRecord = { ...record, progressPercentage: newProgress };

        const newMentorships = mentorships.map(item =>
          item.id === record.id ? updatedRecord : item
        );
        setMentorships(newMentorships);
        localStorage.setItem('mentorshipProjects', JSON.stringify(newMentorships));

        message.success(`进度已更新为 ${newProgress}%`);
        loadData();
      },
    });
  };



  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      console.log('表单提交的原始数据:', values);

      if (activeTab === 'basicInfo') {
        // 处理基础信息
        const basicData = {
          employee_id: values.employeeId || `T${Date.now()}`,
          name: values.name,
          gender: values.gender,
          id_card: values.idCard || null,
          ethnicity: values.ethnicity,
          political_status: values.politicalStatus,
          phone: values.phone,
          title: values.title,
          degree: values.degree || '',
          graduation_cert: values.graduationCert || null,
          degree_cert: values.degreeCert || null,
          title_cert: values.titleCert || null,
          skill_certs: JSON.stringify(values.skillCerts || []),
          resume: values.resume || '',
          join_year: values.entryYear ? parseInt(values.entryYear.format('YYYY')) : new Date().getFullYear(),
          address: values.homeAddress || null,
          emergency_contact: values.emergencyContact || null,
          emergency_phone: values.emergencyPhone || null,
          other_certs: JSON.stringify(values.otherCerts || []),
          status: 'active'
        };

        console.log('准备发送到后端的数据:', basicData);
        console.log('editingRecord:', editingRecord);

        if (editingRecord?.id) {
          // 更新教师信息
          console.log('执行更新操作，ID:', editingRecord.id);
          const response = await request.put(`/teachers/${editingRecord.id}`, basicData);
          if (response.success) {
            message.success('基础信息更新成功');
            await loadData(); // 重新加载数据
          } else {
            message.error('更新失败');
          }
        } else {
          // 创建新教师
          const response = await request.post('/teachers', basicData);
          if (response.success) {
            message.success('基础信息录入成功');
            await loadData(); // 重新加载数据
          } else {
            message.error('录入失败');
          }
        }
      } else {
        // 处理教学工作记录
        await handleWorkRecordSubmit(values);
      }

      setModalVisible(false);
      setEditingRecord(null);
      form.resetFields();
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败，请重试');
    }
  };

  // 处理教学工作记录提交
  const handleWorkRecordSubmit = async (values: any) => {
    if (!basicInfo?.id) {
      message.error('请先录入基础信息');
      return;
    }

    const recordData = {
      teacher_id: basicInfo.id,
      record_type: getRecordType(activeTab),
      title: values.title || values.courseName || values.achievementTitle || values.trainingName || values.projectName,
      description: values.description || '',
      semester: values.semester || '',
      course_name: values.courseName || '',
      hours: values.courseHours || values.trainingHours || 0,
      students_count: values.studentCount || 0,
      materials: JSON.stringify(values.materials || {}),
      achievements: JSON.stringify(values.achievements || {}),
      training_info: JSON.stringify(values.trainingInfo || {}),
      mentorship_info: JSON.stringify(values.mentorshipInfo || {}),
      attachments: JSON.stringify(values.attachments || []),
      status: values.status || 'active'
    };

    try {
      if (editingRecord?.id) {
        // 更新记录
        const response = await request.put(`/teachers/work-records/${editingRecord.id}`, recordData);
        if (response.success) {
          message.success('记录更新成功');
          await loadTeachingWorkRecords(basicInfo.id);
        } else {
          message.error('更新失败');
        }
      } else {
        // 创建新记录
        const response = await request.post('/teachers/work-records', recordData);
        if (response.success) {
          message.success('记录添加成功');
          await loadTeachingWorkRecords(basicInfo.id);
        } else {
          message.error('添加失败');
        }
      }
    } catch (error) {
      console.error('保存教学工作记录失败:', error);
      message.error('保存失败');
    }
  };

  // 获取记录类型
  const getRecordType = (tab: string) => {
    const typeMap: { [key: string]: string } = {
      'courses': 'course_assignment',
      'materials': 'teaching_material',
      'achievements': 'achievement',
      'trainings': 'training',
      'mentorships': 'mentorship'
    };
    return typeMap[tab] || 'course_assignment';
  };



  const calculateAchievementPoints = (type: string, level: string): number => {
    const pointsMap: { [key: string]: { [key: string]: number } } = {
      competition: { national: 100, provincial: 80, municipal: 60, school: 40 },
      award: { national: 120, provincial: 100, municipal: 80, school: 60 },
      paper: { national: 80, provincial: 60, municipal: 40, school: 20 },
      textbook: { national: 150, provincial: 120, municipal: 100, school: 80 },
      patent: { national: 200, provincial: 150, municipal: 100, school: 80 },
      software: { national: 100, provincial: 80, municipal: 60, school: 40 },
    };
    return pointsMap[type]?.[level] || 0;
  };

  // 渲染表单内容
  const renderFormContent = () => {
    switch (activeTab) {
      case 'basicInfo':
        return (
          <>
            {/* 基本信息 */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-4 text-gray-800 border-b pb-2">基本信息</h3>
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    name="employeeId"
                    label="工号"
                    rules={[{ required: true, message: '请输入工号' }]}
                  >
                    <Input placeholder="请输入工号" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="name"
                    label="姓名"
                    rules={[{ required: true, message: '请输入姓名' }]}
                  >
                    <Input placeholder="请输入姓名" />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    name="idCard"
                    label="身份证号"
                    rules={[
                      { required: true, message: '请输入身份证号' },
                      { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的身份证号' }
                    ]}
                  >
                    <Input placeholder="请输入身份证号" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="gender"
                    label="性别"
                    rules={[{ required: true, message: '请选择性别' }]}
                  >
                    <Select placeholder="请选择性别">
                      <Option value="male">男</Option>
                      <Option value="female">女</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    name="ethnicity"
                    label="民族"
                    rules={[{ required: true, message: '请输入民族' }]}
                  >
                    <Input placeholder="请输入民族" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="politicalStatus"
                    label="政治面貌"
                    rules={[{ required: true, message: '请选择政治面貌' }]}
                  >
                    <Select placeholder="请选择政治面貌">
                      <Option value="party_member">中共党员</Option>
                      <Option value="probationary_member">中共预备党员</Option>
                      <Option value="league_member">共青团员</Option>
                      <Option value="democratic_party">民主党派</Option>
                      <Option value="masses">群众</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="phone"
                    label="本人电话"
                    rules={[
                      { required: true, message: '请输入电话号码' },
                      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
                    ]}
                  >
                    <Input placeholder="请输入电话号码" />
                  </Form.Item>
                </Col>
              </Row>
            </div>

            {/* 职业信息 */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-4 text-gray-800 border-b pb-2">职业信息</h3>
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    name="title"
                    label="职称"
                    rules={[{ required: true, message: '请选择职称' }]}
                  >
                    <Select placeholder="请选择职称">
                      <Option value="professor">教授</Option>
                      <Option value="associate_professor">副教授</Option>
                      <Option value="lecturer">讲师</Option>
                      <Option value="assistant">助教</Option>
                      <Option value="senior_engineer">高级工程师</Option>
                      <Option value="engineer">工程师</Option>
                      <Option value="assistant_engineer">助理工程师</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="entryYear"
                    label="入校年份"
                    rules={[{ required: true, message: '请选择入校年份' }]}
                  >
                    <DatePicker picker="year" placeholder="请选择入校年份" style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="homeAddress"
                    label="家庭住址"
                    rules={[{ required: true, message: '请输入家庭住址' }]}
                  >
                    <Input placeholder="请输入家庭住址" />
                  </Form.Item>
                </Col>
              </Row>
            </div>

            {/* 紧急联系人 */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-4 text-gray-800 border-b pb-2">紧急联系人</h3>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="emergencyContact"
                    label="紧急联系人"
                    rules={[{ required: true, message: '请输入紧急联系人姓名' }]}
                  >
                    <Input placeholder="请输入紧急联系人姓名" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="emergencyPhone"
                    label="联系人电话"
                    rules={[
                      { required: true, message: '请输入联系人电话' },
                      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
                    ]}
                  >
                    <Input placeholder="请输入联系人电话" />
                  </Form.Item>
                </Col>
              </Row>
            </div>

            {/* 证书材料 */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-4 text-gray-800 border-b pb-2">证书材料</h3>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="titleCertificate"
                    label="职称证"
                  >
                    <Upload
                      customRequest={async ({ file, onSuccess, onError }) => {
                        try {
                          const result = await handleFileUpload(file);
                          if (result) {
                            onSuccess?.(result);
                          } else {
                            onError?.(new Error('上传失败'));
                          }
                        } catch (error) {
                          onError?.(error as Error);
                        }
                      }}
                      maxCount={1}
                      accept=".pdf,.jpg,.jpeg,.png"
                      showUploadList={{
                        showPreviewIcon: true,
                        showDownloadIcon: true,
                        showRemoveIcon: true,
                      }}
                      onPreview={(file) => {
                        if (file.url) {
                          const correctedUrl = getFileUrl(file.url);
                          window.open(correctedUrl, '_blank');
                        }
                      }}
                    >
                      <Button icon={<UploadOutlined />}>上传职称证</Button>
                    </Upload>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="degreeCertificate"
                    label="学位证"
                  >
                    <Upload
                      customRequest={async ({ file, onSuccess, onError }) => {
                        try {
                          const result = await handleFileUpload(file);
                          if (result) {
                            onSuccess?.(result);
                          } else {
                            onError?.(new Error('上传失败'));
                          }
                        } catch (error) {
                          onError?.(error as Error);
                        }
                      }}
                      maxCount={1}
                      accept=".pdf,.jpg,.jpeg,.png"
                      showUploadList={{
                        showPreviewIcon: true,
                        showDownloadIcon: true,
                        showRemoveIcon: true,
                      }}
                      onPreview={(file) => {
                        if (file.url) {
                          const correctedUrl = getFileUrl(file.url);
                          window.open(correctedUrl, '_blank');
                        }
                      }}
                    >
                      <Button icon={<UploadOutlined />}>上传学位证</Button>
                    </Upload>
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="diplomaCertificate"
                    label="毕业证"
                  >
                    <Upload
                      customRequest={async ({ file, onSuccess, onError }) => {
                        try {
                          const result = await handleFileUpload(file);
                          if (result) {
                            onSuccess?.(result);
                          } else {
                            onError?.(new Error('上传失败'));
                          }
                        } catch (error) {
                          onError?.(error as Error);
                        }
                      }}
                      maxCount={1}
                      accept=".pdf,.jpg,.jpeg,.png"
                      showUploadList={{
                        showPreviewIcon: true,
                        showDownloadIcon: true,
                        showRemoveIcon: true,
                      }}
                      onPreview={(file) => {
                        if (file.url) {
                          const correctedUrl = getFileUrl(file.url);
                          window.open(correctedUrl, '_blank');
                        }
                      }}
                    >
                      <Button icon={<UploadOutlined />}>上传毕业证</Button>
                    </Upload>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="skillCertificates"
                    label="技能证书"
                  >
                    <Upload
                      customRequest={async ({ file, onSuccess, onError }) => {
                        try {
                          const result = await handleFileUpload(file);
                          if (result) {
                            onSuccess?.(result);
                          } else {
                            onError?.(new Error('上传失败'));
                          }
                        } catch (error) {
                          onError?.(error as Error);
                        }
                      }}
                      multiple
                      accept=".pdf,.jpg,.jpeg,.png"
                      showUploadList={{
                        showPreviewIcon: true,
                        showDownloadIcon: true,
                        showRemoveIcon: true,
                      }}
                      onPreview={(file) => {
                        if (file.url) {
                          const correctedUrl = getFileUrl(file.url);
                          window.open(correctedUrl, '_blank');
                        }
                      }}
                    >
                      <Button icon={<UploadOutlined />}>上传技能证书</Button>
                    </Upload>
                  </Form.Item>
                </Col>
              </Row>
              <Form.Item
                name="otherCertificates"
                label="其他证书"
              >
                <Upload
                  customRequest={async ({ file, onSuccess, onError }) => {
                    try {
                      const result = await handleFileUpload(file);
                      if (result) {
                        onSuccess?.(result);
                      } else {
                        onError?.(new Error('上传失败'));
                      }
                    } catch (error) {
                      onError?.(error as Error);
                    }
                  }}
                  multiple
                  accept=".pdf,.jpg,.jpeg,.png"
                  showUploadList={{
                    showPreviewIcon: true,
                    showDownloadIcon: true,
                    showRemoveIcon: true,
                  }}
                  onPreview={(file) => {
                    if (file.url) {
                      const correctedUrl = getFileUrl(file.url);
                      window.open(correctedUrl, '_blank');
                    }
                  }}
                >
                  <Button icon={<UploadOutlined />}>上传其他证书</Button>
                </Upload>
              </Form.Item>
            </div>

            {/* 个人简历 */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-4 text-gray-800 border-b pb-2">个人简历</h3>
              <Form.Item
                name="resume"
                label="个人简历"
                rules={[{ required: true, message: '请输入个人简历' }]}
              >
                <TextArea rows={6} placeholder="请输入个人简历，包括教育背景、工作经历、主要成就等" />
              </Form.Item>
            </div>
          </>
        );

      case 'courses':
        return (
          <>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="courseName"
                  label="课程名称"
                  rules={[{ required: true, message: '请输入课程名称' }]}
                >
                  <Input placeholder="请输入课程名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="courseType"
                  label="课程类型"
                  rules={[{ required: true, message: '请选择课程类型' }]}
                >
                  <Select placeholder="请选择课程类型">
                    <Option value="theory">理论课</Option>
                    <Option value="practice">实践课</Option>
                    <Option value="mixed">理实一体</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="semester"
                  label="学期"
                  rules={[{ required: true, message: '请选择学期' }]}
                >
                  <Select placeholder="请选择学期">
                    <Option value="spring">春季学期</Option>
                    <Option value="fall">秋季学期</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="academicYear"
                  label="学年"
                  rules={[{ required: true, message: '请输入学年' }]}
                >
                  <Input placeholder="如：2023-2024" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="courseHours"
                  label="课时数"
                  rules={[{ required: true, message: '请输入课时数' }]}
                >
                  <Input type="number" placeholder="请输入课时数" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="studentCount"
                  label="学生数"
                  rules={[{ required: true, message: '请输入学生数' }]}
                >
                  <Input type="number" placeholder="请输入学生数" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="classNames"
              label="授课班级"
              rules={[{ required: true, message: '请输入授课班级' }]}
            >
              <Select mode="tags" placeholder="请输入班级名称，支持多个班级">
                <Option value="计算机2021-1班">计算机2021-1班</Option>
                <Option value="计算机2021-2班">计算机2021-2班</Option>
                <Option value="软件工程2021-1班">软件工程2021-1班</Option>
              </Select>
            </Form.Item>
          </>
        );

      case 'materials':
        return (
          <>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="materialType"
                  label="材料类型"
                  rules={[{ required: true, message: '请选择材料类型' }]}
                >
                  <Select placeholder="请选择材料类型">
                    <Option value="calendar">教学日历</Option>
                    <Option value="progress">进度计划</Option>
                    <Option value="syllabus">教学大纲</Option>
                    <Option value="courseware">课件资料</Option>
                    <Option value="other">其他材料</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="courseName"
                  label="课程名称"
                  rules={[{ required: true, message: '请输入课程名称' }]}
                >
                  <Input placeholder="请输入课程名称" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="materialName"
              label="材料名称"
              rules={[{ required: true, message: '请输入材料名称' }]}
            >
              <Input placeholder="请输入材料名称" />
            </Form.Item>
            <Form.Item
              name="description"
              label="材料描述"
              rules={[{ required: true, message: '请输入材料描述' }]}
            >
              <TextArea rows={3} placeholder="请输入材料描述" />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="semester"
                  label="学期"
                  rules={[{ required: true, message: '请选择学期' }]}
                >
                  <Select placeholder="请选择学期">
                    <Option value="spring">春季学期</Option>
                    <Option value="fall">秋季学期</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="academicYear"
                  label="学年"
                  rules={[{ required: true, message: '请输入学年' }]}
                >
                  <Input placeholder="如：2023-2024" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="file"
              label="上传文件"
              rules={[{ required: !editingRecord, message: '请上传文件' }]}
            >
              <Upload
                customRequest={async ({ file, onSuccess, onError }) => {
                  try {
                    const result = await handleFileUpload(file);
                    if (result) {
                      onSuccess?.(result);
                    } else {
                      onError?.(new Error('上传失败'));
                    }
                  } catch (error) {
                    onError?.(error as Error);
                  }
                }}
                maxCount={1}
                accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx"
                showUploadList={{
                  showPreviewIcon: true,
                  showDownloadIcon: true,
                  showRemoveIcon: true,
                }}
                onPreview={(file) => {
                  if (file.url) {
                    const correctedUrl = getFileUrl(file.url);
                    window.open(correctedUrl, '_blank');
                  }
                }}
              >
                <Button icon={<UploadOutlined />}>选择文件</Button>
              </Upload>
            </Form.Item>
          </>
        );

      case 'achievements':
        return (
          <>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="achievementType"
                  label="成果类型"
                  rules={[{ required: true, message: '请选择成果类型' }]}
                >
                  <Select placeholder="请选择成果类型">
                    <Option value="competition">竞赛</Option>
                    <Option value="award">教学成果奖</Option>
                    <Option value="paper">论文</Option>
                    <Option value="textbook">教材</Option>
                    <Option value="patent">专利</Option>
                    <Option value="software">软著</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="level"
                  label="级别"
                  rules={[{ required: true, message: '请选择级别' }]}
                >
                  <Select placeholder="请选择级别">
                    <Option value="national">国家级</Option>
                    <Option value="provincial">省级</Option>
                    <Option value="municipal">市级</Option>
                    <Option value="school">校级</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="title"
              label="成果名称"
              rules={[{ required: true, message: '请输入成果名称' }]}
            >
              <Input placeholder="请输入成果名称" />
            </Form.Item>
            <Form.Item
              name="description"
              label="成果描述"
              rules={[{ required: true, message: '请输入成果描述' }]}
            >
              <TextArea rows={3} placeholder="请输入成果描述" />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="rank"
                  label="排名/等级"
                  rules={[{ required: true, message: '请输入排名或等级' }]}
                >
                  <Input placeholder="如：第一名、一等奖等" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="awardTime"
                  label="获奖时间"
                  rules={[{ required: true, message: '请选择获奖时间' }]}
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="organizer"
              label="主办单位"
              rules={[{ required: true, message: '请输入主办单位' }]}
            >
              <Input placeholder="请输入主办单位" />
            </Form.Item>
            <Form.Item
              name="certificateFile"
              label="证书文件"
            >
              <Upload
                customRequest={async ({ file, onSuccess, onError }) => {
                  try {
                    const result = await handleFileUpload(file);
                    if (result) {
                      onSuccess?.(result);
                    } else {
                      onError?.(new Error('上传失败'));
                    }
                  } catch (error) {
                    onError?.(error as Error);
                  }
                }}
                maxCount={1}
                accept=".pdf,.jpg,.jpeg,.png"
                showUploadList={{
                  showPreviewIcon: true,
                  showDownloadIcon: true,
                  showRemoveIcon: true,
                }}
                onPreview={(file) => {
                  if (file.url) {
                    const correctedUrl = getFileUrl(file.url);
                    window.open(correctedUrl, '_blank');
                  }
                }}
              >
                <Button icon={<UploadOutlined />}>上传证书</Button>
              </Upload>
            </Form.Item>
          </>
        );

      case 'trainings':
        return (
          <>
            <Form.Item
              name="trainingName"
              label="培训名称"
              rules={[{ required: true, message: '请输入培训名称' }]}
            >
              <Input placeholder="请输入培训名称" />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="trainingType"
                  label="培训类型"
                  rules={[{ required: true, message: '请选择培训类型' }]}
                >
                  <Select placeholder="请选择培训类型">
                    <Option value="academic">学术培训</Option>
                    <Option value="skill">技能培训</Option>
                    <Option value="management">管理培训</Option>
                    <Option value="other">其他培训</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="organizer"
                  label="主办单位"
                  rules={[{ required: true, message: '请输入主办单位' }]}
                >
                  <Input placeholder="请输入主办单位" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="startDate"
                  label="开始时间"
                  rules={[{ required: true, message: '请选择开始时间' }]}
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="endDate"
                  label="结束时间"
                  rules={[{ required: true, message: '请选择结束时间' }]}
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="duration"
                  label="培训时长(小时)"
                  rules={[{ required: true, message: '请输入培训时长' }]}
                >
                  <Input type="number" placeholder="请输入培训时长" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="location"
                  label="培训地点"
                  rules={[{ required: true, message: '请输入培训地点' }]}
                >
                  <Input placeholder="请输入培训地点" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="trainingContent"
              label="培训内容"
              rules={[{ required: true, message: '请输入培训内容' }]}
            >
              <TextArea rows={4} placeholder="请输入培训内容" />
            </Form.Item>
            <Form.Item
              name="certificateFile"
              label="培训证书"
            >
              <Upload
                customRequest={async ({ file, onSuccess, onError }) => {
                  try {
                    const result = await handleFileUpload(file);
                    if (result) {
                      onSuccess?.(result);
                    } else {
                      onError?.(new Error('上传失败'));
                    }
                  } catch (error) {
                    onError?.(error as Error);
                  }
                }}
                maxCount={1}
                accept=".pdf,.jpg,.jpeg,.png"
                showUploadList={{
                  showPreviewIcon: true,
                  showDownloadIcon: true,
                  showRemoveIcon: true,
                }}
                onPreview={(file) => {
                  if (file.url) {
                    const correctedUrl = getFileUrl(file.url);
                    window.open(correctedUrl, '_blank');
                  }
                }}
              >
                <Button icon={<UploadOutlined />}>上传证书</Button>
              </Upload>
            </Form.Item>
          </>
        );

      case 'mentorships':
        return (
          <>
            <Form.Item
              name="projectName"
              label="项目名称"
              rules={[{ required: true, message: '请输入项目名称' }]}
            >
              <Input placeholder="请输入项目名称" />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="projectType"
                  label="项目类型"
                  rules={[{ required: true, message: '请选择项目类型' }]}
                >
                  <Select placeholder="请选择项目类型">
                    <Option value="teaching">教学指导</Option>
                    <Option value="research">科研指导</Option>
                    <Option value="comprehensive">综合指导</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="menteeName"
                  label="学员姓名"
                  rules={[{ required: true, message: '请输入学员姓名' }]}
                >
                  <Input placeholder="请输入学员姓名" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="startDate"
                  label="开始时间"
                  rules={[{ required: true, message: '请选择开始时间' }]}
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="endDate"
                  label="结束时间"
                  rules={[{ required: true, message: '请选择结束时间' }]}
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="objectives"
              label="项目目标"
              rules={[{ required: true, message: '请输入项目目标' }]}
            >
              <Select
                mode="tags"
                placeholder="请输入项目目标，支持多个目标"
                style={{ width: '100%' }}
              >
                <Option value="提升教学能力">提升教学能力</Option>
                <Option value="科研能力培养">科研能力培养</Option>
                <Option value="课程建设">课程建设</Option>
                <Option value="教学改革">教学改革</Option>
              </Select>
            </Form.Item>
            <Form.Item
              name="currentProgress"
              label="当前进展"
            >
              <TextArea rows={3} placeholder="请输入当前项目进展" />
            </Form.Item>
          </>
        );

      case 'files':
        return (
          <>
            <Form.Item
              name="fileName"
              label="文件名称"
              rules={[{ required: true, message: '请输入文件名称' }]}
            >
              <Input placeholder="请输入文件名称" />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="fileType"
                  label="文件类型"
                  rules={[{ required: true, message: '请选择文件类型' }]}
                >
                  <Select placeholder="请选择文件类型">
                    <Option value="document">文档资料</Option>
                    <Option value="image">图片文件</Option>
                    <Option value="video">视频文件</Option>
                    <Option value="other">其他文件</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="category"
                  label="文件分类"
                  rules={[{ required: true, message: '请选择文件分类' }]}
                >
                  <Select placeholder="请选择文件分类">
                    <Option value="certificate">证书材料</Option>
                    <Option value="teaching">教学材料</Option>
                    <Option value="research">科研材料</Option>
                    <Option value="personal">个人资料</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="description"
              label="文件描述"
            >
              <TextArea rows={3} placeholder="请输入文件描述" />
            </Form.Item>
            <Form.Item
              name="file"
              label="选择文件"
              rules={[{ required: !editingRecord, message: '请选择文件' }]}
            >
              <Upload
                customRequest={async ({ file, onSuccess, onError }) => {
                  try {
                    const result = await handleFileUpload(file);
                    if (result) {
                      onSuccess?.(result);
                    } else {
                      onError?.(new Error('上传失败'));
                    }
                  } catch (error) {
                    onError?.(error as Error);
                  }
                }}
                maxCount={1}
                accept="*"
                showUploadList={{
                  showPreviewIcon: true,
                  showDownloadIcon: true,
                  showRemoveIcon: true,
                }}
                onPreview={(file) => {
                  if (file.url) {
                    const correctedUrl = getFileUrl(file.url);
                    window.open(correctedUrl, '_blank');
                  }
                }}
              >
                <Button icon={<UploadOutlined />}>选择文件</Button>
              </Upload>
            </Form.Item>
          </>
        );

      case 'files':
        return (
          <div>
            <div className="mb-4">
              <Space>
                <Button
                  type="primary"
                  icon={<UploadOutlined />}
                  onClick={() => {
                    setActiveTab('files');
                    setEditingRecord(null);
                    setModalVisible(true);
                    form.resetFields();
                  }}
                >
                  上传文件
                </Button>
                <Button
                  icon={<DownloadOutlined />}
                  onClick={handleExportArchive}
                >
                  导出档案
                </Button>
              </Space>
            </div>

            <Table
              dataSource={fileList}
              columns={[
                {
                  title: '文件名',
                  dataIndex: 'name',
                  key: 'name',
                  render: (text: string, record: any) => (
                    <Button
                      type="link"
                      onClick={() => handleFilePreview(record)}
                    >
                      {text}
                    </Button>
                  ),
                },
                {
                  title: '文件类型',
                  dataIndex: 'type',
                  key: 'type',
                  width: 100,
                },
                {
                  title: '文件大小',
                  dataIndex: 'size',
                  key: 'size',
                  width: 100,
                  render: (size: number) => {
                    if (size < 1024) return `${size}B`;
                    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`;
                    return `${(size / (1024 * 1024)).toFixed(1)}MB`;
                  },
                },
                {
                  title: '上传时间',
                  dataIndex: 'uploadTime',
                  key: 'uploadTime',
                  width: 150,
                  render: (time: string) => time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '-',
                },
                {
                  title: '操作',
                  key: 'action',
                  width: 150,
                  render: (_, record: any) => (
                    <Space>
                      <Button
                        type="link"
                        size="small"
                        icon={<EyeOutlined />}
                        onClick={() => handleFilePreview(record)}
                      >
                        预览
                      </Button>
                      <Button
                        type="link"
                        size="small"
                        icon={<DownloadOutlined />}
                        onClick={() => handleFileDownload(record)}
                      >
                        下载
                      </Button>
                      <Button
                        type="link"
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => handleDelete(record)}
                      >
                        删除
                      </Button>
                    </Space>
                  ),
                },
              ]}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个文件`,
              }}
            />
          </div>
        );

      default:
        return (
          <div className="text-center py-8 text-gray-500">
            该功能正在开发中，敬请期待...
          </div>
        );
    }
  };

  // 渲染详情内容
  const renderDetailContent = () => {
    if (!selectedRecord) return null;

    console.log('🔍 renderDetailContent - selectedRecord:', selectedRecord);

    // 直接显示教师基础信息，不依赖activeTab
    return (
      <div className="space-y-4">
        <div style={{background: 'lightblue', padding: '15px', marginBottom: '20px'}}>
          <h3>🎯 教师详细信息</h3>
          <p>如果您能看到这个蓝色框，说明模态框正常工作！</p>
        </div>
            <Row gutter={16}>
              <Col span={8}>
                <div><strong>姓名:</strong> {selectedRecord.name || '未填写'}</div>
              </Col>
              <Col span={8}>
                <div><strong>身份证号:</strong> {selectedRecord.id_card || '未填写'}</div>
              </Col>
              <Col span={8}>
                <div><strong>性别:</strong> {selectedRecord.gender === 'male' ? '男' : selectedRecord.gender === 'female' ? '女' : '未填写'}</div>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <div><strong>民族:</strong> {selectedRecord.ethnicity || '未填写'}</div>
              </Col>
              <Col span={8}>
                <div><strong>政治面貌:</strong> {selectedRecord.political_status || '未填写'}</div>
              </Col>
              <Col span={8}>
                <div><strong>联系电话:</strong> {selectedRecord.phone || '未填写'}</div>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <div><strong>职称:</strong> {selectedRecord.title || '未填写'}</div>
              </Col>
              <Col span={8}>
                <div><strong>学历:</strong> {selectedRecord.degree || '未填写'}</div>
              </Col>
              <Col span={8}>
                <div><strong>入职年份:</strong> {selectedRecord.join_year || '未填写'}</div>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <div><strong>家庭住址:</strong> {selectedRecord.address || '未填写'}</div>
              </Col>
              <Col span={12}>
                <div><strong>个人简历:</strong> {selectedRecord.resume || '未填写'}</div>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <div><strong>紧急联系人:</strong> {selectedRecord.emergency_contact || '未填写'}</div>
              </Col>
              <Col span={12}>
                <div><strong>联系人电话:</strong> {selectedRecord.emergency_phone || '未填写'}</div>
              </Col>
            </Row>
            <div><strong>状态:</strong> {selectedRecord.status === 'active' ? '在职' : '离职'}</div>
            <div><strong>创建时间:</strong> {dayjs(selectedRecord.created_at).format('YYYY-MM-DD HH:mm:ss')}</div>
            <div><strong>更新时间:</strong> {dayjs(selectedRecord.updated_at).format('YYYY-MM-DD HH:mm:ss')}</div>
          </div>
        );
  };

  // 渲染统计卡片
  const renderStatistics = () => {
    if (!statistics) return null;

    return (
      <Row gutter={16} className="mb-6">
        <Col span={4}>
          <Card>
            <Statistic
              title="基础信息"
              value={basicInfo ? 1 : 0}
              suffix="份"
              prefix={<UserOutlined />}
              valueStyle={{ color: basicInfo ? '#52c41a' : '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="任课总数"
              value={statistics.totalCourses}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="教学材料"
              value={teachingMaterials.length}
              suffix="份"
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="成果总数"
              value={statistics.totalAchievements}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="积分总计"
              value={statistics.totalPoints}
              prefix={<StarOutlined />}
              valueStyle={{ color: '#eb2f96' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="培训记录"
              value={trainings.length}
              suffix="次"
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#13c2c2' }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // 调试信息 - 强制更新 v2
  console.log('组件渲染，当前状态:', { basicInfo, loading });
  console.log('强制更新时间:', new Date().toISOString());

  return (
    <div className="space-y-6">
      {/* 强制显示测试信息 */}
      <div style={{background: 'red', color: 'white', padding: '20px', fontSize: '16px', marginBottom: '20px'}}>
        <h2>🔥 强制测试显示 🔥</h2>
        <p>如果您能看到这个红色框，说明前端代码已经更新！</p>
        <p>basicInfo状态: {basicInfo ? 'EXISTS' : 'NULL'}</p>
        <p>loading状态: {loading ? 'TRUE' : 'FALSE'}</p>
        {basicInfo && (
          <div>
            <p>姓名: {basicInfo.name}</p>
            <p>身份证: {basicInfo.idCard}</p>
            <p>地址: {basicInfo.homeAddress}</p>
            <p>联系人: {basicInfo.emergencyContact}</p>
            <p>电话: {basicInfo.emergencyPhone}</p>
          </div>
        )}
      </div>

      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">教师业务档案</h1>
          <p className="text-gray-600">管理教师基础信息和教学工作档案</p>
        </div>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            新增记录
          </Button>
          <Button
            type="default"
            onClick={handleLoadTestData}
          >
            加载测试数据
          </Button>
          <Button
            type="default"
            onClick={() => {
              message.info('正在测试API连接...');
              loadData();
            }}
          >
            测试API连接
          </Button>
        </Space>
      </div>

      {/* 统计信息 */}
      {renderStatistics()}

      {/* 主要内容 */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'basicInfo',
              label: (
                <span>
                  <UserOutlined />
                  基础信息
                </span>
              ),
              children: (
                <div className="space-y-6">
                {basicInfo ? (
                  <div>
                {/* 基本信息展示 */}
                <Card title="基本信息" size="small">
                  <Row gutter={16}>
                    <Col span={8}>
                      <div className="mb-4">
                        <span className="text-gray-600">姓名：</span>
                        <span className="font-medium">{basicInfo?.name || '未填写'}</span>
                      </div>
                    </Col>
                    <Col span={8}>
                      <div className="mb-4">
                        <span className="text-gray-600">身份证号：</span>
                        <span className="font-medium">{basicInfo?.idCard || '未填写'}</span>
                      </div>
                    </Col>
                    <Col span={8}>
                      <div className="mb-4">
                        <span className="text-gray-600">性别：</span>
                        <span className="font-medium">{basicInfo?.gender === 'male' ? '男' : basicInfo?.gender === 'female' ? '女' : '未填写'}</span>
                      </div>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    <Col span={8}>
                      <div className="mb-4">
                        <span className="text-gray-600">民族：</span>
                        <span className="font-medium">{basicInfo.ethnicity}</span>
                      </div>
                    </Col>
                    <Col span={8}>
                      <div className="mb-4">
                        <span className="text-gray-600">政治面貌：</span>
                        <span className="font-medium">{basicInfo.politicalStatus}</span>
                      </div>
                    </Col>
                    <Col span={8}>
                      <div className="mb-4">
                        <span className="text-gray-600">联系电话：</span>
                        <span className="font-medium">{basicInfo.phone}</span>
                      </div>
                    </Col>
                  </Row>
                </Card>

                {/* 职业信息展示 */}
                <Card title="职业信息" size="small">
                  <Row gutter={16}>
                    <Col span={8}>
                      <div className="mb-4">
                        <span className="text-gray-600">职称：</span>
                        <span className="font-medium">{basicInfo.title}</span>
                      </div>
                    </Col>
                    <Col span={8}>
                      <div className="mb-4">
                        <span className="text-gray-600">入校年份：</span>
                        <span className="font-medium">{basicInfo.entryYear}</span>
                      </div>
                    </Col>
                    <Col span={8}>
                      <div className="mb-4">
                        <span className="text-gray-600">家庭住址：</span>
                        <span className="font-medium">{basicInfo?.homeAddress || '未填写'}</span>
                      </div>
                    </Col>
                  </Row>
                </Card>

                {/* 紧急联系人信息 */}
                <Card title="紧急联系人" size="small">
                  <Row gutter={16}>
                    <Col span={12}>
                      <div className="mb-4">
                        <span className="text-gray-600">联系人姓名：</span>
                        <span className="font-medium">{basicInfo?.emergencyContact || '未填写'}</span>
                      </div>
                    </Col>
                    <Col span={12}>
                      <div className="mb-4">
                        <span className="text-gray-600">联系人电话：</span>
                        <span className="font-medium">{basicInfo?.emergencyPhone || '未填写'}</span>
                      </div>
                    </Col>
                  </Row>
                </Card>

                {/* 证书材料 */}
                <Card title="证书材料" size="small">
                  <Row gutter={16}>
                    {basicInfo.titleCertificate && (
                      <Col span={6}>
                        <div className="text-center p-4 border rounded">
                          <FileTextOutlined className="text-2xl text-blue-500 mb-2" />
                          <div className="text-sm">职称证</div>
                          <Button type="link" size="small" onClick={() => window.open(basicInfo.titleCertificate.url)}>
                            查看
                          </Button>
                        </div>
                      </Col>
                    )}
                    {basicInfo.degreeCertificate && (
                      <Col span={6}>
                        <div className="text-center p-4 border rounded">
                          <FileTextOutlined className="text-2xl text-green-500 mb-2" />
                          <div className="text-sm">学位证</div>
                          <Button type="link" size="small" onClick={() => window.open(basicInfo.degreeCertificate.url)}>
                            查看
                          </Button>
                        </div>
                      </Col>
                    )}
                    {basicInfo.diplomaCertificate && (
                      <Col span={6}>
                        <div className="text-center p-4 border rounded">
                          <FileTextOutlined className="text-2xl text-orange-500 mb-2" />
                          <div className="text-sm">毕业证</div>
                          <Button type="link" size="small" onClick={() => window.open(basicInfo.diplomaCertificate.url)}>
                            查看
                          </Button>
                        </div>
                      </Col>
                    )}
                  </Row>
                </Card>

                {/* 个人简历 */}
                <Card title="个人简历" size="small">
                  <div className="whitespace-pre-wrap">{basicInfo.resume}</div>
                </Card>

                {/* 编辑按钮 */}
                <div className="text-center">
                  <Button
                    type="primary"
                    icon={<EditOutlined />}
                    onClick={() => {
                      setEditingRecord(basicInfo);
                      form.setFieldsValue({
                        employeeId: basicInfo.employeeId,
                        name: basicInfo.name,
                        idCard: basicInfo.idCard,
                        gender: basicInfo.gender,
                        ethnicity: basicInfo.ethnicity,
                        politicalStatus: basicInfo.politicalStatus,
                        phone: basicInfo.phone,
                        title: basicInfo.title,
                        degree: basicInfo.degree,
                        entryYear: basicInfo.entryYear ? dayjs(basicInfo.entryYear.toString()) : undefined,
                        homeAddress: basicInfo.homeAddress,
                        emergencyContact: basicInfo.emergencyContact,
                        emergencyPhone: basicInfo.emergencyPhone,
                        resume: basicInfo.resume,
                        graduationCert: basicInfo.graduationCert,
                        degreeCert: basicInfo.degreeCert,
                        titleCert: basicInfo.titleCert,
                        skillCerts: basicInfo.skillCerts,
                        otherCerts: basicInfo.otherCerts,
                      });
                      setModalVisible(true);
                    }}
                  >
                    编辑基础信息
                  </Button>
                </div>
                  </div>
            ) : (
              <div className="text-center py-16">
                <UserOutlined className="text-6xl text-gray-300 mb-4" />
                <h3 className="text-lg text-gray-500 mb-4">尚未录入基础信息</h3>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setEditingRecord(null);
                    form.resetFields();
                    setModalVisible(true);
                  }}
                >
                  录入基础信息
                </Button>
              </div>
                )}
                </div>
              )
            },
            {
              key: 'courses',
              label: (
                <span>
                  <BookOutlined />
                  任课情况
                </span>
              ),
              children: (
            <Table
              dataSource={courseAssignments}
              loading={loading}
              rowKey="id"
              scroll={{ x: 1200 }}
              columns={[
                {
                  title: '课程名称',
                  dataIndex: 'courseName',
                  key: 'courseName',
                  width: 150,
                },
                {
                  title: '学期',
                  dataIndex: 'semester',
                  key: 'semester',
                  width: 100,
                },
                {
                  title: '学年',
                  dataIndex: 'academicYear',
                  key: 'academicYear',
                  width: 100,
                },
                {
                  title: '班级',
                  dataIndex: 'classNames',
                  key: 'classNames',
                  width: 200,
                  render: (classNames: string[]) => (
                    <div>
                      {classNames?.map((name, index) => (
                        <Tag key={index} color="blue" className="mb-1">
                          {name}
                        </Tag>
                      ))}
                    </div>
                  ),
                },
                {
                  title: '课程类型',
                  dataIndex: 'courseType',
                  key: 'courseType',
                  width: 100,
                  render: (type: string) => {
                    const typeMap = {
                      theory: { text: '理论课', color: 'blue' },
                      practice: { text: '实践课', color: 'green' },
                      mixed: { text: '理实一体', color: 'orange' },
                    };
                    const config = typeMap[type as keyof typeof typeMap] || { text: type, color: 'default' };
                    return <Tag color={config.color}>{config.text}</Tag>;
                  },
                },
                {
                  title: '课时数',
                  dataIndex: 'courseHours',
                  key: 'courseHours',
                  width: 80,
                  render: (hours: number) => `${hours}h`,
                },
                {
                  title: '学生数',
                  dataIndex: 'studentCount',
                  key: 'studentCount',
                  width: 80,
                },
                {
                  title: '状态',
                  dataIndex: 'status',
                  key: 'status',
                  width: 100,
                  render: (status: string) => {
                    const statusMap = {
                      active: { text: '进行中', color: 'green' },
                      completed: { text: '已完成', color: 'blue' },
                      cancelled: { text: '已取消', color: 'red' },
                    };
                    const config = statusMap[status as keyof typeof statusMap] || { text: status, color: 'default' };
                    return <Tag color={config.color}>{config.text}</Tag>;
                  },
                },
                {
                  title: '操作',
                  key: 'action',
                  width: 150,
                  fixed: 'right',
                  render: (_, record) => (
                    <Space size="small">
                      <Tooltip title="查看详情">
                        <Button
                          type="text"
                          icon={<EyeOutlined />}
                          onClick={() => handleView(record)}
                        />
                      </Tooltip>
                      <Tooltip title="编辑">
                        <Button
                          type="text"
                          icon={<EditOutlined />}
                          onClick={() => handleEdit(record)}
                        />
                      </Tooltip>
                      <Tooltip title="删除">
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => handleDelete(record)}
                        />
                      </Tooltip>
                    </Space>
                  ),
                },
              ]}
            />
              )
            },
            {
              key: 'materials',
              label: (
                <span>
                  <FileTextOutlined />
                  教学材料
                </span>
              ),
              children: (
            <Table
              dataSource={teachingMaterials}
              loading={loading}
              rowKey="id"
              scroll={{ x: 1200 }}
              columns={[
                {
                  title: '材料类型',
                  dataIndex: 'materialType',
                  key: 'materialType',
                  width: 120,
                  render: (type: string) => {
                    const typeMap = {
                      calendar: { text: '教学日历', color: 'blue' },
                      progress: { text: '进度计划', color: 'green' },
                      syllabus: { text: '教学大纲', color: 'orange' },
                      courseware: { text: '课件资料', color: 'purple' },
                      other: { text: '其他材料', color: 'default' },
                    };
                    const config = typeMap[type as keyof typeof typeMap] || { text: type, color: 'default' };
                    return <Tag color={config.color}>{config.text}</Tag>;
                  },
                },
                {
                  title: '材料名称',
                  dataIndex: 'materialName',
                  key: 'materialName',
                  width: 200,
                  ellipsis: true,
                },
                {
                  title: '课程名称',
                  dataIndex: 'courseName',
                  key: 'courseName',
                  width: 150,
                  ellipsis: true,
                },
                {
                  title: '学期',
                  dataIndex: 'semester',
                  key: 'semester',
                  width: 100,
                },
                {
                  title: '学年',
                  dataIndex: 'academicYear',
                  key: 'academicYear',
                  width: 120,
                },
                {
                  title: '文件名',
                  dataIndex: 'fileName',
                  key: 'fileName',
                  width: 180,
                  ellipsis: true,
                  render: (fileName: string, record: any) => (
                    <Tooltip title={fileName}>
                      <Button
                        type="link"
                        size="small"
                        onClick={() => handleFilePreview(record)}
                      >
                        {fileName}
                      </Button>
                    </Tooltip>
                  ),
                },
                {
                  title: '文件大小',
                  dataIndex: 'fileSize',
                  key: 'fileSize',
                  width: 100,
                  render: (size: number) => {
                    if (size < 1024) return `${size}B`;
                    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`;
                    return `${(size / (1024 * 1024)).toFixed(1)}MB`;
                  },
                },
                {
                  title: '上传时间',
                  dataIndex: 'uploadTime',
                  key: 'uploadTime',
                  width: 120,
                  render: (time: string) => time ? dayjs(time).format('MM-DD HH:mm') : '-',
                },
                {
                  title: '状态',
                  dataIndex: 'status',
                  key: 'status',
                  width: 100,
                  render: (status: string) => {
                    const statusMap = {
                      draft: { text: '草稿', color: 'default' },
                      submitted: { text: '已提交', color: 'blue' },
                      approved: { text: '已通过', color: 'green' },
                      rejected: { text: '已拒绝', color: 'red' },
                    };
                    const config = statusMap[status as keyof typeof statusMap] || { text: status, color: 'default' };
                    return <Tag color={config.color}>{config.text}</Tag>;
                  },
                },
                {
                  title: '操作',
                  key: 'action',
                  width: 150,
                  fixed: 'right',
                  render: (_, record) => (
                    <Space size="small">
                      <Tooltip title="查看详情">
                        <Button
                          type="text"
                          icon={<EyeOutlined />}
                          onClick={() => handleView(record)}
                        />
                      </Tooltip>
                      <Tooltip title="编辑">
                        <Button
                          type="text"
                          icon={<EditOutlined />}
                          onClick={() => handleEdit(record)}
                        />
                      </Tooltip>
                      <Tooltip title="删除">
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => handleDelete(record)}
                        />
                      </Tooltip>
                    </Space>
                  ),
                },
              ]}
            />
              )
            },
            {
              key: 'achievements',
              label: (
                <span>
                  <TrophyOutlined />
                  个人成果
                </span>
              ),
              children: (
            <Table
              dataSource={achievements}
              loading={loading}
              rowKey="id"
              scroll={{ x: 1200 }}
              columns={[
                {
                  title: '成果类型',
                  dataIndex: 'achievementType',
                  key: 'achievementType',
                  width: 100,
                  render: (type: string) => {
                    const typeMap = {
                      competition: { text: '竞赛', color: 'gold' },
                      award: { text: '教学成果奖', color: 'red' },
                      paper: { text: '论文', color: 'blue' },
                      textbook: { text: '教材', color: 'green' },
                      patent: { text: '专利', color: 'purple' },
                      software: { text: '软著', color: 'cyan' },
                    };
                    const config = typeMap[type as keyof typeof typeMap] || { text: type, color: 'default' };
                    return <Tag color={config.color}>{config.text}</Tag>;
                  },
                },
                {
                  title: '成果名称',
                  dataIndex: 'title',
                  key: 'title',
                  width: 200,
                  ellipsis: true,
                },
                {
                  title: '级别',
                  dataIndex: 'level',
                  key: 'level',
                  width: 100,
                  render: (level: string) => {
                    const levelMap = {
                      national: { text: '国家级', color: 'red' },
                      provincial: { text: '省级', color: 'orange' },
                      municipal: { text: '市级', color: 'blue' },
                      school: { text: '校级', color: 'green' },
                    };
                    const config = levelMap[level as keyof typeof levelMap] || { text: level, color: 'default' };
                    return <Tag color={config.color}>{config.text}</Tag>;
                  },
                },
                {
                  title: '排名/等级',
                  dataIndex: 'rank',
                  key: 'rank',
                  width: 100,
                },
                {
                  title: '获奖时间',
                  dataIndex: 'awardTime',
                  key: 'awardTime',
                  width: 120,
                  render: (date: string) => date ? dayjs(date).format('YYYY-MM-DD') : '-',
                },
                {
                  title: '主办单位',
                  dataIndex: 'organizer',
                  key: 'organizer',
                  width: 150,
                  ellipsis: true,
                },
                {
                  title: '积分',
                  dataIndex: 'points',
                  key: 'points',
                  width: 80,
                  render: (points: number) => (
                    <Badge count={points} style={{ backgroundColor: '#52c41a' }} />
                  ),
                },
                {
                  title: '状态',
                  dataIndex: 'status',
                  key: 'status',
                  width: 100,
                  render: (status: string) => {
                    const statusMap = {
                      pending: { text: '待审核', color: 'orange' },
                      approved: { text: '已通过', color: 'green' },
                      rejected: { text: '已拒绝', color: 'red' },
                    };
                    const config = statusMap[status as keyof typeof statusMap] || { text: status, color: 'default' };
                    return <Tag color={config.color}>{config.text}</Tag>;
                  },
                },
                {
                  title: '操作',
                  key: 'action',
                  width: 150,
                  fixed: 'right',
                  render: (_, record) => (
                    <Space size="small">
                      <Tooltip title="查看详情">
                        <Button
                          type="text"
                          icon={<EyeOutlined />}
                          onClick={() => handleView(record)}
                        />
                      </Tooltip>
                      <Tooltip title="编辑">
                        <Button
                          type="text"
                          icon={<EditOutlined />}
                          onClick={() => handleEdit(record)}
                        />
                      </Tooltip>
                      <Tooltip title="删除">
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => handleDelete(record)}
                        />
                      </Tooltip>
                    </Space>
                  ),
                },
              ]}
            />
              )
            },
            {
              key: 'trainings',
              label: (
                <span>
                  <TeamOutlined />
                  师资培训
                </span>
              ),
              children: (
            <Table
              dataSource={trainings}
              loading={loading}
              rowKey="id"
              scroll={{ x: 1200 }}
              columns={[
                {
                  title: '培训名称',
                  dataIndex: 'trainingName',
                  key: 'trainingName',
                  width: 200,
                  ellipsis: true,
                },
                {
                  title: '培训类型',
                  dataIndex: 'trainingType',
                  key: 'trainingType',
                  width: 120,
                  render: (type: string) => {
                    const typeMap = {
                      academic: { text: '学术培训', color: 'blue' },
                      skill: { text: '技能培训', color: 'green' },
                      management: { text: '管理培训', color: 'orange' },
                      other: { text: '其他培训', color: 'default' },
                    };
                    const config = typeMap[type as keyof typeof typeMap] || { text: type, color: 'default' };
                    return <Tag color={config.color}>{config.text}</Tag>;
                  },
                },
                {
                  title: '主办单位',
                  dataIndex: 'organizer',
                  key: 'organizer',
                  width: 150,
                  ellipsis: true,
                },
                {
                  title: '培训时间',
                  key: 'trainingPeriod',
                  width: 200,
                  render: (_, record) => (
                    <div>
                      <div>{dayjs(record.startDate).format('YYYY-MM-DD')}</div>
                      <div className="text-gray-500 text-xs">
                        至 {dayjs(record.endDate).format('YYYY-MM-DD')}
                      </div>
                    </div>
                  ),
                },
                {
                  title: '培训时长',
                  dataIndex: 'duration',
                  key: 'duration',
                  width: 100,
                  render: (duration: number) => `${duration}小时`,
                },
                {
                  title: '培训地点',
                  dataIndex: 'location',
                  key: 'location',
                  width: 120,
                  ellipsis: true,
                },
                {
                  title: '状态',
                  dataIndex: 'status',
                  key: 'status',
                  width: 100,
                  render: (status: string) => {
                    const statusMap = {
                      registered: { text: '已报名', color: 'blue' },
                      attending: { text: '进行中', color: 'orange' },
                      completed: { text: '已完成', color: 'green' },
                      cancelled: { text: '已取消', color: 'red' },
                    };
                    const config = statusMap[status as keyof typeof statusMap] || { text: status, color: 'default' };
                    return <Tag color={config.color}>{config.text}</Tag>;
                  },
                },
                {
                  title: '证书',
                  key: 'certificate',
                  width: 80,
                  render: (_, record) => (
                    record.certificateUrl ? (
                      <Button
                        type="link"
                        size="small"
                        onClick={() => handleCertificateView(record)}
                      >
                        查看
                      </Button>
                    ) : (
                      <span className="text-gray-400">无</span>
                    )
                  ),
                },
                {
                  title: '操作',
                  key: 'action',
                  width: 150,
                  fixed: 'right',
                  render: (_, record) => (
                    <Space size="small">
                      <Tooltip title="查看详情">
                        <Button
                          type="text"
                          icon={<EyeOutlined />}
                          onClick={() => handleView(record)}
                        />
                      </Tooltip>
                      <Tooltip title="编辑">
                        <Button
                          type="text"
                          icon={<EditOutlined />}
                          onClick={() => handleEdit(record)}
                        />
                      </Tooltip>
                      <Tooltip title="删除">
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => handleDelete(record)}
                        />
                      </Tooltip>
                    </Space>
                  ),
                },
              ]}
            />
              )
            },
            {
              key: 'mentorships',
              label: (
                <span>
                  <StarOutlined />
                  青蓝筑梦
                </span>
              ),
              children: (
            <Table
              dataSource={mentorships}
              loading={loading}
              rowKey="id"
              scroll={{ x: 1200 }}
              columns={[
                {
                  title: '项目名称',
                  dataIndex: 'projectName',
                  key: 'projectName',
                  width: 200,
                  ellipsis: true,
                },
                {
                  title: '项目类型',
                  dataIndex: 'projectType',
                  key: 'projectType',
                  width: 120,
                  render: (type: string) => {
                    const typeMap = {
                      teaching: { text: '教学指导', color: 'blue' },
                      research: { text: '科研指导', color: 'green' },
                      comprehensive: { text: '综合指导', color: 'orange' },
                    };
                    const config = typeMap[type as keyof typeof typeMap] || { text: type, color: 'default' };
                    return <Tag color={config.color}>{config.text}</Tag>;
                  },
                },
                {
                  title: '导师',
                  dataIndex: 'mentorName',
                  key: 'mentorName',
                  width: 100,
                },
                {
                  title: '学员',
                  dataIndex: 'menteeName',
                  key: 'menteeName',
                  width: 100,
                },
                {
                  title: '项目周期',
                  key: 'projectPeriod',
                  width: 200,
                  render: (_, record) => (
                    <div>
                      <div>{dayjs(record.startDate).format('YYYY-MM-DD')}</div>
                      <div className="text-gray-500 text-xs">
                        至 {dayjs(record.endDate).format('YYYY-MM-DD')}
                      </div>
                    </div>
                  ),
                },
                {
                  title: '进度',
                  key: 'progress',
                  width: 150,
                  render: (_, record) => (
                    <div>
                      <Progress
                        percent={record.progressPercentage}
                        size="small"
                        status={record.progressPercentage === 100 ? 'success' : 'active'}
                      />
                      <div className="text-xs text-gray-500 mt-1">
                        {record.progressPercentage}%
                      </div>
                    </div>
                  ),
                },
                {
                  title: '里程碑',
                  key: 'milestones',
                  width: 120,
                  render: (_, record) => {
                    const total = record.milestones?.length || 0;
                    const completed = record.milestones?.filter((m: any) => m.status === 'completed').length || 0;
                    return (
                      <div className="text-center">
                        <Badge count={completed} style={{ backgroundColor: '#52c41a' }} />
                        <span className="text-gray-500">/{total}</span>
                      </div>
                    );
                  },
                },
                {
                  title: '状态',
                  dataIndex: 'status',
                  key: 'status',
                  width: 100,
                  render: (status: string) => {
                    const statusMap = {
                      planning: { text: '规划中', color: 'blue' },
                      active: { text: '进行中', color: 'green' },
                      completed: { text: '已完成', color: 'success' },
                      suspended: { text: '已暂停', color: 'orange' },
                    };
                    const config = statusMap[status as keyof typeof statusMap] || { text: status, color: 'default' };
                    return <Tag color={config.color}>{config.text}</Tag>;
                  },
                },
                {
                  title: '操作',
                  key: 'action',
                  width: 180,
                  fixed: 'right',
                  render: (_, record) => (
                    <Space size="small">
                      <Tooltip title="查看详情">
                        <Button
                          type="text"
                          icon={<EyeOutlined />}
                          onClick={() => handleView(record)}
                        />
                      </Tooltip>
                      <Tooltip title="更新进度">
                        <Button
                          type="text"
                          icon={<BarChartOutlined />}
                          onClick={() => handleProgressUpdate(record)}
                        />
                      </Tooltip>
                      <Tooltip title="编辑">
                        <Button
                          type="text"
                          icon={<EditOutlined />}
                          onClick={() => handleEdit(record)}
                        />
                      </Tooltip>
                      <Tooltip title="删除">
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => handleDelete(record)}
                        />
                      </Tooltip>
                    </Space>
                  ),
                },
              ]}
            />
              )
            }
          ]}
        />
      </Card>

      {/* 添加/编辑模态框 */}
      <Modal
        title={editingRecord ? '编辑记录' : '新增记录'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        width={800}
        destroyOnHidden
      >
        <Form form={form} layout="vertical">
          {renderFormContent()}
        </Form>
      </Modal>

      {/* 详情模态框 */}
      <Modal
        title="详细信息"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedRecord && renderDetailContent()}
      </Modal>
      {contextHolder}
    </div>
  );
};

export default TeachingWorkList;
