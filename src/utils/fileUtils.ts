// 获取文件完整访问URL
export const getFileUrl = (filePath: string): string => {
  if (!filePath) return '';

  // 如果已经是完整URL，检查并修复端口
  if (filePath.startsWith('http')) {
    // 如果URL包含错误的8085端口，替换为正确的3002端口
    if (filePath.includes(':8085/')) {
      return filePath.replace(':8085/', ':3002/');
    }
    return filePath;
  }

  // 构建完整的文件访问URL - 确保使用正确的后端端口3002
  const baseUrl = 'http://8.130.81.46:3002';
  return `${baseUrl}${filePath.startsWith('/') ? '' : '/'}${filePath}`;
};