// 获取文件完整访问URL
export const getFileUrl = (filePath: string): string => {
  console.log('🔍 getFileUrl 输入:', filePath);

  if (!filePath) {
    console.log('❌ 文件路径为空');
    return '';
  }

  // 如果已经是完整URL，检查并修复端口
  if (filePath.startsWith('http')) {
    console.log('🌐 检测到完整URL:', filePath);

    // 强制替换所有8085端口为3002端口
    let correctedUrl = filePath;

    // 替换8085端口为3002端口
    if (correctedUrl.includes(':8085')) {
      console.log('🔧 发现8085端口，正在修复...');
      correctedUrl = correctedUrl.replace(/8\.130\.81\.46:8085/g, '8.130.81.46:3002');
      console.log('✅ 修复后URL:', correctedUrl);
    }

    return correctedUrl;
  }

  // 构建完整的文件访问URL - 确保使用正确的后端端口3002
  const baseUrl = 'http://8.130.81.46:3002';
  const finalUrl = `${baseUrl}${filePath.startsWith('/') ? '' : '/'}${filePath}`;
  console.log('🔗 构建的完整URL:', finalUrl);

  return finalUrl;
};

// 全局URL拦截器 - 拦截所有可能的8085端口URL
export const interceptFileUrl = (url: string): string => {
  if (!url) return url;

  // 强制修复任何包含8085端口的URL
  if (url.includes('8.130.81.46:8085')) {
    console.log('🚨 拦截到8085端口URL:', url);
    const fixedUrl = url.replace(/8\.130\.81\.46:8085/g, '8.130.81.46:3002');
    console.log('🔧 修复为:', fixedUrl);
    return fixedUrl;
  }

  return url;
};