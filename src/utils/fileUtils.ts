// 获取文件完整访问URL
export const getFileUrl = (filePath: string): string => {
  if (!filePath) return '';

  // 如果已经是完整URL，检查并修复端口
  if (filePath.startsWith('http')) {
    // 强制替换所有8085端口为3002端口
    let correctedUrl = filePath;

    // 替换8085端口为3002端口
    if (correctedUrl.includes(':8085/')) {
      correctedUrl = correctedUrl.replace(':8085/', ':3002/');
    }

    // 如果URL指向前端端口，重定向到后端端口的uploads路径
    if (correctedUrl.includes('8.130.81.46:8085/uploads/')) {
      correctedUrl = correctedUrl.replace('8.130.81.46:8085/uploads/', '8.130.81.46:3002/uploads/');
    }

    return correctedUrl;
  }

  // 构建完整的文件访问URL - 确保使用正确的后端端口3002
  const baseUrl = 'http://8.130.81.46:3002';
  return `${baseUrl}${filePath.startsWith('/') ? '' : '/'}${filePath}`;
};